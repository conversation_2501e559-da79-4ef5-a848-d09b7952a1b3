using System.ComponentModel;

namespace HabitBuilder.Models;

public class ColorOption : INotifyPropertyChanged
{
    public string Name { get; set; } = string.Empty;
    public string HexValue { get; set; } = string.Empty;
    public Color Color => Color.FromArgb(HexValue);

    private bool _isSelected = false;
    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            if (_isSelected != value)
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    public ColorOption(string name, string hexValue)
    {
        Name = name;
        HexValue = hexValue;
    }

    public static List<ColorOption> GetDefaultColors()
    {
        return new List<ColorOption>
        {
            new("Purple", "#512BD4"),
            new("Red", "#FF6B6B"),
            new("Teal", "#4ECDC4"),
            new("Blue", "#45B7D1"),
            new("Green", "#96CEB4"),
            new("Yellow", "#FFEAA7"),
            new("Pink", "#DDA0DD"),
            new("Mint", "#98D8C8"),
            new("Gold", "#F7DC6F"),
            new("Lavender", "#BB8FCE"),
            new("Orange", "#FFA726"),
            new("Cyan", "#26C6DA"),
            new("Lime", "#66BB6A"),
            new("Indigo", "#5C6BC0"),
            new("Rose", "#EC407A")
        };
    }
}
