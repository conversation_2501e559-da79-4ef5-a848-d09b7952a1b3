using Azure;
using Azure.Data.Tables;
using HabitBuilder.Constants;

namespace HabitBuilder.Models;

public class Activity : ITableEntity
{
    public string PartitionKey { get; set; } = string.Empty; // Will be user ID
    public string RowKey { get; set; } = string.Empty; // Will be activity ID
    public DateTimeOffset? Timestamp { get; set; }
    public ETag ETag { get; set; }

    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Frequency { get; set; } = AppConstants.ActivityFrequencies.Weekly;
    public int TargetCount { get; set; } = 1; // How many times per frequency period
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? ArchivedAt { get; set; }
    public bool IsArchived { get; set; } = false;
    public DateTime? StartDate { get; set; } // For limited period activities
    public DateTime? EndDate { get; set; } // For limited period activities
    public string Color { get; set; } = AppConstants.UI.PrimaryColor;
    public int SortOrder { get; set; } = 0;
}

public class ActivityCompletion : ITableEntity
{
    public string PartitionKey { get; set; } = string.Empty; // Will be user ID
    public string RowKey { get; set; } = string.Empty; // Will be completion ID
    public DateTimeOffset? Timestamp { get; set; }
    public ETag ETag { get; set; }

    public string ActivityId { get; set; } = string.Empty;
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
    public int Count { get; set; } = 1; // How many times completed on this date
    public string Notes { get; set; } = string.Empty;
}

public class ActivityViewModel
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Frequency { get; set; } = AppConstants.ActivityFrequencies.Weekly;
    public int TargetCount { get; set; } = 1;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? ArchivedAt { get; set; }
    public bool IsArchived { get; set; } = false;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string Color { get; set; } = AppConstants.UI.PrimaryColor;
    public int SortOrder { get; set; } = 0;

    // Computed properties
    public int CurrentWeekCompletions { get; set; } = 0;
    public int CurrentFortnightCompletions { get; set; } = 0;
    public List<DayCompletion> WeeklyCompletions { get; set; } = new();
    public List<DayCompletion> FortnightlyCompletions { get; set; } = new();
    public double CurrentScore { get; set; } = 0;
    public List<WeeklyScore> WeeklyHistory { get; set; } = new();
    public DateTime? LastCompletedAt { get; set; }
    public bool CanCompleteToday { get; set; } = true;
    public DateTime? LastUndoableCompletion { get; set; }
}

public class DayCompletion
{
    public DateTime Date { get; set; }
    public int Count { get; set; } = 0;
    public bool IsCompleted => Count > 0;
    public bool IsToday => Date.Date == DateTime.Today;
}

public class WeeklyScore
{
    public DateTime WeekStartDate { get; set; }
    public DateTime WeekEndDate { get; set; }
    public int CompletedCount { get; set; }
    public int TargetCount { get; set; }
    public double Score { get; set; }
    public double Percentage => TargetCount > 0 ? (double)CompletedCount / TargetCount * 100 : 0;
}
