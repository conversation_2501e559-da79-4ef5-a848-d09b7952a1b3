using HabitBuilder.Constants;

namespace HabitBuilder.Models;

public class ScoringResult
{
    public double TotalScore { get; set; }
    public double MaxPossibleScore { get; set; }
    public double Percentage => MaxPossibleScore > 0 ? (TotalScore / MaxPossibleScore) * 100 : 0;
    public List<ActivityScore> ActivityScores { get; set; } = new();
    public string ResolveLevel { get; set; } = AppConstants.Defaults.DefaultResolveLevel;
    public DateTime CalculatedAt { get; set; } = DateTime.UtcNow;
}

public class ActivityScore
{
    public string ActivityId { get; set; } = string.Empty;
    public string ActivityName { get; set; } = string.Empty;
    public int CompletedCount { get; set; }
    public int TargetCount { get; set; }
    public double RawScore { get; set; }
    public double AdjustedScore { get; set; }
    public double Percentage => TargetCount > 0 ? (double)CompletedCount / TargetCount * 100 : 0;
    public ScoreGrade Grade { get; set; } = ScoreGrade.Incomplete;
}

public enum ScoreGrade
{
    Incomplete,
    Poor,
    Fair,
    Good,
    Excellent,
    Perfect
}

public static class ScoringEngine
{
    public static ScoringResult CalculateScore(List<ActivityViewModel> activities, string resolveLevel)
    {
        var result = new ScoringResult
        {
            ResolveLevel = resolveLevel
        };

        var multiplier = GetResolveLevelMultiplier(resolveLevel);
        
        foreach (var activity in activities.Where(a => !a.IsArchived))
        {
            var activityScore = CalculateActivityScore(activity, multiplier);
            result.ActivityScores.Add(activityScore);
            result.TotalScore += activityScore.AdjustedScore;
            result.MaxPossibleScore += AppConstants.Scoring.MaxScorePerActivity;
        }

        return result;
    }

    private static ActivityScore CalculateActivityScore(ActivityViewModel activity, double multiplier)
    {
        var completedCount = activity.Frequency == AppConstants.ActivityFrequencies.Fortnightly 
            ? activity.CurrentFortnightCompletions 
            : activity.CurrentWeekCompletions;

        var rawScore = CalculateRawScore(completedCount, activity.TargetCount);
        var adjustedScore = rawScore * multiplier;

        return new ActivityScore
        {
            ActivityId = activity.Id,
            ActivityName = activity.Name,
            CompletedCount = completedCount,
            TargetCount = activity.TargetCount,
            RawScore = rawScore,
            AdjustedScore = Math.Min(adjustedScore, AppConstants.Scoring.MaxScorePerActivity),
            Grade = GetScoreGrade(completedCount, activity.TargetCount)
        };
    }

    private static double CalculateRawScore(int completed, int target)
    {
        if (target == 0) return 0;
        
        var percentage = (double)completed / target;
        
        // Base score calculation
        var baseScore = Math.Min(percentage, 1.0) * AppConstants.Scoring.MaxScorePerActivity;
        
        // Bonus for exceeding target (diminishing returns)
        if (completed > target)
        {
            var excess = completed - target;
            var bonusPercentage = Math.Min(excess / (double)target * 0.5, 0.2); // Max 20% bonus
            baseScore += bonusPercentage * AppConstants.Scoring.MaxScorePerActivity;
        }
        
        return baseScore;
    }

    private static double GetResolveLevelMultiplier(string resolveLevel)
    {
        return resolveLevel switch
        {
            AppConstants.ResolveLevels.Easygoing => AppConstants.Scoring.EasygoingMultiplier,
            AppConstants.ResolveLevels.Resolved => AppConstants.Scoring.ResolvedMultiplier,
            AppConstants.ResolveLevels.Clockwork => AppConstants.Scoring.ClockworkMultiplier,
            _ => AppConstants.Scoring.EasygoingMultiplier
        };
    }

    private static ScoreGrade GetScoreGrade(int completed, int target)
    {
        if (target == 0) return ScoreGrade.Incomplete;
        
        var percentage = (double)completed / target;
        
        return percentage switch
        {
            >= 1.0 => ScoreGrade.Perfect,
            >= 0.8 => ScoreGrade.Excellent,
            >= 0.6 => ScoreGrade.Good,
            >= 0.4 => ScoreGrade.Fair,
            > 0 => ScoreGrade.Poor,
            _ => ScoreGrade.Incomplete
        };
    }
}
