using Azure;
using Azure.Data.Tables;
using HabitBuilder.Constants;

namespace HabitBuilder.Models;

public class User : ITableEntity
{
    public string PartitionKey { get; set; } = "Users";
    public string RowKey { get; set; } = string.Empty; // Will be user ID
    public DateTimeOffset? Timestamp { get; set; }
    public ETag ETag { get; set; }

    public string Email { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string SubscriptionTier { get; set; } = AppConstants.Defaults.DefaultSubscriptionTier;
    public string ResolveLevel { get; set; } = AppConstants.Defaults.DefaultResolveLevel;
    public string Language { get; set; } = AppConstants.Defaults.DefaultLanguage;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime LastLoginAt { get; set; } = DateTime.UtcNow;
    public bool IsEmailVerified { get; set; } = false;
    public bool NotificationsEnabled { get; set; } = true;
    public int NotificationHour { get; set; } = AppConstants.Defaults.DefaultNotificationHour;
    public int NotificationMinute { get; set; } = AppConstants.Defaults.DefaultNotificationMinute;
    public string NotificationDaysDisabled { get; set; } = string.Empty; // JSON array of day numbers
    public DateTime? VacationStartDate { get; set; }
    public DateTime? VacationEndDate { get; set; }
    public bool IsDarkMode { get; set; } = false;
}

public class UserProfile
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string SubscriptionTier { get; set; } = AppConstants.Defaults.DefaultSubscriptionTier;
    public string ResolveLevel { get; set; } = AppConstants.Defaults.DefaultResolveLevel;
    public string Language { get; set; } = AppConstants.Defaults.DefaultLanguage;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime LastLoginAt { get; set; } = DateTime.UtcNow;
    public bool IsEmailVerified { get; set; } = false;
    public UserSettings Settings { get; set; } = new();
}

public class UserSettings
{
    public bool NotificationsEnabled { get; set; } = true;
    public int NotificationHour { get; set; } = AppConstants.Defaults.DefaultNotificationHour;
    public int NotificationMinute { get; set; } = AppConstants.Defaults.DefaultNotificationMinute;
    public List<int> NotificationDaysDisabled { get; set; } = new();
    public DateTime? VacationStartDate { get; set; }
    public DateTime? VacationEndDate { get; set; }
    public bool IsDarkMode { get; set; } = false;
    public bool TwiceADayNotifications { get; set; } = false;
    public int SecondNotificationHour { get; set; } = 18; // 6 PM
    public int SecondNotificationMinute { get; set; } = 0;
}
