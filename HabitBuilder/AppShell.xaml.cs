﻿using HabitBuilder.Views;

namespace HabitBuilder;

public partial class AppShell : Shell
{
	public AppShell()
	{
		InitializeComponent();

		// Register routes for navigation
		Routing.RegisterRoute("signup", typeof(SignupPage));
		Routing.RegisterRoute("login", typeof(LoginPage));
		Routing.RegisterRoute("onboarding", typeof(OnboardingPage));
		Routing.RegisterRoute("addactivity", typeof(AddActivityPage));
		Routing.RegisterRoute("activityhistory", typeof(ActivityHistoryPage));
		Routing.RegisterRoute("main", typeof(AppShell));
	}
}
