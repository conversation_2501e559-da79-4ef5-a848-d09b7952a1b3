﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:HabitBuilder.ViewModels"
             xmlns:models="clr-namespace:HabitBuilder.Models"
             x:Class="HabitBuilder.MainPage"
             x:DataType="viewmodels:MainViewModel"
             Title="Activities">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Progress Section -->
        <Frame Grid.Row="0"
               BackgroundColor="#512BD4"
               CornerRadius="0"
               HasShadow="False"
               Padding="20">
            <StackLayout Spacing="10">
                <Label Text="Your Progress"
                       TextColor="White"
                       FontSize="18"
                       FontAttributes="Bold"
                       HorizontalOptions="Center" />

                <ProgressBar Progress="{Binding TotalScore, Converter={StaticResource ScoreToProgressConverter}}"
                            ProgressColor="White"
                            BackgroundColor="#7B68EE"
                            HeightRequest="20" />

                <Label Text="{Binding TotalScore, StringFormat='{0:F0}% Complete'}"
                       TextColor="White"
                       FontSize="16"
                       HorizontalOptions="Center" />
            </StackLayout>
        </Frame>

        <!-- Activities List -->
        <RefreshView Grid.Row="1"
                    IsRefreshing="{Binding IsLoading}"
                    Command="{Binding LoadActivitiesCommand}"
                    Margin="10">

            <CollectionView ItemsSource="{Binding Activities}">
                <CollectionView.EmptyView>
                    <StackLayout VerticalOptions="CenterAndExpand"
                                HorizontalOptions="CenterAndExpand"
                                Spacing="20">
                        <Label Text="No activities yet!"
                               FontSize="18"
                               TextColor="Gray"
                               HorizontalOptions="Center" />
                        <Label Text="Tap the + button to add your first activity"
                               FontSize="14"
                               TextColor="Gray"
                               HorizontalOptions="Center" />
                    </StackLayout>
                </CollectionView.EmptyView>

                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="models:ActivityViewModel">
                        <SwipeView>
                            <SwipeView.LeftItems>
                                <SwipeItems>
                                    <SwipeItem Text="Complete"
                                              BackgroundColor="#52C41A"
                                              Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:MainViewModel}}, Path=CompleteActivityCommand}"
                                              CommandParameter="{Binding .}" />
                                </SwipeItems>
                            </SwipeView.LeftItems>

                            <SwipeView.RightItems>
                                <SwipeItems>
                                    <SwipeItem Text="Undo"
                                              BackgroundColor="#FAAD14"
                                              Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:MainViewModel}}, Path=UndoActivityCommand}"
                                              CommandParameter="{Binding .}" />
                                </SwipeItems>
                            </SwipeView.RightItems>

                            <Frame BackgroundColor="White"
                                   CornerRadius="10"
                                   HasShadow="True"
                                   Margin="5"
                                   Padding="15">

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!-- Activity Name and Progress -->
                                    <Label Grid.Row="0" Grid.Column="0"
                                           Text="{Binding Name}"
                                           FontSize="16"
                                           FontAttributes="Bold"
                                           TextColor="#333" />

                                    <Label Grid.Row="0" Grid.Column="1"
                                           Text="{Binding CurrentWeekCompletions, StringFormat='{0}/{1}'}"
                                           FontSize="14"
                                           TextColor="#666"
                                           VerticalOptions="Center" />

                                    <!-- Description -->
                                    <Label Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                                           Text="{Binding Description}"
                                           FontSize="14"
                                           TextColor="#666"
                                           IsVisible="{Binding Description, Converter={StaticResource StringToBoolConverter}}"
                                           Margin="0,5,0,10" />

                                    <!-- Weekly Progress Cells -->
                                    <CollectionView Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2"
                                                   ItemsSource="{Binding WeeklyCompletions}"
                                                   HeightRequest="40">
                                        <CollectionView.ItemsLayout>
                                            <LinearItemsLayout Orientation="Horizontal" ItemSpacing="2" />
                                        </CollectionView.ItemsLayout>

                                        <CollectionView.ItemTemplate>
                                            <DataTemplate>
                                                <Frame BackgroundColor="{Binding IsCompleted, Converter={StaticResource BoolToColorConverter}}"
                                                       WidthRequest="35"
                                                       HeightRequest="35"
                                                       CornerRadius="5"
                                                       HasShadow="False"
                                                       Padding="0">
                                                    <Label Text="{Binding Count}"
                                                           TextColor="White"
                                                           FontSize="12"
                                                           FontAttributes="Bold"
                                                           HorizontalOptions="Center"
                                                           VerticalOptions="Center"
                                                           IsVisible="{Binding Count, Converter={StaticResource IntToBoolConverter}}" />
                                                </Frame>
                                            </DataTemplate>
                                        </CollectionView.ItemTemplate>
                                    </CollectionView>

                                </Grid>

                                <Frame.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:MainViewModel}}, Path=ShowActivityHistoryCommand}"
                                                         CommandParameter="{Binding .}" />
                                </Frame.GestureRecognizers>
                            </Frame>
                        </SwipeView>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </RefreshView>

        <!-- Loading indicator -->
        <ActivityIndicator Grid.Row="1"
                          IsRunning="{Binding IsLoading}"
                          IsVisible="{Binding IsLoading}"
                          Color="#512BD4"
                          HorizontalOptions="Center"
                          VerticalOptions="Center" />

        <!-- Floating Action Button -->
        <Button Grid.Row="1"
               Text="+"
               Command="{Binding AddActivityCommand}"
               BackgroundColor="#512BD4"
               TextColor="White"
               FontSize="24"
               FontAttributes="Bold"
               WidthRequest="60"
               HeightRequest="60"
               CornerRadius="30"
               HorizontalOptions="End"
               VerticalOptions="End"
               Margin="20" />

    </Grid>

</ContentPage>
