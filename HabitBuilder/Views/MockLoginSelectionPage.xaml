<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="HabitBuilder.Views.MockLoginSelectionPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:HabitBuilder.ViewModels"
             x:DataType="viewmodels:MockLoginSelectionViewModel"
             Shell.NavBarIsVisible="False"
             BackgroundColor="#512BD4">

    <Grid>
        <!-- Background gradient -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#512BD4" Offset="0.0" />
                <GradientStop Color="#7B68EE" Offset="1.0" />
            </LinearGradientBrush>
        </Grid.Background>

        <ScrollView>
            <StackLayout Spacing="20" 
                        Padding="40,0"
                        VerticalOptions="CenterAndExpand">

                <!-- App title -->
                <Label Text="HabitBuilder"
                       FontSize="36"
                       FontAttributes="Bold"
                       TextColor="White"
                       HorizontalOptions="Center"
                       Margin="0,0,0,20" />

                <Label Text="Demo Login Selection"
                       FontSize="18"
                       TextColor="White"
                       HorizontalOptions="Center"
                       Margin="0,0,0,40" />

                <!-- Mock Users -->
                <Frame BackgroundColor="White"
                       CornerRadius="15"
                       HasShadow="True"
                       Padding="20">
                    
                    <StackLayout Spacing="15">
                        <Label Text="Choose a Test User"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="#333"
                               HorizontalOptions="Center" />

                        <!-- Test User 1 - Free Account -->
                        <Button Text="🆓 Test User (Free Account)"
                               Command="{Binding LoginAsTestUserCommand}"
                               CommandParameter="<EMAIL>"
                               BackgroundColor="#52C41A"
                               TextColor="White"
                               FontSize="16"
                               CornerRadius="25"
                               HeightRequest="50" />

                        <!-- Premium User -->
                        <Button Text="⭐ Premium User (Paid Account)"
                               Command="{Binding LoginAsTestUserCommand}"
                               CommandParameter="<EMAIL>"
                               BackgroundColor="#FAAD14"
                               TextColor="White"
                               FontSize="16"
                               CornerRadius="25"
                               HeightRequest="50" />

                        <!-- Guest User -->
                        <Button Text="👤 Guest User (No Account)"
                               Command="{Binding LoginAsTestUserCommand}"
                               CommandParameter="<EMAIL>"
                               BackgroundColor="#999"
                               TextColor="White"
                               FontSize="16"
                               CornerRadius="25"
                               HeightRequest="50" />

                        <!-- Custom Login -->
                        <Label Text="Or enter custom credentials:"
                               FontSize="14"
                               TextColor="#666"
                               HorizontalOptions="Center"
                               Margin="0,20,0,10" />

                        <Entry Text="{Binding CustomEmail}"
                               Placeholder="Email"
                               Keyboard="Email"
                               BackgroundColor="#F5F5F5"
                               TextColor="#333" />

                        <Entry Text="{Binding CustomPassword}"
                               Placeholder="Password"
                               IsPassword="True"
                               BackgroundColor="#F5F5F5"
                               TextColor="#333" />

                        <Button Text="Login with Custom Credentials"
                               Command="{Binding LoginWithCustomCredentialsCommand}"
                               BackgroundColor="#512BD4"
                               TextColor="White"
                               FontSize="16"
                               CornerRadius="25"
                               HeightRequest="50"
                               IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}" />

                    </StackLayout>
                </Frame>

                <!-- Error Message -->
                <Label Text="{Binding ErrorMessage}"
                       TextColor="Red"
                       FontSize="14"
                       IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}"
                       HorizontalOptions="Center"
                       BackgroundColor="White"
                       Padding="10"
                       Margin="10,0" />

                <!-- Loading indicator -->
                <ActivityIndicator IsRunning="{Binding IsLoading}"
                                  IsVisible="{Binding IsLoading}"
                                  Color="White"
                                  HorizontalOptions="Center" />

                <!-- Skip to regular login -->
                <Label Text="Use regular login instead"
                       TextColor="White"
                       FontSize="14"
                       HorizontalOptions="Center"
                       Margin="0,20,0,0"
                       Opacity="0.8">
                    <Label.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToRegularLoginCommand}" />
                    </Label.GestureRecognizers>
                </Label>

            </StackLayout>
        </ScrollView>

    </Grid>

</ContentPage>
