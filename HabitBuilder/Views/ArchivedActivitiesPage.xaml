<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="HabitBuilder.Views.ArchivedActivitiesPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:HabitBuilder.ViewModels"
             x:DataType="viewmodels:ArchivedActivitiesViewModel"
             Title="Archived Activities">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <Label Grid.Row="0"
               Text="Archived Activities"
               FontSize="24"
               FontAttributes="Bold"
               HorizontalOptions="Center"
               Margin="20" />

        <!-- Activities List -->
        <CollectionView Grid.Row="1"
                       ItemsSource="{Binding ArchivedActivities}"
                       Margin="20,0">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="15" Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <StackLayout Grid.Column="0">
                            <Label Text="{Binding Name}"
                                   FontSize="16"
                                   FontAttributes="Bold" />
                            <Label Text="{Binding Description}"
                                   FontSize="14"
                                   TextColor="Gray" />
                        </StackLayout>

                        <Button Grid.Column="1"
                               Text="Unarchive"
                               Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:ArchivedActivitiesViewModel}}, Path=UnarchiveActivityCommand}"
                               CommandParameter="{Binding .}"
                               BackgroundColor="#512BD4"
                               TextColor="White"
                               CornerRadius="15"
                               WidthRequest="100" />
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>

        <!-- Loading indicator -->
        <ActivityIndicator Grid.Row="1"
                          IsRunning="{Binding IsLoading}"
                          IsVisible="{Binding IsLoading}"
                          Color="#512BD4"
                          HorizontalOptions="Center"
                          VerticalOptions="Center" />

    </Grid>

</ContentPage>
