<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="HabitBuilder.Views.LoginPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:HabitBuilder.ViewModels"
             x:DataType="viewmodels:LoginViewModel"
             Shell.NavBarIsVisible="False"
             BackgroundColor="#512BD4">

    <Grid>
        <!-- Background with pastel circles -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#512BD4" Offset="0.0" />
                <GradientStop Color="#7B68EE" Offset="0.5" />
                <GradientStop Color="#9370DB" Offset="1.0" />
            </LinearGradientBrush>
        </Grid.Background>

        <!-- Decorative circles -->
        <Ellipse Fill="#FF6B6B" 
                 Opacity="0.3"
                 WidthRequest="120" 
                 HeightRequest="120"
                 HorizontalOptions="Start"
                 VerticalOptions="Start"
                 Margin="20,50,0,0" />
        
        <Ellipse Fill="#FFD93D" 
                 Opacity="0.3"
                 WidthRequest="80" 
                 HeightRequest="80"
                 HorizontalOptions="End"
                 VerticalOptions="Start"
                 Margin="0,100,30,0" />
        
        <Ellipse Fill="#6BCF7F" 
                 Opacity="0.3"
                 WidthRequest="100" 
                 HeightRequest="100"
                 HorizontalOptions="Start"
                 VerticalOptions="End"
                 Margin="50,0,0,150" />

        <!-- Main content -->
        <ScrollView>
            <StackLayout Spacing="20" 
                        Padding="40,0"
                        VerticalOptions="CenterAndExpand">

                <!-- App title -->
                <Label Text="HabitBuilder"
                       FontSize="36"
                       FontAttributes="Bold"
                       TextColor="White"
                       HorizontalOptions="Center"
                       Margin="0,0,0,40" />

                <!-- Login form -->
                <Frame BackgroundColor="White"
                       CornerRadius="15"
                       HasShadow="True"
                       Padding="30">
                    
                    <StackLayout Spacing="20">
                        
                        <!-- Email field -->
                        <StackLayout>
                            <Label Text="Email" 
                                   TextColor="#333"
                                   FontSize="16" />
                            <Entry x:Name="EmailEntry"
                                   Text="{Binding Email}"
                                   Placeholder="Enter your email"
                                   Keyboard="Email"
                                   BackgroundColor="#F5F5F5"
                                   TextColor="#333" />
                        </StackLayout>

                        <!-- Password field -->
                        <StackLayout>
                            <Label Text="Password" 
                                   TextColor="#333"
                                   FontSize="16" />
                            <Entry x:Name="PasswordEntry"
                                   Text="{Binding Password}"
                                   Placeholder="Enter your password"
                                   IsPassword="True"
                                   BackgroundColor="#F5F5F5"
                                   TextColor="#333" />
                        </StackLayout>

                        <!-- Error message -->
                        <Label Text="{Binding ErrorMessage}"
                               TextColor="Red"
                               FontSize="14"
                               IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}"
                               HorizontalOptions="Center" />

                        <!-- Login button -->
                        <Button Text="Login"
                                Command="{Binding LoginCommand}"
                                BackgroundColor="#512BD4"
                                TextColor="White"
                                FontSize="18"
                                FontAttributes="Bold"
                                CornerRadius="25"
                                HeightRequest="50"
                                IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}" />

                        <!-- Loading indicator -->
                        <ActivityIndicator IsRunning="{Binding IsLoading}"
                                          IsVisible="{Binding IsLoading}"
                                          Color="#512BD4"
                                          HorizontalOptions="Center" />

                        <!-- Biometric login button -->
                        <Button Text="Login with Face ID / Fingerprint"
                                Command="{Binding LoginWithBiometricsCommand}"
                                BackgroundColor="Transparent"
                                TextColor="#512BD4"
                                FontSize="16"
                                BorderColor="#512BD4"
                                BorderWidth="2"
                                CornerRadius="25"
                                HeightRequest="45"
                                IsVisible="{Binding IsBiometricAvailable}"
                                IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}" />

                        <!-- Signup link -->
                        <StackLayout Orientation="Horizontal" 
                                    HorizontalOptions="Center">
                            <Label Text="Don't have an account? "
                                   TextColor="#666"
                                   FontSize="14" />
                            <Label Text="Sign up"
                                   TextColor="#512BD4"
                                   FontSize="14"
                                   FontAttributes="Bold">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding NavigateToSignupCommand}" />
                                </Label.GestureRecognizers>
                            </Label>
                        </StackLayout>

                        <!-- Skip login -->
                        <Label Text="Skip for now"
                               TextColor="#999"
                               FontSize="14"
                               HorizontalOptions="Center"
                               Margin="0,10,0,0">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ShowSkipModalCommand}" />
                            </Label.GestureRecognizers>
                        </Label>

                    </StackLayout>
                </Frame>

            </StackLayout>
        </ScrollView>

        <!-- Skip modal -->
        <Frame IsVisible="{Binding ShowSkipModal}"
               BackgroundColor="White"
               CornerRadius="15"
               HasShadow="True"
               Padding="30"
               HorizontalOptions="Center"
               VerticalOptions="Center"
               WidthRequest="300">
            
            <StackLayout Spacing="20">
                <Label Text="Continue without account?"
                       FontSize="18"
                       FontAttributes="Bold"
                       TextColor="#333"
                       HorizontalOptions="Center" />
                
                <Label Text="You'll have limited features:&#x0a;• Only 2 activities&#x0a;• No sync between devices&#x0a;• Limited notifications"
                       FontSize="14"
                       TextColor="#666"
                       HorizontalOptions="Center" />
                
                <StackLayout Orientation="Horizontal" 
                            HorizontalOptions="Center"
                            Spacing="20">
                    <Button Text="Go Back"
                            Command="{Binding HideSkipModalCommand}"
                            BackgroundColor="Transparent"
                            TextColor="#512BD4"
                            BorderColor="#512BD4"
                            BorderWidth="2"
                            CornerRadius="20"
                            WidthRequest="100" />
                    
                    <Button Text="Continue"
                            Command="{Binding SkipLoginCommand}"
                            BackgroundColor="#512BD4"
                            TextColor="White"
                            CornerRadius="20"
                            WidthRequest="100" />
                </StackLayout>
            </StackLayout>
        </Frame>

    </Grid>

</ContentPage>
