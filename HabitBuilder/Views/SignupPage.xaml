<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="HabitBuilder.Views.SignupPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:HabitBuilder.ViewModels"
             x:DataType="viewmodels:SignupViewModel"
             Shell.NavBarIsVisible="False"
             BackgroundColor="#512BD4">

    <Grid>
        <!-- Background with pastel circles -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#512BD4" Offset="0.0" />
                <GradientStop Color="#7B68EE" Offset="0.5" />
                <GradientStop Color="#9370DB" Offset="1.0" />
            </LinearGradientBrush>
        </Grid.Background>

        <!-- Main content -->
        <ScrollView>
            <StackLayout Spacing="20" 
                        Padding="40,0"
                        VerticalOptions="CenterAndExpand">

                <!-- App title -->
                <Label Text="Create Account"
                       FontSize="32"
                       FontAttributes="Bold"
                       TextColor="White"
                       HorizontalOptions="Center"
                       Margin="0,0,0,40" />

                <!-- Signup form -->
                <Frame BackgroundColor="White"
                       CornerRadius="15"
                       HasShadow="True"
                       Padding="30">
                    
                    <StackLayout Spacing="20">
                        
                        <!-- Email field -->
                        <StackLayout>
                            <Label Text="Email" 
                                   TextColor="#333"
                                   FontSize="16" />
                            <Entry Text="{Binding Email}"
                                   Placeholder="Enter your email"
                                   Keyboard="Email"
                                   BackgroundColor="#F5F5F5"
                                   TextColor="#333" />
                        </StackLayout>

                        <!-- Password field -->
                        <StackLayout>
                            <Label Text="Password" 
                                   TextColor="#333"
                                   FontSize="16" />
                            <Entry Text="{Binding Password}"
                                   Placeholder="Create a password"
                                   IsPassword="True"
                                   BackgroundColor="#F5F5F5"
                                   TextColor="#333" />
                        </StackLayout>

                        <!-- Error message -->
                        <Label Text="{Binding ErrorMessage}"
                               TextColor="Red"
                               FontSize="14"
                               IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}"
                               HorizontalOptions="Center" />

                        <!-- Signup button -->
                        <Button Text="Sign Up"
                                Command="{Binding SignupCommand}"
                                BackgroundColor="#512BD4"
                                TextColor="White"
                                FontSize="18"
                                FontAttributes="Bold"
                                CornerRadius="25"
                                HeightRequest="50"
                                IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}" />

                        <!-- Loading indicator -->
                        <ActivityIndicator IsRunning="{Binding IsLoading}"
                                          IsVisible="{Binding IsLoading}"
                                          Color="#512BD4"
                                          HorizontalOptions="Center" />

                        <!-- Login link -->
                        <StackLayout Orientation="Horizontal" 
                                    HorizontalOptions="Center">
                            <Label Text="Already have an account? "
                                   TextColor="#666"
                                   FontSize="14" />
                            <Label Text="Login"
                                   TextColor="#512BD4"
                                   FontSize="14"
                                   FontAttributes="Bold">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding NavigateToLoginCommand}" />
                                </Label.GestureRecognizers>
                            </Label>
                        </StackLayout>

                    </StackLayout>
                </Frame>

            </StackLayout>
        </ScrollView>

    </Grid>

</ContentPage>
