<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="HabitBuilder.Views.SettingsPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:HabitBuilder.ViewModels"
             x:DataType="viewmodels:SettingsViewModel"
             Title="Settings">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <Label Text="Settings"
                   FontSize="24"
                   FontAttributes="Bold"
                   HorizontalOptions="Center"
                   Margin="0,0,0,20" />

            <!-- Dark Mode Toggle -->
            <StackLayout Orientation="Horizontal">
                <Label Text="Dark Mode"
                       VerticalOptions="Center"
                       HorizontalOptions="StartAndExpand" />
                <Switch IsToggled="{Binding IsDarkMode}" />
            </StackLayout>

            <!-- Notifications Toggle -->
            <StackLayout Orientation="Horizontal">
                <Label Text="Notifications"
                       VerticalOptions="Center"
                       HorizontalOptions="StartAndExpand" />
                <Switch IsToggled="{Binding NotificationsEnabled}" />
            </StackLayout>

            <!-- Logout Button -->
            <Button Text="Logout"
                    Command="{Binding LogoutCommand}"
                    BackgroundColor="#FF6B6B"
                    TextColor="White"
                    CornerRadius="25"
                    HeightRequest="50"
                    Margin="0,40,0,0" />

        </StackLayout>
    </ScrollView>

</ContentPage>
