<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="HabitBuilder.Views.ActivityHistoryPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:HabitBuilder.ViewModels"
             x:DataType="viewmodels:ActivityHistoryViewModel"
             Title="{Binding ActivityName}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Activity Info Header -->
        <Frame Grid.Row="0"
               BackgroundColor="{Binding ActivityColor}"
               CornerRadius="0"
               HasShadow="False"
               Padding="20">
            <StackLayout Spacing="10">
                <Label Text="{Binding ActivityName}"
                       TextColor="White"
                       FontSize="20"
                       FontAttributes="Bold"
                       HorizontalOptions="Center" />
                
                <Label Text="{Binding ActivityDescription}"
                       TextColor="White"
                       FontSize="14"
                       HorizontalOptions="Center"
                       IsVisible="{Binding ActivityDescription, Converter={StaticResource StringToBoolConverter}}" />
                
                <Label Text="{Binding FrequencyText}"
                       TextColor="White"
                       FontSize="16"
                       HorizontalOptions="Center" />
            </StackLayout>
        </Frame>

        <!-- Current Period Progress -->
        <Frame Grid.Row="1"
               BackgroundColor="White"
               CornerRadius="10"
               HasShadow="True"
               Margin="15,10">
            <StackLayout Spacing="15">
                <Label Text="Current Progress"
                       FontSize="18"
                       FontAttributes="Bold"
                       TextColor="#333"
                       HorizontalOptions="Center" />

                <!-- Weekly/Fortnightly Grid -->
                <CollectionView ItemsSource="{Binding CurrentPeriodCompletions}"
                               HeightRequest="60">
                    <CollectionView.ItemsLayout>
                        <GridItemsLayout Orientation="Vertical"
                                        Span="{Binding GridSpan}"
                                        HorizontalItemSpacing="5"
                                        VerticalItemSpacing="5" />
                    </CollectionView.ItemsLayout>
                    
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame BackgroundColor="{Binding IsCompleted, Converter={StaticResource BoolToColorConverter}}"
                                   WidthRequest="40"
                                   HeightRequest="40"
                                   CornerRadius="8"
                                   HasShadow="False"
                                   Padding="0">
                                <StackLayout VerticalOptions="Center" HorizontalOptions="Center">
                                    <Label Text="{Binding Date, StringFormat='{0:dd}'}"
                                           TextColor="White"
                                           FontSize="10"
                                           FontAttributes="Bold"
                                           HorizontalOptions="Center" />
                                    <Label Text="{Binding Count}"
                                           TextColor="White"
                                           FontSize="12"
                                           FontAttributes="Bold"
                                           HorizontalOptions="Center"
                                           IsVisible="{Binding Count, Converter={StaticResource IntToBoolConverter}}" />
                                </StackLayout>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

                <Label Text="{Binding ProgressText}"
                       FontSize="16"
                       TextColor="#666"
                       HorizontalOptions="Center" />
            </StackLayout>
        </Frame>

        <!-- Weekly History -->
        <ScrollView Grid.Row="2">
            <StackLayout Padding="15" Spacing="10">
                <Label Text="Weekly History"
                       FontSize="18"
                       FontAttributes="Bold"
                       TextColor="#333" />

                <CollectionView ItemsSource="{Binding WeeklyHistory}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame BackgroundColor="White"
                                   CornerRadius="10"
                                   HasShadow="True"
                                   Margin="0,5"
                                   Padding="15">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <StackLayout Grid.Column="0">
                                        <Label Text="{Binding WeekStartDate, StringFormat='Week of {0:MMM dd}'}"
                                               FontSize="16"
                                               FontAttributes="Bold"
                                               TextColor="#333" />
                                        <Label Text="{Binding CompletedCount, StringFormat='{0} completions'}"
                                               FontSize="14"
                                               TextColor="#666" />
                                    </StackLayout>

                                    <StackLayout Grid.Column="1" HorizontalOptions="End">
                                        <Label Text="{Binding Percentage, StringFormat='{0:F0}%'}"
                                               FontSize="18"
                                               FontAttributes="Bold"
                                               TextColor="{Binding Score, Converter={StaticResource ScoreToColorConverter}}"
                                               HorizontalOptions="Center" />
                                        <Label Text="{Binding Score, StringFormat='{0:F0} pts'}"
                                               FontSize="12"
                                               TextColor="#666"
                                               HorizontalOptions="Center" />
                                    </StackLayout>
                                </Grid>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

                <!-- Upgrade prompt for free users -->
                <Frame BackgroundColor="#FFF3CD"
                       BorderColor="#FFEAA7"
                       CornerRadius="10"
                       HasShadow="False"
                       Padding="15"
                       IsVisible="{Binding ShowUpgradePrompt}">
                    <StackLayout Spacing="10">
                        <Label Text="📈 Want to see more history?"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="#856404" />
                        <Label Text="Upgrade to see unlimited weekly history and detailed analytics."
                               FontSize="14"
                               TextColor="#856404" />
                        <Button Text="Upgrade Now"
                               Command="{Binding UpgradeCommand}"
                               BackgroundColor="#512BD4"
                               TextColor="White"
                               CornerRadius="20"
                               HeightRequest="40" />
                    </StackLayout>
                </Frame>

            </StackLayout>
        </ScrollView>

        <!-- Loading indicator -->
        <ActivityIndicator Grid.Row="2"
                          IsRunning="{Binding IsLoading}"
                          IsVisible="{Binding IsLoading}"
                          Color="#512BD4"
                          HorizontalOptions="Center"
                          VerticalOptions="Center" />

    </Grid>

</ContentPage>
