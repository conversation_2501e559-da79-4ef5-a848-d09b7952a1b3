<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="HabitBuilder.Views.AddActivityPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:HabitBuilder.ViewModels"
             x:DataType="viewmodels:AddActivityViewModel"
             Title="Add Activity">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">

            <!-- Activity Name -->
            <StackLayout>
                <Label Text="Activity Name *"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="#333" />
                <Entry Text="{Binding ActivityName}"
                       Placeholder="e.g., Read for 30 minutes"
                       BackgroundColor="#F5F5F5"
                       TextColor="#333" />
            </StackLayout>

            <!-- Description -->
            <StackLayout>
                <Label Text="Description (Optional)"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="#333" />
                <Editor Text="{Binding Description}"
                        Placeholder="Add more details about this activity..."
                        BackgroundColor="#F5F5F5"
                        TextColor="#333"
                        HeightRequest="80" />
            </StackLayout>

            <!-- Frequency -->
            <StackLayout>
                <Label Text="Frequency *"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="#333" />
                <Picker ItemsSource="{Binding FrequencyOptions}"
                       SelectedItem="{Binding SelectedFrequency}"
                       BackgroundColor="#F5F5F5"
                       TextColor="#333" />
            </StackLayout>

            <!-- Target Count -->
            <StackLayout>
                <Label Text="Target Count *"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="#333" />
                <StackLayout Orientation="Horizontal" Spacing="10">
                    <Stepper Value="{Binding TargetCount}"
                            Minimum="1"
                            Maximum="14"
                            Increment="1" />
                    <Label Text="{Binding TargetCount, StringFormat='{0} times per {1}'}"
                           VerticalOptions="Center"
                           TextColor="#666" />
                </StackLayout>
            </StackLayout>

            <!-- Color Selection -->
            <StackLayout>
                <Label Text="Color"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="#333" />
                <CollectionView ItemsSource="{Binding ColorOptions}"
                               SelectedItem="{Binding SelectedColor}"
                               SelectionMode="Single"
                               HeightRequest="60">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Horizontal" ItemSpacing="10" />
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame BackgroundColor="{Binding .}"
                                   WidthRequest="50"
                                   HeightRequest="50"
                                   CornerRadius="25"
                                   HasShadow="True"
                                   Padding="0">
                                <Frame.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:AddActivityViewModel}}, Path=SelectColorCommand}"
                                                         CommandParameter="{Binding .}" />
                                </Frame.GestureRecognizers>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
            </StackLayout>

            <!-- Limited Period Activity (Paid Feature) -->
            <StackLayout IsVisible="{Binding IsPaidUser}">
                <StackLayout Orientation="Horizontal">
                    <Label Text="Limited Period Activity"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="#333"
                           VerticalOptions="Center"
                           HorizontalOptions="StartAndExpand" />
                    <Switch IsToggled="{Binding IsLimitedPeriod}" />
                </StackLayout>
                
                <StackLayout IsVisible="{Binding IsLimitedPeriod}" Spacing="10">
                    <StackLayout Orientation="Horizontal" Spacing="10">
                        <Label Text="Start Date:"
                               VerticalOptions="Center"
                               TextColor="#666" />
                        <DatePicker Date="{Binding StartDate}"
                                   BackgroundColor="#F5F5F5" />
                    </StackLayout>
                    
                    <StackLayout Orientation="Horizontal" Spacing="10">
                        <Label Text="End Date:"
                               VerticalOptions="Center"
                               TextColor="#666" />
                        <DatePicker Date="{Binding EndDate}"
                                   BackgroundColor="#F5F5F5" />
                    </StackLayout>
                </StackLayout>
            </StackLayout>

            <!-- Error Message -->
            <Label Text="{Binding ErrorMessage}"
                   TextColor="Red"
                   FontSize="14"
                   IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}"
                   HorizontalOptions="Center" />

            <!-- Buttons -->
            <StackLayout Orientation="Horizontal" 
                        HorizontalOptions="FillAndExpand"
                        Spacing="10"
                        Margin="0,20,0,0">
                
                <Button Text="Cancel"
                       Command="{Binding CancelCommand}"
                       BackgroundColor="Transparent"
                       TextColor="#512BD4"
                       BorderColor="#512BD4"
                       BorderWidth="2"
                       CornerRadius="25"
                       HeightRequest="50"
                       HorizontalOptions="FillAndExpand" />
                
                <Button Text="Save Activity"
                       Command="{Binding SaveActivityCommand}"
                       BackgroundColor="#512BD4"
                       TextColor="White"
                       CornerRadius="25"
                       HeightRequest="50"
                       HorizontalOptions="FillAndExpand"
                       IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}" />
            </StackLayout>

            <!-- Loading indicator -->
            <ActivityIndicator IsRunning="{Binding IsLoading}"
                              IsVisible="{Binding IsLoading}"
                              Color="#512BD4"
                              HorizontalOptions="Center" />

        </StackLayout>
    </ScrollView>

</ContentPage>
