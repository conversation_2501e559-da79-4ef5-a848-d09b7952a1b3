<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="HabitBuilder.Views.OnboardingPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:HabitBuilder.ViewModels"
             x:DataType="viewmodels:OnboardingViewModel"
             Shell.NavBarIsVisible="False"
             BackgroundColor="#512BD4">

    <Grid>
        <!-- Background gradient -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#512BD4" Offset="0.0" />
                <GradientStop Color="#7B68EE" Offset="1.0" />
            </LinearGradientBrush>
        </Grid.Background>

        <!-- Language Selection Step -->
        <StackLayout IsVisible="{Binding ShowLanguageSelection}"
                    Spacing="30"
                    Padding="40"
                    VerticalOptions="CenterAndExpand">
            
            <Label Text="Welcome to HabitBuilder!"
                   FontSize="28"
                   FontAttributes="Bold"
                   TextColor="White"
                   HorizontalOptions="Center" />
            
            <Label Text="Choose your language"
                   FontSize="18"
                   TextColor="White"
                   HorizontalOptions="Center" />
            
            <Picker ItemsSource="{Binding Languages}"
                   SelectedItem="{Binding SelectedLanguage}"
                   BackgroundColor="White"
                   TextColor="#333"
                   FontSize="16" />
            
            <Button Text="Continue"
                   Command="{Binding ContinueFromLanguageCommand}"
                   BackgroundColor="White"
                   TextColor="#512BD4"
                   FontSize="18"
                   FontAttributes="Bold"
                   CornerRadius="25"
                   HeightRequest="50" />
        </StackLayout>

        <!-- Resolve Level Selection Step -->
        <StackLayout IsVisible="{Binding ShowResolveLevelSelection}"
                    Spacing="30"
                    Padding="40"
                    VerticalOptions="CenterAndExpand">
            
            <Label Text="Set Your Resolve Level"
                   FontSize="24"
                   FontAttributes="Bold"
                   TextColor="White"
                   HorizontalOptions="Center" />
            
            <Label Text="How strict should we be with your habit tracking?"
                   FontSize="16"
                   TextColor="White"
                   HorizontalOptions="Center"
                   HorizontalTextAlignment="Center" />

            <!-- Easygoing Option -->
            <Frame BackgroundColor="White"
                   CornerRadius="15"
                   HasShadow="True"
                   Padding="20">
                <StackLayout>
                    <Label Text="🌱 Easygoing (Recommended)"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#512BD4" />
                    <Label Text="Forgiving scoring that encourages progress over perfection. Great for building sustainable habits."
                           FontSize="14"
                           TextColor="#666"
                           Margin="0,5,0,10" />
                    <Button Text="Choose Easygoing"
                           Command="{Binding SelectResolveLevelCommand}"
                           CommandParameter="Easygoing"
                           BackgroundColor="#52C41A"
                           TextColor="White"
                           CornerRadius="20" />
                </StackLayout>
            </Frame>

            <!-- Resolved Option -->
            <Frame BackgroundColor="White"
                   CornerRadius="15"
                   HasShadow="True"
                   Padding="20">
                <StackLayout>
                    <Label Text="💪 Resolved"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#512BD4" />
                    <Label Text="Balanced approach with moderate expectations. Good for those who want some accountability."
                           FontSize="14"
                           TextColor="#666"
                           Margin="0,5,0,10" />
                    <Button Text="Choose Resolved"
                           Command="{Binding SelectResolveLevelCommand}"
                           CommandParameter="Resolved"
                           BackgroundColor="#FAAD14"
                           TextColor="White"
                           CornerRadius="20" />
                </StackLayout>
            </Frame>

            <!-- Clockwork Option (Disabled) -->
            <Frame BackgroundColor="#F0F0F0"
                   CornerRadius="15"
                   HasShadow="True"
                   Padding="20"
                   Opacity="0.6">
                <StackLayout>
                    <Label Text="⚙️ Clockwork (Coming Soon)"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#999" />
                    <Label Text="No cheat days, perfect execution required. For the most ambitious go-getters."
                           FontSize="14"
                           TextColor="#999"
                           Margin="0,5,0,10" />
                    <Button Text="Not Available Yet"
                           BackgroundColor="#CCC"
                           TextColor="White"
                           CornerRadius="20"
                           IsEnabled="False" />
                </StackLayout>
            </Frame>

        </StackLayout>

    </Grid>

</ContentPage>
