using HabitBuilder.ViewModels;

namespace HabitBuilder.Views;

public partial class ArchivedActivitiesPage : ContentPage
{
    public ArchivedActivitiesPage(ArchivedActivitiesViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        if (BindingContext is ArchivedActivitiesViewModel viewModel)
        {
            await viewModel.LoadArchivedActivitiesCommand.ExecuteAsync(null);
        }
    }
}
