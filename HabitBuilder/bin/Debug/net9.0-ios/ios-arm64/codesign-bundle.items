﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <_CodesignBundle Include="HabitBuilder.app">
      <CodesignSigningKey>B6A62E11421A4E003479EF2EFF4A9DC34CBC6EFA</CodesignSigningKey>
      <CodesignEntitlements>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-ios/ios-arm64/Entitlements.xcent</CodesignEntitlements>
      <CodesignUseSecureTimestamp></CodesignUseSecureTimestamp>
      <CodesignAllocate>/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/codesign_allocate</CodesignAllocate>
      <RequireCodeSigning>true</RequireCodeSigning>
      <CodesignExtraArgs></CodesignExtraArgs>
      <CodesignResourceRules></CodesignResourceRules>
      <CodesignDisableTimestamp>true</CodesignDisableTimestamp>
      <CodesignKeychain></CodesignKeychain>
      <CodesignUseHardenedRuntime></CodesignUseHardenedRuntime>
      <SourceProjectPath>/Users/<USER>/Documents/src/habit-builder/HabitBuilder</SourceProjectPath>
    </_CodesignBundle>
  </ItemGroup>
</Project>