<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AboutAssets.txt</key>
		<data>
		87D3//yZmTijel0tM/5kvTDtVRM=
		</data>
		<key>Assets.car</key>
		<data>
		Inn4nnH7Hj8jHhynut/GUqTSdB8=
		</data>
		<key>Azure.Core.dll</key>
		<data>
		Eg0+lbckWwvPioRxb+K4TU5OeOY=
		</data>
		<key>Azure.Data.Tables.dll</key>
		<data>
		PfT0N7CP1XgAttHl17u+9Is2kuI=
		</data>
		<key>CommunityToolkit.Mvvm.dll</key>
		<data>
		V3xFjVNisuKyHljJia3AKMyWVv8=
		</data>
		<key>HabitBuilder.dll</key>
		<data>
		nu75UUkc+9Q9iAEjQ5XxKhLTIfE=
		</data>
		<key>HabitBuilder.pdb</key>
		<data>
		sPaGyS5lqjOnVhWzupV26DSv+0Y=
		</data>
		<key>Info.plist</key>
		<data>
		Ntj7uNi2BWh2VJSU2kx9T5+ApZc=
		</data>
		<key>MauiInfo.plist</key>
		<data>
		iEtuUGCV6JM3gxS/pO33swhmpp8=
		</data>
		<key>MauiSplash.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		Y78DTyz7+rkuDbhphGJe+YK2rLQ=
		</data>
		<key>MauiSplash.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>MauiSplash.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Microsoft.AspNetCore.Authorization.dll</key>
		<data>
		ANAbtKGmu1QXjKzx9lFhtBy/gXE=
		</data>
		<key>Microsoft.AspNetCore.Components.Authorization.dll</key>
		<data>
		9/d2+jf3BJFQ6UD6GpQIphIDMNg=
		</data>
		<key>Microsoft.AspNetCore.Components.Forms.dll</key>
		<data>
		ez4qQwGgWaDOvLUVoXdotVYpolo=
		</data>
		<key>Microsoft.AspNetCore.Components.Web.dll</key>
		<data>
		G5Hn63NnOpa++u+BYP9euNm4x2k=
		</data>
		<key>Microsoft.AspNetCore.Components.WebAssembly.Authentication.dll</key>
		<data>
		zb+00b3rRtwBjL7OHrtnECGaGRY=
		</data>
		<key>Microsoft.AspNetCore.Components.dll</key>
		<data>
		gIqnJz/C6BxzBa2MAW+zdpW2CCw=
		</data>
		<key>Microsoft.AspNetCore.Metadata.dll</key>
		<data>
		ySATfP3MKR+rrkbFkWB2iWanX0k=
		</data>
		<key>Microsoft.Authentication.WebAssembly.Msal.dll</key>
		<data>
		jd/hIx205puZEDLYovC6Q14cVV0=
		</data>
		<key>Microsoft.Bcl.AsyncInterfaces.dll</key>
		<data>
		LkOK74MHlbLSQM8xYKM1O8bLAjI=
		</data>
		<key>Microsoft.CSharp.dll</key>
		<data>
		FQ8p43M4lh/ZVfGK4vqzT3ujSE4=
		</data>
		<key>Microsoft.Extensions.Configuration.Abstractions.dll</key>
		<data>
		/UBQ5rVRIYhRuUhwPBX3in7IKGo=
		</data>
		<key>Microsoft.Extensions.Configuration.Binder.dll</key>
		<data>
		25HMh3+leou5jWn/2WI13OwaVFA=
		</data>
		<key>Microsoft.Extensions.Configuration.dll</key>
		<data>
		+WVwf1K0s/8iMxjZeBxb3rji6BI=
		</data>
		<key>Microsoft.Extensions.DependencyInjection.Abstractions.dll</key>
		<data>
		NwU5qmqsQZjZ1oqSf8zW4t6d3ag=
		</data>
		<key>Microsoft.Extensions.DependencyInjection.dll</key>
		<data>
		eCdDpsVFC4AVmgdmT4m2+e9ggPg=
		</data>
		<key>Microsoft.Extensions.Diagnostics.Abstractions.dll</key>
		<data>
		/67TLXfbl78xi2g6BNYQgqei4Cs=
		</data>
		<key>Microsoft.Extensions.Diagnostics.dll</key>
		<data>
		fRl2LzCqDxLAnPpn2wiOfiQmaYI=
		</data>
		<key>Microsoft.Extensions.Http.dll</key>
		<data>
		WH8EQrF3YG9sp/sa1f19cFeZEIs=
		</data>
		<key>Microsoft.Extensions.Logging.Abstractions.dll</key>
		<data>
		FOhjH8vTPBGvzQDnuTArxDkOvwQ=
		</data>
		<key>Microsoft.Extensions.Logging.Debug.dll</key>
		<data>
		/7TxubYTQsTSRuXyDUHb8ctnThc=
		</data>
		<key>Microsoft.Extensions.Logging.dll</key>
		<data>
		pn58Cbli/n5U3wcElqKNxhMNonI=
		</data>
		<key>Microsoft.Extensions.Options.ConfigurationExtensions.dll</key>
		<data>
		KMpDMsYN/2+Mc5OA3WRM9HUvVbY=
		</data>
		<key>Microsoft.Extensions.Options.dll</key>
		<data>
		1EbeUKQnbSI4Xb38DRYy4q5Xzdk=
		</data>
		<key>Microsoft.Extensions.Primitives.dll</key>
		<data>
		KgI+gnarvmUhOlIx0Uvhg27rpBA=
		</data>
		<key>Microsoft.Identity.Client.dll</key>
		<data>
		C5XGxi2cRWxExQEIsSLLaUhzeD0=
		</data>
		<key>Microsoft.IdentityModel.Abstractions.dll</key>
		<data>
		FJLnFwxaZ/5uRehAF4RJmJFfs4w=
		</data>
		<key>Microsoft.JSInterop.dll</key>
		<data>
		QXLh50DEOaEooMrnMalMHVxhGOc=
		</data>
		<key>Microsoft.Maui.Controls.Xaml.dll</key>
		<data>
		gZLTTASeYNdCyzlvbnMsh0g9baY=
		</data>
		<key>Microsoft.Maui.Controls.dll</key>
		<data>
		o3wSD+ZS7CY/Zd/o/dKK822BqlA=
		</data>
		<key>Microsoft.Maui.Essentials.dll</key>
		<data>
		ZX0N0W9oGbkV+3xxQEB2NfAFDXM=
		</data>
		<key>Microsoft.Maui.Graphics.dll</key>
		<data>
		DxNvF8VQl75FFdQv6J1ZUpyfTls=
		</data>
		<key>Microsoft.Maui.dll</key>
		<data>
		FKhFiM/nP8ddlFen2Fi+mTIhmRA=
		</data>
		<key>Microsoft.VisualBasic.Core.dll</key>
		<data>
		D8bcO1YlJN+WvFBT2hj27vuJi38=
		</data>
		<key>Microsoft.VisualBasic.dll</key>
		<data>
		XwIUQzdYgeuz6kN3ci+2quzam28=
		</data>
		<key>Microsoft.Win32.Primitives.dll</key>
		<data>
		WqkbkW4X1e/EuNvf6NZw9daUfJc=
		</data>
		<key>Microsoft.Win32.Registry.dll</key>
		<data>
		wKzqKNHWvH3MlTgzlbz0Wo9PP/I=
		</data>
		<key>Microsoft.iOS.dll</key>
		<data>
		49u4usgWTxwZoW9KPHIEiUmoz1w=
		</data>
		<key>Microsoft.iOS.pdb</key>
		<data>
		4tzedn+yKKM0jKj3bPym9BUaM3c=
		</data>
		<key>MonoTouchDebugConfiguration.txt</key>
		<data>
		oowkf1sL6T19nanXUObgmuayWqM=
		</data>
		<key>Newtonsoft.Json.dll</key>
		<data>
		8xMPf9S0FLWuwE64ftgA64TdIVQ=
		</data>
		<key>OpenSans-Regular.ttf</key>
		<data>
		E7tDjzwBoWNBKyICttcpD2lF/d8=
		</data>
		<key>OpenSans-Semibold.ttf</key>
		<data>
		5sf/EzXZsYij5woLxld7dbOGWVY=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		+59cRc8ev+HwAO70fp1P8R6hyjc=
		</data>
		<key>Settings.bundle/Root.plist</key>
		<data>
		nrVMGKClE6pvxn6IcBgG4it6i0A=
		</data>
		<key>System.AppContext.dll</key>
		<data>
		PAOqR41S4nMhSsHKIaT3Qf8YycQ=
		</data>
		<key>System.Buffers.dll</key>
		<data>
		5JTIFyIiD8o8Zc+078kYSE7pEw0=
		</data>
		<key>System.ClientModel.dll</key>
		<data>
		zq+Ff00tuqZKD6K/rq+sl7ba69o=
		</data>
		<key>System.Collections.Concurrent.dll</key>
		<data>
		F8emFGiwMQcDBCqkFOfxtOWhcJw=
		</data>
		<key>System.Collections.Immutable.dll</key>
		<data>
		wg8JiaYg9brWEzGCkUBY3xz9PrQ=
		</data>
		<key>System.Collections.NonGeneric.dll</key>
		<data>
		zmd2VMMBgDZnZGyCCoLPIm9X5Yw=
		</data>
		<key>System.Collections.Specialized.dll</key>
		<data>
		qjTY+sivgP82yXljhJPR59HEcGA=
		</data>
		<key>System.Collections.dll</key>
		<data>
		5fYzq8uVVBDV5tmdO6Y+iYYtAPg=
		</data>
		<key>System.ComponentModel.Annotations.dll</key>
		<data>
		GYj8zTqEEGRHHIbfhqhlySKvIh4=
		</data>
		<key>System.ComponentModel.DataAnnotations.dll</key>
		<data>
		c3zDCn6Uv/x9FiQpQo1smNvl6OA=
		</data>
		<key>System.ComponentModel.EventBasedAsync.dll</key>
		<data>
		OYN73p3grUlBwDOS/GJtlKa/Fbg=
		</data>
		<key>System.ComponentModel.Primitives.dll</key>
		<data>
		lsgadN10WmBLs6u/eUV9kVCDsNQ=
		</data>
		<key>System.ComponentModel.TypeConverter.dll</key>
		<data>
		pCuMG+z1C1bu1lcqfhzqyqrO5Mk=
		</data>
		<key>System.ComponentModel.dll</key>
		<data>
		V9oU8EK2iaPYe2049wwSInzTAsY=
		</data>
		<key>System.Configuration.dll</key>
		<data>
		HK7weJsfgFLIRsK3KV6a2q8WUJE=
		</data>
		<key>System.Console.dll</key>
		<data>
		jH0BQ3K2+NTzu+92oQHUkCUOHiU=
		</data>
		<key>System.Core.dll</key>
		<data>
		C2PBZpUeUxlrARWL3/Mh+3J8k/A=
		</data>
		<key>System.Data.Common.dll</key>
		<data>
		x6tLa0SGw4Wp09SQVxkzy0gOt8E=
		</data>
		<key>System.Data.DataSetExtensions.dll</key>
		<data>
		h9QoIzscSKeiQACYs516DyUx/z0=
		</data>
		<key>System.Data.dll</key>
		<data>
		0QGJX6iJNwjA5r5nTlGipL9NBTQ=
		</data>
		<key>System.Diagnostics.Contracts.dll</key>
		<data>
		SrOZRG+6Z7z7jqFUWoEswPlo5KY=
		</data>
		<key>System.Diagnostics.Debug.dll</key>
		<data>
		A3Fu3GpRbvHpNDUhRjhGWb9JCDY=
		</data>
		<key>System.Diagnostics.DiagnosticSource.dll</key>
		<data>
		YKiHvnMhNE4TV9UrpKDlLWFgygo=
		</data>
		<key>System.Diagnostics.FileVersionInfo.dll</key>
		<data>
		aClgoXNmSB1t9WL5cvfLwWF8W0I=
		</data>
		<key>System.Diagnostics.Process.dll</key>
		<data>
		gUj6f2zLgRSI4ErQ/g5uPchDuGE=
		</data>
		<key>System.Diagnostics.StackTrace.dll</key>
		<data>
		LBskzxXhn2kA2WpUPLmvohUaPc8=
		</data>
		<key>System.Diagnostics.TextWriterTraceListener.dll</key>
		<data>
		bgsXJNvzAMIVsbBdDUyqM2Ni0lI=
		</data>
		<key>System.Diagnostics.Tools.dll</key>
		<data>
		vL+cuM6KD2/uynTaZlu77ftB4E0=
		</data>
		<key>System.Diagnostics.TraceSource.dll</key>
		<data>
		mk39MpjaPW+DhowY8m35a55f/rE=
		</data>
		<key>System.Diagnostics.Tracing.dll</key>
		<data>
		R029WhRnpJ/40CGKszumRmTFPjk=
		</data>
		<key>System.Drawing.Primitives.dll</key>
		<data>
		uU8wNoVcjba4I12V4EsG261J4ac=
		</data>
		<key>System.Drawing.dll</key>
		<data>
		jBH/IoqWiGdL7zhuq1qPUcb5rYo=
		</data>
		<key>System.Dynamic.Runtime.dll</key>
		<data>
		WsQMic4i91kTJGdk5GuKEYWhpPU=
		</data>
		<key>System.Formats.Asn1.dll</key>
		<data>
		OZXbroZbw/oockMqCfX/Mabzv+8=
		</data>
		<key>System.Formats.Tar.dll</key>
		<data>
		HEzPVVoEuvb3qycyNd22N2k48io=
		</data>
		<key>System.Globalization.Calendars.dll</key>
		<data>
		lThkA0ol2NFin0sGJtlNj9TW8/Q=
		</data>
		<key>System.Globalization.Extensions.dll</key>
		<data>
		/nKvgQ5GeO5vRdbYVSycqXs3U4A=
		</data>
		<key>System.Globalization.dll</key>
		<data>
		Oi5vVMrsnkQrPe1Cu/ISChh3+sI=
		</data>
		<key>System.IO.Compression.Brotli.dll</key>
		<data>
		Jhk1iV0bqVXyBM8CCRHszMTzOdM=
		</data>
		<key>System.IO.Compression.FileSystem.dll</key>
		<data>
		6UmRwqx2lusYNpMUotXEjFSFxGA=
		</data>
		<key>System.IO.Compression.ZipFile.dll</key>
		<data>
		YluQOAFaFqvY0Af5vjN2uXhVNZU=
		</data>
		<key>System.IO.Compression.dll</key>
		<data>
		WSQ5SOyt/QHazj2EoK5ed5VSzuw=
		</data>
		<key>System.IO.FileSystem.AccessControl.dll</key>
		<data>
		/2Wn71RaMcdT0EpO7hBA/msPxYs=
		</data>
		<key>System.IO.FileSystem.DriveInfo.dll</key>
		<data>
		OeST92yln+NDPAgAW6h49TRv3gI=
		</data>
		<key>System.IO.FileSystem.Primitives.dll</key>
		<data>
		SoGBSvY9DTPl2jpU+M46cZzTBuc=
		</data>
		<key>System.IO.FileSystem.Watcher.dll</key>
		<data>
		C7fBA+om0EqkC8p9iVCh+x2g7AE=
		</data>
		<key>System.IO.FileSystem.dll</key>
		<data>
		6MSuqBuUhlIvQF/8n7CCrXfOPcc=
		</data>
		<key>System.IO.IsolatedStorage.dll</key>
		<data>
		UH/pOlgl8gLsEAp57vzw/OV+mo8=
		</data>
		<key>System.IO.MemoryMappedFiles.dll</key>
		<data>
		YAWOe8BBhLymJmNmemvKxqsPQl8=
		</data>
		<key>System.IO.Pipelines.dll</key>
		<data>
		uxKTuyGUPYeU90KNW6Xa9FRDU2s=
		</data>
		<key>System.IO.Pipes.AccessControl.dll</key>
		<data>
		TUMJlJvVzUkiEna3wTfvMYdI+1w=
		</data>
		<key>System.IO.Pipes.dll</key>
		<data>
		sxSFll01sOzbLIUYrNOf/gOOWZ4=
		</data>
		<key>System.IO.UnmanagedMemoryStream.dll</key>
		<data>
		uMxFbOyFkQ0nvOkuLgwoxK9tPI4=
		</data>
		<key>System.IO.dll</key>
		<data>
		6D/gTG8as6T2E4HySGduphVAEdM=
		</data>
		<key>System.Linq.Expressions.dll</key>
		<data>
		oV65bipm+z+g3gd50ECQbpWLBYE=
		</data>
		<key>System.Linq.Parallel.dll</key>
		<data>
		uajDEnIviSlfzzxKvD9FdkFv0yE=
		</data>
		<key>System.Linq.Queryable.dll</key>
		<data>
		a4e6XKGtSWxAHkz52NQHwUcqovA=
		</data>
		<key>System.Linq.dll</key>
		<data>
		z5bTqZC7oq/bJn2F7tTLGQUKnXs=
		</data>
		<key>System.Memory.Data.dll</key>
		<data>
		mLzVyQoOCiJFvfnZ68pui5x7MAo=
		</data>
		<key>System.Memory.dll</key>
		<data>
		YynrUdUU8E24wH/3JhgUvXtgvmk=
		</data>
		<key>System.Net.Http.Json.dll</key>
		<data>
		XqGJ7SJIHrhYPfY8SP/ImCWcyvY=
		</data>
		<key>System.Net.Http.dll</key>
		<data>
		7PF/184uBfbm0AemZYe0QbBISMc=
		</data>
		<key>System.Net.HttpListener.dll</key>
		<data>
		TDH1QszV4guyl6dS6u2d6wMyLYc=
		</data>
		<key>System.Net.Mail.dll</key>
		<data>
		iLGCa9R5x6SXKXwFJsenw6y+YJk=
		</data>
		<key>System.Net.NameResolution.dll</key>
		<data>
		sNHuvUzQNKLs4cnQr2YwKHf74Ko=
		</data>
		<key>System.Net.NetworkInformation.dll</key>
		<data>
		u8HlR/ERqkmOnp4HY7jieOYJP4g=
		</data>
		<key>System.Net.Ping.dll</key>
		<data>
		9gk6SIsv61N8Z1HrP5OWK7Pntu4=
		</data>
		<key>System.Net.Primitives.dll</key>
		<data>
		IZys/mJDMqCPy/6F7QxiiSNnRak=
		</data>
		<key>System.Net.Quic.dll</key>
		<data>
		sYXTl62WuFh/po1Y2Vw6E7TYRF0=
		</data>
		<key>System.Net.Requests.dll</key>
		<data>
		Ef4Tmij4Dfnp727jTtQjGhpSdcM=
		</data>
		<key>System.Net.Security.dll</key>
		<data>
		xXADRad5Cb00gVrwrvEd5eizfL0=
		</data>
		<key>System.Net.ServicePoint.dll</key>
		<data>
		/knuz98jCq9HYlM1QLoZXmuiBQE=
		</data>
		<key>System.Net.Sockets.dll</key>
		<data>
		akf+Wk4GikZB6mcA7w8q0ww5tr4=
		</data>
		<key>System.Net.WebClient.dll</key>
		<data>
		5YrFPKWEG7FX9DUPAZAh+eo66Zs=
		</data>
		<key>System.Net.WebHeaderCollection.dll</key>
		<data>
		sGYBolfWNBt6Du1Kw0Qpz1wiDkk=
		</data>
		<key>System.Net.WebProxy.dll</key>
		<data>
		XPXpRe8ElGcsqti30MLCOVPsxTs=
		</data>
		<key>System.Net.WebSockets.Client.dll</key>
		<data>
		C3RKqWvN78PfVKAWyVMaOjfQOpM=
		</data>
		<key>System.Net.WebSockets.dll</key>
		<data>
		fySrWSIAqasjKRHJ8PLcvVV9eKA=
		</data>
		<key>System.Net.dll</key>
		<data>
		WnNMupsRLtg0i09AXR1B2j9wySY=
		</data>
		<key>System.Numerics.Vectors.dll</key>
		<data>
		bl10462/mMVWZ206+XbDFHTlINw=
		</data>
		<key>System.Numerics.dll</key>
		<data>
		WRruP83ilWISy9vi9RO+FnYyWPc=
		</data>
		<key>System.ObjectModel.dll</key>
		<data>
		aydFT0sKcHOa7hN4HIt1zjDSXoc=
		</data>
		<key>System.Private.CoreLib.aotdata.arm64</key>
		<data>
		IrWKpPqnSaWG5HYbh0EkYMXsE7M=
		</data>
		<key>System.Private.CoreLib.dll</key>
		<data>
		QvGfsl9E32nqj5y7UsIWd7xZTKc=
		</data>
		<key>System.Private.DataContractSerialization.dll</key>
		<data>
		49DO8dS0OZhwqklH08YGgfok5+I=
		</data>
		<key>System.Private.Uri.dll</key>
		<data>
		kV/0TT7uJfBLJPHYtom6yG+wnd4=
		</data>
		<key>System.Private.Xml.Linq.dll</key>
		<data>
		FADa7UGGR4aTN3ZLin3HNV8PomY=
		</data>
		<key>System.Private.Xml.dll</key>
		<data>
		FatWmv7aBSFBEZ7ujCMBSmwJ4eQ=
		</data>
		<key>System.Reflection.DispatchProxy.dll</key>
		<data>
		eyGuRIYlU93ZSslhD0kOxUO9oRQ=
		</data>
		<key>System.Reflection.Emit.ILGeneration.dll</key>
		<data>
		TozS3mYkrTzZ8H1uX+LyGJuwwp4=
		</data>
		<key>System.Reflection.Emit.Lightweight.dll</key>
		<data>
		D6U5ldd3/Cpx9d/R5yPkvDENfio=
		</data>
		<key>System.Reflection.Emit.dll</key>
		<data>
		pyQ01r3a6kn/HshUTs0hFIhJMY4=
		</data>
		<key>System.Reflection.Extensions.dll</key>
		<data>
		LtfLgvncykeNF49JILmYNgT4Ffs=
		</data>
		<key>System.Reflection.Metadata.dll</key>
		<data>
		7mOGfJZn4fHioSxZNVsYfj+CYOQ=
		</data>
		<key>System.Reflection.Primitives.dll</key>
		<data>
		LaVYBXEueESLenz9ZKWvQdj0rvQ=
		</data>
		<key>System.Reflection.TypeExtensions.dll</key>
		<data>
		WlRvuLXhEml3fh+daIT+aeQpPPk=
		</data>
		<key>System.Reflection.dll</key>
		<data>
		KKVreUeAHPL3cn9VSolF0V0k5sE=
		</data>
		<key>System.Resources.Reader.dll</key>
		<data>
		4hmKwFCBq0Fsi1GMlqQUluj4xJQ=
		</data>
		<key>System.Resources.ResourceManager.dll</key>
		<data>
		N0Wym/vkutr0aMcRa3UaouF+sYU=
		</data>
		<key>System.Resources.Writer.dll</key>
		<data>
		QIr1N9ugOPJ8P7SNu7RoRkepymw=
		</data>
		<key>System.Runtime.CompilerServices.Unsafe.dll</key>
		<data>
		j5bWD9EJkjOq0u6lEsZJ7y1hkZU=
		</data>
		<key>System.Runtime.CompilerServices.VisualC.dll</key>
		<data>
		QKMOrbzpeb4RqyiSWwmk0WEIOCc=
		</data>
		<key>System.Runtime.Extensions.dll</key>
		<data>
		dAo77whDfTgG8zcPq3a9QG5qlDQ=
		</data>
		<key>System.Runtime.Handles.dll</key>
		<data>
		3EbeEv8LMNKxtV4nMVZFbigXXLw=
		</data>
		<key>System.Runtime.InteropServices.JavaScript.dll</key>
		<data>
		ccDJwKTVRdcc+FICLVi7LLglmbg=
		</data>
		<key>System.Runtime.InteropServices.RuntimeInformation.dll</key>
		<data>
		Nx2PvpmsNyEliQ20aAfUbcmZxw4=
		</data>
		<key>System.Runtime.InteropServices.dll</key>
		<data>
		NVzCO/o7ImWUyyJCcgdQ0E1ELmc=
		</data>
		<key>System.Runtime.Intrinsics.dll</key>
		<data>
		pvuAjjW+9hoRB6xe2ZTT3ARTH7Y=
		</data>
		<key>System.Runtime.Loader.dll</key>
		<data>
		enTIwWKu3F6v2aW3TWW1tcckfIM=
		</data>
		<key>System.Runtime.Numerics.dll</key>
		<data>
		wFmwI8z98WGBACZIOtKxMagz/No=
		</data>
		<key>System.Runtime.Serialization.Formatters.dll</key>
		<data>
		wvhAyGFev+Ni9u5kHI5I4x98ZgU=
		</data>
		<key>System.Runtime.Serialization.Json.dll</key>
		<data>
		x2wg/DVmJDJuIIIbqkGe3YrhXXE=
		</data>
		<key>System.Runtime.Serialization.Primitives.dll</key>
		<data>
		8p8AsHt7ySlrEfeAr0CaiEsopqo=
		</data>
		<key>System.Runtime.Serialization.Xml.dll</key>
		<data>
		qniTTp9J8Aj29wsHo0CGjfJsGE4=
		</data>
		<key>System.Runtime.Serialization.dll</key>
		<data>
		DwqzXVcA7V4mBtll96CQiodj8jo=
		</data>
		<key>System.Runtime.dll</key>
		<data>
		csYBnCgotm3LvvVnh3eiq2iEryo=
		</data>
		<key>System.Security.AccessControl.dll</key>
		<data>
		sC17T9VJ0ZjeoeUskjcelGXbjHs=
		</data>
		<key>System.Security.Claims.dll</key>
		<data>
		AkqnxcmqFae32u+1FxsTUxUebaQ=
		</data>
		<key>System.Security.Cryptography.Algorithms.dll</key>
		<data>
		Dl/mWOn6/UyFa5sprVlPP4UKKaQ=
		</data>
		<key>System.Security.Cryptography.Cng.dll</key>
		<data>
		ymH9zNGpc4TcsXgZci4s7wtsw0U=
		</data>
		<key>System.Security.Cryptography.Csp.dll</key>
		<data>
		PnXP/lKRTIiWTk5FeUFvbD5ko+M=
		</data>
		<key>System.Security.Cryptography.Encoding.dll</key>
		<data>
		JuYUq2X4bRjBzGfCM/NA+wfEzM0=
		</data>
		<key>System.Security.Cryptography.OpenSsl.dll</key>
		<data>
		rcZDZtlzlKJQhuTbJtgci4mlK3g=
		</data>
		<key>System.Security.Cryptography.Primitives.dll</key>
		<data>
		1z0cr1eIt+UtazjlPBSV3ogFnHI=
		</data>
		<key>System.Security.Cryptography.X509Certificates.dll</key>
		<data>
		261EyGQVVpllap15j/VvGxsJrv8=
		</data>
		<key>System.Security.Cryptography.dll</key>
		<data>
		bdodJJaujn5VYEIg+cLVbKzBJgM=
		</data>
		<key>System.Security.Principal.Windows.dll</key>
		<data>
		sZKCerMUVUfE2igP2LRbKt/s+N4=
		</data>
		<key>System.Security.Principal.dll</key>
		<data>
		TwNFxCvMJ2pxgjDtIMc5ZPsIwFk=
		</data>
		<key>System.Security.SecureString.dll</key>
		<data>
		sAH8okTu2UfCSuDPo0RLjovO0jY=
		</data>
		<key>System.Security.dll</key>
		<data>
		0Yd6ex0PrdgvDSAfUSgPWuS/9us=
		</data>
		<key>System.ServiceModel.Web.dll</key>
		<data>
		aHGJEtJ5W2NSTKAzkjMeC6mi6uU=
		</data>
		<key>System.ServiceProcess.dll</key>
		<data>
		9YGXx1icUTQD1C/wkyjTv/umWz8=
		</data>
		<key>System.Text.Encoding.CodePages.dll</key>
		<data>
		u/GYaQpMyspOJpE+IoXKdLOpZ7o=
		</data>
		<key>System.Text.Encoding.Extensions.dll</key>
		<data>
		GD7OpNgklG904goRQpcxXGH4kpU=
		</data>
		<key>System.Text.Encoding.dll</key>
		<data>
		DGOUNL/OaEsMZxMzEo18yDE9GOs=
		</data>
		<key>System.Text.Encodings.Web.dll</key>
		<data>
		UImx9xhUxJ6Oo/yzfMe7+LTSi3I=
		</data>
		<key>System.Text.Json.dll</key>
		<data>
		v9YJrCgq2Te+D2CRJxySPBRIVOw=
		</data>
		<key>System.Text.RegularExpressions.dll</key>
		<data>
		4dYINp/3sPJcGkZJyBAfxjNNAAk=
		</data>
		<key>System.Threading.Channels.dll</key>
		<data>
		9cMw92kn2duXADIhbt2ZgWyxlYo=
		</data>
		<key>System.Threading.Overlapped.dll</key>
		<data>
		EHY7F+7SgWaHX0OhkKSBuiKOTEg=
		</data>
		<key>System.Threading.Tasks.Dataflow.dll</key>
		<data>
		SXFaawwqRpcrgKV1+JSVMr0c6VI=
		</data>
		<key>System.Threading.Tasks.Extensions.dll</key>
		<data>
		802NlSlfdHIQpXq3/I3rM8PdWMk=
		</data>
		<key>System.Threading.Tasks.Parallel.dll</key>
		<data>
		AHzsvHiktMRURqLF9Iay9a6P554=
		</data>
		<key>System.Threading.Tasks.dll</key>
		<data>
		TRCjlSBQCNAnJt5+uCSKr7ROwRg=
		</data>
		<key>System.Threading.Thread.dll</key>
		<data>
		QvqWJ4GRwA2DUvKsRpytvZd1dOw=
		</data>
		<key>System.Threading.ThreadPool.dll</key>
		<data>
		yrw2YQlOqZkMDcs2Mfv2zDvRuq4=
		</data>
		<key>System.Threading.Timer.dll</key>
		<data>
		viuied290uuiJUAVaaqTqgNagYY=
		</data>
		<key>System.Threading.dll</key>
		<data>
		1Rt44WQ3pZ7WX2NzD28d+w/UAXs=
		</data>
		<key>System.Transactions.Local.dll</key>
		<data>
		13n1NBBAP5lohb5SqXRrMN4Ql1c=
		</data>
		<key>System.Transactions.dll</key>
		<data>
		oFHhWHEhJeGdD1uoxXfkNbaxm+c=
		</data>
		<key>System.ValueTuple.dll</key>
		<data>
		MAsHHykHxIwODaEEgh5X3kaxQfY=
		</data>
		<key>System.Web.HttpUtility.dll</key>
		<data>
		tKM9RT3UEcqOaoQuBbVOIKOdc0k=
		</data>
		<key>System.Web.dll</key>
		<data>
		Mw6nCZFNkw+PbqHbzZk11jz0rB4=
		</data>
		<key>System.Windows.dll</key>
		<data>
		pGPF1bk8ylsbcaCISsP/jzUW+JY=
		</data>
		<key>System.Xml.Linq.dll</key>
		<data>
		UXy62pwyr5IRx4zJl0tZfjFinpA=
		</data>
		<key>System.Xml.ReaderWriter.dll</key>
		<data>
		vKIW1pFzYeXymH2pskgECwccyYQ=
		</data>
		<key>System.Xml.Serialization.dll</key>
		<data>
		iazvUO2/kO6Vrr1eEuEJsFGuCYo=
		</data>
		<key>System.Xml.XDocument.dll</key>
		<data>
		j1ffSSd8LYyiQbSbv9SNRF4qD1Q=
		</data>
		<key>System.Xml.XPath.XDocument.dll</key>
		<data>
		BgUv7puFKh3yhZ6Q4D1hT3+xI6A=
		</data>
		<key>System.Xml.XPath.dll</key>
		<data>
		s/h9s9/2QgZZBhF2/GikscKhxzY=
		</data>
		<key>System.Xml.XmlDocument.dll</key>
		<data>
		uTvOU86fimS1uHnHtcxsm2Og1/c=
		</data>
		<key>System.Xml.XmlSerializer.dll</key>
		<data>
		n3eHAmtazttCj7PWm2A16GPvrCE=
		</data>
		<key>System.Xml.dll</key>
		<data>
		1cg/Idcg+eYYG7aNUG2FqoiRMEc=
		</data>
		<key>System.dll</key>
		<data>
		oUojDTqn3C8ID2jze+dEaRm9MMs=
		</data>
		<key>WindowsBase.dll</key>
		<data>
		2o4TI+sWIK8N/eaaILJ8lSCpl7A=
		</data>
		<key><EMAIL></key>
		<data>
		UfYwHkF8a5bRubVQY9ODZOMM7h4=
		</data>
		<key>appicon76x76@2x~ipad.png</key>
		<data>
		FeUR736WBwP2X6hb/+Lv/NmnrUY=
		</data>
		<key>ar/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		cd3rk5htjPDxLPgLCsS3wAymnLU=
		</data>
		<key>archived-expanded-entitlements.xcent</key>
		<data>
		nDZSh+0+TIHU/8mmtdwiEvpQsVM=
		</data>
		<key>ca/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		NmUMSRt0vNwcUGZudLcK1nAQmBg=
		</data>
		<key>cs/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		77UQcHBDiVM9Cy6sASkK8WKPyBU=
		</data>
		<key>da/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		HWLGMIxtE9kO8b9sqdhtyA7zw3w=
		</data>
		<key>de/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		MflIuiE8VKFgcp7lX64ENZlb08M=
		</data>
		<key>dotnet_bot.png</key>
		<data>
		TCPYt8l244J9a2sCFwVUdoBhDdA=
		</data>
		<key><EMAIL></key>
		<data>
		nsnZE/LubM8lWn5PzK0t5hD6/Qg=
		</data>
		<key><EMAIL></key>
		<data>
		km7arW01LDKD2LzrEduLpKYf69k=
		</data>
		<key>el/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		gDclXnsjpy4kgZXyjSPM/p/pmd4=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		4BQ0NV6xGwul+JJAQ0Q+C998yDc=
		</data>
		<key>es/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		l1yDWPgKfEH5FetRlXDJmmOC3lI=
		</data>
		<key>fi/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		UGju/JUAhoHCTE27MGcZfxSzraI=
		</data>
		<key>fr/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		5RLZ14p8dSGgvcuk+8k+nUyeyRw=
		</data>
		<key>he/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		a77yUBWxniPKHBloD4pEiQWSzPs=
		</data>
		<key>hi/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		lOZt3YSPenKhcFLP1vl7qVBMKdE=
		</data>
		<key>hr/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		RD3t+aPEZg8FtsI0DIfTMq27LHA=
		</data>
		<key>hu/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		FaotItdFZD8bR1v7U8Gp8VT3Jio=
		</data>
		<key>id/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		OEDK4AptpN7irjz5bkXs3hD9DHQ=
		</data>
		<key>it/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		EoGB8C6vVCxUREDUFhBVpAlJhkM=
		</data>
		<key>ja/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		n4varzkd3UYe0riiCWcACBO6f6E=
		</data>
		<key>ko/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		aOFF42NLnxOkVmjcdFzBDu+OsbM=
		</data>
		<key>ms/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		1YYtUmLFWs2HsyqqZ2Ijvlq9XTU=
		</data>
		<key>mscorlib.dll</key>
		<data>
		L/WYb2bFOvhbEkVX8fHMbGWlN2E=
		</data>
		<key>nb/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		U+u9RY3+kgAAfIUZHNOEtajMKoE=
		</data>
		<key>netstandard.dll</key>
		<data>
		STIqmnlHWfBepcMZi2/m8Lev9yA=
		</data>
		<key>nl/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		re7jcdUdXTxDpcgqeZEXcxfPhfU=
		</data>
		<key>pl/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		svF/Z9jroAjdNhO11417egsQSkA=
		</data>
		<key>pt-BR/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		jeQ7a5SC12THYVW792hiXqHdPOw=
		</data>
		<key>pt/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		2a06BPclc6R63qdL3RtVy7hKqL4=
		</data>
		<key>ro/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		XYqBLOeXje0ejL9MrYFvIme4Dm4=
		</data>
		<key>ru/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		Iq8lggBrBETfjP5lJ03SSRatktw=
		</data>
		<key>runtimeconfig.bin</key>
		<data>
		boG5McNNjtQaNB0dknq8aSPQp+M=
		</data>
		<key>sk/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		Q18Hd9r7iR5/FGfQ//eXf7zroQg=
		</data>
		<key>splash_7CF9AE42DA2017EC6621B726A870D40F01970097ED8F3D99ABBC1EC8470F119D.png</key>
		<data>
		e6LlBMI9b91XaCSQDhDOglC5cA8=
		</data>
		<key><EMAIL></key>
		<data>
		gr+pgKyzNNxtcT1JaZIj7N80Fxc=
		</data>
		<key><EMAIL></key>
		<data>
		V9uL2U0a2H/Rg28AStjUugC+RP4=
		</data>
		<key>sv/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		jbBlEvShSTQ9yvhs4CsdLxlSTzs=
		</data>
		<key>th/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		pQmui4P7NPJN710lOOxciYstvLk=
		</data>
		<key>tr/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		PLJ3ar9WCE2HnlzTokdwcf0Tx8Y=
		</data>
		<key>uk/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		yk10yLUR1/FcJzmLjhzcbEEN8Z8=
		</data>
		<key>vi/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		xPDs+GPDOWNx9DudrDJoBL7Uolk=
		</data>
		<key>zh-HK/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		afFSNYZLM9RlZz6kxUs+kDdzyDg=
		</data>
		<key>zh-Hans/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		F/pu92wrEuwIWsundXCkFtJDSGU=
		</data>
		<key>zh-Hant/Microsoft.Maui.Controls.resources.dll</key>
		<data>
		lDJs36coAuqu7J5Dj3n1jSSfWSg=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AboutAssets.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			fVnsP1e2L88aqi4aLkmySlc7msxv3FPDQxEKAAgiips=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			bWA37LlrT1M13GP58A/+DSGWSTcqnWleIDx1Qpb2+PM=
			</data>
		</dict>
		<key>Azure.Core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			t0zbc4kDu2q+gmkQXArk5xJp8Q3XNnsf1WL1c9Drz68=
			</data>
		</dict>
		<key>Azure.Data.Tables.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			i3rfqEepCGCSLpy5C/J4OWUvjXXfwewLVb7KMB4uKew=
			</data>
		</dict>
		<key>CommunityToolkit.Mvvm.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			fX61wGbP6IO0ObEgC/Xcs4ex5TGd0u29qp4jrf5BZFI=
			</data>
		</dict>
		<key>HabitBuilder.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FjT01GPmhVHODzfYS2DGjNfxUgV8TPAIuiZ+BFTWHOc=
			</data>
		</dict>
		<key>HabitBuilder.pdb</key>
		<dict>
			<key>hash2</key>
			<data>
			XmQp24YFgknUddqj9YqiI7jFYlYIIMnI4COrYh8wfcA=
			</data>
		</dict>
		<key>MauiInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tx0KXqEC+DvcCGyogQTEQWHcCWJYYqH24NtPwhpQEjo=
			</data>
		</dict>
		<key>MauiSplash.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			PD0GUDDeE5JffNIPVoucOIHmED6dLPnIxI/a9Oh8ADc=
			</data>
		</dict>
		<key>MauiSplash.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>MauiSplash.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Microsoft.AspNetCore.Authorization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			R3Pu5WN8shLrjA3WAWHHI/Kd/mgFxxT7p84HHptXdOM=
			</data>
		</dict>
		<key>Microsoft.AspNetCore.Components.Authorization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PwZydeVWEbHTclhDad2demeVJouR7p7kyzg+loZHBa8=
			</data>
		</dict>
		<key>Microsoft.AspNetCore.Components.Forms.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PBmF8vgbrjyxs4h+Ri7hXhA0QIsFd6ZseLS45vGuMyI=
			</data>
		</dict>
		<key>Microsoft.AspNetCore.Components.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			n7K0eKdQkUj7JfmFtULSfWCuBGm2RX5CV488kCkw9D0=
			</data>
		</dict>
		<key>Microsoft.AspNetCore.Components.WebAssembly.Authentication.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			AnuG24EHWHI4nGpYEcC0ZstbAjp4dXF6Pyni5VoJTTo=
			</data>
		</dict>
		<key>Microsoft.AspNetCore.Components.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ard94ZKHFIKDB59FweWRvTl4bjxJFMk8Rt0e/Ift4bI=
			</data>
		</dict>
		<key>Microsoft.AspNetCore.Metadata.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			CwmcsAV1NcOfllhXyOFdf4ZgeWerBpva5OZNkIm3Xwc=
			</data>
		</dict>
		<key>Microsoft.Authentication.WebAssembly.Msal.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Yjmm5ot9gtKL5pWTI+qBNLMlU+WSJ1hHck+QOfCaS30=
			</data>
		</dict>
		<key>Microsoft.Bcl.AsyncInterfaces.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			VwXSRQctPreEAFR7MhR9u24siwK6i9p2cpeY9e/erss=
			</data>
		</dict>
		<key>Microsoft.CSharp.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vdwImkd7vqJVVshNLIHNarDveA8Kj/wpq4uUPFaJH2U=
			</data>
		</dict>
		<key>Microsoft.Extensions.Configuration.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QiMRuMj66AstnNkZFPpuiBFkNXm+1t+dlF3swb8hQ9Y=
			</data>
		</dict>
		<key>Microsoft.Extensions.Configuration.Binder.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			o3yfndH5x10rAjrEdI1ko5uZmsmg/72neNNNrSnlkyw=
			</data>
		</dict>
		<key>Microsoft.Extensions.Configuration.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			YVGmxdCNsxUUvZ8liO7xKjHG6KDep08uYPiINzawVVs=
			</data>
		</dict>
		<key>Microsoft.Extensions.DependencyInjection.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kjnI/khISpCav1lGepfL0iFf9sc+az0+C/1Fb78v2SA=
			</data>
		</dict>
		<key>Microsoft.Extensions.DependencyInjection.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			06p/5BlWqVa+ehxKZs36DOtXW0Hb9cI//egaIP/m0dA=
			</data>
		</dict>
		<key>Microsoft.Extensions.Diagnostics.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ipSl4J2rWXXks04P5aMDp6Qd8vOZUg4LNRja0l/DCxQ=
			</data>
		</dict>
		<key>Microsoft.Extensions.Diagnostics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QYH0qc8IEXCfmCDfxRTynGOZgSJm2GA2lI1Azlmvk/Q=
			</data>
		</dict>
		<key>Microsoft.Extensions.Http.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PKGBMIXvHHC/RDkeiMkx+7xYaM6b1pgQSLozEUcrgkU=
			</data>
		</dict>
		<key>Microsoft.Extensions.Logging.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RrU4rv8JyB83guc6W3hXhqb2sLAPgKQXJGsfYrkQYDQ=
			</data>
		</dict>
		<key>Microsoft.Extensions.Logging.Debug.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FyyI7owQaytVxFWdFnB+QS4sA0a9bmKWneXMzKMS/5Y=
			</data>
		</dict>
		<key>Microsoft.Extensions.Logging.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			SJcs56kSwtAyvwl77UOgipXN2chOq8Zymw2DkBUFTSU=
			</data>
		</dict>
		<key>Microsoft.Extensions.Options.ConfigurationExtensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			pBay1IEDz/RWMkaTNRybbZadQtOT43mhY6GjtCFcIrg=
			</data>
		</dict>
		<key>Microsoft.Extensions.Options.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Y1vWOPcCBlchBvAJumeHZU1cRyp3cAvIRWUUVEpxtMc=
			</data>
		</dict>
		<key>Microsoft.Extensions.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RAlllKNZCPPdH34p1Vcf0gXB4CJchAOgONnhZfffqD0=
			</data>
		</dict>
		<key>Microsoft.Identity.Client.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			p6SjJUlC5ZaBLGWUJDbzmYIoJKZAO7yeQ0HaEeukgTA=
			</data>
		</dict>
		<key>Microsoft.IdentityModel.Abstractions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			9GlNI/BcwCY6u8tTbRMuJDvJwXWuZCOH+phlK4gKS+A=
			</data>
		</dict>
		<key>Microsoft.JSInterop.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			V/Znd/h46yOfAwbtUJM+xcEo/sslN1NtAHpkgdtFs/c=
			</data>
		</dict>
		<key>Microsoft.Maui.Controls.Xaml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			YCGQ5SNoyFs0EZdiBuklGogmdObS9kmSjYSLYYm3yg8=
			</data>
		</dict>
		<key>Microsoft.Maui.Controls.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lzxtiJmgFNwoYSGR2tpUdTrYESJCURreK+UKFsa82VE=
			</data>
		</dict>
		<key>Microsoft.Maui.Essentials.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			X7y/w3TCVRIeFoFxJBonp5BXetMQMO+viUFJzV+eGYQ=
			</data>
		</dict>
		<key>Microsoft.Maui.Graphics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			gv5Kasa+US4LVYYFolUM6mo/YBwXtJ4uQJgh3Uv76ww=
			</data>
		</dict>
		<key>Microsoft.Maui.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZQ4nawFEb67tYUBCOClsXNTDR42fcIweXmPsq1T3xTM=
			</data>
		</dict>
		<key>Microsoft.VisualBasic.Core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			3GGaB1Q/+Ss5foA1p9I2HIw8G79BPfETgei4xuFeTxU=
			</data>
		</dict>
		<key>Microsoft.VisualBasic.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			NH4eCbrP/lt/KsZ58vkCg4VSh0NpOwofZIORONTG6uU=
			</data>
		</dict>
		<key>Microsoft.Win32.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bItVc6+eIUDTrT7GTIdpYC4ilD8yNQMyC9tFl6T7jP8=
			</data>
		</dict>
		<key>Microsoft.Win32.Registry.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZwA9qVsxWgrV4si0riumniiwdoKONoiWYnsN+WBExRE=
			</data>
		</dict>
		<key>Microsoft.iOS.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+tYECmNla5GkFYUoPt5SsWHiuNxQHc37F0B/VRYd544=
			</data>
		</dict>
		<key>Microsoft.iOS.pdb</key>
		<dict>
			<key>hash2</key>
			<data>
			9P6A6DPkHIUTvolvzwTk8AdMLDQgM9N9TjwxgUjHYFw=
			</data>
		</dict>
		<key>MonoTouchDebugConfiguration.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			RS51BO98d+zhJWK1abJOyD5jB4nYJnWJPtL2DZyA/zw=
			</data>
		</dict>
		<key>Newtonsoft.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IsZJ91/OW+fHzNqIgEc7Y072ns8z9dGritiSyvR9Wgc=
			</data>
		</dict>
		<key>OpenSans-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			6vmgCNlEbtolyBtGmuantYgNtzA6aeHJJyA81HTjlE4=
			</data>
		</dict>
		<key>OpenSans-Semibold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			yvihPI4b1mopdS0hU/Lvxi7Wurv7VhlmfVMPkRaVDu0=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			vSdOCYnhm9fj+qcbPQi0IfGVKZjXx/f8EMHfqNdMxU8=
			</data>
		</dict>
		<key>Settings.bundle/Root.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			zi1Y0A+tVNNfppRDjT3LpbeC+xDRJTirIvMQJ0HkgMQ=
			</data>
		</dict>
		<key>System.AppContext.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			q0lY+lPlnWol3MYK1EHimmVEf5VEaC2nEBrf7vv5vH8=
			</data>
		</dict>
		<key>System.Buffers.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			k+y16VtG0Cfb4NGzcFjreXCmRSqDNxNoyW/f8JH/xQg=
			</data>
		</dict>
		<key>System.ClientModel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			2ya6Vao1CDqf9T8K5EQB5l1RTCyYN/1G9injKSz/93w=
			</data>
		</dict>
		<key>System.Collections.Concurrent.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			qqW/l5RWFdqjkxuYxh6YIh+HKMtlhRIE79yk3waTj4E=
			</data>
		</dict>
		<key>System.Collections.Immutable.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FhF1KCDFyRusmFdrWymu5bjq6yI2TOQmZgj3is/wJ/g=
			</data>
		</dict>
		<key>System.Collections.NonGeneric.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			7LYAVewPrDmq+D7soBl71KzyCnOuJwGYK+8qUdSQcNU=
			</data>
		</dict>
		<key>System.Collections.Specialized.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			0CS8uDfp4kpuT//f8NNlmP3UEL2AuSuKb2xRd8PO6SI=
			</data>
		</dict>
		<key>System.Collections.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sgofo2Cih8itmnKpe57t8fsHlmsQAz7EelOU7l/MtME=
			</data>
		</dict>
		<key>System.ComponentModel.Annotations.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eyE2YuTpxXj+VAc0bmkNZVDZo1THylO9wgNIotvSpPM=
			</data>
		</dict>
		<key>System.ComponentModel.DataAnnotations.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Em2ci6dNrLhTRUeXcb8pa8Q+imEk/wpQw5+jpRjHWxE=
			</data>
		</dict>
		<key>System.ComponentModel.EventBasedAsync.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HGN4GOY8nJSUQlpHVwslgM1D+GkxRP1gwywd2ydm9Bw=
			</data>
		</dict>
		<key>System.ComponentModel.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			79tuOJ+1FYwkg96iplPOx9zNPDtjE3CiOkz/MZhi65c=
			</data>
		</dict>
		<key>System.ComponentModel.TypeConverter.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			bgTjX8quKshY8w3jRh28PB4qGBFCTNbgXeBq4wJTHVQ=
			</data>
		</dict>
		<key>System.ComponentModel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			9tLV47S1R+CWbzv8YWJd1aRP2mpuYCQeDWlaZVEFjmA=
			</data>
		</dict>
		<key>System.Configuration.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZaKj27SYWCkIUxAYCOixEZJ+Qy7AjmQ57BH7+B9FXeU=
			</data>
		</dict>
		<key>System.Console.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6A7u0mYUlyQypOW8N5ZCOazK68CTuHvqudvXI4jHSvs=
			</data>
		</dict>
		<key>System.Core.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			J00EQOtYWSqgPkvDtxCLEinlXksPGGmaPTgMjjbZFkU=
			</data>
		</dict>
		<key>System.Data.Common.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/m2Z4pb8GlpdZjReOQPqkzxGzFqB9IAtIhUeCr+d4C4=
			</data>
		</dict>
		<key>System.Data.DataSetExtensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			R4x90y+haB99UEq5f20uDMiRkU7xEZ17GxZRXT6P4tA=
			</data>
		</dict>
		<key>System.Data.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			1TqPl7lHGDPG4wcN94LEuS5BysljVpmRi5PGOt6k1U4=
			</data>
		</dict>
		<key>System.Diagnostics.Contracts.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kVjTbcttJaxCHiSLnxAuej1XQUo6lRNW50MFGTYTnm8=
			</data>
		</dict>
		<key>System.Diagnostics.Debug.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			KELczyQKQ4nd4ujMGDu69HIdJ8qFi67shB+tUa7PWUg=
			</data>
		</dict>
		<key>System.Diagnostics.DiagnosticSource.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lv5lM1uUp7IWsVyRmSJ7VCe/QJutyUQejwix3NwLLyk=
			</data>
		</dict>
		<key>System.Diagnostics.FileVersionInfo.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			D30si1ZeLe5OzTCAdHABn6lj49Ydn5OEoeBijuBfmXE=
			</data>
		</dict>
		<key>System.Diagnostics.Process.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sX3kFBmpW1Zj1iW4w338iW11pKGLe1gZ5aQvK2o93Vw=
			</data>
		</dict>
		<key>System.Diagnostics.StackTrace.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			D0QL6+wm7HxxErYQltUepgmFaN4PqMHgSQ4/q+T4QOo=
			</data>
		</dict>
		<key>System.Diagnostics.TextWriterTraceListener.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			J6nvheKztYWCy4d7TWasye+iOl4v719tugRN9wxLnGk=
			</data>
		</dict>
		<key>System.Diagnostics.Tools.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			s4FxyHzMMSUwBZQxthBJ6yZ0E24+YvaWsDZkKzzR4XY=
			</data>
		</dict>
		<key>System.Diagnostics.TraceSource.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			da6P0urNbb8Nry9aLS1nstpy/8PLquf8H9zgsSiwsYU=
			</data>
		</dict>
		<key>System.Diagnostics.Tracing.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			j9Q1BXCNg+qLbkvycER5Z40XISgzf2N1qTXW9yhLU0s=
			</data>
		</dict>
		<key>System.Drawing.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/2/RqhMiQIa/CEZfWs8wcpZjSZu163TMterUQWQspc4=
			</data>
		</dict>
		<key>System.Drawing.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			9DQbrVBxa8ZwkJe4PMwaMehYRN/3mCh60T8lSUNP590=
			</data>
		</dict>
		<key>System.Dynamic.Runtime.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6YiqP1jmSj+q1di8A5A29mHQrEudJs5YNjXhTbFg8Xs=
			</data>
		</dict>
		<key>System.Formats.Asn1.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			yHEJsANaas12t/CbNoqhfGoXwR0Lid1G6Lx3LSb81E4=
			</data>
		</dict>
		<key>System.Formats.Tar.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			y8tKwJUIIpwnmvw1yoIsDMyOKtTeJthaciQPpE1T6Yk=
			</data>
		</dict>
		<key>System.Globalization.Calendars.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Oa4wV27NqInZf95IyUgySI2VBxDdy54YWTV8BsRNwQw=
			</data>
		</dict>
		<key>System.Globalization.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			B4R1J9dTvSwquuF66tDhwPLt5IEc1AjKCT885rssQt4=
			</data>
		</dict>
		<key>System.Globalization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HyoAZd36hPrpQIw3h0N1BG92MKAbboDzH0G33hwbeSk=
			</data>
		</dict>
		<key>System.IO.Compression.Brotli.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			nzAGYKGEXKIdFDcxEYqcrRBo+adZeASUZ5dbKtzyL2Q=
			</data>
		</dict>
		<key>System.IO.Compression.FileSystem.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			SmFcICFfGcGPr1xWIS1l8WBRXK3ZOGLMktmrSHzWTlo=
			</data>
		</dict>
		<key>System.IO.Compression.ZipFile.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			38JVN5s1wcmt/uo7QMyn7Bjrmgc6j/pwpU75xxudNb8=
			</data>
		</dict>
		<key>System.IO.Compression.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			SyFWt+0xpY09THpDLO5P0DB2WNERYZf5H0Bpl1oKIlo=
			</data>
		</dict>
		<key>System.IO.FileSystem.AccessControl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			aWhY5lhIZy5HL9rmaitx4hEnVynEac+NIHOXWWLwvK8=
			</data>
		</dict>
		<key>System.IO.FileSystem.DriveInfo.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			7rL57oq77jcxT4/R7nqsB3YIqDNWElmuXGPOBKhEvTw=
			</data>
		</dict>
		<key>System.IO.FileSystem.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lIRYwia0PmrLAG5gfZ8h/JNPXmjAdHP78AhdPzZ3xQY=
			</data>
		</dict>
		<key>System.IO.FileSystem.Watcher.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kYIoRVPL6ZJXG5rPYoKVKmlYmH68lQ8Wcs5PeX4GH58=
			</data>
		</dict>
		<key>System.IO.FileSystem.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			qqvUveqNmo9ps8F8PTpaWtEd6iMDegS6vkM8VboMyLI=
			</data>
		</dict>
		<key>System.IO.IsolatedStorage.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RyKih37YOG90YMoPlYWeb/RVZszT3G2MuIhPUJWdlOk=
			</data>
		</dict>
		<key>System.IO.MemoryMappedFiles.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BEJ1b5NnbPDwB/iMZBJW/pwIN3qQ0TFv71g4kodimxs=
			</data>
		</dict>
		<key>System.IO.Pipelines.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lka8Mr74EyAAY0LYPAOPUPEocxadlYFCB2a704QynIw=
			</data>
		</dict>
		<key>System.IO.Pipes.AccessControl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kdGRhGjuB5qv/qvCpSfPusyzjELoyhE9sMSPy/JmjVA=
			</data>
		</dict>
		<key>System.IO.Pipes.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			NSZbJJieSg7NLnYjeq0RoCfyNPh6h+JDqlLKHQQ1Z5Q=
			</data>
		</dict>
		<key>System.IO.UnmanagedMemoryStream.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			L58AXCLEGz+5D5j25vdwSIEdNsl6DBmDiqFvWq5F9Pg=
			</data>
		</dict>
		<key>System.IO.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ivMXN73vZwoJQ+9W4vwH/N3xD4pvzfy0VINjD6obPBc=
			</data>
		</dict>
		<key>System.Linq.Expressions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			UN1VzstNB7f3WQnDps4PedtPEXUd1uXJKYRNOoJxBv4=
			</data>
		</dict>
		<key>System.Linq.Parallel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			uUEEqaiMtZ5IdYaPdN0JVVkl9MZeUGcBVyRKl1XvEbs=
			</data>
		</dict>
		<key>System.Linq.Queryable.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			V8g+wQsJQ7XYyNhy5ri/get4gu6RnBV+V3wv8vuLbKQ=
			</data>
		</dict>
		<key>System.Linq.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			tYtp4guMBpwC5KhJkWJ/l1GJG9Ip1Fr45e7vUGt37vo=
			</data>
		</dict>
		<key>System.Memory.Data.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			iBfRamKXEMeLN1fyILdIeCg8Vv59moZuzg6u8L4KK/E=
			</data>
		</dict>
		<key>System.Memory.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZuP+5z/83cFX9cF+NEM9HkeQRkn65atqqSaKmo3Q5TM=
			</data>
		</dict>
		<key>System.Net.Http.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xoi1TqdW7S20tXr2p2qFjBJRT4q72K4kB7RXPGfA4Rk=
			</data>
		</dict>
		<key>System.Net.Http.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			hresGktD6nqCBTJjwFJPe4UBhghlD3YsxIQfjuPee6M=
			</data>
		</dict>
		<key>System.Net.HttpListener.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rqzjr/TKA/ocWSThB5rbg3VfqIH0pPCSaHawu+bB9MY=
			</data>
		</dict>
		<key>System.Net.Mail.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			F1L+Fj4q4bnBpbm8E0BtOSjqemuZ7H/6A2219AWGkdI=
			</data>
		</dict>
		<key>System.Net.NameResolution.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RpWMkp5/cuyes65rqAjfdCSHQr/aNElBVa39BqAK7ws=
			</data>
		</dict>
		<key>System.Net.NetworkInformation.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RIOltrIQPpsVzLu6+Z2KCvpW8FEFzIJAFdZTn/bKG5Y=
			</data>
		</dict>
		<key>System.Net.Ping.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			9AL/YzGnUJMQKzUUFIzvxb9Ko0ZNdR8jqXoTJ0kPXpk=
			</data>
		</dict>
		<key>System.Net.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			yvSVdU1lU9bBsiRxN5wKWAzZnsVUdZ21XjLjU5aYLwQ=
			</data>
		</dict>
		<key>System.Net.Quic.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			O5t3CIpkVP9eGW1rfFI+foVqxPr2ieuM6vg2kFBzua0=
			</data>
		</dict>
		<key>System.Net.Requests.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			TNuUFzBWsw2Ey0yxsxkcrG5nieb8EeOf40eVrAiAr0E=
			</data>
		</dict>
		<key>System.Net.Security.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+Ycwsw77uwdD5kIRiZzPSgQLnzYLh9dcfsqgbYLnp3k=
			</data>
		</dict>
		<key>System.Net.ServicePoint.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			D8mEB/+ahgrERwNGzH6jWw+tr/j+TmFv7A3lZVfbLM4=
			</data>
		</dict>
		<key>System.Net.Sockets.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			myd6WJ4SFJP40eRgcqHkjHEM/ygrMITPExFBvNCw5U0=
			</data>
		</dict>
		<key>System.Net.WebClient.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rPwU23X1EfngFPwkEKzaiQIFyQzsp2xkgBongoLBikg=
			</data>
		</dict>
		<key>System.Net.WebHeaderCollection.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			z1bOh/N+gPIwMPEpqmcHgE8DahVR4Fy44s7mmXZcPFY=
			</data>
		</dict>
		<key>System.Net.WebProxy.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Pn11hjiVFLqfVG8UE+dPCKSa9oO3zq0f84BlZwNTDyI=
			</data>
		</dict>
		<key>System.Net.WebSockets.Client.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			0i+ioh7cOaUdRqFXq16PuwRkDPP4bgqnh7UTXN9Fbgo=
			</data>
		</dict>
		<key>System.Net.WebSockets.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Nh+KhdXJCEActog07iTU9rbFufJq6QnIORW0C2QxyO0=
			</data>
		</dict>
		<key>System.Net.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			JL+7DumiDqLxeNJr3W1pqKKTFJZQbH3klbLYg8wOldI=
			</data>
		</dict>
		<key>System.Numerics.Vectors.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vgCzZVEFnU593/ChADyqpCFH8HkqbHHjwk2onGrDV1M=
			</data>
		</dict>
		<key>System.Numerics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			pYzmpUedauw4jvf+dE7Hy+5fxBaMWYobKiVTOl545Gk=
			</data>
		</dict>
		<key>System.ObjectModel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PGkfd0gaKLC3/w2kiBZjiYTH41+Rest8gdoediYAx5M=
			</data>
		</dict>
		<key>System.Private.CoreLib.aotdata.arm64</key>
		<dict>
			<key>hash2</key>
			<data>
			4S5xsn37wPpurJ1nPEO1IFczmtzN9pAplgKGUEINh9Y=
			</data>
		</dict>
		<key>System.Private.CoreLib.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			RyTWkTCoiyedYul6/ukJa+NfsChjS+ewsmpAjxZNiTo=
			</data>
		</dict>
		<key>System.Private.DataContractSerialization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			PpNfg8CFcqPb9C+alISWPinIzSbZUa21y1OMZxHi7JY=
			</data>
		</dict>
		<key>System.Private.Uri.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Tq46WwBqh16SJB0znv+OGpKxYyN1rXje116HS9zlTBw=
			</data>
		</dict>
		<key>System.Private.Xml.Linq.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8tbfE110kgzP6Xp9UhrLhJtozyqoEyBKLAwSbTTsfRw=
			</data>
		</dict>
		<key>System.Private.Xml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			q4IKjmQZvltPf5v3IGLx5ljJdYY1DtLkHn4woZuCBUs=
			</data>
		</dict>
		<key>System.Reflection.DispatchProxy.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QGBOIyUjt3avVvyFGbSRrBqIBa9BRH2rf1ELG0iX+1E=
			</data>
		</dict>
		<key>System.Reflection.Emit.ILGeneration.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8pVzgvBVdZxZAJyzmFBQaCKJrUdZAE1Bt2D6gKGGHq8=
			</data>
		</dict>
		<key>System.Reflection.Emit.Lightweight.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kwTKaQ100VDlqqpQcfcIkPToxUodXRbM3A98xKZPw2w=
			</data>
		</dict>
		<key>System.Reflection.Emit.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eK9R2/kZfJfCm6xN4sCv5vgNsfXVM7NIj9XQar4K2eE=
			</data>
		</dict>
		<key>System.Reflection.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Rii1NEKMnX6i3prKdYKaDJsm4ifDwVXNVGu10B5frlY=
			</data>
		</dict>
		<key>System.Reflection.Metadata.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			sA3ld7awvhA/O0p5lcViJauRtIgYzyYzaNX+KfEGyYw=
			</data>
		</dict>
		<key>System.Reflection.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HcgRLtM5S01xF1msXnhZfaOmg46ONBsV2l1loHfs/kk=
			</data>
		</dict>
		<key>System.Reflection.TypeExtensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zhpSTxBdqmwh94RLNKpM4N8/iX7OrXnjdh+FS9QJ1Vs=
			</data>
		</dict>
		<key>System.Reflection.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+Ng5NszXuGjNaFodRAH+G9xk8lFV4inDHpKeONIND9o=
			</data>
		</dict>
		<key>System.Resources.Reader.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			dAxOuFMytyU2VHC4UzK75J11ecr2UIKxRKitCL43M+s=
			</data>
		</dict>
		<key>System.Resources.ResourceManager.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ryByfxuovQIQTKXVmej5ecziMk0DIdRDGDizNgMrsNY=
			</data>
		</dict>
		<key>System.Resources.Writer.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			LAL7hLQ3oznOCMWQgRAFhgjrmW2iDQE5iGN1wgj2r/Q=
			</data>
		</dict>
		<key>System.Runtime.CompilerServices.Unsafe.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BmnJGWyubpuuPLkpt2rJGUHWy6wSvvZK1A3COckWJ7w=
			</data>
		</dict>
		<key>System.Runtime.CompilerServices.VisualC.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			O6GQOgYGLe2Ia0RUBD1EHxJRzNOTEphvpfflDg2bWJw=
			</data>
		</dict>
		<key>System.Runtime.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			yHzW3CU+JSi3yLlLAVIJEZ55Ae6Rm+MxOMk4LcmjT1Q=
			</data>
		</dict>
		<key>System.Runtime.Handles.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kohfrhw4lAcPVKTQhuRzkJUJ33EaKNyNpBo1h2DM2zY=
			</data>
		</dict>
		<key>System.Runtime.InteropServices.JavaScript.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			4MZ9pUS1V9gxUOKMIX5RIiQrP/Fr1giMKmhk0IvUqL8=
			</data>
		</dict>
		<key>System.Runtime.InteropServices.RuntimeInformation.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			tZsyJlMKh4FMtviR1fxk4btOWA2hyvPEvvCIxekTnu0=
			</data>
		</dict>
		<key>System.Runtime.InteropServices.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xs1LX3bt5WrTcXW/b1z4B80SgxfiCaNlM9PU+yaFQoU=
			</data>
		</dict>
		<key>System.Runtime.Intrinsics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			EmREIkK9eR2JXuYU5FpWJN5JYnnAoi/62BcFpp/Ip6A=
			</data>
		</dict>
		<key>System.Runtime.Loader.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			DLKM6R4ueOsMFYfXY1V+LnCZlpjdniw40Y7a3pwhmr8=
			</data>
		</dict>
		<key>System.Runtime.Numerics.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QC7CL6KRH5q6IR/vsvWH7FGNjTVERpcVUlNQ0uaeLBk=
			</data>
		</dict>
		<key>System.Runtime.Serialization.Formatters.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+Gj10cuJGDhYr/imm5r6Uyhfbmk/WWSRClGlKu6O2Ec=
			</data>
		</dict>
		<key>System.Runtime.Serialization.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vAUckr3yFtilSkUiaLJEDBbL/kDisytHW+4NlsQx/c0=
			</data>
		</dict>
		<key>System.Runtime.Serialization.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Oo5LZ0UGmfcItgRyKBaOhEu3OcjSF7/Btx9qBqXjl3k=
			</data>
		</dict>
		<key>System.Runtime.Serialization.Xml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lBca0sD2qnfDrbjf0Sa8UFa3m8xIWVoaEC4HtdnheaU=
			</data>
		</dict>
		<key>System.Runtime.Serialization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			U/d+bB45SYzGjPUwg7ethGWdu9ldtZdMpZloOgzN5j4=
			</data>
		</dict>
		<key>System.Runtime.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ztszS2ha+wjKewtTYJX5aPnZ3JQ4FtIJgzR8PXXGABA=
			</data>
		</dict>
		<key>System.Security.AccessControl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			aBs8/aHLlgbO7CmZcjdgP1Yu45PXdtSqlObzNxmb2NM=
			</data>
		</dict>
		<key>System.Security.Claims.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			dKw5ZSJwh8iremVe2N/IA+/URs60Eaz0iiZ5qgFUULI=
			</data>
		</dict>
		<key>System.Security.Cryptography.Algorithms.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			lsU+nMsjpwjqfguxWgZlQfpXnO4J1cVxT4ZpJfCj4aY=
			</data>
		</dict>
		<key>System.Security.Cryptography.Cng.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			NZnI4tqnLrZSTrUdHWoKlY9pciYpZoR8lGRzYwjSA6Q=
			</data>
		</dict>
		<key>System.Security.Cryptography.Csp.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			NMEwN8fkGtKcl9vOSBO41ltPqtgcSJp23Sq062MhT1Q=
			</data>
		</dict>
		<key>System.Security.Cryptography.Encoding.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			TT6dyqlwaqAFoEq/oRh0CDQVRJc9OM0PP9WAkTmI6fU=
			</data>
		</dict>
		<key>System.Security.Cryptography.OpenSsl.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			wcf+iXTJwHZoh5Ewg0YPpZh6a11Uy+SqIhYRHCUu3sA=
			</data>
		</dict>
		<key>System.Security.Cryptography.Primitives.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			nBSfWWaUxKBxC0XIe7XeuIfaYU6BxDTiI8Xeor7MdA0=
			</data>
		</dict>
		<key>System.Security.Cryptography.X509Certificates.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Kfz/qw2A3JsELLui/5Uw2vvXDGovmcdQ2EwbClbBEE0=
			</data>
		</dict>
		<key>System.Security.Cryptography.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eS/Zp82mnDj8keNhiFPfgS7Hf3efkZAKHm8OrM862Hc=
			</data>
		</dict>
		<key>System.Security.Principal.Windows.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Z00jiu5VBEO3jO7jbkchgj4pt6lKA3UbVu6+ONknl00=
			</data>
		</dict>
		<key>System.Security.Principal.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FS4PdPwxUAFwbK3jXoqXfx/lceOd7ykOiu57rNSFL0k=
			</data>
		</dict>
		<key>System.Security.SecureString.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			g4GqBU252Z/HfFNCTrG+3f7rpYGpDWVE85GKj+GkZoU=
			</data>
		</dict>
		<key>System.Security.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			eJe6h3DnVeo5vqJv/lTwEBqiSNqkeSPJkk0o8O2R5Ug=
			</data>
		</dict>
		<key>System.ServiceModel.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			KZy/ojKC2PchxfDCyt7XEs27hZ75uIW5dhAqMu89cp4=
			</data>
		</dict>
		<key>System.ServiceProcess.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			IbSUik1gUL83Exkx452j2pRST9Bs5PwRjaS+N9G2mQI=
			</data>
		</dict>
		<key>System.Text.Encoding.CodePages.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			xobrOS0bh93LasfAgmTSb5M7xhOxJfUGtDKtW/GP8bg=
			</data>
		</dict>
		<key>System.Text.Encoding.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			JMzpXvnl/aBW0EwleORLT1SiDWS62CcbMHVL32j8JaA=
			</data>
		</dict>
		<key>System.Text.Encoding.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			F8e01w4nZEp5m1245dGhGVozzkTil89lisVyiY0xmcg=
			</data>
		</dict>
		<key>System.Text.Encodings.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			7UisCEAzWDT9OvRgao4pna7GRHNNCIhfqUcPVSQ8RV0=
			</data>
		</dict>
		<key>System.Text.Json.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			VGDr0qK9TXArAwCvzJDsRfJROLS+vSmJIREnKkFEnec=
			</data>
		</dict>
		<key>System.Text.RegularExpressions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			G6h1wE6kSZ3o8w0Qmm3Jb1MvE+K2DAMkpmxf4rciEzM=
			</data>
		</dict>
		<key>System.Threading.Channels.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			/ittOl3Ietawzxc0G28L2cI6aZdEro/G8m2n0d7QsjQ=
			</data>
		</dict>
		<key>System.Threading.Overlapped.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			MFx0F0aI6WV7Y58AIBPpBpPhnXBY2/Z87qDDecFtTYM=
			</data>
		</dict>
		<key>System.Threading.Tasks.Dataflow.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			P+cXW912+7tcqfjeiwS5p4GvYgtRftxfN2N39JT0Gpo=
			</data>
		</dict>
		<key>System.Threading.Tasks.Extensions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ycRdxMTe3Qiee/thDZXcp5WtUMYmvAK8QGgoxZbcEC0=
			</data>
		</dict>
		<key>System.Threading.Tasks.Parallel.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			TZI7+4yYd3xLK8G4J/WmBXicvXmIhVF+bF5FzKdW27Y=
			</data>
		</dict>
		<key>System.Threading.Tasks.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			1P+NOYn3SpuZaYLVxa4ENiGbJDbA5jBEEc5JjGi+xVA=
			</data>
		</dict>
		<key>System.Threading.Thread.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kIgTAIq9RqwiB93ugSHe1NnxYVm6hSW2RCHgmCq3Bzk=
			</data>
		</dict>
		<key>System.Threading.ThreadPool.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			SJ7W346Z39HL2tjc6kuweXlQGU/9giPEf1v2vVl91Q4=
			</data>
		</dict>
		<key>System.Threading.Timer.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zzSKOcBUpVeiEP8Y1NU/jXDk1OUU6TvuyWCb8Rmqwug=
			</data>
		</dict>
		<key>System.Threading.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zNJ/xrIzKnowys+NoQ1vLYCuRVriMBjR4/jGQxLlyLM=
			</data>
		</dict>
		<key>System.Transactions.Local.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			egnsgFQ8LY7U1qKUjREXOETBZqkZKX4owYZLJIheyUo=
			</data>
		</dict>
		<key>System.Transactions.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			gKHbEBm6Pc33cevjPmwbWoWzpd8Pf76Npv+UWFqL0R8=
			</data>
		</dict>
		<key>System.ValueTuple.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			HyBloEHpozRWLay8gESaPv9PgeGIiGvWS3BsHqN+SLk=
			</data>
		</dict>
		<key>System.Web.HttpUtility.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			KBriTqOEsajUjqzq42OCMyFWexFqN/Z7n+IX3k11P1U=
			</data>
		</dict>
		<key>System.Web.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Di6EGSZ7sBLRLc0S9OcBw3vuClVC4q3LKp2gFiWwlVE=
			</data>
		</dict>
		<key>System.Windows.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			WkZEfaW6cQ2GeNmtFUDl/vDHSxQ/mlzu86Q3Nl+Dwh8=
			</data>
		</dict>
		<key>System.Xml.Linq.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			p254kZsV4zYif/PAgpJatDDxdCx8Qi22182QnJVItLI=
			</data>
		</dict>
		<key>System.Xml.ReaderWriter.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			jQiy6jQYYdcXnJ60u99y8LpgSQWamL+4y/K5rlJS+ME=
			</data>
		</dict>
		<key>System.Xml.Serialization.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			FP8QwITQCjZA22vWEaNbGn/qtIZwB9AVt7MM5egNJLw=
			</data>
		</dict>
		<key>System.Xml.XDocument.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			VgSYNbhrR/tnRSbedeegFpLrSfJor42CfWF3EHE3Pcg=
			</data>
		</dict>
		<key>System.Xml.XPath.XDocument.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			kEc2/RCvIWf63y/jILR7uxyJgZTUPb1q8mpnYde3g7k=
			</data>
		</dict>
		<key>System.Xml.XPath.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BT5Dx1tuGF1Hdjppbmlf2H+Mp3fdiaWpi+YYgtrRCYM=
			</data>
		</dict>
		<key>System.Xml.XmlDocument.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			YSHdPTR6v/jbWjUi9YWjAaDBBJgLAl4djA91fqYwxcQ=
			</data>
		</dict>
		<key>System.Xml.XmlSerializer.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			DQNWf5ZhsnxZoyEdyx9CTT6XoIMExhQI0twNgqqT5h4=
			</data>
		</dict>
		<key>System.Xml.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			DX7ifhLG5pJqWn83FzBJ86zcy4GV5wF46mEFteMWO5M=
			</data>
		</dict>
		<key>System.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			rugYxjUNYOmAYhtptJVEVhe30DFQW6klHl0U6gRN6o8=
			</data>
		</dict>
		<key>WindowsBase.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Vz24JN+UBJDq5b9pYpOFUgEdFkNQlaIxTBnKSZCIBds=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			N7u25MUVZUgf1Di9RatfPTOcKL1xsMc3fpcd2vGhUZw=
			</data>
		</dict>
		<key>appicon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			5qE+AQJr7f3tqx/efy8uO8+/pwoOGk86jZOuINvXklk=
			</data>
		</dict>
		<key>ar/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			tR504UMIG99RTESyAcPwt1IRkSOw+rfFeHXLSij43G4=
			</data>
		</dict>
		<key>archived-expanded-entitlements.xcent</key>
		<dict>
			<key>hash2</key>
			<data>
			wKp5Derw7xd9j6X8kiQ4GWHij9eyOaYhC6ZQdPLg6NE=
			</data>
		</dict>
		<key>ca/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			6nVpZ0FA0OilZC99TiTtv2cTV0kMhDXWQ4yoOwQAMTY=
			</data>
		</dict>
		<key>cs/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ITAUvs6dNoWxLuyEyE017xy21kSEmh2c2votQAHU3Ts=
			</data>
		</dict>
		<key>da/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			pkCtdDiPLmc7FPZZgrNUOKi2kQh8QPYYeab75OxogV0=
			</data>
		</dict>
		<key>de/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8dywUuthY5fOrlmCvbk5orIY4O+l809pdJf6N2w3+Pc=
			</data>
		</dict>
		<key>dotnet_bot.png</key>
		<dict>
			<key>hash2</key>
			<data>
			p7zGimT/tpOpt6JMjvqTXeLvr7gO+6owAxCI06IQS70=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			WxfuPXP2HzlHzcT6HPpxjHyIjhUetCXyRrIRo7hyF2Q=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			a/Og/prhaP+ksiVrPdm6zyz46YfBdrD0oFdkAveKI18=
			</data>
		</dict>
		<key>el/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			0L7HyYRauPGQ+4IlKmQfR1bAfCQeEoBAQXJP35xNhVU=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			SwgwO+XNesnSqbSqYihZKELsykPv1uP6Pg3vGLjI4Xs=
			</data>
		</dict>
		<key>es/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			1M8Pj9yRx9VJNgI1Mq2MAS32EymNhB2PxJA4zA550VU=
			</data>
		</dict>
		<key>fi/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Bon3igwFyMUa3kSqyL8x8gKq7F+BH04l9hs8b2VWTUk=
			</data>
		</dict>
		<key>fr/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ayFS3xV8EKjdnEktUisv7uHsWhVAA+UAuQQzftUUKlk=
			</data>
		</dict>
		<key>he/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ochP6NfAXNtFeGPD7wX1LxhTJ3D4Yn9ohW88aIAvrrc=
			</data>
		</dict>
		<key>hi/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			krpsWfhjIZylmpHCW94mSXr4EcXVCJHSY6Kd7xMStU8=
			</data>
		</dict>
		<key>hr/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			V5MFLcIewvBIyI94raJV3zwzQo8nXeihagYihnZlXyQ=
			</data>
		</dict>
		<key>hu/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			x3vAh8NDvlUHE8eLvPNZshqSfxmULZWbSQfzrcDGCOw=
			</data>
		</dict>
		<key>id/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			QijIivyTzNUQYGj/3bQE8mcL/RqhLCaNlXWZJxA8/j0=
			</data>
		</dict>
		<key>it/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			uohjYZ97BY162aYUjjNWpa9Tz8fhxhpSM/DoZrBnC5w=
			</data>
		</dict>
		<key>ja/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Op6xIBRLW6Du9L+N4A8cD18uw5G2Aw6YdwFBpqbz5Po=
			</data>
		</dict>
		<key>ko/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ZvleEITedtQwkvIoTRms82kNadDD7WZzSGeZ6lgh2m0=
			</data>
		</dict>
		<key>ms/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			wzaNjb1tEA7pF+NFmPZJ+C2hKRbBdW4clYz8koY6QP0=
			</data>
		</dict>
		<key>mscorlib.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			oRCOKKpjknHtMucRThc5OhTiQawzVT6sGRFCppcuRTQ=
			</data>
		</dict>
		<key>nb/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			qmjgPA8M0izn2RgDZWPOMpLf+aJOKtf1ZivIiW0V9pU=
			</data>
		</dict>
		<key>netstandard.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			mZe44YIcSxENRgr4OfKvO/lWBsICNGyX4SwysyYUMQg=
			</data>
		</dict>
		<key>nl/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			S3i3HF7wQ3KT0qJ+pXom41vRLLQnp5wZVaaQQbJCU4o=
			</data>
		</dict>
		<key>pl/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			5d6rgE1xwEokFK4ODDHhSS/VjeeI8udZAMPRuUGL36s=
			</data>
		</dict>
		<key>pt-BR/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			+1qbb+YeHcazlOeRI9OH/6kOKMoKEg8VCu4JXU47ULw=
			</data>
		</dict>
		<key>pt/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			aiWnh41leuF2ai5MSWruhOMK2M4Cz8T2UuKCuEs5qM8=
			</data>
		</dict>
		<key>ro/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			urS/n/eoEiyhFrMMepGqOm/BoOH0xsDHbrPhYPy/Wdo=
			</data>
		</dict>
		<key>ru/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			p6lRNo3YsYIlBgc2WzExvw+VoDyMwEpIHTyf35+zYdQ=
			</data>
		</dict>
		<key>runtimeconfig.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			tbhkCzzpdp5dNG4HqO3iv71VJC5HeGsi+uJxzuIzmOk=
			</data>
		</dict>
		<key>sk/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			8ERmnS5BjxHox4B+AqYcR8k2sSNI3RR4ipYbEF1DYu0=
			</data>
		</dict>
		<key>splash_7CF9AE42DA2017EC6621B726A870D40F01970097ED8F3D99ABBC1EC8470F119D.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Dpf6ieSaphEudmfGWM4o56pf3pK2gzcp9vYU5dZF9S0=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			YMCf+Kl7v6nI7SmNKxsD3oxz8b7W8NbOK9pl/268poY=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			TR5wkKzih6lYQ3pqkvy+F+ZIOuoGZ1uEfeG93l0BSfE=
			</data>
		</dict>
		<key>sv/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			2OJGMWA5NxQPhkVJ0lLt9QRqGhepbmQfM+UDM9ybWeE=
			</data>
		</dict>
		<key>th/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			ISo9cL2MCZiJPBgJy0MkRUREmTidqASo3t97g6vQndo=
			</data>
		</dict>
		<key>tr/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			aF2i2xPdWO2Ju9C9xItG5aZJ6L8WQx2wlcdj9h9eAp4=
			</data>
		</dict>
		<key>uk/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			vKqJMjEbDTeN86ZtAGE7jc1NrjSxn1GQC75RYkASdjU=
			</data>
		</dict>
		<key>vi/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			Hw2JvnOpsBvjsQn/JuesUE5BjeQ1dbCUIlUdL7j/ZvU=
			</data>
		</dict>
		<key>zh-HK/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			1iZ567EQPbovMgJIaIaCFJIRNHw6owRnsDAxNJGuGig=
			</data>
		</dict>
		<key>zh-Hans/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			BxTA9ycIqI2LUQzmCFLNViawVdhAUonKNB9/cL/fUCE=
			</data>
		</dict>
		<key>zh-Hant/Microsoft.Maui.Controls.resources.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			zzRs21J4fF4sdGSlUx8qFQULq/hN0seSAC063OmLzvw=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
