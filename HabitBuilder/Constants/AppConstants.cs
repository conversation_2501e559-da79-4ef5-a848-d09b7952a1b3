namespace HabitBuilder.Constants;

public static class AppConstants
{
    // Subscription Tiers
    public static class SubscriptionTiers
    {
        public const string NoAccount = "NoAccount";
        public const string FreeAccount = "FreeAccount";
        public const string PaidAccount = "PaidAccount";
    }

    // Activity Limits by Tier
    public static class ActivityLimits
    {
        public const int NoAccountMaxActivities = 2;
        public const int FreeAccountMaxActivities = 5;
        public const int PaidAccountMaxActivities = 10;
        public const int MaxRecommendedActivities = 5;
        public const int AbsoluteMaxActivities = 20;
    }

    // Notification Limits by Tier
    public static class NotificationLimits
    {
        public const int NoAccountNotificationsPerDay = 1;
        public const int FreeAccountNotificationsPerDay = 2;
        public const int PaidAccountNotificationsPerDay = 2;
    }

    // Resolve Levels
    public static class ResolveLevels
    {
        public const string Easygoing = "Easygoing";
        public const string Resolved = "Resolved";
        public const string Clockwork = "Clockwork"; // Joke level - not selectable
    }

    // Activity Frequencies
    public static class ActivityFrequencies
    {
        public const string Daily = "Daily";
        public const string Weekly = "Weekly";
        public const string Fortnightly = "Fortnightly";
    }

    // Storage Keys
    public static class StorageKeys
    {
        public const string UserProfile = "user_profile";
        public const string Activities = "activities";
        public const string Settings = "settings";
        public const string LastSync = "last_sync";
        public const string IsFirstLaunch = "is_first_launch";
        public const string SelectedLanguage = "selected_language";
        public const string ResolveLevel = "resolve_level";
        public const string SubscriptionTier = "subscription_tier";
    }

    // Default Values
    public static class Defaults
    {
        public const string DefaultLanguage = "English";
        public const string DefaultResolveLevel = ResolveLevels.Easygoing;
        public const string DefaultSubscriptionTier = SubscriptionTiers.NoAccount;
        public const int DefaultNotificationHour = 9; // 9 AM
        public const int DefaultNotificationMinute = 0;
    }

    // Azure Configuration
    public static class Azure
    {
        public const string TableStorageConnectionString = "DefaultEndpointsProtocol=https;AccountName=habitbuilder;AccountKey=YOUR_KEY;EndpointSuffix=core.windows.net";
        public const string ActivitiesTableName = "Activities";
        public const string UsersTableName = "Users";
        public const string CompletionsTableName = "Completions";
    }

    // Authentication
    public static class Auth
    {
        public const string ClientId = "YOUR_AZURE_CLIENT_ID";
        public const string TenantId = "YOUR_AZURE_TENANT_ID";
        public const string RedirectUri = "msauth://com.companyname.habitbuilder";
        public static readonly string[] Scopes = { "https://graph.microsoft.com/User.Read" };
    }

    // UI Constants
    public static class UI
    {
        public const double ProgressBarHeight = 20;
        public const int WeeklyCells = 7;
        public const int FortnightlyCells = 14;
        public const int SwipeUndoTimeoutSeconds = 60;
        public const string PrimaryColor = "#512BD4";
        public const string SecondaryColor = "#DFD8F7";
        public const string SuccessColor = "#52C41A";
        public const string WarningColor = "#FAAD14";
        public const string ErrorColor = "#F5222D";
    }

    // Scoring Constants
    public static class Scoring
    {
        public const double EasygoingMultiplier = 1.2;
        public const double ResolvedMultiplier = 1.0;
        public const double ClockworkMultiplier = 0.8;
        public const int MaxScorePerActivity = 100;
        public const int WeeklyHistoryLimit = 4; // Free account limit
    }
}
