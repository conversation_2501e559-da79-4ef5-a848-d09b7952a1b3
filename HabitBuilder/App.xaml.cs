﻿using HabitBuilder.Services;
using HabitBuilder.Constants;

namespace HabitBuilder;

public partial class App : Application
{
	public App()
	{
		InitializeComponent();
	}

	protected override Window CreateWindow(IActivationState? activationState)
	{
		var shell = new AppShell();

		// Determine initial route based on authentication and onboarding status
		Task.Run(async () =>
		{
			try
			{
				var localStorageService = Handler?.MauiContext?.Services.GetService<ILocalStorageService>();
				var authService = Handler?.MauiContext?.Services.GetService<IAuthenticationService>();

				if (localStorageService != null && authService != null)
				{
					var isFirstLaunch = await localStorageService.GetAsync<bool?>(AppConstants.StorageKeys.IsFirstLaunch) ?? true;
					var isAuthenticated = await authService.IsAuthenticatedAsync();

					if (isAuthenticated && isFirstLaunch)
					{
						await shell.GoToAsync("//onboarding");
					}
					else if (isAuthenticated)
					{
						await shell.GoToAsync("//main");
					}
					else
					{
						await shell.GoToAsync("//login");
					}
				}
				else
				{
					await shell.GoToAsync("//login");
				}
			}
			catch
			{
				await shell.GoToAsync("//login");
			}
		});

		return new Window(shell);
	}
}