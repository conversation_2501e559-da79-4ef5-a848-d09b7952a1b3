<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:HabitBuilder"
             xmlns:helpers="clr-namespace:HabitBuilder.Helpers"
             x:Class="HabitBuilder.App">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <helpers:StringToBoolConverter x:Key="StringToBoolConverter" />
            <helpers:InvertedBoolConverter x:Key="InvertedBoolConverter" />
            <helpers:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
            <helpers:ScoreToProgressConverter x:Key="ScoreToProgressConverter" />
            <helpers:BoolToColorConverter x:Key="BoolToColorConverter" />
            <helpers:IntToBoolConverter x:Key="IntToBoolConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
