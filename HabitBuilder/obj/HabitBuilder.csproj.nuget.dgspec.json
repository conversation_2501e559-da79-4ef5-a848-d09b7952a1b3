{"format": 1, "restore": {"/Users/<USER>/Documents/src/habit-builder/HabitBuilder/HabitBuilder.csproj": {}}, "projects": {"/Users/<USER>/Documents/src/habit-builder/HabitBuilder/HabitBuilder.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/src/habit-builder/HabitBuilder/HabitBuilder.csproj", "projectName": "HabitBuilder", "projectPath": "/Users/<USER>/Documents/src/habit-builder/HabitBuilder/HabitBuilder.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0-android", "net9.0-ios", "net9.0-maccatalyst"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-android35.0": {"targetAlias": "net9.0-android", "projectReferences": {}}, "net9.0-ios18.5": {"targetAlias": "net9.0-ios", "projectReferences": {}}, "net9.0-maccatalyst18.5": {"targetAlias": "net9.0-maccatalyst", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0-android35.0": {"targetAlias": "net9.0-android", "dependencies": {"Azure.Data.Tables": {"target": "Package", "version": "[12.11.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.Authentication.WebAssembly.Msal": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.73.1, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.51, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.1, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.6, 9.0.6]"}], "frameworkReferences": {"Microsoft.Android": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.102/PortableRuntimeIdentifierGraph.json"}, "net9.0-ios18.5": {"targetAlias": "net9.0-ios", "dependencies": {"Azure.Data.Tables": {"target": "Package", "version": "[12.11.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.Authentication.WebAssembly.Msal": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.73.1, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.51, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.1, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.6, 9.0.6]"}], "frameworkReferences": {"Microsoft.iOS": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.102/PortableRuntimeIdentifierGraph.json"}, "net9.0-maccatalyst18.5": {"targetAlias": "net9.0-maccatalyst", "dependencies": {"Azure.Data.Tables": {"target": "Package", "version": "[12.11.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.Authentication.WebAssembly.Msal": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.73.1, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.51, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.1, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["xamarinios10", "net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.6, 9.0.6]"}], "frameworkReferences": {"Microsoft.MacCatalyst": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.102/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"ios-arm64": {"#import": []}}}}}