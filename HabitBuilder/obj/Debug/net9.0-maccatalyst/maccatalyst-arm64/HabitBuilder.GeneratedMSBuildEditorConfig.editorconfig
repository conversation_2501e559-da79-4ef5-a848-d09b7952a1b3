is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = 
build_property.EnableTrimAnalyzer = false
build_property.IncludeAllContentForSelfExtract = 
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property._MvvmToolkitIsUsingWindowsRuntimePack = false
build_property.CsWinRTComponent = 
build_property.CsWinRTAotOptimizerEnabled = 
build_property.CsWinRTAotWarningLevel = 
build_property.TargetFramework = net9.0-maccatalyst
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = false
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = HabitBuilder
build_property.ProjectDir = /Users/<USER>/Documents/src/habit-builder/HabitBuilder/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/App.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.App.xaml
build_metadata.AdditionalFiles.TargetPath = App.xaml
build_metadata.AdditionalFiles.RelativePath = App.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/AppShell.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.AppShell.xaml
build_metadata.AdditionalFiles.TargetPath = AppShell.xaml
build_metadata.AdditionalFiles.RelativePath = AppShell.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/MainPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = MainPage.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/Resources/Styles/Colors.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.Resources.Styles.Colors.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Colors.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Colors.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/Resources/Styles/Styles.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.Resources.Styles.Styles.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Styles.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Styles.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/Views/ActivityHistoryPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.Views.ActivityHistoryPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/ActivityHistoryPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/ActivityHistoryPage.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/Views/AddActivityPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.Views.AddActivityPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/AddActivityPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/AddActivityPage.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/Views/ArchivedActivitiesPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.Views.ArchivedActivitiesPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/ArchivedActivitiesPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/ArchivedActivitiesPage.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/Views/LoginPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.Views.LoginPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/LoginPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/LoginPage.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/Views/OnboardingPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.Views.OnboardingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/OnboardingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/OnboardingPage.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/Views/SettingsPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.Views.SettingsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/SettingsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/SettingsPage.xaml

[/Users/<USER>/Documents/src/habit-builder/HabitBuilder/Views/SignupPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = HabitBuilder.Views.SignupPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/SignupPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/SignupPage.xaml
