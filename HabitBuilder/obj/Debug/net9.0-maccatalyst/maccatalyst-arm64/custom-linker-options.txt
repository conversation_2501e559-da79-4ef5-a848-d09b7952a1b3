AreAnyAssembliesTrimmed=false
				AssemblyName=HabitBuilder.dll
				
				AOTCompiler=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.AOT.osx-arm64.Cross.maccatalyst-arm64/9.0.6/Sdk/../tools/mono-aot-cross
				AOTOutputDirectory=obj/Debug/net9.0-maccatalyst/maccatalyst-arm64/nativelibraries/aot-output/
				DedupAssembly=
				AppBundleManifestPath=bin/Debug/net9.0-maccatalyst/maccatalyst-arm64/HabitBuilder.app/Contents/Info.plist
				CacheDirectory=/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-maccatalyst/maccatalyst-arm64/linker-cache
				
				Debug=true
				DeploymentTarget=15.0
				
				EnableSGenConc=false
				
				FrameworkAssembly=Microsoft.CSharp
FrameworkAssembly=Microsoft.VisualBasic.Core
FrameworkAssembly=Microsoft.VisualBasic
FrameworkAssembly=Microsoft.Win32.Primitives
FrameworkAssembly=Microsoft.Win32.Registry
FrameworkAssembly=System.AppContext
FrameworkAssembly=System.Buffers
FrameworkAssembly=System.Collections.Concurrent
FrameworkAssembly=System.Collections.Immutable
FrameworkAssembly=System.Collections.NonGeneric
FrameworkAssembly=System.Collections.Specialized
FrameworkAssembly=System.Collections
FrameworkAssembly=System.ComponentModel.Annotations
FrameworkAssembly=System.ComponentModel.DataAnnotations
FrameworkAssembly=System.ComponentModel.EventBasedAsync
FrameworkAssembly=System.ComponentModel.Primitives
FrameworkAssembly=System.ComponentModel.TypeConverter
FrameworkAssembly=System.ComponentModel
FrameworkAssembly=System.Configuration
FrameworkAssembly=System.Console
FrameworkAssembly=System.Core
FrameworkAssembly=System.Data.Common
FrameworkAssembly=System.Data.DataSetExtensions
FrameworkAssembly=System.Data
FrameworkAssembly=System.Diagnostics.Contracts
FrameworkAssembly=System.Diagnostics.Debug
FrameworkAssembly=System.Diagnostics.DiagnosticSource
FrameworkAssembly=System.Diagnostics.FileVersionInfo
FrameworkAssembly=System.Diagnostics.Process
FrameworkAssembly=System.Diagnostics.StackTrace
FrameworkAssembly=System.Diagnostics.TextWriterTraceListener
FrameworkAssembly=System.Diagnostics.Tools
FrameworkAssembly=System.Diagnostics.TraceSource
FrameworkAssembly=System.Diagnostics.Tracing
FrameworkAssembly=System.Drawing.Primitives
FrameworkAssembly=System.Drawing
FrameworkAssembly=System.Dynamic.Runtime
FrameworkAssembly=System.Formats.Asn1
FrameworkAssembly=System.Formats.Tar
FrameworkAssembly=System.Globalization.Calendars
FrameworkAssembly=System.Globalization.Extensions
FrameworkAssembly=System.Globalization
FrameworkAssembly=System.IO.Compression.Brotli
FrameworkAssembly=System.IO.Compression.FileSystem
FrameworkAssembly=System.IO.Compression.ZipFile
FrameworkAssembly=System.IO.Compression
FrameworkAssembly=System.IO.FileSystem.AccessControl
FrameworkAssembly=System.IO.FileSystem.DriveInfo
FrameworkAssembly=System.IO.FileSystem.Primitives
FrameworkAssembly=System.IO.FileSystem.Watcher
FrameworkAssembly=System.IO.FileSystem
FrameworkAssembly=System.IO.IsolatedStorage
FrameworkAssembly=System.IO.MemoryMappedFiles
FrameworkAssembly=System.IO.Pipelines
FrameworkAssembly=System.IO.Pipes.AccessControl
FrameworkAssembly=System.IO.Pipes
FrameworkAssembly=System.IO.UnmanagedMemoryStream
FrameworkAssembly=System.IO
FrameworkAssembly=System.Linq.Expressions
FrameworkAssembly=System.Linq.Parallel
FrameworkAssembly=System.Linq.Queryable
FrameworkAssembly=System.Linq
FrameworkAssembly=System.Memory
FrameworkAssembly=System.Net.Http.Json
FrameworkAssembly=System.Net.Http
FrameworkAssembly=System.Net.HttpListener
FrameworkAssembly=System.Net.Mail
FrameworkAssembly=System.Net.NameResolution
FrameworkAssembly=System.Net.NetworkInformation
FrameworkAssembly=System.Net.Ping
FrameworkAssembly=System.Net.Primitives
FrameworkAssembly=System.Net.Quic
FrameworkAssembly=System.Net.Requests
FrameworkAssembly=System.Net.Security
FrameworkAssembly=System.Net.ServicePoint
FrameworkAssembly=System.Net.Sockets
FrameworkAssembly=System.Net.WebClient
FrameworkAssembly=System.Net.WebHeaderCollection
FrameworkAssembly=System.Net.WebProxy
FrameworkAssembly=System.Net.WebSockets.Client
FrameworkAssembly=System.Net.WebSockets
FrameworkAssembly=System.Net
FrameworkAssembly=System.Numerics.Vectors
FrameworkAssembly=System.Numerics
FrameworkAssembly=System.ObjectModel
FrameworkAssembly=System.Private.DataContractSerialization
FrameworkAssembly=System.Private.Uri
FrameworkAssembly=System.Private.Xml.Linq
FrameworkAssembly=System.Private.Xml
FrameworkAssembly=System.Reflection.DispatchProxy
FrameworkAssembly=System.Reflection.Emit.ILGeneration
FrameworkAssembly=System.Reflection.Emit.Lightweight
FrameworkAssembly=System.Reflection.Emit
FrameworkAssembly=System.Reflection.Extensions
FrameworkAssembly=System.Reflection.Metadata
FrameworkAssembly=System.Reflection.Primitives
FrameworkAssembly=System.Reflection.TypeExtensions
FrameworkAssembly=System.Reflection
FrameworkAssembly=System.Resources.Reader
FrameworkAssembly=System.Resources.ResourceManager
FrameworkAssembly=System.Resources.Writer
FrameworkAssembly=System.Runtime.CompilerServices.Unsafe
FrameworkAssembly=System.Runtime.CompilerServices.VisualC
FrameworkAssembly=System.Runtime.Extensions
FrameworkAssembly=System.Runtime.Handles
FrameworkAssembly=System.Runtime.InteropServices.JavaScript
FrameworkAssembly=System.Runtime.InteropServices.RuntimeInformation
FrameworkAssembly=System.Runtime.InteropServices
FrameworkAssembly=System.Runtime.Intrinsics
FrameworkAssembly=System.Runtime.Loader
FrameworkAssembly=System.Runtime.Numerics
FrameworkAssembly=System.Runtime.Serialization.Formatters
FrameworkAssembly=System.Runtime.Serialization.Json
FrameworkAssembly=System.Runtime.Serialization.Primitives
FrameworkAssembly=System.Runtime.Serialization.Xml
FrameworkAssembly=System.Runtime.Serialization
FrameworkAssembly=System.Runtime
FrameworkAssembly=System.Security.AccessControl
FrameworkAssembly=System.Security.Claims
FrameworkAssembly=System.Security.Cryptography.Algorithms
FrameworkAssembly=System.Security.Cryptography.Cng
FrameworkAssembly=System.Security.Cryptography.Csp
FrameworkAssembly=System.Security.Cryptography.Encoding
FrameworkAssembly=System.Security.Cryptography.OpenSsl
FrameworkAssembly=System.Security.Cryptography.Primitives
FrameworkAssembly=System.Security.Cryptography.X509Certificates
FrameworkAssembly=System.Security.Cryptography
FrameworkAssembly=System.Security.Principal.Windows
FrameworkAssembly=System.Security.Principal
FrameworkAssembly=System.Security.SecureString
FrameworkAssembly=System.Security
FrameworkAssembly=System.ServiceModel.Web
FrameworkAssembly=System.ServiceProcess
FrameworkAssembly=System.Text.Encoding.CodePages
FrameworkAssembly=System.Text.Encoding.Extensions
FrameworkAssembly=System.Text.Encoding
FrameworkAssembly=System.Text.Encodings.Web
FrameworkAssembly=System.Text.Json
FrameworkAssembly=System.Text.RegularExpressions
FrameworkAssembly=System.Threading.Channels
FrameworkAssembly=System.Threading.Overlapped
FrameworkAssembly=System.Threading.Tasks.Dataflow
FrameworkAssembly=System.Threading.Tasks.Extensions
FrameworkAssembly=System.Threading.Tasks.Parallel
FrameworkAssembly=System.Threading.Tasks
FrameworkAssembly=System.Threading.Thread
FrameworkAssembly=System.Threading.ThreadPool
FrameworkAssembly=System.Threading.Timer
FrameworkAssembly=System.Threading
FrameworkAssembly=System.Transactions.Local
FrameworkAssembly=System.Transactions
FrameworkAssembly=System.ValueTuple
FrameworkAssembly=System.Web.HttpUtility
FrameworkAssembly=System.Web
FrameworkAssembly=System.Windows
FrameworkAssembly=System.Xml.Linq
FrameworkAssembly=System.Xml.ReaderWriter
FrameworkAssembly=System.Xml.Serialization
FrameworkAssembly=System.Xml.XDocument
FrameworkAssembly=System.Xml.XPath.XDocument
FrameworkAssembly=System.Xml.XPath
FrameworkAssembly=System.Xml.XmlDocument
FrameworkAssembly=System.Xml.XmlSerializer
FrameworkAssembly=System.Xml
FrameworkAssembly=System
FrameworkAssembly=WindowsBase
FrameworkAssembly=mscorlib
FrameworkAssembly=netstandard
FrameworkAssembly=System.Private.CoreLib
				Interpreter=all
				IntermediateLinkDir=obj/Debug/net9.0-maccatalyst/maccatalyst-arm64/linked/
				InvariantGlobalization=false
				HybridGlobalization=true
				ItemsDirectory=/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-maccatalyst/maccatalyst-arm64/linker-items
				IsAppExtension=false
				IsSimulatorBuild=False
				LibMonoLinkMode=static
				LibXamarinLinkMode=static
				MarshalManagedExceptionMode=
				MarshalObjectiveCExceptionMode=
				MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libSystem.Globalization.Native.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libSystem.IO.Compression.Native.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libSystem.Native.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libSystem.Net.Security.Native.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libSystem.Security.Cryptography.Native.Apple.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libmono-component-debugger-static.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libmono-component-diagnostics_tracing-static.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libmono-component-hot_reload-static.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libmono-component-marshal-ilgen-static.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libmonosgen-2.0.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libmono-component-debugger-static.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libmono-component-hot_reload-static.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libmono-component-diagnostics_tracing-static.a
MonoLibrary=/usr/local/share/dotnet/packs/Microsoft.NETCore.App.Runtime.Mono.maccatalyst-arm64/9.0.6/runtimes/maccatalyst-arm64/native/libmono-component-marshal-ilgen-static.a
				MtouchFloat32=
				NoWarn=
				Optimize=
				PartialStaticRegistrarLibrary=/usr/local/share/dotnet/packs/Microsoft.MacCatalyst.Runtime.maccatalyst-arm64.net9.0_18.5/18.5.9199/runtimes/maccatalyst-arm64/native/Microsoft.MacCatalyst.registrar.a
				Platform=MacCatalyst
				PlatformAssembly=Microsoft.MacCatalyst.dll
				RelativeAppBundlePath=../HabitBuilder.app/
				Registrar=partial-static
				
				RequireLinkWithAttributeForObjectiveCClassSearch=
				RequirePInvokeWrappers=
				RuntimeConfigurationFile=runtimeconfig.bin
				SdkDevPath=/Applications/Xcode.app/Contents/Developer
				SdkRootDirectory=/usr/local/share/dotnet/packs/Microsoft.MacCatalyst.Sdk.net9.0_18.5/18.5.9199/
				SdkVersion=15.5
				SkipMarkingNSObjectsInUserAssemblies=
				TargetArchitectures=ARM64
				TargetFramework=.NETCoreApp,Version=v9.0,Profile=maccatalyst
				UseLlvm=
				Verbosity=0
				Warn=
				WarnAsError=
				XamarinNativeLibraryDirectory=/usr/local/share/dotnet/packs/Microsoft.MacCatalyst.Runtime.maccatalyst-arm64.net9.0_18.5/18.5.9199/runtimes/maccatalyst-arm64/native
				XamarinRuntime=MonoVM
