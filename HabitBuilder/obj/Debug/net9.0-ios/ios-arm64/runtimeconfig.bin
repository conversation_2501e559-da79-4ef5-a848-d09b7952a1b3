2MVVMTOOLKIT_ENABLE_INOTIFYPROPERTYCHANGING_SUPPORTtrueMMicrosoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmabilitytrue"System.AggressiveAttributeTrimmingtruefSystem.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerializationfalseCSystem.ComponentModel.TypeDescriptor.IsComObjectDescriptorSupportedfalse'System.Diagnostics.Debugger.IsSupportedtrue2System.Diagnostics.Tracing.EventSource.IsSupportedfalseSystem.Globalization.InvariantfalseSystem.Globalization.Hybridtrue$System.Net.Http.UseNativeHttpHandlertrue4System.Reflection.NullabilityInfoContext.IsSupportedfalse9System.Resources.ResourceManager.AllowCustomResourceTypesfalse&System.Resources.UseSystemResourceKeysfalse<System.Runtime.InteropServices.BuiltInComInterop.IsSupportedfalseJSystem.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHostingfalse9System.Runtime.InteropServices.EnableCppCLIHostActivationfalseESystem.Runtime.Serialization.EnableUnsafeBinaryFormatterSerializationfalse&System.StartupHookProvider.IsSupportedfalse-System.Text.Encoding.EnableUnsafeUTF7Encodingfalse<System.Text.Json.JsonSerializer.IsReflectionEnabledByDefaulttrue-System.Threading.Thread.EnableAutoreleasePooltrue>Microsoft.Maui.RuntimeFeature.IsIVisualAssemblyScanningEnabledfalse=Microsoft.Maui.RuntimeFeature.AreBindingInterceptorsSupportedtrue