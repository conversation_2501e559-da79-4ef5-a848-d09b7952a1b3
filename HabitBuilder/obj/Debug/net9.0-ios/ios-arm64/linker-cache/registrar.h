#pragma clang diagnostic ignored "-Wdeprecated-declarations"
#pragma clang diagnostic ignored "-Wtypedef-redefinition"
#pragma clang diagnostic ignored "-Wobjc-designated-initializers"
#pragma clang diagnostic ignored "-Wunguarded-availability-new"
#define DEBUG 1
#include <stdarg.h>
#include <objc/objc.h>
#include <objc/runtime.h>
#include <objc/message.h>
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <AuthenticationServices/AuthenticationServices.h>
#import <QuartzCore/QuartzCore.h>
#import <SafariServices/SafariServices.h>
#import <WebKit/WebKit.h>
#import <CoreGraphics/CoreGraphics.h>
#import <CoreLocation/CoreLocation.h>
#import <ContactsUI/ContactsUI.h>
#import <VisionKit/VisionKit.h>
#import <UserNotificationsUI/UserNotificationsUI.h>
#import <UniformTypeIdentifiers/UniformTypeIdentifiers.h>
#import <ThreadNetwork/THClient.h>
#import <Symbols/Symbols.h>
#import <WatchConnectivity/WatchConnectivity.h>
#import <SoundAnalysis/SoundAnalysis.h>
#import <ShazamKit/ShazamKit.h>
#import <SharedWithYou/SharedWithYou.h>
#import <SensitiveContentAnalysis/SensitiveContentAnalysis.h>
#import <SecurityUI/SecurityUI.h>
#import <SafetyKit/SafetyKit.h>
#import <QuickLookThumbnailing/QuickLookThumbnailing.h>
#import <QuickLook/QuickLook.h>
#import <PushKit/PushKit.h>
#import <PhotosUI/PhotosUI.h>
#import <PHASE/PHASE.h>
#import <PencilKit/PencilKit.h>
#import <OSLog/OSLog.h>
#import <NotificationCenter/NotificationCenter.h>
#import <MetalFX/MetalFX.h>
#import <Messages/Messages.h>
#import <MediaSetup/MediaSetup.h>
#import <LinkPresentation/LinkPresentation.h>
#import <IntentsUI/IntentsUI.h>
#import <IdentityLookupUI/IdentityLookupUI.h>
#import <IdentityLookup/IdentityLookup.h>
#import <FileProviderUI/FileProviderUI.h>
#import <ExternalAccessory/ExternalAccessory.h>
#import <EventKit/EventKit.h>
#import <DataDetection/DataDetection.h>
#import <CoreNFC/CoreNFC.h>
#import <CoreHaptics/CoreHaptics.h>
#import <CoreAudioKit/CoreAudioKit.h>
#import <BackgroundTasks/BackgroundTasks.h>
#import <AutomaticAssessmentConfiguration/AutomaticAssessmentConfiguration.h>
#import <AdSupport/AdSupport.h>
#import <AdServices/AdServices.h>
#import <AccessorySetupKit/AccessorySetupKit.h>
#import <Vision/Vision.h>
#import <VideoSubscriberAccount/VideoSubscriberAccount.h>
#import <UserNotifications/UserNotifications.h>
#import <Twitter/Twitter.h>
#import <StoreKit/StoreKit.h>
#import <SpriteKit/SpriteKit.h>
#import <GameplayKit/GameplayKit.h>
#import <Speech/Speech.h>
#import <Social/Social.h>
#import <SharedWithYouCore/SharedWithYouCore.h>
#import <SensorKit/SensorKit.h>
#import <ScreenTime/ScreenTime.h>
#import <SceneKit/SceneKit.h>
#import <ReplayKit/ReplayKit.h>
#import <PushToTalk/PushToTalk.h>
#import <Photos/Photos.h>
#import <PDFKit/PDFKit.h>
#import <PassKit/PassKit.h>
#import <NetworkExtension/NetworkExtension.h>
#import <NearbyInteraction/NearbyInteraction.h>
#import <NaturalLanguage/NaturalLanguage.h>
#import <MultipeerConnectivity/MultipeerConnectivity.h>
#import <ModelIO/ModelIO.h>
#import <MetricKit/MetricKit.h>
#import <MetalPerformanceShadersGraph/MetalPerformanceShadersGraph.h>
#import <MetalPerformanceShaders/MetalPerformanceShaders.h>
#import <Metal/Metal.h>
#import <MetalKit/MetalKit.h>
#import <MessageUI/MessageUI.h>
#import <MediaPlayer/MediaPlayer.h>
#import <MediaAccessibility/MediaAccessibility.h>
#import <MapKit/MapKit.h>
#import <MLCompute/MLCompute.h>
#import <LocalAuthentication/LocalAuthentication.h>
#import <JavaScriptCore/JavaScriptCore.h>
#import <Intents/Intents.h>
#import <IOSurface/IOSurfaceObjC.h>
#import <HomeKit/HomeKit.h>
#import <HealthKit/HealthKit.h>
#import <GameKit/GameKit.h>
#import <GameController/GameController.h>
#import <GLKit/GLKit.h>
#import <CoreText/CoreText.h>
#import <FileProvider/FileProvider.h>
#import <EventKitUI/EventKitUI.h>
#import <DeviceDiscoveryExtension/DeviceDiscoveryExtension.h>
#import <CryptoTokenKit/CryptoTokenKit.h>
#import <CoreTelephony/CoreTelephonyDefines.h>
#import <CoreTelephony/CTCall.h>
#import <CoreTelephony/CTCallCenter.h>
#import <CoreTelephony/CTCarrier.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import <CoreTelephony/CTSubscriber.h>
#import <CoreTelephony/CTSubscriberInfo.h>
#import <CoreSpotlight/CoreSpotlight.h>
#import <CloudKit/CloudKit.h>
#import <CoreMotion/CoreMotion.h>
#import <CoreMIDI/CoreMIDI.h>
#import <CoreML/CoreML.h>
#import <CoreImage/CoreImage.h>
#import <CoreImage/CIFilterBuiltins.h>
#import <CoreData/CoreData.h>
#import <CoreBluetooth/CoreBluetooth.h>
#import <Contacts/Contacts.h>
#import <ClassKit/ClassKit.h>
#import <Cinematic/Cinematic.h>
#import <CarPlay/CarPlay.h>
#import <CallKit/CallKit.h>
#import <BusinessChat/BusinessChat.h>
#import <BrowserEngineKit/BrowserEngineKit.h>
#import <BackgroundAssets/BackgroundAssets.h>
#import <AudioUnit/AudioUnit.h>
#import <AddressBookUI/AddressBookUI.h>
#import <Accounts/Accounts.h>
#import <Accessibility/Accessibility.h>
#import <AVRouting/AVRouting.h>
#import <AVKit/AVKit.h>
#import <AVFoundation/AVFoundation.h>
#import <ARKit/ARKit.h>
#import <HealthKitUI/HealthKitUI.h>
#import <DeviceCheck/DeviceCheck.h>
#import <CoreLocationUI/CoreLocationUI.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <AppClip/AppClip.h>

@class Microsoft_Maui_MauiUIApplicationDelegate;
@class AppDelegate;
@class Microsoft_Identity_Client_Platforms_iOS_SystemWebview_ASWebAuthenticationPresentationContextProviderWindow;
@class MsalUniversalView;
@class MsalAuthenticationAgentUINavigationController;
@class Microsoft_Identity_Client_Platforms_iOS_iOSBroker;
@class Microsoft_iOS__Foundation_NSUrlConnectionDelegate;
@class Microsoft_iOS__Foundation_NSUrlConnectionDataDelegate;
@class Microsoft_Identity_Client_Platforms_iOS_CoreCustomUrlProtocol_CoreCustomConnectionDelegate;
@class Microsoft_Identity_Client_Platforms_iOS_CoreCustomUrlProtocol;
@class Microsoft_Identity_Client_Platforms_iOS_WebviewBase;
@class Microsoft_Identity_Client_Platforms_iOS_SystemWebview_SystemWebUI;
@class Microsoft_Identity_Client_Platforms_iOS_EmbeddedWebview_EmbeddedWebUI;
@class MsalAuthenticationAgentUIViewController;
@class Microsoft_iOS__WebKit_WKUIDelegate;
@class Microsoft_Identity_Client_Platforms_iOS_EmbeddedWebview_WKWebNavigationDelegate_WKWebViewUIDelegate;
@class Microsoft_iOS__WebKit_WKNavigationDelegate;
@class Microsoft_Identity_Client_Platforms_iOS_EmbeddedWebview_WKWebNavigationDelegate;
@class Microsoft_Maui_Controls_Compatibility_Platform_iOS_GlobalCloseContextGestureRecognizer;
@class Microsoft_Maui_Controls_Compatibility_Platform_iOS_NativeViewPropertyListener;
@class Microsoft_Maui_Platform_ModalWrapper;
@class Microsoft_Maui_Controls_Platform_ControlsModalWrapper;
@class Microsoft_Maui_Controls_Platform_Compatibility_UIContainerView;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellFlyoutHeaderContainer;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellItemRenderer;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellTableViewController;
@class Microsoft_Maui_Controls_Platform_Compatibility_UIContainerCell;
@class Microsoft_Maui_Controls_Handlers_Items_ItemsViewCell;
@class Microsoft_Maui_Controls_Handlers_Items_TemplatedCell;
@class Microsoft_Maui_Controls_Handlers_Items_CarouselTemplatedCell;
@class Microsoft_iOS__UIKit_UICollectionViewDelegate;
@class Microsoft_iOS__UIKit_UICollectionViewDelegateFlowLayout;
@class Microsoft_Maui_Controls_Handlers_Items_ItemsViewDelegator_2;
@class Microsoft_Maui_Controls_Handlers_Items_CarouselViewDelegator;
@class Microsoft_Maui_Controls_Handlers_Items_ItemsViewLayout;
@class Microsoft_Maui_Controls_Handlers_Items_CarouselViewLayout;
@class Microsoft_Maui_Controls_Handlers_Items_DefaultCell;
@class Microsoft_Maui_Controls_Handlers_Items_GridViewLayout;
@protocol UICollectionViewSource;
@class Microsoft_Maui_Controls_Handlers_Items_ItemsViewController_1;
@class Microsoft_Maui_Controls_Handlers_Items_StructuredItemsViewController_1;
@class Microsoft_Maui_Controls_Handlers_Items_SelectableItemsViewController_1;
@class Microsoft_Maui_Controls_Handlers_Items_GroupableItemsViewController_1;
@class Microsoft_Maui_Controls_Handlers_Items_SelectableItemsViewDelegator_2;
@class Microsoft_Maui_Controls_Handlers_Items_GroupableItemsViewDelegator_2;
@class Microsoft_Maui_Controls_Handlers_Items_HeightConstrainedTemplatedCell;
@class Microsoft_Maui_Controls_Handlers_Items_HorizontalCell;
@class Microsoft_Maui_Controls_Handlers_Items_HorizontalDefaultCell;
@class Microsoft_Maui_Controls_Handlers_Items_HorizontalDefaultSupplementalView;
@class Microsoft_Maui_Controls_Handlers_Items_HorizontalSupplementaryView;
@class Microsoft_Maui_Controls_Handlers_Items_ListViewLayout;
@class Microsoft_Maui_Controls_Handlers_Items_ReorderableItemsViewController_1;
@class Microsoft_Maui_Controls_Handlers_Items_ReorderableItemsViewDelegator_2;
@class Microsoft_Maui_Controls_Handlers_Items_WidthConstrainedTemplatedCell;
@class Microsoft_Maui_Controls_Handlers_Items_VerticalCell;
@class Microsoft_Maui_Controls_Handlers_Items_VerticalDefaultCell;
@class Microsoft_Maui_Controls_Handlers_Items_VerticalDefaultSupplementalView;
@class Microsoft_Maui_Controls_Handlers_Items_VerticalSupplementaryView;
@class Microsoft_Maui_Controls_Handlers_Items2_ItemsViewDelegator2_2;
@class Microsoft_Maui_Controls_Handlers_Items2_CarouselViewDelegator2;
@class Microsoft_Maui_Controls_Handlers_Items2_ItemsViewCell2;
@class Microsoft_Maui_Controls_Handlers_Items2_DefaultCell2;
@class Microsoft_Maui_Controls_Handlers_Items2_ItemsViewController2_1;
@class Microsoft_Maui_Controls_Handlers_Items2_StructuredItemsViewController2_1;
@class Microsoft_Maui_Controls_Handlers_Items2_SelectableItemsViewController2_1;
@class Microsoft_Maui_Controls_Handlers_Items2_GroupableItemsViewController2_1;
@class Microsoft_Maui_Controls_Handlers_Items2_SelectableItemsViewDelegator2_2;
@class Microsoft_Maui_Controls_Handlers_Items2_GroupableItemsViewDelegator2_2;
@class Microsoft_Maui_Controls_Handlers_Items2_TemplatedCell2;
@class Microsoft_Maui_Controls_Handlers_Items2_HeightConstrainedTemplatedCell2;
@class Microsoft_Maui_Controls_Handlers_Items2_HorizontalCell2;
@class Microsoft_Maui_Controls_Handlers_Items2_HorizontalDefaultCell2;
@class Microsoft_Maui_Controls_Handlers_Items2_HorizontalDefaultSupplementalView2;
@class Microsoft_Maui_Controls_Handlers_Items2_HorizontalSupplementaryView2;
@class Microsoft_Maui_Controls_Handlers_Items2_ReorderableItemsViewController2_1;
@class Microsoft_Maui_Controls_Handlers_Items2_ReorderableItemsViewDelegator2_2;
@class Microsoft_Maui_Controls_Handlers_Items2_WidthConstrainedTemplatedCell2;
@class Microsoft_Maui_Controls_Handlers_Items2_VerticalCell2;
@class Microsoft_Maui_Controls_Handlers_Items2_VerticalDefaultCell2;
@class Microsoft_Maui_Controls_Handlers_Items2_VerticalDefaultSupplementalView2;
@class Microsoft_Maui_Controls_Handlers_Items2_VerticalSupplementaryView2;
@class Microsoft_Maui_Controls_Handlers_Compatibility_MauiNavigationBar;
@class Microsoft_Maui_Controls_Handlers_Compatibility_VisualElementRenderer_1;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ViewRenderer_2;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ViewRenderer;
@class Microsoft_Maui_Controls_Handlers_Compatibility_CellTableViewCell;
@class Microsoft_Maui_Controls_Handlers_Compatibility_iOS7ButtonContainer;
@class Microsoft_Maui_Controls_Handlers_Compatibility_HeaderWrapperView;
@class Microsoft_Maui_Controls_Handlers_Compatibility_FormsRefreshControl;
@class Microsoft_iOS__UIKit_UIScrollViewDelegate;
@class Microsoft_iOS__UIKit_UITableViewSource;
@class Microsoft_Maui_Controls_Handlers_Compatibility_TableViewModelRenderer;
@class Microsoft_Maui_Controls_Handlers_Compatibility_UnEvenTableViewModelRenderer;
@class Microsoft_Maui_Controls_Handlers_Compatibility_TableViewRenderer;
@class Microsoft_Maui_Platform_ResignFirstResponderTouchGestureRecognizer;
@class Microsoft_Maui_Controls_Platform_DragAndDropDelegate_CustomLocalStateData;
@class Microsoft_Maui_Controls_Platform_DragAndDropDelegate;
@class __UIGestureRecognizerToken;
@class __UIGestureRecognizer;
@class Microsoft_Maui_Controls_Platform_iOS_CustomPressGestureRecognizer;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellFlyoutContentRenderer;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellFlyoutRenderer;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellPageRendererTracker_TitleViewContainer;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellSearchResultsRenderer;
@class Microsoft_iOS__UIKit_UIGestureRecognizerDelegate;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRenderer_GestureDelegate;
@class Microsoft_iOS__UIKit_UINavigationControllerDelegate;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRenderer_NavDelegate;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRenderer;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRootHeader_ShellSectionHeaderCell;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRootHeader;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRootRenderer;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellTableViewSource_SeparatorView;
@class Microsoft_Maui_Controls_Platform_Compatibility_ShellTableViewSource;
@class Microsoft_Maui_Controls_Handlers_Items_CarouselViewController;
@class Microsoft_Maui_Controls_Handlers_Items_MauiCollectionView;
@class Microsoft_Maui_Controls_Handlers_Items2_CarouselViewController2;
@class Microsoft_Maui_Controls_Handlers_Items2_LayoutFactory2_CustomUICollectionViewCompositionalLayout;
@class Microsoft_Maui_Controls_Handlers_Compatibility_PhoneFlyoutPageRenderer_ChildViewController;
@class Microsoft_Maui_Controls_Handlers_Compatibility_PhoneFlyoutPageRenderer;
@class Microsoft_Maui_Platform_MauiView;
@class Microsoft_Maui_Platform_ContentView;
@class Microsoft_Maui_Controls_Handlers_Compatibility_FrameRenderer_FrameView;
@class Microsoft_Maui_Controls_Handlers_Compatibility_FrameRenderer;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ContextActionsCell_MoreActionSheetController;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ContextActionsCell;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ContextScrollViewDelegate;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ListViewRenderer_ListViewDataSource;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ListViewRenderer_UnevenListViewDataSource;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ListViewRenderer;
@class Microsoft_Maui_Controls_Handlers_Compatibility_FormsUITableViewController;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ViewCellRenderer_ViewTableCell;
@class Microsoft_Maui_Controls_Handlers_Compatibility_NavigationRenderer_MauiNavigationDelegate;
@class Microsoft_Maui_Controls_Handlers_Compatibility_NavigationRenderer_MauiControlsNavigationBar;
@class Microsoft_Maui_Controls_Handlers_Compatibility_NavigationRenderer_Container;
@class Microsoft_Maui_Controls_Handlers_Compatibility_NavigationRenderer;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ShellRenderer;
@class Microsoft_Maui_Controls_Handlers_Compatibility_TabbedRenderer;
@class Microsoft_Maui_Controls_Compatibility_Platform_iOS_ToolbarItemExtensions_PrimaryToolbarItem;
@class Microsoft_Maui_Controls_Compatibility_Platform_iOS_ToolbarItemExtensions_SecondaryToolbarItem_SecondaryToolbarItemContent;
@class Microsoft_Maui_Controls_Compatibility_Platform_iOS_ToolbarItemExtensions_SecondaryToolbarItem;
@class Microsoft_iOS__UIKit_UIContextMenuInteractionDelegate;
@class Microsoft_Maui_Controls_Platform_GesturePlatformManager_FakeRightClickContextMenuInteraction_FakeRightClickDelegate;
@class Microsoft_Maui_Controls_Platform_GesturePlatformManager_FakeRightClickContextMenuInteraction;
@class Microsoft_Maui_Controls_Handlers_Compatibility_ContextActionsCell_SelectGestureRecognizer;
@class Microsoft_Maui_Controls_Handlers_Compatibility_EntryCellRenderer_EntryCellTableViewCell;
@class Microsoft_Maui_Controls_Handlers_Compatibility_NavigationRenderer_SecondaryToolbar;
@class Microsoft_Maui_Controls_Handlers_Compatibility_NavigationRenderer_ParentingViewController;
@class MauiCALayerAutosizeObserver;
@class Microsoft_Maui_Platform_CollapseConstraint;
@class Microsoft_Maui_Platform_ContainerViewController;
@class Microsoft_Maui_Platform_GeneralWrapperView;
@class Microsoft_Maui_Platform_LayoutView;
@class Microsoft_Maui_Platform_MauiActivityIndicator;
@class PlatformGraphicsView;
@class Microsoft_Maui_Platform_MauiBoxView;
@class Microsoft_Maui_Platform_MauiCALayer;
@class Microsoft_Maui_Platform_MauiImageView;
@class Microsoft_Maui_Platform_MauiLabel;
@class Microsoft_Maui_Platform_MauiPageControl;
@class Microsoft_Maui_Platform_NoCaretField;
@class Microsoft_Maui_Platform_MauiPicker;
@class Microsoft_Maui_Platform_MauiRefreshView;
@class Microsoft_Maui_Platform_MauiScrollView;
@class Microsoft_Maui_Platform_MauiSearchBar;
@class Microsoft_Maui_Platform_MauiShapeView;
@class Microsoft_Maui_Platform_MauiTextField;
@class Microsoft_Maui_Platform_MauiTextView;
@class Microsoft_Maui_Platform_MauiWebViewNavigationDelegate;
@class Microsoft_Maui_Platform_PageViewController;
@class Microsoft_Maui_Platform_SemanticSwitchContentView;
@class Microsoft_Maui_Platform_StaticCAGradientLayer;
@class Microsoft_Maui_Platform_StaticCALayer;
@class Microsoft_Maui_Platform_StaticCAShapeLayer;
@class Microsoft_Maui_Platform_WrapperView;
@protocol UIPickerViewModel;
@class Microsoft_iOS__UIKit_UIPickerViewModel;
@class Microsoft_Maui_Handlers_PickerSource;
@class Microsoft_Maui_Handlers_SwipeItemButton;
@class Microsoft_Maui_MauiUISceneDelegate;
@class Microsoft_Maui_WindowOverlay_OverlayGraphicsView;
@class Microsoft_Maui_Platform_MauiCheckBox;
@class Microsoft_Maui_Platform_MauiDatePicker;
@class Microsoft_Maui_Platform_MauiDoneAccessoryView;
@class Microsoft_Maui_Platform_MauiHybridWebView;
@class Microsoft_Maui_Platform_MauiSwipeView;
@class Microsoft_Maui_Platform_MauiTimePicker;
@class Microsoft_Maui_Platform_MauiUIContextMenuInteraction_FlyoutUIContextMenuInteractionDelegate;
@class Microsoft_Maui_Platform_MauiUIContextMenuInteraction;
@class Microsoft_Maui_Platform_MauiWebViewUIDelegate;
@class Microsoft_Maui_Platform_MauiWKWebView;
@class Microsoft_Maui_Platform_PlatformTouchGraphicsView;
@class Microsoft_Maui_Handlers_HybridWebViewHandler_WebViewScriptMessageHandler;
@class Microsoft_Maui_WindowOverlay_PassthroughView;
@class Microsoft_Maui_Handlers_HybridWebViewHandler_SchemeHandler;
@class Microsoft_Maui_Authentication_AuthManager;
@class Microsoft_iOS__UIKit_UIAdaptivePresentationControllerDelegate;
@class Microsoft_Maui_ApplicationModel_UIPresentationControllerDelegate;
@class Microsoft_iOS__UIKit_UIActivityItemSource;
@class Microsoft_Maui_ApplicationModel_DataTransfer_ShareActivityItemSource;
@class Microsoft_iOS__CoreLocation_CLLocationManagerDelegate;
@class Microsoft_Maui_Devices_Sensors_SingleLocationListener;
@class Microsoft_Maui_Devices_Sensors_ContinuousLocationListener;
@class Microsoft_iOS__SafariServices_SFSafariViewControllerDelegate;
@class Microsoft_Maui_Authentication_WebAuthenticatorImplementation_NativeSFSafariViewControllerDelegate;
@class Microsoft_Maui_Authentication_WebAuthenticatorImplementation_ContextProvider;
@class Microsoft_iOS__UIKit_UIImagePickerControllerDelegate;
@class Microsoft_Maui_Media_MediaPickerImplementation_PhotoPickerDelegate;
@class Microsoft_iOS__UIKit_UIDocumentPickerDelegate;
@class Microsoft_Maui_Storage_FilePickerImplementation_PickerDelegate;
@class Microsoft_iOS__ContactsUI_CNContactPickerDelegate;
@class Microsoft_Maui_ApplicationModel_Communication_ContactsImplementation_ContactPickerDelegate;
@class Microsoft_Maui_ApplicationModel_Permissions_LocationWhenInUse_ManagerDelegate;
@class Microsoft_iOS__VisionKit_VNDocumentCameraViewControllerDelegate;
@class Microsoft_iOS__WatchConnectivity_WCSessionDelegate;
@class Microsoft_iOS__ShazamKit_SHSessionDelegate;
@class Microsoft_iOS__SharedWithYou_SWCollaborationViewDelegate;
@class Microsoft_iOS__SharedWithYou_SWHighlightCenterDelegate;
@class Microsoft_iOS__SafetyKit_SACrashDetectionDelegate;
@class Microsoft_iOS__SafetyKit_SAEmergencyResponseDelegate;
@class Microsoft_iOS__QuickLook_QLPreviewControllerDataSource;
@class Microsoft_iOS__QuickLook_QLPreviewControllerDelegate;
@class Microsoft_iOS__QuickLook_QLPreviewItem;
@class Microsoft_iOS__PushKit_PKPushRegistryDelegate;
@class Microsoft_iOS__PhotosUI_PHLivePhotoViewDelegate;
@class Microsoft_iOS__PhotosUI_PHPickerViewControllerDelegate;
@class Microsoft_iOS__PencilKit_PKCanvasViewDelegate;
@class Microsoft_iOS__PencilKit_PKToolPickerDelegate;
@class Microsoft_iOS__NotificationCenter_NCWidgetProviding;
@class Microsoft_iOS__Messages_MSStickerBrowserViewDataSource;
@class Microsoft_iOS__IntentsUI_INUIAddVoiceShortcutButtonDelegate;
@class Microsoft_iOS__IntentsUI_INUIAddVoiceShortcutViewControllerDelegate;
@class Microsoft_iOS__IntentsUI_INUIEditVoiceShortcutViewControllerDelegate;
@class Microsoft_iOS__ExternalAccessory_EAAccessoryDelegate;
@class Microsoft_iOS__ExternalAccessory_EAWiFiUnconfiguredAccessoryBrowserDelegate;
@class Microsoft_iOS__CoreNFC_NFCNdefReaderSessionDelegate;
@class Microsoft_iOS__CoreNFC_NFCReaderSessionDelegate;
@class Microsoft_iOS__CoreNFC_NFCTagReaderSessionDelegate;
@class Microsoft_iOS__CoreNFC_NFCVasReaderSessionDelegate;
@class Microsoft_iOS__ContactsUI_CNContactViewControllerDelegate;
@class Microsoft_iOS__AutomaticAssessmentConfiguration_AEAssessmentSessionDelegate;
@class Microsoft_iOS__VideoSubscriberAccount_VSAccountManagerDelegate;
@class Microsoft_iOS__UserNotifications_UNUserNotificationCenterDelegate;
@class Microsoft_iOS__StoreKit_SKCloudServiceSetupViewControllerDelegate;
@class Microsoft_iOS__StoreKit_SKOverlayDelegate;
@class Microsoft_iOS__StoreKit_SKPaymentQueueDelegate;
@class Microsoft_iOS__StoreKit_SKPaymentTransactionObserver;
@class Microsoft_iOS__StoreKit_SKRequestDelegate;
@class Microsoft_iOS__StoreKit_SKProductsRequestDelegate;
@class Microsoft_iOS__StoreKit_SKStoreProductViewControllerDelegate;
@class Microsoft_iOS__SpriteKit_SKPhysicsContactDelegate;
@class Microsoft_iOS__SpriteKit_SKSceneDelegate;
@class Microsoft_iOS__SpriteKit_SKViewDelegate;
@class Microsoft_iOS__Speech_SFSpeechRecognitionTaskDelegate;
@class Microsoft_iOS__Speech_SFSpeechRecognizerDelegate;
@class Microsoft_iOS__SensorKit_SRSensorReaderDelegate;
@class Microsoft_iOS__SceneKit_SCNAnimatable;
@class Microsoft_iOS__SceneKit_SCNActionable;
@class Microsoft_iOS__SceneKit_SCNAvoidOccluderConstraintDelegate;
@class Microsoft_iOS__SceneKit_SCNBoundingVolume;
@class Microsoft_iOS__SceneKit_SCNCameraControllerDelegate;
@class Microsoft_iOS__SceneKit_SCNNodeRendererDelegate;
@class Microsoft_iOS__SceneKit_SCNPhysicsContactDelegate;
@class Microsoft_iOS__SceneKit_SCNProgramDelegate;
@class Microsoft_iOS__SceneKit_SCNSceneExportDelegate;
@class Microsoft_iOS__SceneKit_SCNSceneRenderer;
@class Microsoft_iOS__SceneKit_SCNSceneRendererDelegate;
@class Microsoft_iOS__SceneKit_SCNShadable;
@class Microsoft_iOS__SceneKit_SCNTechniqueSupport;
@class Microsoft_iOS__ReplayKit_RPBroadcastActivityViewControllerDelegate;
@class Microsoft_iOS__ReplayKit_RPBroadcastControllerDelegate;
@class Microsoft_iOS__ReplayKit_RPPreviewViewControllerDelegate;
@class Microsoft_iOS__ReplayKit_RPScreenRecorderDelegate;
@class Microsoft_iOS__PushToTalk_PTChannelManagerDelegate;
@class Microsoft_iOS__PushToTalk_PTChannelRestorationDelegate;
@class Microsoft_iOS__Photos_PHPhotoLibraryChangeObserver;
@class Microsoft_iOS__PdfKit_PdfDocumentDelegate;
@class Microsoft_iOS__PdfKit_PdfViewDelegate;
@class PassKit_PKDisbursementVoucher;
@class Microsoft_iOS__PassKit_PKAddPassesViewControllerDelegate;
@class Microsoft_iOS__PassKit_PKAddPaymentPassViewControllerDelegate;
@class Microsoft_iOS__PassKit_PKAddSecureElementPassViewControllerDelegate;
@class Microsoft_iOS__PassKit_PKPayLaterViewDelegate;
@class Microsoft_iOS__PassKit_PKPaymentAuthorizationControllerDelegate;
@class Microsoft_iOS__PassKit_PKPaymentAuthorizationViewControllerDelegate;
@class Microsoft_iOS__PassKit_PKShareSecureElementPassViewControllerDelegate;
@class Microsoft_iOS__PassKit_PKVehicleConnectionDelegate;
@class Microsoft_iOS__NetworkExtension_NEAppPushDelegate;
@class Microsoft_iOS__NetworkExtension_NWTcpConnectionAuthenticationDelegate;
@class Microsoft_iOS__NearbyInteraction_NISessionDelegate;
@class Microsoft_iOS__MultipeerConnectivity_MCAdvertiserAssistantDelegate;
@class Microsoft_iOS__MultipeerConnectivity_MCBrowserViewControllerDelegate;
@class Microsoft_iOS__MultipeerConnectivity_MCNearbyServiceAdvertiserDelegate;
@class Microsoft_iOS__MultipeerConnectivity_MCNearbyServiceBrowserDelegate;
@class Microsoft_iOS__MultipeerConnectivity_MCSessionDelegate;
@class Microsoft_iOS__ModelIO_MDLLightProbeIrradianceDataSource;
@class Microsoft_iOS__MetalPerformanceShaders_MPSCnnBatchNormalizationDataSource;
@class Microsoft_iOS__MetalPerformanceShaders_MPSCnnConvolutionDataSource;
@class Microsoft_iOS__MetalPerformanceShaders_MPSCnnInstanceNormalizationDataSource;
@class Microsoft_iOS__MetalKit_MTKViewDelegate;
@class Microsoft_iOS__Metal_MTLCaptureScope;
@class Microsoft_iOS__Metal_MTLDrawable;
@class Microsoft_iOS__MessageUI_MFMailComposeViewControllerDelegate;
@class MessageUI_Mono_MFMailComposeViewControllerDelegate;
@class Microsoft_iOS__MessageUI_MFMessageComposeViewControllerDelegate;
@class MessageUI_Mono_MFMessageComposeViewControllerDelegate;
@class Microsoft_iOS__MediaPlayer_MPMediaPickerControllerDelegate;
@class Microsoft_iOS__MediaPlayer_MPNowPlayingSessionDelegate;
@class Microsoft_iOS__MediaPlayer_MPPlayableContentDataSource;
@class Microsoft_iOS__MediaPlayer_MPPlayableContentDelegate;
@class Microsoft_iOS__MapKit_MKLocalSearchCompleterDelegate;
@class Microsoft_iOS__MapKit_MKLookAroundViewControllerDelegate;
@class Microsoft_iOS__MapKit_MKMapItemDetailViewControllerDelegate;
@class Microsoft_iOS__MapKit_MKMapViewDelegate;
@class Microsoft_iOS__MapKit_MKAnnotation;
@class Microsoft_iOS__MapKit_MKOverlay;
@class Microsoft_iOS__MapKit_MKReverseGeocoderDelegate;
@class Microsoft_iOS__LocalAuthentication_LAEnvironmentObserver;
@class Microsoft_iOS__JavaScriptCore_JSExport;
@class Microsoft_iOS__HomeKit_HMAccessoryBrowserDelegate;
@class Microsoft_iOS__HomeKit_HMAccessoryDelegate;
@class Microsoft_iOS__HomeKit_HMCameraSnapshotControlDelegate;
@class Microsoft_iOS__HomeKit_HMCameraStreamControlDelegate;
@class Microsoft_iOS__HomeKit_HMHomeDelegate;
@class Microsoft_iOS__HomeKit_HMHomeManagerDelegate;
@class Microsoft_iOS__HomeKit_HMNetworkConfigurationProfileDelegate;
@class Microsoft_iOS__HealthKit_HKWorkoutSessionDelegate;
@class Microsoft_iOS__GameplayKit_GKAgentDelegate;
@class GameKit_GKPeerPickerControllerDelegate;
@class GameKit_GKPeerPickerController;
@class Microsoft_iOS__GameKit_GKSessionDelegate;
@class GameKit_Mono_GKSessionDelegate;
@class Microsoft_iOS__GameKit_GKAchievementViewControllerDelegate;
@class Microsoft_iOS__GameKit_GKChallengeEventHandlerDelegate;
@class Microsoft_iOS__GameKit_GKChallengeListener;
@class Microsoft_iOS__GameKit_GKFriendRequestComposeViewControllerDelegate;
@class Microsoft_iOS__GameKit_GKGameCenterControllerDelegate;
@class Microsoft_iOS__GameKit_GKInviteEventListener;
@class Microsoft_iOS__GameKit_GKLeaderboardViewControllerDelegate;
@class Microsoft_iOS__GameKit_GKLocalPlayerListener;
@class Microsoft_iOS__GameKit_GKMatchDelegate;
@class Microsoft_iOS__GameKit_GKMatchmakerViewControllerDelegate;
@class Microsoft_iOS__GameKit_GKSavedGameListener;
@class Microsoft_iOS__GameKit_GKTurnBasedEventHandlerDelegate;
@class Microsoft_iOS__GameKit_GKTurnBasedEventListener;
@class Microsoft_iOS__GameKit_GKTurnBasedMatchmakerViewControllerDelegate;
@class Microsoft_iOS__GameKit_GKVoiceChatClient;
@class Microsoft_iOS__GameController_GCGameControllerSceneDelegate;
@class Microsoft_iOS__GLKit_GLKNamedEffect;
@class Microsoft_iOS__GLKit_GLKViewControllerDelegate;
@class Microsoft_iOS__GLKit_GLKViewDelegate;
@class Microsoft_iOS__WebKit_WKDownloadDelegate;
@class Microsoft_iOS__WebKit_WKScriptMessageHandler;
@class Microsoft_iOS__WebKit_WKWebExtensionControllerDelegate;
@class UIKit_UIControlEventProxy;
@class __MonoTouch_UIImageStatusDispatcher;
@class __MonoTouch_UIVideoStatusDispatcher;
@class Microsoft_iOS__UIKit_NSLayoutManagerDelegate;
@class Microsoft_iOS__UIKit_NSTextAttachmentContainer;
@class Microsoft_iOS__UIKit_NSTextContentManagerDelegate;
@class Microsoft_iOS__UIKit_NSTextContentStorageDelegate;
@class Microsoft_iOS__UIKit_NSTextLayoutManagerDelegate;
@class Microsoft_iOS__UIKit_NSTextSelectionDataSource;
@class Microsoft_iOS__UIKit_NSTextStorageDelegate;
@class Microsoft_iOS__UIKit_NSTextViewportLayoutControllerDelegate;
@class Microsoft_iOS__UIKit_UIAccelerometerDelegate;
@protocol UIAccessibilityContainer;
@class Microsoft_iOS__UIKit_UIAccessibilityContainerDataTable;
@class Microsoft_iOS__UIKit_UIActionSheetDelegate;
@class Microsoft_iOS__UIKit_UIAlertViewDelegate;
@class Microsoft_iOS__UIKit_UIAppearanceContainer;
@class Microsoft_iOS__UIKit_UIApplicationDelegate;
@class Microsoft_iOS__UIKit_UIBarPositioning;
@class Microsoft_iOS__UIKit_UIBarPositioningDelegate;
@class Microsoft_iOS__UIKit_UICalendarSelectionMultiDateDelegate;
@class Microsoft_iOS__UIKit_UICalendarSelectionSingleDateDelegate;
@class Microsoft_iOS__UIKit_UICalendarSelectionWeekOfYearDelegate;
@class Microsoft_iOS__UIKit_UICalendarViewDelegate;
@class Microsoft_iOS__UIKit_UICGFloatTraitDefinition;
@class Microsoft_iOS__UIKit_UICloudSharingControllerDelegate;
@class Microsoft_iOS__UIKit_UICollectionViewDataSource;
@class Microsoft_iOS__UIKit_UICollectionViewDragDelegate;
@class Microsoft_iOS__UIKit_UICollectionViewDropDelegate;
@class Microsoft_iOS__UIKit_UICollectionViewSource;
@class Microsoft_iOS__UIKit_UICollisionBehaviorDelegate;
@class Microsoft_iOS__UIKit_UIColorPickerViewControllerDelegate;
@class Microsoft_iOS__UIKit_UIContentContainer;
@class Microsoft_iOS__UIKit_UICoordinateSpace;
@class Microsoft_iOS__UIKit_UIDocumentBrowserViewControllerDelegate;
@class Microsoft_iOS__UIKit_UIDocumentInteractionControllerDelegate;
@class Microsoft_iOS__UIKit_UIDocumentMenuDelegate;
@class Microsoft_iOS__UIKit_UIDragInteractionDelegate;
@class Microsoft_iOS__UIKit_UIDropInteractionDelegate;
@class Microsoft_iOS__UIKit_UIDynamicAnimatorDelegate;
@class Microsoft_iOS__UIKit_UIDynamicItem;
@class Microsoft_iOS__UIKit_UIEditMenuInteractionDelegate;
@class Microsoft_iOS__UIKit_UIFindInteractionDelegate;
@class Microsoft_iOS__UIKit_UIFontPickerViewControllerDelegate;
@class Microsoft_iOS__UIKit_UIIndirectScribbleInteractionDelegate;
@class Microsoft_iOS__UIKit_UILargeContentViewerInteractionDelegate;
@class Microsoft_iOS__UIKit_UILayoutSupport;
@class Microsoft_iOS__UIKit_UIMutableTraits;
@class Microsoft_iOS__UIKit_UINavigationBarDelegate;
@class Microsoft_iOS__UIKit_UINavigationItemRenameDelegate;
@class Microsoft_iOS__UIKit_UINSIntegerTraitDefinition;
@class Microsoft_iOS__UIKit_UIObjectRestoration;
@class Microsoft_iOS__UIKit_UIObjectTraitDefinition;
@class Microsoft_iOS__UIKit_UIPageControlProgressDelegate;
@class Microsoft_iOS__UIKit_UIPageControlTimerProgressDelegate;
@class Microsoft_iOS__UIKit_UIPageViewControllerDataSource;
@class Microsoft_iOS__UIKit_UIPageViewControllerDelegate;
@class Microsoft_iOS__UIKit_UIPencilInteractionDelegate;
@class Microsoft_iOS__UIKit_UIPickerViewDelegate;
@class Microsoft_iOS__UIKit_UIPickerViewAccessibilityDelegate;
@class Microsoft_iOS__UIKit_UIPickerViewDataSource;
@class Microsoft_iOS__UIKit_UIPointerInteractionDelegate;
@class Microsoft_iOS__UIKit_UIPopoverControllerDelegate;
@class Microsoft_iOS__UIKit_UIPopoverPresentationControllerDelegate;
@class Microsoft_iOS__UIKit_UIPreviewInteractionDelegate;
@class Microsoft_iOS__UIKit_UIPrinterPickerControllerDelegate;
@class Microsoft_iOS__UIKit_UIPrintInteractionControllerDelegate;
@class Microsoft_iOS__UIKit_UISceneDelegate;
@class Microsoft_iOS__UIKit_UIScreenshotServiceDelegate;
@class Microsoft_iOS__UIKit_UIScribbleInteractionDelegate;
@class Microsoft_iOS__UIKit_UIScrollViewAccessibilityDelegate;
@class Microsoft_iOS__UIKit_UISearchBarDelegate;
@class Microsoft_iOS__UIKit_UISearchControllerDelegate;
@class Microsoft_iOS__UIKit_UISearchDisplayDelegate;
@class Microsoft_iOS__UIKit_UISearchResultsUpdating;
@class Microsoft_iOS__UIKit_UISearchTextFieldDelegate;
@class Microsoft_iOS__UIKit_UISheetPresentationControllerDelegate;
@class Microsoft_iOS__UIKit_UISplitViewControllerDelegate;
@class Microsoft_iOS__UIKit_UIStateRestoring;
@class Microsoft_iOS__UIKit_UITabBarControllerDelegate;
@class Microsoft_iOS__UIKit_UITabBarControllerSidebarDelegate;
@class Microsoft_iOS__UIKit_UITabBarDelegate;
@class Microsoft_iOS__UIKit_UITableViewDataSource;
@class Microsoft_iOS__UIKit_UITableViewDelegate;
@class Microsoft_iOS__UIKit_UITableViewDragDelegate;
@class Microsoft_iOS__UIKit_UITableViewDropDelegate;
@class Microsoft_iOS__UIKit_UITextDocumentProxy;
@class Microsoft_iOS__UIKit_UITextDragDelegate;
@class Microsoft_iOS__UIKit_UITextDropDelegate;
@class Microsoft_iOS__UIKit_UITextFieldDelegate;
@class Microsoft_iOS__UIKit_UITextFormattingCoordinatorDelegate;
@class Microsoft_iOS__UIKit_UITextFormattingViewControllerDelegate;
@class Microsoft_iOS__UIKit_UITextInputDelegate;
@class Microsoft_iOS__UIKit_UITextInputTokenizer;
@class Microsoft_iOS__UIKit_UITextInteractionDelegate;
@class Microsoft_iOS__UIKit_UITextPasteDelegate;
@class Microsoft_iOS__UIKit_UITextSelectionDisplayInteractionDelegate;
@class Microsoft_iOS__UIKit_UITextViewDelegate;
@class Microsoft_iOS__UIKit_UIToolbarDelegate;
@class Microsoft_iOS__UIKit_UIToolTipInteractionDelegate;
@class Microsoft_iOS__UIKit_UITraitEnvironment;
@class Microsoft_iOS__UIKit_UIVideoEditorControllerDelegate;
@class Microsoft_iOS__UIKit_UIViewControllerAnimatedTransitioning;
@class Microsoft_iOS__UIKit_UIViewControllerContextTransitioning;
@class Microsoft_iOS__UIKit_UIViewControllerInteractiveTransitioning;
@class Microsoft_iOS__UIKit_UIViewControllerPreviewingDelegate;
@class Microsoft_iOS__UIKit_UIViewControllerTransitioningDelegate;
@class Microsoft_iOS__UIKit_UIWebViewDelegate;
@class Microsoft_iOS__UIKit_UIWindowSceneDelegate;
@class Microsoft_iOS__UIKit_UIWritingToolsCoordinatorDelegate;
@class Microsoft_iOS__EventKitUI_EKCalendarChooserDelegate;
@class Microsoft_iOS__EventKitUI_EKEventEditViewDelegate;
@class Microsoft_iOS__EventKitUI_EKEventViewDelegate;
@class Microsoft_iOS__CryptoTokenKit_TKTokenDelegate;
@class Microsoft_iOS__CryptoTokenKit_TKTokenDriverDelegate;
@class Microsoft_iOS__CryptoTokenKit_TKTokenSessionDelegate;
@class Microsoft_iOS__CoreTelephony_CTTelephonyNetworkInfoDelegate;
@class Microsoft_iOS__CoreSpotlight_CSSearchableIndexDelegate;
@class Microsoft_iOS__CoreMotion_CMHeadphoneMotionManagerDelegate;
@class Microsoft_iOS__CoreMotion_CMWaterSubmersionManagerDelegate;
@class Microsoft_iOS__CoreMidi_MidiCIProfileResponderDelegate;
@class Microsoft_iOS__CoreML_MLCustomModel;
@class CoreImage_CIAccordionFoldTransition;
@class CoreImage_CICompositingFilter;
@class CoreImage_CIAdditionCompositing;
@class CoreImage_CIAffineFilter;
@class CoreImage_CIAffineClamp;
@class CoreImage_CIAffineTile;
@class CoreImage_CIAffineTransform;
@class CoreImage_CIReductionFilter;
@class CoreImage_CIAreaAverage;
@class CoreImage_CIAreaBoundsRed;
@class CoreImage_CIAreaHistogram;
@class CoreImage_CIAreaLogarithmicHistogram;
@class CoreImage_CIAreaMaximum;
@class CoreImage_CIAreaMaximumAlpha;
@class CoreImage_CIAreaMinimum;
@class CoreImage_CIAreaMinimumAlpha;
@class CoreImage_CIAreaMinMax;
@class CoreImage_CIAreaMinMaxRed;
@class CoreImage_CIImageGenerator;
@class CoreImage_CIAttributedTextImageGenerator;
@class CoreImage_CICodeGenerator;
@class CoreImage_CIAztecCodeGenerator;
@class CoreImage_CIBarcodeGenerator;
@class CoreImage_CITransitionFilter;
@class CoreImage_CIBarsSwipeTransition;
@class CoreImage_CIBicubicScaleTransform;
@class CoreImage_CIBlendFilter;
@class CoreImage_CIBlendWithMask;
@class CoreImage_CIBlendWithAlphaMask;
@class CoreImage_CIBlendWithBlueMask;
@class CoreImage_CIBlendWithRedMask;
@class CoreImage_CIBloom;
@class CoreImage_CIBlurredRectangleGenerator;
@class CoreImage_CILinearBlur;
@class CoreImage_CIBokehBlur;
@class CoreImage_CIBoxBlur;
@class CoreImage_CIDistortionFilter;
@class CoreImage_CIBumpDistortion;
@class CoreImage_CIBumpDistortionLinear;
@class CoreImage_CICameraCalibrationLensCorrection;
@class CoreImage_CICannyEdgeDetector;
@class CoreImage_CICheckerboardGenerator;
@class CoreImage_CICircleSplashDistortion;
@class CoreImage_CIScreenFilter;
@class CoreImage_CICircularScreen;
@class CoreImage_CICircularWrap;
@class CoreImage_CIClamp;
@class CoreImage_CICmykHalftone;
@class CoreImage_CICode128BarcodeGenerator;
@class CoreImage_CIColorAbsoluteDifference;
@class CoreImage_CIColorBlendMode;
@class CoreImage_CIColorBurnBlendMode;
@class CoreImage_CIColorClamp;
@class CoreImage_CIColorControls;
@class CoreImage_CIColorCrossPolynomial;
@class CoreImage_CIColorCube;
@class CoreImage_CIColorCubesMixedWithMask;
@class CoreImage_CIColorCubeWithColorSpace;
@class CoreImage_CIColorCurves;
@class CoreImage_CIColorDodgeBlendMode;
@class CoreImage_CIColorInvert;
@class CoreImage_CIColorMap;
@class CoreImage_CIColorMatrix;
@class CoreImage_CIColorMonochrome;
@class CoreImage_CIColorPolynomial;
@class CoreImage_CIColorPosterize;
@class CoreImage_CIColorThreshold;
@class CoreImage_CIColorThresholdOtsu;
@class CoreImage_CIColumnAverage;
@class CoreImage_CIComicEffect;
@class CoreImage_CIConstantColorGenerator;
@class CoreImage_CIConvolutionCore;
@class CoreImage_CIConvolution3X3;
@class CoreImage_CIConvolution5X5;
@class CoreImage_CIConvolution7X7;
@class CoreImage_CIConvolution9Horizontal;
@class CoreImage_CIConvolution9Vertical;
@class CoreImage_CIConvolutionRGB3X3;
@class CoreImage_CIConvolutionRGB5X5;
@class CoreImage_CIConvolutionRGB7X7;
@class CoreImage_CIConvolutionRGB9Horizontal;
@class CoreImage_CIConvolutionRGB9Vertical;
@class CoreImage_CICopyMachineTransition;
@class CoreImage_CICoreMLModelFilter;
@class CoreImage_CICrop;
@class CoreImage_CICrystallize;
@class CoreImage_CIDarkenBlendMode;
@class CoreImage_CIDepthBlurEffect;
@class CoreImage_CIDepthDisparityConverter;
@class CoreImage_CIDepthOfField;
@class CoreImage_CIDepthToDisparity;
@class CoreImage_CIDifferenceBlendMode;
@class CoreImage_CIDiscBlur;
@class CoreImage_CIDisintegrateWithMaskTransition;
@class CoreImage_CIDisparityToDepth;
@class CoreImage_CIDisplacementDistortion;
@class CoreImage_CIDissolveTransition;
@class CoreImage_CIDistanceGradientFromRedMask;
@class CoreImage_CIDither;
@class CoreImage_CIDivideBlendMode;
@class CoreImage_CIDocumentEnhancer;
@class CoreImage_CIDotScreen;
@class CoreImage_CIDroste;
@class CoreImage_CIEdgePreserveUpsampleFilter;
@class CoreImage_CIEdges;
@class CoreImage_CIEdgeWork;
@class CoreImage_CITileFilter;
@class CoreImage_CIEightfoldReflectedTile;
@class CoreImage_CIExclusionBlendMode;
@class CoreImage_CIExposureAdjust;
@class CoreImage_CIFaceBalance;
@class CoreImage_CIFalseColor;
@class CoreImage_CIFlashTransition;
@class CoreImage_CIFourfoldReflectedTile;
@class CoreImage_CIFourfoldRotatedTile;
@class CoreImage_CIFourfoldTranslatedTile;
@class CoreImage_CIGaborGradients;
@class CoreImage_CIGammaAdjust;
@class CoreImage_CIGaussianBlur;
@class CoreImage_CIGaussianGradient;
@class CoreImage_CIGlassDistortion;
@class CoreImage_CIGlassLozenge;
@class CoreImage_CIGlideReflectedTile;
@class CoreImage_CIGloom;
@class CoreImage_CIGuidedFilter;
@class CoreImage_CIHardLightBlendMode;
@class CoreImage_CIHatchedScreen;
@class CoreImage_CIHeightFieldFromMask;
@class CoreImage_CIHexagonalPixellate;
@class CoreImage_CIHighlightShadowAdjust;
@class CoreImage_CIHistogramDisplayFilter;
@class CoreImage_CIHoleDistortion;
@class CoreImage_CIHueAdjust;
@class CoreImage_CIHueBlendMode;
@class CoreImage_CIHueSaturationValueGradient;
@protocol CIImageProvider;
@class CoreImage_CIKaleidoscope;
@class CoreImage_CIKeystoneCorrection;
@class CoreImage_CIKeystoneCorrectionCombined;
@class CoreImage_CIKeystoneCorrectionHorizontal;
@class CoreImage_CIKeystoneCorrectionVertical;
@class CoreImage_CIKMeans;
@class CoreImage_CILabDeltaE;
@class CoreImage_CILanczosScaleTransform;
@class CoreImage_CILenticularHaloGenerator;
@class CoreImage_CILightenBlendMode;
@class CoreImage_CILightTunnel;
@class CoreImage_CILinearBurnBlendMode;
@class CoreImage_CILinearDodgeBlendMode;
@class CoreImage_CILinearGradient;
@class CoreImage_CILinearLightBlendMode;
@class CoreImage_CILinearToSRGBToneCurve;
@class CoreImage_CILineOverlay;
@class CoreImage_CILineScreen;
@class CoreImage_CILuminosityBlendMode;
@class CoreImage_CIMaskedVariableBlur;
@class CoreImage_CIMaskToAlpha;
@class CoreImage_CIMaximumComponent;
@class CoreImage_CIMaximumCompositing;
@class CoreImage_CIMaximumScaleTransform;
@class CoreImage_CIMedianFilter;
@class CoreImage_CIMeshGenerator;
@class CoreImage_CIMinimumComponent;
@class CoreImage_CIMinimumCompositing;
@class CoreImage_CIMix;
@class CoreImage_CIModTransition;
@class CoreImage_CIMorphology;
@class CoreImage_CIMorphologyGradient;
@class CoreImage_CIMorphologyMaximum;
@class CoreImage_CIMorphologyMinimum;
@class CoreImage_CIMorphologyRectangle;
@class CoreImage_CIMorphologyRectangleMaximum;
@class CoreImage_CIMorphologyRectangleMinimum;
@class CoreImage_CIMotionBlur;
@class CoreImage_CIMultiplyBlendMode;
@class CoreImage_CIMultiplyCompositing;
@class CoreImage_CINinePartStretched;
@class CoreImage_CINinePartTiled;
@class CoreImage_CINoiseReduction;
@class CoreImage_CIOpTile;
@class CoreImage_CIOverlayBlendMode;
@class CoreImage_CIPageCurlTransition;
@class CoreImage_CIPageCurlWithShadowTransition;
@class CoreImage_CIPaletteCentroid;
@class CoreImage_CIPalettize;
@class CoreImage_CIParallelogramTile;
@class CoreImage_CIPdf417BarcodeGenerator;
@class CoreImage_CIPersonSegmentation;
@class CoreImage_CIPerspectiveTransform;
@class CoreImage_CIPerspectiveCorrection;
@class CoreImage_CIPerspectiveRotate;
@class CoreImage_CIPerspectiveTile;
@class CoreImage_CIPerspectiveTransformWithExtent;
@class CoreImage_CIPhotoEffect;
@class CoreImage_CIPhotoEffectChrome;
@class CoreImage_CIPhotoEffectFade;
@class CoreImage_CIPhotoEffectInstant;
@class CoreImage_CIPhotoEffectMono;
@class CoreImage_CIPhotoEffectNoir;
@class CoreImage_CIPhotoEffectProcess;
@class CoreImage_CIPhotoEffectTonal;
@class CoreImage_CIPhotoEffectTransfer;
@class CoreImage_CIPinchDistortion;
@class CoreImage_CIPinLightBlendMode;
@class CoreImage_CIPixellate;
@class CoreImage_CIPointillize;
@class CoreImage_CIQRCodeGenerator;
@class CoreImage_CIRadialGradient;
@class CoreImage_CIRandomGenerator;
@class CoreImage_CIRippleTransition;
@class CoreImage_CIRoundedRectangleGenerator;
@class CoreImage_CIRoundedRectangleStrokeGenerator;
@class CoreImage_CIRowAverage;
@class CoreImage_CISaliencyMapFilter;
@class CoreImage_CISampleNearest;
@class CoreImage_CISaturationBlendMode;
@class CoreImage_CIScreenBlendMode;
@class CoreImage_CISepiaTone;
@class CoreImage_CIShadedMaterial;
@class CoreImage_CISharpenLuminance;
@class CoreImage_CISixfoldReflectedTile;
@class CoreImage_CISixfoldRotatedTile;
@class CoreImage_CISmoothLinearGradient;
@class CoreImage_CISobelGradients;
@class CoreImage_CISoftLightBlendMode;
@class CoreImage_CISourceAtopCompositing;
@class CoreImage_CISourceInCompositing;
@class CoreImage_CISourceOutCompositing;
@class CoreImage_CISourceOverCompositing;
@class CoreImage_CISpotColor;
@class CoreImage_CISpotLight;
@class CoreImage_CISRGBToneCurveToLinear;
@class CoreImage_CIStarShineGenerator;
@class CoreImage_CIStraightenFilter;
@class CoreImage_CIStretchCrop;
@class CoreImage_CIStripesGenerator;
@class CoreImage_CISubtractBlendMode;
@class CoreImage_CISunbeamsGenerator;
@class CoreImage_CISwipeTransition;
@class CoreImage_CITemperatureAndTint;
@class CoreImage_CITextImageGenerator;
@class CoreImage_CIThermal;
@class CoreImage_CIToneCurve;
@class CoreImage_CIToneMapHeadroom;
@class CoreImage_CITorusLensDistortion;
@class CoreImage_CITriangleKaleidoscope;
@class CoreImage_CITriangleTile;
@class CoreImage_CITwelvefoldReflectedTile;
@class CoreImage_CITwirlDistortion;
@class CoreImage_CIUnsharpMask;
@class CoreImage_CIVibrance;
@class CoreImage_CIVignette;
@class CoreImage_CIVignetteEffect;
@class CoreImage_CIVividLightBlendMode;
@class CoreImage_CIVortexDistortion;
@class CoreImage_CIWhitePointAdjust;
@class CoreImage_CIXRay;
@class CoreImage_CIZoomBlur;
@class Microsoft_iOS__CoreData_NSFetchedResultsControllerDelegate;
@class Microsoft_iOS__CoreData_NSFetchedResultsSectionInfo;
@class Microsoft_iOS__CoreBluetooth_CBCentralManagerDelegate;
@class Microsoft_iOS__CoreBluetooth_CBPeripheralDelegate;
@class Microsoft_iOS__CoreBluetooth_CBPeripheralManagerDelegate;
@class Foundation_NSDispatcher;
@class __MonoMac_NSActionDispatcher;
@class __MonoMac_NSSynchronizationContextDispatcher;
@class __Xamarin_NSTimerActionDispatcher;
@class Foundation_NSAsyncDispatcher;
@class __MonoMac_NSAsyncActionDispatcher;
@class __MonoMac_NSAsyncSynchronizationContextDispatcher;
@class Foundation_NSExceptionError;
@class Microsoft_iOS__Foundation_NSFileManagerDelegate;
@class Foundation_InternalNSNotificationHandler;
@class Microsoft_iOS__Foundation_NSCacheDelegate;
@class Microsoft_iOS__Foundation_NSCoding;
@class Microsoft_iOS__Foundation_NSCopying;
@class Microsoft_iOS__Foundation_NSExtensionRequestHandling;
@class Microsoft_iOS__Foundation_NSFilePresenter;
@class Microsoft_iOS__Foundation_NSKeyedArchiverDelegate;
@class Microsoft_iOS__Foundation_NSKeyedUnarchiverDelegate;
@class Microsoft_iOS__Foundation_NSPortDelegate;
@class Microsoft_iOS__Foundation_NSMachPortDelegate;
@class Microsoft_iOS__Foundation_NSMetadataQueryDelegate;
@class Microsoft_iOS__Foundation_NSMutableCopying;
@class Microsoft_iOS__Foundation_NSNetServiceBrowserDelegate;
@class Microsoft_iOS__Foundation_NSNetServiceDelegate;
@class Microsoft_iOS__Foundation_NSStreamDelegate;
@class Microsoft_iOS__Foundation_NSUrlConnectionDownloadDelegate;
@class Microsoft_iOS__Foundation_NSUrlSessionDelegate;
@class Microsoft_iOS__Foundation_NSUrlSessionTaskDelegate;
@class Microsoft_iOS__Foundation_NSUrlSessionDataDelegate;
@class Microsoft_iOS__Foundation_NSUrlSessionDownloadDelegate;
@class Microsoft_iOS__Foundation_NSUrlSessionStreamDelegate;
@class Microsoft_iOS__Foundation_NSUrlSessionWebSocketDelegate;
@class Microsoft_iOS__Foundation_NSUserActivityDelegate;
@class Microsoft_iOS__Foundation_NSXpcListenerDelegate;
@class Microsoft_iOS__CoreAnimation_CALayerDelegate;
@class Microsoft_iOS__CoreAnimation_CAAnimationDelegate;
@class Microsoft_iOS__CoreAnimation_CAMediaTiming;
@class Microsoft_iOS__CoreAnimation_CAMetalDisplayLinkDelegate;
@class Microsoft_iOS__CloudKit_CKRecordValue;
@class Microsoft_iOS__CloudKit_CKSyncEngineDelegate;
@class Microsoft_iOS__ClassKit_CLSDataStoreDelegate;
@class Microsoft_iOS__CarPlay_CPApplicationDelegate;
@class Microsoft_iOS__CarPlay_CPInstrumentClusterControllerDelegate;
@class Microsoft_iOS__CarPlay_CPInterfaceControllerDelegate;
@class Microsoft_iOS__CarPlay_CPListTemplateDelegate;
@class Microsoft_iOS__CarPlay_CPMapTemplateDelegate;
@class Microsoft_iOS__CarPlay_CPPointOfInterestTemplateDelegate;
@class Microsoft_iOS__CarPlay_CPSearchTemplateDelegate;
@class Microsoft_iOS__CarPlay_CPSessionConfigurationDelegate;
@class Microsoft_iOS__CarPlay_CPTabBarTemplateDelegate;
@class Microsoft_iOS__CarPlay_CPTemplateApplicationDashboardSceneDelegate;
@class Microsoft_iOS__CarPlay_CPTemplateApplicationInstrumentClusterSceneDelegate;
@class Microsoft_iOS__CarPlay_CPTemplateApplicationSceneDelegate;
@class Microsoft_iOS__CallKit_CXCallDirectoryExtensionContextDelegate;
@class Microsoft_iOS__CallKit_CXCallObserverDelegate;
@class Microsoft_iOS__CallKit_CXProviderDelegate;
@class Microsoft_iOS__BrowserEngineKit_BEDragInteractionDelegate;
@class Microsoft_iOS__BrowserEngineKit_BEScrollViewDelegate;
@class Microsoft_iOS__BrowserEngineKit_BETextInputDelegate;
@class Microsoft_iOS__BrowserEngineKit_BETextInteractionDelegate;
@class Microsoft_iOS__BackgroundAssets_BADownloadManagerDelegate;
@class Microsoft_iOS__AuthenticationServices_ASAccountAuthenticationModificationControllerDelegate;
@class Microsoft_iOS__AuthenticationServices_ASAuthorizationControllerDelegate;
@class Microsoft_iOS__AddressBookUI_ABNewPersonViewControllerDelegate;
@class AddressBookUI_InternalABNewPersonViewControllerDelegate;
@class Microsoft_iOS__AddressBookUI_ABPeoplePickerNavigationControllerDelegate;
@class AddressBookUI_InternalABPeoplePickerNavigationControllerDelegate;
@class Microsoft_iOS__AddressBookUI_ABPersonViewControllerDelegate;
@class AddressBookUI_InternalABPersonViewControllerDelegate;
@class Microsoft_iOS__AddressBookUI_ABUnknownPersonViewControllerDelegate;
@class AddressBookUI_InternalABUnknownPersonViewControllerDelegate;
@class Microsoft_iOS__AVKit_AVCustomRoutingControllerDelegate;
@class Microsoft_iOS__AVKit_AVPictureInPictureControllerDelegate;
@class Microsoft_iOS__AVKit_AVPictureInPictureSampleBufferPlaybackDelegate;
@class Microsoft_iOS__AVKit_AVPlayerViewControllerDelegate;
@class Microsoft_iOS__AVKit_AVRoutePickerViewDelegate;
@class Microsoft_iOS__AVFoundation_AVAudioPlayerDelegate;
@class AVFoundation_InternalAVAudioPlayerDelegate;
@class Microsoft_iOS__AVFoundation_AVAudioRecorderDelegate;
@class AVFoundation_InternalAVAudioRecorderDelegate;
@class Microsoft_iOS__AVFoundation_AVAudioSessionDelegate;
@class AVFoundation_InternalAVAudioSessionDelegate;
@class Microsoft_iOS__AVFoundation_AVAssetDownloadDelegate;
@class Microsoft_iOS__AVFoundation_AVAssetResourceLoaderDelegate;
@class Microsoft_iOS__AVFoundation_AVAssetWriterDelegate;
@class Microsoft_iOS__AVFoundation_AVAsynchronousKeyValueLoading;
@class Microsoft_iOS__AVFoundation_AVAudio3DMixing;
@class Microsoft_iOS__AVFoundation_AVAudioStereoMixing;
@class Microsoft_iOS__AVFoundation_AVCaptureAudioDataOutputSampleBufferDelegate;
@class Microsoft_iOS__AVFoundation_AVCaptureDataOutputSynchronizerDelegate;
@class Microsoft_iOS__AVFoundation_AVCaptureDepthDataOutputDelegate;
@class Microsoft_iOS__AVFoundation_AVCaptureFileOutputRecordingDelegate;
@class Microsoft_iOS__AVFoundation_AVCaptureMetadataOutputObjectsDelegate;
@class Microsoft_iOS__AVFoundation_AVCapturePhotoCaptureDelegate;
@class Microsoft_iOS__AVFoundation_AVCapturePhotoOutputReadinessCoordinatorDelegate;
@class Microsoft_iOS__AVFoundation_AVCaptureSessionControlsDelegate;
@class Microsoft_iOS__AVFoundation_AVCaptureVideoDataOutputSampleBufferDelegate;
@class Microsoft_iOS__AVFoundation_AVContentKeySessionDelegate;
@class Microsoft_iOS__AVFoundation_AVPlaybackCoordinatorPlaybackControlDelegate;
@class Microsoft_iOS__AVFoundation_AVPlayerItemOutputPushDelegate;
@class Microsoft_iOS__AVFoundation_AVPlayerItemLegibleOutputPushDelegate;
@class Microsoft_iOS__AVFoundation_AVPlayerItemMetadataCollectorPushDelegate;
@class Microsoft_iOS__AVFoundation_AVPlayerItemMetadataOutputPushDelegate;
@class Microsoft_iOS__AVFoundation_AVPlayerItemOutputPullDelegate;
@class Microsoft_iOS__AVFoundation_AVPlayerItemRenderedLegibleOutputPushDelegate;
@class Microsoft_iOS__AVFoundation_AVPlayerPlaybackCoordinatorDelegate;
@class Microsoft_iOS__AVFoundation_AVSpeechSynthesizerDelegate;
@class Microsoft_iOS__AVFoundation_AVVideoCompositing;
@class Microsoft_iOS__ARKit_ARCoachingOverlayViewDelegate;
@class Microsoft_iOS__ARKit_ARSCNViewDelegate;
@class Microsoft_iOS__ARKit_ARSessionDelegate;
@class Microsoft_iOS__ARKit_ARSKViewDelegate;
@class UIKit_UIView_UIViewAppearance;
@class SharedWithYou_SWAttributionView_SWAttributionViewAppearance;
@class SharedWithYou_SWCollaborationView_SWCollaborationViewAppearance;
@class QuickLook_QLPreviewController__QLPreviewControllerDelegate;
@class PhotosUI_PHLivePhotoView_PHLivePhotoViewAppearance;
@class UIKit_UIScrollView_UIScrollViewAppearance;
@class PencilKit_PKCanvasView_PKCanvasViewAppearance;
@class Messages_MSStickerBrowserView_MSStickerBrowserViewAppearance;
@class Messages_MSStickerView_MSStickerViewAppearance;
@class LinkPresentation_LPLinkView_LPLinkViewAppearance;
@class UIKit_UIControl_UIControlAppearance;
@class UIKit_UIButton_UIButtonAppearance;
@class IntentsUI_INUIAddVoiceShortcutButton_INUIAddVoiceShortcutButtonAppearance;
@class HealthKitUI_HKActivityRingView_HKActivityRingViewAppearance;
@class ExternalAccessory_EAAccessory__EAAccessoryDelegate;
@class ExternalAccessory_EAWiFiUnconfiguredAccessoryBrowser__EAWiFiUnconfiguredAccessoryBrowserDelegate;
@class CoreLocationUI_CLLocationButton_CLLocationButtonAppearance;
@class CoreAudioKit_CAInterAppAudioSwitcherView_CAInterAppAudioSwitcherViewAppearance;
@class CoreAudioKit_CAInterAppAudioTransportView_CAInterAppAudioTransportViewAppearance;
@class StoreKit_SKRequest__SKRequestDelegate;
@class StoreKit_SKProductsRequest__SKProductsRequestDelegate;
@class StoreKit_SKStoreProductViewController__SKStoreProductViewControllerDelegate;
@class SpriteKit_SKPhysicsWorld__SKPhysicsContactDelegate;
@class SpriteKit_SKView_SKViewAppearance;
@class SceneKit_SCNPhysicsWorld__SCNPhysicsContactDelegate;
@class SceneKit_SCNView_SCNViewAppearance;
@class ReplayKit_RPSystemBroadcastPickerView_RPSystemBroadcastPickerViewAppearance;
@class Photos_PHPhotoLibrary___phlib_observer;
@class PdfKit_PdfDocument__PdfDocumentDelegate;
@class PdfKit_PdfThumbnailView_PdfThumbnailViewAppearance;
@class PdfKit_PdfView__PdfViewDelegate;
@class PdfKit_PdfView_PdfViewAppearance;
@class PassKit_PKPayLaterView_PKPayLaterViewAppearance;
@class PassKit_PKAddPassButton_PKAddPassButtonAppearance;
@class PassKit_PKAddPassesViewController__PKAddPassesViewControllerDelegate;
@class PassKit_PKIdentityButton_PKIdentityButtonAppearance;
@class PassKit_PKPaymentAuthorizationViewController__PKPaymentAuthorizationViewControllerDelegate;
@class PassKit_PKPaymentButton_PKPaymentButtonAppearance;
@class MetalKit_MTKView_MTKViewAppearance;
@class MessageUI_MFMailComposeViewController_MFMailComposeViewControllerAppearance;
@class MessageUI_MFMessageComposeViewController_MFMessageComposeViewControllerAppearance;
@class MediaPlayer_MPMediaPickerController__MPMediaPickerControllerDelegate;
@class MediaPlayer_MPVolumeView_MPVolumeViewAppearance;
@class MapKit_MKOverlayView_MKOverlayViewAppearance;
@class MapKit_MKAnnotationView_MKAnnotationViewAppearance;
@class MapKit_MKOverlayPathView_MKOverlayPathViewAppearance;
@class MapKit_MKCircleView_MKCircleViewAppearance;
@class MapKit_MKCompassButton_MKCompassButtonAppearance;
@class MapKit_MKMapView__MKMapViewDelegate;
@class MapKit_MKMapView_MKMapViewAppearance;
@class MapKit_MKMarkerAnnotationView_MKMarkerAnnotationViewAppearance;
@class MapKit_MKPinAnnotationView_MKPinAnnotationViewAppearance;
@class MapKit_MKPolygonView_MKPolygonViewAppearance;
@class MapKit_MKPolylineView_MKPolylineViewAppearance;
@class MapKit_MKScaleView_MKScaleViewAppearance;
@class MapKit_MKUserLocationView_MKUserLocationViewAppearance;
@class UIKit_UIBarItem_UIBarItemAppearance;
@class UIKit_UIBarButtonItem_UIBarButtonItemAppearance;
@class MapKit_MKUserTrackingBarButtonItem_MKUserTrackingBarButtonItemAppearance;
@class MapKit_MKUserTrackingButton_MKUserTrackingButtonAppearance;
@class HomeKit_HMHome__HMHomeDelegate;
@class HomeKit_HMAccessory__HMAccessoryDelegate;
@class HomeKit_HMAccessoryBrowser__HMAccessoryBrowserDelegate;
@class HomeKit_HMCameraView_HMCameraViewAppearance;
@class HomeKit_HMHomeManager__HMHomeManagerDelegate;
@class GameKit_GKGameCenterViewController__GKGameCenterControllerDelegate;
@class MonoTouch_GKSession_ReceivedObject;
@class GameKit_GKMatch__GKMatchDelegate;
@class GameKit_GKAchievementViewController__GKAchievementViewControllerDelegate;
@class GameKit_GKAchievementViewController_GKAchievementViewControllerAppearance;
@class GameKit_GKChallengeEventHandler__GKChallengeEventHandlerDelegate;
@class GameKit_GKFriendRequestComposeViewController__GKFriendRequestComposeViewControllerDelegate;
@class GameKit_GKFriendRequestComposeViewController_GKFriendRequestComposeViewControllerAppearance;
@class GameKit_GKLeaderboardViewController__GKLeaderboardViewControllerDelegate;
@class GameKit_GKLeaderboardViewController_GKLeaderboardViewControllerAppearance;
@class GameKit_GKMatchmakerViewController__GKMatchmakerViewControllerDelegate;
@class GameKit_GKTurnBasedMatchmakerViewController_GKTurnBasedMatchmakerViewControllerAppearance;
@class GLKit_GLKView__GLKViewDelegate;
@class GLKit_GLKView_GLKViewAppearance;
@class WebKit_WKWebView_WKWebViewAppearance;
@class __MonoMac_FuncBoolDispatcher;
@class UIKit_UIActionSheet__UIActionSheetDelegate;
@class UIKit_UIActionSheet_UIActionSheetAppearance;
@class UIKit_UIAlertView__UIAlertViewDelegate;
@class UIKit_UIAlertView_UIAlertViewAppearance;
@class UIKit_UIBarButtonItem_Callback;
@class UIKit_UICollectionView_UICollectionViewAppearance;
@class __UIGestureRecognizerGenericCB;
@class __UIGestureRecognizerParameterlessToken;
@class __UIGestureRecognizerParametrizedToken;
@class UIKit_UIGestureRecognizer__UIGestureRecognizerDelegate;
@class UIKit_UIImagePickerController__UIImagePickerControllerDelegate;
@class UIKit_UINavigationBar_UINavigationBarAppearance;
@class UIKit_UIPageViewController__UIPageViewControllerDelegate;
@class UIKit_UIPageViewController__UIPageViewControllerDataSource;
@class UIKit_UIPickerView_UIPickerViewAppearance;
@class UIKit_UIPopoverController__UIPopoverControllerDelegate;
@class UIKit_UIPopoverPresentationController__UIPopoverPresentationControllerDelegate;
@class UIKit_UISearchBar_UISearchBarAppearance;
@class UIKit_UISearchBar__UISearchBarDelegate;
@class UIKit_UISearchController___Xamarin_UISearchResultsUpdating;
@class UIKit_UISegmentedControl_UISegmentedControlAppearance;
@class UIKit_UITableView_UITableViewAppearance;
@class UIKit_UITableViewCell_UITableViewCellAppearance;
@class UIKit_UITextField__UITextFieldDelegate;
@class UIKit_UITextField_UITextFieldAppearance;
@class UIKit_UIScrollView__UIScrollViewDelegate;
@class UIKit_UITextView__UITextViewDelegate;
@class UIKit_UITextView_UITextViewAppearance;
@class UIKit_UIToolbar_UIToolbarAppearance;
@class UIKit_UIView__UIViewStaticCallback;
@class UIKit_NSTextStorage__NSTextStorageDelegate;
@class UIKit_UIAccelerometer__UIAccelerometerDelegate;
@class UIKit_UIActivityIndicatorView_UIActivityIndicatorViewAppearance;
@class UIKit_UICalendarView_UICalendarViewAppearance;
@class UIKit_UICollectionReusableView_UICollectionReusableViewAppearance;
@class UIKit_UICollectionViewCell_UICollectionViewCellAppearance;
@class UIKit_UICollectionViewListCell_UICollectionViewListCellAppearance;
@class UIKit_UICollisionBehavior__UICollisionBehaviorDelegate;
@class UIKit_UIColorWell_UIColorWellAppearance;
@class UIKit_UIContentUnavailableView_UIContentUnavailableViewAppearance;
@class UIKit_UIDatePicker_UIDatePickerAppearance;
@class UIKit_UIDocumentInteractionController__UIDocumentInteractionControllerDelegate;
@class UIKit_UIDocumentMenuViewController__UIDocumentMenuDelegate;
@class UIKit_UIDocumentPickerViewController__UIDocumentPickerDelegate;
@class UIKit_UIEventAttributionView_UIEventAttributionViewAppearance;
@class UIKit_UIImageView_UIImageViewAppearance;
@class UIKit_UIInputView_UIInputViewAppearance;
@class UIKit_UILabel_UILabelAppearance;
@class UIKit_UIListContentView_UIListContentViewAppearance;
@class UIKit_UIPageControl_UIPageControlAppearance;
@class UIKit_UIPasteControl_UIPasteControlAppearance;
@class UIKit_UIPopoverBackgroundView_UIPopoverBackgroundViewAppearance;
@class UIKit_UIPreviewInteraction__UIPreviewInteractionDelegate;
@class UIKit_UIPrintInteractionController__UIPrintInteractionControllerDelegate;
@class UIKit_UIProgressView_UIProgressViewAppearance;
@class UIKit_UIRefreshControl_UIRefreshControlAppearance;
@class UIKit_UISearchTextField_UISearchTextFieldAppearance;
@class UIKit_UISlider_UISliderAppearance;
@class UIKit_UISplitViewController__UISplitViewControllerDelegate;
@class UIKit_UIStackView_UIStackViewAppearance;
@class UIKit_UIStandardTextCursorView_UIStandardTextCursorViewAppearance;
@class UIKit_UIStepper_UIStepperAppearance;
@class UIKit_UISwitch_UISwitchAppearance;
@class UIKit_UITabBar__UITabBarDelegate;
@class UIKit_UITabBar_UITabBarAppearance;
@class UIKit_UITabBarController__UITabBarControllerDelegate;
@class UIKit_UITabBarItem_UITabBarItemAppearance;
@class UIKit_UITableViewHeaderFooterView_UITableViewHeaderFooterViewAppearance;
@class UIKit_UIVideoEditorController__UIVideoEditorControllerDelegate;
@class UIKit_UIVisualEffectView_UIVisualEffectViewAppearance;
@class UIKit_UIWebView__UIWebViewDelegate;
@class UIKit_UIWebView_UIWebViewAppearance;
@class UIKit_UIWindow_UIWindowAppearance;
@class EventKitUI_EKCalendarChooser__EKCalendarChooserDelegate;
@class EventKitUI_EKEventEditViewController__EKEventEditViewDelegate;
@class EventKitUI_EKEventEditViewController_EKEventEditViewControllerAppearance;
@class EventKitUI_EKEventViewController__EKEventViewDelegate;
@class CoreLocation_CLLocationManager__CLLocationManagerDelegate;
@class CoreBluetooth_CBCentralManager__CBCentralManagerDelegate;
@class CoreBluetooth_CBPeripheral__CBPeripheralDelegate;
@class CoreBluetooth_CBPeripheralManager__CBPeripheralManagerDelegate;
@class Foundation_NSKeyedArchiver__NSKeyedArchiverDelegate;
@class Foundation_NSKeyedUnarchiver__NSKeyedUnarchiverDelegate;
@class Foundation_NSNetService__NSNetServiceDelegate;
@class __NSObject_Disposer;
@class __XamarinObjectObserver;
@class Foundation_NSStream__NSStreamDelegate;
@class Foundation_NSCache__NSCacheDelegate;
@class Foundation_NSMetadataQuery__NSMetadataQueryDelegate;
@class Foundation_NSNetServiceBrowser__NSNetServiceBrowserDelegate;
@class CoreAnimation_CAAnimation__CAAnimationDelegate;
@class System_Net_Http_NSUrlSessionHandler_WrappedNSInputStream;
@class CarPlay_CPWindow_CPWindowAppearance;
@class BusinessChat_BCChatButton_BCChatButtonAppearance;
@class BrowserEngineKit_BELayerHierarchyHostingView_BELayerHierarchyHostingViewAppearance;
@class BrowserEngineKit_BEScrollView_BEScrollViewAppearance;
@class AuthenticationServices_ASAuthorizationAppleIdButton_ASAuthorizationAppleIdButtonAppearance;
@class AddressBookUI_ABPeoplePickerNavigationController_ABPeoplePickerNavigationControllerAppearance;
@class AVKit_AVRoutePickerView_AVRoutePickerViewAppearance;
@class AVFoundation_AVCaptureFileOutput_recordingProxy;
@class AVFoundation_AVSpeechSynthesizer__AVSpeechSynthesizerDelegate;
@class ARKit_ARCoachingOverlayView_ARCoachingOverlayViewAppearance;
@class ARKit_ARSCNView_ARSCNViewAppearance;
@class ARKit_ARSKView_ARSKViewAppearance;
@class System_Net_Http_NSUrlSessionHandler_NSUrlSessionHandlerDelegate;

@interface Microsoft_Maui_MauiUIApplicationDelegate : UIResponder<UIApplicationDelegate> {
}
	@property (nonatomic, assign) UIWindow * window;
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(UIWindow *) window;
	-(void) setWindow:(UIWindow *)p0;
	-(BOOL) application:(UIApplication *)p0 willFinishLaunchingWithOptions:(NSDictionary *)p1;
	-(BOOL) application:(UIApplication *)p0 didFinishLaunchingWithOptions:(NSDictionary *)p1;
	-(BOOL) respondsToSelector:(SEL)p0;
	-(UISceneConfiguration *) application:(UIApplication *)p0 configurationForConnectingSceneSession:(UISceneSession *)p1 options:(UISceneConnectionOptions *)p2;
	-(void) application:(UIApplication *)p0 performActionForShortcutItem:(UIApplicationShortcutItem *)p1 completionHandler:(void (^)(BOOL))p2;
	-(BOOL) application:(UIApplication *)p0 openURL:(NSURL *)p1 options:(NSDictionary *)p2;
	-(BOOL) application:(UIApplication *)p0 continueUserActivity:(NSUserActivity *)p1 restorationHandler:(void (^)(id *))p2;
	-(void) applicationDidBecomeActive:(UIApplication *)p0;
	-(void) applicationWillResignActive:(UIApplication *)p0;
	-(void) applicationWillTerminate:(UIApplication *)p0;
	-(void) applicationDidEnterBackground:(UIApplication *)p0;
	-(void) applicationWillEnterForeground:(UIApplication *)p0;
	-(void) applicationSignificantTimeChange:(UIApplication *)p0;
	-(void) application:(UIApplication *)p0 performFetchWithCompletionHandler:(void (^)(void *))p1;
	-(void) buildMenuWithBuilder:(id)p0;
	-(BOOL) canPerformAction:(SEL)p0 withSender:(NSObject *)p1;
	-(void) MenuItemSelected:(UICommand *)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface AppDelegate : Microsoft_Maui_MauiUIApplicationDelegate<UIApplicationDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUrlConnectionDelegate : NSObject<NSURLConnectionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUrlConnectionDataDelegate : NSObject<NSURLConnectionDataDelegate, NSURLConnectionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__WebKit_WKUIDelegate : NSObject<WKUIDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__WebKit_WKNavigationDelegate : NSObject<WKNavigationDelegate> {
}
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_UIContainerView : UIView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGSize) sizeThatFits:(CGSize)p0;
	-(void) willRemoveSubview:(UIView *)p0;
	-(void) addSubview:(UIView *)p0;
	-(void) layoutSubviews;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellItemRenderer : UITabBarController<UINavigationControllerDelegate> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(UIViewController *) selectedViewController;
	-(void) setSelectedViewController:(UIViewController *)p0;
	-(void) navigationController:(UINavigationController *)p0 didShowViewController:(UIViewController *)p1 animated:(BOOL)p2;
	-(void) viewDidLayoutSubviews;
	-(void) viewDidLoad;
	-(void) viewWillLayoutSubviews;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellTableViewController : UITableViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) viewDidLoad;
	-(void) viewSafeAreaInsetsDidChange;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_UIContainerCell : UITableViewCell {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) layoutSubviews;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_ItemsViewCell : UICollectionViewCell {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithFrame:(CGRect)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_TemplatedCell : Microsoft_Maui_Controls_Handlers_Items_ItemsViewCell {
}
	-(UICollectionViewLayoutAttributes *) preferredLayoutAttributesFittingAttributes:(UICollectionViewLayoutAttributes *)p0;
	-(void) prepareForReuse;
	-(BOOL) isSelected;
	-(void) setSelected:(BOOL)p0;
	-(id) initWithFrame:(CGRect)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_CarouselTemplatedCell : Microsoft_Maui_Controls_Handlers_Items_TemplatedCell {
}
	-(id) initWithFrame:(CGRect)p0;
@end

@interface Microsoft_iOS__UIKit_UICollectionViewDelegate : NSObject<UICollectionViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICollectionViewDelegateFlowLayout : NSObject<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_ItemsViewDelegator_2 : NSObject<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) scrollViewDidScroll:(UIScrollView *)p0;
	-(UIEdgeInsets) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 insetForSectionAtIndex:(void *)p2;
	-(CGFloat) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 minimumInteritemSpacingForSectionAtIndex:(void *)p2;
	-(CGFloat) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 minimumLineSpacingForSectionAtIndex:(void *)p2;
	-(void) collectionView:(UICollectionView *)p0 didEndDisplayingCell:(UICollectionViewCell *)p1 forItemAtIndexPath:(NSIndexPath *)p2;
	-(CGSize) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 sizeForItemAtIndexPath:(NSIndexPath *)p2;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_CarouselViewDelegator : Microsoft_Maui_Controls_Handlers_Items_ItemsViewDelegator_2<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(void) scrollViewDidScroll:(UIScrollView *)p0;
	-(void) scrollViewDidEndScrollingAnimation:(UIScrollView *)p0;
	-(void) scrollViewDidEndDecelerating:(UIScrollView *)p0;
	-(void) scrollViewWillBeginDragging:(UIScrollView *)p0;
	-(void) scrollViewDidEndDragging:(UIScrollView *)p0 willDecelerate:(BOOL)p1;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_ItemsViewLayout : UICollectionViewFlowLayout {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) flipsHorizontallyInOppositeLayoutDirection;
	-(BOOL) shouldInvalidateLayoutForPreferredLayoutAttributes:(UICollectionViewLayoutAttributes *)p0 withOriginalAttributes:(UICollectionViewLayoutAttributes *)p1;
	-(CGPoint) targetContentOffsetForProposedContentOffset:(CGPoint)p0 withScrollingVelocity:(CGPoint)p1;
	-(UICollectionViewLayoutInvalidationContext *) invalidationContextForPreferredLayoutAttributes:(UICollectionViewLayoutAttributes *)p0 withOriginalAttributes:(UICollectionViewLayoutAttributes *)p1;
	-(void) prepareLayout;
	-(void) prepareForCollectionViewUpdates:(NSArray *)p0;
	-(CGPoint) targetContentOffsetForProposedContentOffset:(CGPoint)p0;
	-(void) finalizeCollectionViewUpdates;
	-(BOOL) shouldInvalidateLayoutForBoundsChange:(CGRect)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_CarouselViewLayout : Microsoft_Maui_Controls_Handlers_Items_ItemsViewLayout {
}
	-(void) prepareForCollectionViewUpdates:(NSArray *)p0;
	-(void) finalizeCollectionViewUpdates;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_DefaultCell : Microsoft_Maui_Controls_Handlers_Items_ItemsViewCell {
}
	-(id) initWithFrame:(CGRect)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_GridViewLayout : Microsoft_Maui_Controls_Handlers_Items_ItemsViewLayout {
}
	-(CGSize) collectionViewContentSize;
	-(NSArray *) layoutAttributesForElementsInRect:(CGRect)p0;
	-(UICollectionViewLayoutInvalidationContext *) invalidationContextForPreferredLayoutAttributes:(UICollectionViewLayoutAttributes *)p0 withOriginalAttributes:(UICollectionViewLayoutAttributes *)p1;
@end

@protocol UICollectionViewSource<UICollectionViewDataSource, UICollectionViewDelegate>
@end

@interface Microsoft_Maui_Controls_Handlers_Items_ItemsViewController_1 : UICollectionViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(UICollectionViewCell *) collectionView:(UICollectionView *)p0 cellForItemAtIndexPath:(NSIndexPath *)p1;
	-(void *) collectionView:(UICollectionView *)p0 numberOfItemsInSection:(void *)p1;
	-(void) viewDidLoad;
	-(void) loadView;
	-(void) viewWillAppear:(BOOL)p0;
	-(void) viewWillLayoutSubviews;
	-(void *) numberOfSectionsInCollectionView:(UICollectionView *)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_StructuredItemsViewController_1 : Microsoft_Maui_Controls_Handlers_Items_ItemsViewController_1 {
}
	-(void) viewWillLayoutSubviews;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_SelectableItemsViewController_1 : Microsoft_Maui_Controls_Handlers_Items_StructuredItemsViewController_1 {
}
	-(void) collectionView:(UICollectionView *)p0 didSelectItemAtIndexPath:(NSIndexPath *)p1;
	-(void) collectionView:(UICollectionView *)p0 didDeselectItemAtIndexPath:(NSIndexPath *)p1;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_GroupableItemsViewController_1 : Microsoft_Maui_Controls_Handlers_Items_SelectableItemsViewController_1 {
}
	-(UICollectionReusableView *) collectionView:(UICollectionView *)p0 viewForSupplementaryElementOfKind:(NSString *)p1 atIndexPath:(NSIndexPath *)p2;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_SelectableItemsViewDelegator_2 : Microsoft_Maui_Controls_Handlers_Items_ItemsViewDelegator_2<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(void) collectionView:(UICollectionView *)p0 didSelectItemAtIndexPath:(NSIndexPath *)p1;
	-(void) collectionView:(UICollectionView *)p0 didDeselectItemAtIndexPath:(NSIndexPath *)p1;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_GroupableItemsViewDelegator_2 : Microsoft_Maui_Controls_Handlers_Items_SelectableItemsViewDelegator_2<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(CGSize) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 referenceSizeForHeaderInSection:(void *)p2;
	-(CGSize) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 referenceSizeForFooterInSection:(void *)p2;
	-(void) scrollViewDidEndScrollingAnimation:(UIScrollView *)p0;
	-(UIEdgeInsets) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 insetForSectionAtIndex:(void *)p2;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_ListViewLayout : Microsoft_Maui_Controls_Handlers_Items_ItemsViewLayout {
}
@end

@interface Microsoft_Maui_Controls_Handlers_Items_ReorderableItemsViewController_1 : Microsoft_Maui_Controls_Handlers_Items_GroupableItemsViewController_1 {
}
	-(BOOL) collectionView:(UICollectionView *)p0 canMoveItemAtIndexPath:(NSIndexPath *)p1;
	-(void) collectionView:(UICollectionView *)p0 moveItemAtIndexPath:(NSIndexPath *)p1 toIndexPath:(NSIndexPath *)p2;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_ReorderableItemsViewDelegator_2 : Microsoft_Maui_Controls_Handlers_Items_GroupableItemsViewDelegator_2<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(NSIndexPath *) collectionView:(UICollectionView *)p0 targetIndexPathForMoveFromItemAtIndexPath:(NSIndexPath *)p1 toProposedIndexPath:(NSIndexPath *)p2;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_ItemsViewDelegator2_2 : NSObject<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) scrollViewDidScroll:(UIScrollView *)p0;
	-(UIEdgeInsets) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 insetForSectionAtIndex:(void *)p2;
	-(CGFloat) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 minimumInteritemSpacingForSectionAtIndex:(void *)p2;
	-(CGFloat) collectionView:(UICollectionView *)p0 layout:(UICollectionViewLayout *)p1 minimumLineSpacingForSectionAtIndex:(void *)p2;
	-(void) collectionView:(UICollectionView *)p0 didEndDisplayingCell:(UICollectionViewCell *)p1 forItemAtIndexPath:(NSIndexPath *)p2;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_CarouselViewDelegator2 : Microsoft_Maui_Controls_Handlers_Items2_ItemsViewDelegator2_2<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(void) scrollViewDidScroll:(UIScrollView *)p0;
	-(void) scrollViewDidEndScrollingAnimation:(UIScrollView *)p0;
	-(void) scrollViewDidEndDecelerating:(UIScrollView *)p0;
	-(void) scrollViewWillBeginDragging:(UIScrollView *)p0;
	-(void) scrollViewDidEndDragging:(UIScrollView *)p0 willDecelerate:(BOOL)p1;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_ItemsViewCell2 : UICollectionViewCell {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithFrame:(CGRect)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_DefaultCell2 : Microsoft_Maui_Controls_Handlers_Items2_ItemsViewCell2 {
}
	-(id) initWithFrame:(CGRect)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_ItemsViewController2_1 : UICollectionViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(UICollectionViewCell *) collectionView:(UICollectionView *)p0 cellForItemAtIndexPath:(NSIndexPath *)p1;
	-(void *) collectionView:(UICollectionView *)p0 numberOfItemsInSection:(void *)p1;
	-(void) viewDidLoad;
	-(void) loadView;
	-(void) viewWillLayoutSubviews;
	-(void *) numberOfSectionsInCollectionView:(UICollectionView *)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_StructuredItemsViewController2_1 : Microsoft_Maui_Controls_Handlers_Items2_ItemsViewController2_1 {
}
	-(UICollectionReusableView *) collectionView:(UICollectionView *)p0 viewForSupplementaryElementOfKind:(NSString *)p1 atIndexPath:(NSIndexPath *)p2;
	-(void) viewWillLayoutSubviews;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_SelectableItemsViewController2_1 : Microsoft_Maui_Controls_Handlers_Items2_StructuredItemsViewController2_1 {
}
	-(void) collectionView:(UICollectionView *)p0 didSelectItemAtIndexPath:(NSIndexPath *)p1;
	-(void) collectionView:(UICollectionView *)p0 didDeselectItemAtIndexPath:(NSIndexPath *)p1;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_GroupableItemsViewController2_1 : Microsoft_Maui_Controls_Handlers_Items2_SelectableItemsViewController2_1 {
}
	-(UICollectionReusableView *) collectionView:(UICollectionView *)p0 viewForSupplementaryElementOfKind:(NSString *)p1 atIndexPath:(NSIndexPath *)p2;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_SelectableItemsViewDelegator2_2 : Microsoft_Maui_Controls_Handlers_Items2_ItemsViewDelegator2_2<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(void) collectionView:(UICollectionView *)p0 didSelectItemAtIndexPath:(NSIndexPath *)p1;
	-(void) collectionView:(UICollectionView *)p0 didDeselectItemAtIndexPath:(NSIndexPath *)p1;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_GroupableItemsViewDelegator2_2 : Microsoft_Maui_Controls_Handlers_Items2_SelectableItemsViewDelegator2_2<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(void) scrollViewDidEndScrollingAnimation:(UIScrollView *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_TemplatedCell2 : Microsoft_Maui_Controls_Handlers_Items2_ItemsViewCell2 {
}
	-(UICollectionViewLayoutAttributes *) preferredLayoutAttributesFittingAttributes:(UICollectionViewLayoutAttributes *)p0;
	-(void) prepareForReuse;
	-(BOOL) isSelected;
	-(void) setSelected:(BOOL)p0;
	-(id) initWithFrame:(CGRect)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_ReorderableItemsViewController2_1 : Microsoft_Maui_Controls_Handlers_Items2_GroupableItemsViewController2_1 {
}
	-(BOOL) collectionView:(UICollectionView *)p0 canMoveItemAtIndexPath:(NSIndexPath *)p1;
	-(void) collectionView:(UICollectionView *)p0 moveItemAtIndexPath:(NSIndexPath *)p1 toIndexPath:(NSIndexPath *)p2;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_ReorderableItemsViewDelegator2_2 : Microsoft_Maui_Controls_Handlers_Items2_GroupableItemsViewDelegator2_2<UICollectionViewDelegate, UICollectionViewDelegateFlowLayout> {
}
	-(NSIndexPath *) collectionView:(UICollectionView *)p0 targetIndexPathForMoveFromItemAtIndexPath:(NSIndexPath *)p1 toProposedIndexPath:(NSIndexPath *)p2;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_VisualElementRenderer_1 : UIView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_ViewRenderer_2 : Microsoft_Maui_Controls_Handlers_Compatibility_VisualElementRenderer_1 {
}
	-(void) layoutSubviews;
	-(void) sizeToFit;
	-(CGSize) sizeThatFits:(CGSize)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_ViewRenderer : Microsoft_Maui_Controls_Handlers_Compatibility_ViewRenderer_2 {
}
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_CellTableViewCell : UITableViewCell {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_FormsRefreshControl : UIRefreshControl {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) isHidden;
	-(void) setHidden:(BOOL)p0;
	-(void) beginRefreshing;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_iOS__UIKit_UIScrollViewDelegate : NSObject<UIScrollViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITableViewSource : NSObject<UIScrollViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_TableViewModelRenderer : NSObject<UIScrollViewDelegate> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(UITableViewCell *) tableView:(UITableView *)p0 cellForRowAtIndexPath:(NSIndexPath *)p1;
	-(CGFloat) tableView:(UITableView *)p0 heightForHeaderInSection:(void *)p1;
	-(UIView *) tableView:(UITableView *)p0 viewForHeaderInSection:(void *)p1;
	-(void) tableView:(UITableView *)p0 willDisplayHeaderView:(UIView *)p1 forSection:(void *)p2;
	-(void *) numberOfSectionsInTableView:(UITableView *)p0;
	-(void) tableView:(UITableView *)p0 didSelectRowAtIndexPath:(NSIndexPath *)p1;
	-(void *) tableView:(UITableView *)p0 numberOfRowsInSection:(void *)p1;
	-(NSArray *) sectionIndexTitlesForTableView:(UITableView *)p0;
	-(NSString *) tableView:(UITableView *)p0 titleForHeaderInSection:(void *)p1;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_UnEvenTableViewModelRenderer : Microsoft_Maui_Controls_Handlers_Compatibility_TableViewModelRenderer<UIScrollViewDelegate> {
}
	-(CGFloat) tableView:(UITableView *)p0 heightForRowAtIndexPath:(NSIndexPath *)p1;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_TableViewRenderer : Microsoft_Maui_Controls_Handlers_Compatibility_ViewRenderer_2 {
}
	-(void) layoutSubviews;
	-(void) traitCollectionDidChange:(UITraitCollection *)p0;
	-(id) init;
@end

@interface __UIGestureRecognizerToken : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellFlyoutContentRenderer : UIViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) viewWillLayoutSubviews;
	-(void) viewDidLayoutSubviews;
	-(void) viewDidLoad;
	-(void) viewWillAppear:(BOOL)p0;
	-(void) viewWillDisappear:(BOOL)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellFlyoutRenderer : UIViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) prefersHomeIndicatorAutoHidden;
	-(BOOL) prefersStatusBarHidden;
	-(NSInteger) preferredStatusBarUpdateAnimation;
	-(void) viewDidLayoutSubviews;
	-(void) viewWillAppear:(BOOL)p0;
	-(void) viewDidLoad;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellPageRendererTracker_TitleViewContainer : Microsoft_Maui_Controls_Platform_Compatibility_UIContainerView {
}
	-(CGRect) frame;
	-(void) setFrame:(CGRect)p0;
	-(void) layoutSubviews;
	-(void) willMoveToSuperview:(UIView *)p0;
	-(CGSize) intrinsicContentSize;
	-(CGSize) sizeThatFits:(CGSize)p0;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellSearchResultsRenderer : UITableViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(UITableViewCell *) tableView:(UITableView *)p0 cellForRowAtIndexPath:(NSIndexPath *)p1;
	-(void) tableView:(UITableView *)p0 didSelectRowAtIndexPath:(NSIndexPath *)p1;
	-(void *) numberOfSectionsInTableView:(UITableView *)p0;
	-(void *) tableView:(UITableView *)p0 numberOfRowsInSection:(void *)p1;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_iOS__UIKit_UIGestureRecognizerDelegate : NSObject<UIGestureRecognizerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UINavigationControllerDelegate : NSObject<UINavigationControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRenderer : UINavigationController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) navigationBar:(UINavigationBar *)p0 shouldPopItem:(UINavigationItem *)p1;
	-(BOOL) navigationBar:(UINavigationBar *)p0 didPopItem:(UINavigationItem *)p1;
	-(void) viewDidDisappear:(BOOL)p0;
	-(void) viewWillAppear:(BOOL)p0;
	-(void) viewDidLayoutSubviews;
	-(void) viewDidLoad;
	-(void) viewDidAppear:(BOOL)p0;
	-(NSArray *) popToRootViewControllerAnimated:(BOOL)p0;
	-(NSArray *) viewControllers;
	-(void) setViewControllers:(NSArray *)p0;
	-(NSArray *) popToViewController:(UIViewController *)p0 animated:(BOOL)p1;
	-(void) pushViewController:(UIViewController *)p0 animated:(BOOL)p1;
	-(UIViewController *) popViewControllerAnimated:(BOOL)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRootHeader_ShellSectionHeaderCell : UICollectionViewCell {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) isSelected;
	-(void) setSelected:(BOOL)p0;
	-(void) layoutSubviews;
	-(CGSize) sizeThatFits:(CGSize)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithFrame:(CGRect)p0;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRootHeader : UICollectionViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) collectionView:(UICollectionView *)p0 canMoveItemAtIndexPath:(NSIndexPath *)p1;
	-(UICollectionViewCell *) collectionView:(UICollectionView *)p0 cellForItemAtIndexPath:(NSIndexPath *)p1;
	-(void *) collectionView:(UICollectionView *)p0 numberOfItemsInSection:(void *)p1;
	-(void) collectionView:(UICollectionView *)p0 didDeselectItemAtIndexPath:(NSIndexPath *)p1;
	-(void) collectionView:(UICollectionView *)p0 didSelectItemAtIndexPath:(NSIndexPath *)p1;
	-(void *) numberOfSectionsInCollectionView:(UICollectionView *)p0;
	-(BOOL) collectionView:(UICollectionView *)p0 shouldSelectItemAtIndexPath:(NSIndexPath *)p1;
	-(void) viewDidLayoutSubviews;
	-(void) viewDidLoad;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellSectionRootRenderer : UIViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) viewDidLayoutSubviews;
	-(void) viewWillTransitionToSize:(CGSize)p0 withTransitionCoordinator:(id)p1;
	-(void) viewDidLoad;
	-(void) viewWillAppear:(BOOL)p0;
	-(void) viewSafeAreaInsetsDidChange;
	-(void) traitCollectionDidChange:(UITraitCollection *)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Platform_Compatibility_ShellTableViewSource : NSObject<UIScrollViewDelegate> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGFloat) tableView:(UITableView *)p0 heightForRowAtIndexPath:(NSIndexPath *)p1;
	-(UITableViewCell *) tableView:(UITableView *)p0 cellForRowAtIndexPath:(NSIndexPath *)p1;
	-(CGFloat) tableView:(UITableView *)p0 heightForFooterInSection:(void *)p1;
	-(UIView *) tableView:(UITableView *)p0 viewForFooterInSection:(void *)p1;
	-(void *) numberOfSectionsInTableView:(UITableView *)p0;
	-(void) tableView:(UITableView *)p0 didSelectRowAtIndexPath:(NSIndexPath *)p1;
	-(void *) tableView:(UITableView *)p0 numberOfRowsInSection:(void *)p1;
	-(void) scrollViewDidScroll:(UIScrollView *)p0;
	-(void) tableView:(UITableView *)p0 willDisplayCell:(UITableViewCell *)p1 forRowAtIndexPath:(NSIndexPath *)p2;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Controls_Handlers_Items_CarouselViewController : Microsoft_Maui_Controls_Handlers_Items_ItemsViewController_1 {
}
	-(UICollectionViewCell *) collectionView:(UICollectionView *)p0 cellForItemAtIndexPath:(NSIndexPath *)p1;
	-(void *) collectionView:(UICollectionView *)p0 numberOfItemsInSection:(void *)p1;
	-(void) viewDidLoad;
	-(void) viewWillLayoutSubviews;
	-(void) viewDidLayoutSubviews;
	-(void) scrollViewWillBeginDragging:(UIScrollView *)p0;
	-(void) scrollViewDidEndDragging:(UIScrollView *)p0 willDecelerate:(BOOL)p1;
@end

@interface Microsoft_Maui_Controls_Handlers_Items2_CarouselViewController2 : Microsoft_Maui_Controls_Handlers_Items2_ItemsViewController2_1 {
}
	-(UICollectionViewCell *) collectionView:(UICollectionView *)p0 cellForItemAtIndexPath:(NSIndexPath *)p1;
	-(void *) collectionView:(UICollectionView *)p0 numberOfItemsInSection:(void *)p1;
	-(void) viewDidLoad;
	-(void) viewWillLayoutSubviews;
	-(void) viewDidLayoutSubviews;
	-(void) scrollViewWillBeginDragging:(UIScrollView *)p0;
	-(void) scrollViewDidEndDragging:(UIScrollView *)p0 willDecelerate:(BOOL)p1;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_PhoneFlyoutPageRenderer : UIViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) viewDidAppear:(BOOL)p0;
	-(void) viewDidDisappear:(BOOL)p0;
	-(void) viewDidLayoutSubviews;
	-(void) viewWillLayoutSubviews;
	-(void) viewDidLoad;
	-(void) viewWillTransitionToSize:(CGSize)p0 withTransitionCoordinator:(id)p1;
	-(UIViewController *) childViewControllerForStatusBarHidden;
	-(UIViewController *) childViewControllerForHomeIndicatorAutoHidden;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiView : UIView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) safeAreaInsetsDidChange;
	-(CGSize) sizeThatFits:(CGSize)p0;
	-(void) layoutSubviews;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_ContentView : Microsoft_Maui_Platform_MauiView {
}
	-(void) layoutSubviews;
	-(void) willRemoveSubview:(UIView *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_FrameRenderer : Microsoft_Maui_Controls_Handlers_Compatibility_VisualElementRenderer_1 {
}
	-(void) addSubview:(UIView *)p0;
	-(void) traitCollectionDidChange:(UITraitCollection *)p0;
	-(void) layoutSubviews;
	-(CGSize) sizeThatFits:(CGSize)p0;
	-(void) drawRect:(CGRect)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_ListViewRenderer : Microsoft_Maui_Controls_Handlers_Compatibility_ViewRenderer_2 {
}
	-(void) layoutSubviews;
	-(void) traitCollectionDidChange:(UITraitCollection *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_NavigationRenderer : UINavigationController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) didRotateFromInterfaceOrientation:(NSInteger)p0;
	-(NSArray *) popToRootViewControllerAnimated:(BOOL)p0;
	-(UIViewController *) popViewControllerAnimated:(BOOL)p0;
	-(void) viewDidAppear:(BOOL)p0;
	-(void) viewWillAppear:(BOOL)p0;
	-(void) viewDidDisappear:(BOOL)p0;
	-(void) viewWillLayoutSubviews;
	-(void) viewDidLoad;
	-(void) traitCollectionDidChange:(UITraitCollection *)p0;
	-(BOOL) navigationBar:(UINavigationBar *)p0 shouldPopItem:(UINavigationItem *)p1;
	-(BOOL) navigationBar:(UINavigationBar *)p0 didPopItem:(UINavigationItem *)p1;
	-(UIViewController *) childViewControllerForStatusBarHidden;
	-(UIViewController *) childViewControllerForHomeIndicatorAutoHidden;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_ShellRenderer : UIViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) prefersHomeIndicatorAutoHidden;
	-(BOOL) prefersStatusBarHidden;
	-(NSInteger) preferredStatusBarUpdateAnimation;
	-(void) viewDidLayoutSubviews;
	-(void) viewDidLoad;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_TabbedRenderer : UITabBarController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(UIViewController *) selectedViewController;
	-(void) setSelectedViewController:(UIViewController *)p0;
	-(void) didRotateFromInterfaceOrientation:(NSInteger)p0;
	-(void) viewDidAppear:(BOOL)p0;
	-(void) viewDidDisappear:(BOOL)p0;
	-(void) viewDidLayoutSubviews;
	-(UIViewController *) childViewControllerForStatusBarHidden;
	-(UIViewController *) childViewControllerForHomeIndicatorAutoHidden;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIContextMenuInteractionDelegate : NSObject<UIContextMenuInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_Maui_Controls_Handlers_Compatibility_EntryCellRenderer_EntryCellTableViewCell : Microsoft_Maui_Controls_Handlers_Compatibility_CellTableViewCell {
}
	-(void) layoutSubviews;
@end

@interface Microsoft_Maui_Platform_ContainerViewController : UIViewController {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) loadView;
	-(void) viewDidLayoutSubviews;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_LayoutView : Microsoft_Maui_Platform_MauiView {
}
	-(void) didAddSubview:(UIView *)p0;
	-(void) willRemoveSubview:(UIView *)p0;
	-(UIView *) hitTest:(CGPoint)p0 withEvent:(UIEvent *)p1;
	-(BOOL) isUserInteractionEnabled;
	-(void) setUserInteractionEnabled:(BOOL)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiActivityIndicator : UIActivityIndicatorView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) drawRect:(CGRect)p0;
	-(void) layoutSubviews;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface PlatformGraphicsView : UIView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) willMoveToSuperview:(UIView *)p0;
	-(void) drawRect:(CGRect)p0;
	-(CGRect) bounds;
	-(void) setBounds:(CGRect)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Platform_MauiBoxView : PlatformGraphicsView {
}
	-(void) didMoveToWindow;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiCALayer : CALayer {
}
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) removeFromSuperlayer;
	-(void) addAnimation:(CAAnimation *)p0 forKey:(NSString *)p1;
	-(void) layoutSublayers;
	-(void) drawInContext:(id)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiImageView : UIImageView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiLabel : UILabel {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) drawTextInRect:(CGRect)p0;
	-(CGSize) sizeThatFits:(CGSize)p0;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiPageControl : UIPageControl {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) layoutSubviews;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_NoCaretField : UITextField {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGRect) caretRectForPosition:(UITextPosition *)p0;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiPicker : Microsoft_Maui_Platform_NoCaretField {
}
	-(BOOL) canPerformAction:(SEL)p0 withSender:(NSObject *)p1;
@end

@interface Microsoft_Maui_Platform_MauiRefreshView : Microsoft_Maui_Platform_MauiView {
}
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiScrollView : UIScrollView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) layoutSubviews;
	-(CGSize) sizeThatFits:(CGSize)p0;
	-(void) scrollRectToVisible:(CGRect)p0 animated:(BOOL)p1;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiSearchBar : UISearchBar {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(NSString *) text;
	-(void) setText:(NSString *)p0;
	-(void) willMoveToWindow:(UIWindow *)p0;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiShapeView : PlatformGraphicsView {
}
	-(void) didMoveToWindow;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiTextField : UITextField {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) willMoveToWindow:(UIWindow *)p0;
	-(NSString *) text;
	-(void) setText:(NSString *)p0;
	-(NSAttributedString *) attributedText;
	-(void) setAttributedText:(NSAttributedString *)p0;
	-(UITextRange *) selectedTextRange;
	-(void) setSelectedTextRange:(UITextRange *)p0;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiTextView : UITextView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) willMoveToWindow:(UIWindow *)p0;
	-(NSString *) text;
	-(void) setText:(NSString *)p0;
	-(UIFont *) font;
	-(void) setFont:(UIFont *)p0;
	-(NSAttributedString *) attributedText;
	-(void) setAttributedText:(NSAttributedString *)p0;
	-(void) layoutSubviews;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiWebViewNavigationDelegate : NSObject<WKNavigationDelegate> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) webView:(WKWebView *)p0 didFinishNavigation:(WKNavigation *)p1;
	-(void) webView:(WKWebView *)p0 didFailNavigation:(WKNavigation *)p1 withError:(NSError *)p2;
	-(void) webView:(WKWebView *)p0 didFailProvisionalNavigation:(WKNavigation *)p1 withError:(NSError *)p2;
	-(void) webView:(WKWebView *)p0 decidePolicyForNavigationAction:(WKNavigationAction *)p1 decisionHandler:(void (^)(void *))p2;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Platform_PageViewController : Microsoft_Maui_Platform_ContainerViewController {
}
	-(BOOL) prefersHomeIndicatorAutoHidden;
	-(BOOL) prefersStatusBarHidden;
	-(NSInteger) preferredStatusBarUpdateAnimation;
	-(void) traitCollectionDidChange:(UITraitCollection *)p0;
@end

@interface Microsoft_Maui_Platform_WrapperView : UIView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) layoutSubviews;
	-(CGSize) sizeThatFits:(CGSize)p0;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@protocol UIPickerViewModel<UIPickerViewDataSource, UIPickerViewDelegate>
@end

@interface Microsoft_iOS__UIKit_UIPickerViewModel : NSObject<UIPickerViewDataSource, UIPickerViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_Maui_Handlers_PickerSource : NSObject<UIPickerViewDataSource, UIPickerViewDelegate> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void *) numberOfComponentsInPickerView:(UIPickerView *)p0;
	-(void *) pickerView:(UIPickerView *)p0 numberOfRowsInComponent:(void *)p1;
	-(NSString *) pickerView:(UIPickerView *)p0 titleForRow:(void *)p1 forComponent:(void *)p2;
	-(void) pickerView:(UIPickerView *)p0 didSelectRow:(void *)p1 inComponent:(void *)p2;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Handlers_SwipeItemButton : UIButton {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGRect) frame;
	-(void) setFrame:(CGRect)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_MauiUISceneDelegate : UIResponder<UIWindowSceneDelegate> {
}
	@property (nonatomic, assign) UIWindow * window;
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(UIWindow *) window;
	-(void) setWindow:(UIWindow *)p0;
	-(void) scene:(UIScene *)p0 willConnectToSession:(UISceneSession *)p1 options:(UISceneConnectionOptions *)p2;
	-(void) sceneDidDisconnect:(UIScene *)p0;
	-(NSUserActivity *) stateRestorationActivityForScene:(UIScene *)p0;
	-(void) sceneWillEnterForeground:(UIScene *)p0;
	-(void) sceneDidBecomeActive:(UIScene *)p0;
	-(void) sceneWillResignActive:(UIScene *)p0;
	-(void) sceneDidEnterBackground:(UIScene *)p0;
	-(BOOL) scene:(UIScene *)p0 openURLContexts:(NSSet <UIOpenURLContext *>*)p1;
	-(BOOL) scene:(UIScene *)p0 continueUserActivity:(NSUserActivity *)p1;
	-(void) scene:(UIScene *)p0 willContinueUserActivityWithType:(NSString *)p1;
	-(void) scene:(UIScene *)p0 didFailToContinueUserActivityWithType:(NSString *)p1 error:(NSError *)p2;
	-(void) scene:(UIScene *)p0 didUpdateUserActivity:(NSUserActivity *)p1;
	-(void) scene:(UIScene *)p0 restoreInteractionStateWithUserActivity:(NSUserActivity *)p1;
	-(void) windowScene:(UIWindowScene *)p0 didUpdateCoordinateSpace:(id)p1 interfaceOrientation:(NSInteger)p2 traitCollection:(UITraitCollection *)p3;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiCheckBox : UIButton {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) isEnabled;
	-(void) setEnabled:(BOOL)p0;
	-(CGSize) sizeThatFits:(CGSize)p0;
	-(void) layoutSubviews;
	-(long long) accessibilityTraits;
	-(void) setAccessibilityTraits:(long long)p0;
	-(NSString *) accessibilityValue;
	-(void) setAccessibilityValue:(NSString *)p0;
	-(void) didMoveToWindow;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiDatePicker : Microsoft_Maui_Platform_NoCaretField {
}
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiHybridWebView : WKWebView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Platform_MauiSwipeView : Microsoft_Maui_Platform_ContentView {
}
	-(void) layoutSubviews;
	-(void) touchesEnded:(NSSet *)p0 withEvent:(UIEvent *)p1;
	-(void) touchesCancelled:(NSSet *)p0 withEvent:(UIEvent *)p1;
	-(UIView *) hitTest:(CGPoint)p0 withEvent:(UIEvent *)p1;
	-(id) init;
@end

@interface Microsoft_Maui_Platform_MauiTimePicker : Microsoft_Maui_Platform_NoCaretField {
}
@end

@interface Microsoft_Maui_Platform_MauiWebViewUIDelegate : NSObject<WKUIDelegate> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) webView:(WKWebView *)p0 contextMenuConfigurationForElement:(WKContextMenuElementInfo *)p1 completionHandler:(void (^)(void *))p2;
	-(void) webView:(WKWebView *)p0 runJavaScriptAlertPanelWithMessage:(NSString *)p1 initiatedByFrame:(WKFrameInfo *)p2 completionHandler:(void (^)())p3;
	-(void) webView:(WKWebView *)p0 runJavaScriptConfirmPanelWithMessage:(NSString *)p1 initiatedByFrame:(WKFrameInfo *)p2 completionHandler:(void (^)(void *))p3;
	-(void) webView:(WKWebView *)p0 runJavaScriptTextInputPanelWithPrompt:(NSString *)p1 defaultText:(NSString *)p2 initiatedByFrame:(WKFrameInfo *)p3 completionHandler:(void (^)(void *))p4;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Platform_MauiWKWebView : WKWebView {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(void) didMoveToWindow;
	-(void) webViewWebContentProcessDidTerminate:(WKWebView *)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_Maui_Platform_PlatformTouchGraphicsView : PlatformGraphicsView {
}
	-(void) layoutSubviews;
	-(void) touchesBegan:(NSSet *)p0 withEvent:(UIEvent *)p1;
	-(void) touchesMoved:(NSSet *)p0 withEvent:(UIEvent *)p1;
	-(void) touchesEnded:(NSSet *)p0 withEvent:(UIEvent *)p1;
	-(void) touchesCancelled:(NSSet *)p0 withEvent:(UIEvent *)p1;
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIAdaptivePresentationControllerDelegate : NSObject<UIAdaptivePresentationControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIActivityItemSource : NSObject<UIActivityItemSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreLocation_CLLocationManagerDelegate : NSObject<CLLocationManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SafariServices_SFSafariViewControllerDelegate : NSObject<SFSafariViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIImagePickerControllerDelegate : NSObject<UIImagePickerControllerDelegate, UINavigationControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIDocumentPickerDelegate : NSObject<UIDocumentPickerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ContactsUI_CNContactPickerDelegate : NSObject<CNContactPickerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__VisionKit_VNDocumentCameraViewControllerDelegate : NSObject<VNDocumentCameraViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__WatchConnectivity_WCSessionDelegate : NSObject<WCSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ShazamKit_SHSessionDelegate : NSObject<SHSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SharedWithYou_SWCollaborationViewDelegate : NSObject<SWCollaborationViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SharedWithYou_SWHighlightCenterDelegate : NSObject<SWHighlightCenterDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SafetyKit_SACrashDetectionDelegate : NSObject<SACrashDetectionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SafetyKit_SAEmergencyResponseDelegate : NSObject<SAEmergencyResponseDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__QuickLook_QLPreviewControllerDataSource : NSObject<QLPreviewControllerDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__QuickLook_QLPreviewControllerDelegate : NSObject<QLPreviewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__QuickLook_QLPreviewItem : NSObject<QLPreviewItem> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PushKit_PKPushRegistryDelegate : NSObject<PKPushRegistryDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PhotosUI_PHLivePhotoViewDelegate : NSObject<PHLivePhotoViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PhotosUI_PHPickerViewControllerDelegate : NSObject<PHPickerViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PencilKit_PKCanvasViewDelegate : NSObject<PKCanvasViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PencilKit_PKToolPickerDelegate : NSObject<PKToolPickerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__NotificationCenter_NCWidgetProviding : NSObject<NCWidgetProviding> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Messages_MSStickerBrowserViewDataSource : NSObject<MSStickerBrowserViewDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__IntentsUI_INUIAddVoiceShortcutButtonDelegate : NSObject<INUIAddVoiceShortcutButtonDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__IntentsUI_INUIAddVoiceShortcutViewControllerDelegate : NSObject<INUIAddVoiceShortcutViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__IntentsUI_INUIEditVoiceShortcutViewControllerDelegate : NSObject<INUIEditVoiceShortcutViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ExternalAccessory_EAAccessoryDelegate : NSObject<EAAccessoryDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ExternalAccessory_EAWiFiUnconfiguredAccessoryBrowserDelegate : NSObject<EAWiFiUnconfiguredAccessoryBrowserDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreNFC_NFCNdefReaderSessionDelegate : NSObject<NFCNDEFReaderSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreNFC_NFCReaderSessionDelegate : NSObject<NFCReaderSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreNFC_NFCTagReaderSessionDelegate : NSObject<NFCTagReaderSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreNFC_NFCVasReaderSessionDelegate : NSObject<NFCVASReaderSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ContactsUI_CNContactViewControllerDelegate : NSObject<CNContactViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AutomaticAssessmentConfiguration_AEAssessmentSessionDelegate : NSObject<AEAssessmentSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__VideoSubscriberAccount_VSAccountManagerDelegate : NSObject<VSAccountManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UserNotifications_UNUserNotificationCenterDelegate : NSObject<UNUserNotificationCenterDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__StoreKit_SKCloudServiceSetupViewControllerDelegate : NSObject<SKCloudServiceSetupViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__StoreKit_SKOverlayDelegate : NSObject<SKOverlayDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__StoreKit_SKPaymentQueueDelegate : NSObject<SKPaymentQueueDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__StoreKit_SKPaymentTransactionObserver : NSObject<SKPaymentTransactionObserver> {
}
	-(id) init;
@end

@interface Microsoft_iOS__StoreKit_SKRequestDelegate : NSObject<SKRequestDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__StoreKit_SKProductsRequestDelegate : NSObject<SKProductsRequestDelegate, SKRequestDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__StoreKit_SKStoreProductViewControllerDelegate : NSObject<SKStoreProductViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SpriteKit_SKPhysicsContactDelegate : NSObject<SKPhysicsContactDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SpriteKit_SKSceneDelegate : NSObject<SKSceneDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SpriteKit_SKViewDelegate : NSObject<SKViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Speech_SFSpeechRecognitionTaskDelegate : NSObject<SFSpeechRecognitionTaskDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Speech_SFSpeechRecognizerDelegate : NSObject<SFSpeechRecognizerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SensorKit_SRSensorReaderDelegate : NSObject<SRSensorReaderDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNAnimatable : NSObject<SCNAnimatable> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNActionable : NSObject<SCNActionable> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNAvoidOccluderConstraintDelegate : NSObject<SCNAvoidOccluderConstraintDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNBoundingVolume : NSObject<SCNBoundingVolume> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNCameraControllerDelegate : NSObject<SCNCameraControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNNodeRendererDelegate : NSObject<SCNNodeRendererDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNPhysicsContactDelegate : NSObject<SCNPhysicsContactDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNProgramDelegate : NSObject<SCNProgramDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNSceneExportDelegate : NSObject<SCNSceneExportDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNSceneRenderer : NSObject<SCNSceneRenderer> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNSceneRendererDelegate : NSObject<SCNSceneRendererDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNShadable : NSObject<SCNShadable> {
}
	-(id) init;
@end

@interface Microsoft_iOS__SceneKit_SCNTechniqueSupport : NSObject<SCNTechniqueSupport> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ReplayKit_RPBroadcastActivityViewControllerDelegate : NSObject<RPBroadcastActivityViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ReplayKit_RPBroadcastControllerDelegate : NSObject<RPBroadcastControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ReplayKit_RPPreviewViewControllerDelegate : NSObject<RPPreviewViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ReplayKit_RPScreenRecorderDelegate : NSObject<RPScreenRecorderDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PushToTalk_PTChannelManagerDelegate : NSObject<PTChannelManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PushToTalk_PTChannelRestorationDelegate : NSObject<PTChannelRestorationDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Photos_PHPhotoLibraryChangeObserver : NSObject<PHPhotoLibraryChangeObserver> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PdfKit_PdfDocumentDelegate : NSObject<PDFDocumentDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PdfKit_PdfViewDelegate : NSObject<PDFViewDelegate> {
}
	-(id) init;
@end

@interface PassKit_PKDisbursementVoucher : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_iOS__PassKit_PKAddPassesViewControllerDelegate : NSObject<PKAddPassesViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PassKit_PKAddPaymentPassViewControllerDelegate : NSObject<PKAddPaymentPassViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PassKit_PKAddSecureElementPassViewControllerDelegate : NSObject<PKAddSecureElementPassViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PassKit_PKPayLaterViewDelegate : NSObject<PKPayLaterViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PassKit_PKPaymentAuthorizationControllerDelegate : NSObject<PKPaymentAuthorizationControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PassKit_PKPaymentAuthorizationViewControllerDelegate : NSObject<PKPaymentAuthorizationViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PassKit_PKShareSecureElementPassViewControllerDelegate : NSObject<PKShareSecureElementPassViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__PassKit_PKVehicleConnectionDelegate : NSObject<PKVehicleConnectionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__NetworkExtension_NEAppPushDelegate : NSObject<NEAppPushDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__NetworkExtension_NWTcpConnectionAuthenticationDelegate : NSObject<NWTCPConnectionAuthenticationDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__NearbyInteraction_NISessionDelegate : NSObject<NISessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MultipeerConnectivity_MCAdvertiserAssistantDelegate : NSObject<MCAdvertiserAssistantDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MultipeerConnectivity_MCBrowserViewControllerDelegate : NSObject<MCBrowserViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MultipeerConnectivity_MCNearbyServiceAdvertiserDelegate : NSObject<MCNearbyServiceAdvertiserDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MultipeerConnectivity_MCNearbyServiceBrowserDelegate : NSObject<MCNearbyServiceBrowserDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MultipeerConnectivity_MCSessionDelegate : NSObject<MCSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ModelIO_MDLLightProbeIrradianceDataSource : NSObject<MDLLightProbeIrradianceDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MetalPerformanceShaders_MPSCnnBatchNormalizationDataSource : NSObject<MPSCNNBatchNormalizationDataSource> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface Microsoft_iOS__MetalPerformanceShaders_MPSCnnConvolutionDataSource : NSObject<MPSCNNConvolutionDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MetalPerformanceShaders_MPSCnnInstanceNormalizationDataSource : NSObject<MPSCNNInstanceNormalizationDataSource> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface Microsoft_iOS__MetalKit_MTKViewDelegate : NSObject<MTKViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Metal_MTLCaptureScope : NSObject<MTLCaptureScope> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Metal_MTLDrawable : NSObject<MTLDrawable> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MessageUI_MFMailComposeViewControllerDelegate : NSObject<MFMailComposeViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MessageUI_MFMessageComposeViewControllerDelegate : NSObject<MFMessageComposeViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MediaPlayer_MPMediaPickerControllerDelegate : NSObject<MPMediaPickerControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MediaPlayer_MPNowPlayingSessionDelegate : NSObject<MPNowPlayingSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MediaPlayer_MPPlayableContentDataSource : NSObject<MPPlayableContentDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MediaPlayer_MPPlayableContentDelegate : NSObject<MPPlayableContentDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MapKit_MKLocalSearchCompleterDelegate : NSObject<MKLocalSearchCompleterDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MapKit_MKLookAroundViewControllerDelegate : NSObject<MKLookAroundViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MapKit_MKMapItemDetailViewControllerDelegate : NSObject<MKMapItemDetailViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MapKit_MKMapViewDelegate : NSObject<MKMapViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MapKit_MKAnnotation : NSObject<MKAnnotation> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MapKit_MKOverlay : NSObject<MKAnnotation, MKOverlay> {
}
	-(id) init;
@end

@interface Microsoft_iOS__MapKit_MKReverseGeocoderDelegate : NSObject<MKReverseGeocoderDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__LocalAuthentication_LAEnvironmentObserver : NSObject<LAEnvironmentObserver> {
}
	-(id) init;
@end

@interface Microsoft_iOS__JavaScriptCore_JSExport : NSObject<JSExport> {
}
	-(id) init;
@end

@interface Microsoft_iOS__HomeKit_HMAccessoryBrowserDelegate : NSObject<HMAccessoryBrowserDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__HomeKit_HMAccessoryDelegate : NSObject<HMAccessoryDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__HomeKit_HMCameraSnapshotControlDelegate : NSObject<HMCameraSnapshotControlDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__HomeKit_HMCameraStreamControlDelegate : NSObject<HMCameraStreamControlDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__HomeKit_HMHomeDelegate : NSObject<HMHomeDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__HomeKit_HMHomeManagerDelegate : NSObject<HMHomeManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__HomeKit_HMNetworkConfigurationProfileDelegate : NSObject<HMNetworkConfigurationProfileDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__HealthKit_HKWorkoutSessionDelegate : NSObject<HKWorkoutSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameplayKit_GKAgentDelegate : NSObject<GKAgentDelegate> {
}
	-(id) init;
@end

@interface GameKit_GKPeerPickerControllerDelegate : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface GameKit_GKPeerPickerController : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKSessionDelegate : NSObject<GKSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKAchievementViewControllerDelegate : NSObject<GKAchievementViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKChallengeEventHandlerDelegate : NSObject<GKChallengeEventHandlerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKChallengeListener : NSObject<GKChallengeListener> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKFriendRequestComposeViewControllerDelegate : NSObject<GKFriendRequestComposeViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKGameCenterControllerDelegate : NSObject<GKGameCenterControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKInviteEventListener : NSObject<GKInviteEventListener> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKLeaderboardViewControllerDelegate : NSObject<GKLeaderboardViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKLocalPlayerListener : NSObject<GKLocalPlayerListener> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKMatchDelegate : NSObject<GKMatchDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKMatchmakerViewControllerDelegate : NSObject<GKMatchmakerViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKSavedGameListener : NSObject<GKSavedGameListener> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKTurnBasedEventHandlerDelegate : NSObject<GKTurnBasedEventHandlerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKTurnBasedEventListener : NSObject<GKTurnBasedEventListener> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKTurnBasedMatchmakerViewControllerDelegate : NSObject<GKTurnBasedMatchmakerViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameKit_GKVoiceChatClient : NSObject<GKVoiceChatClient> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GameController_GCGameControllerSceneDelegate : NSObject<GCGameControllerSceneDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GLKit_GLKNamedEffect : NSObject<GLKNamedEffect> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GLKit_GLKViewControllerDelegate : NSObject<GLKViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__GLKit_GLKViewDelegate : NSObject<GLKViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__WebKit_WKDownloadDelegate : NSObject<WKDownloadDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__WebKit_WKScriptMessageHandler : NSObject<WKScriptMessageHandler> {
}
	-(id) init;
@end

@interface Microsoft_iOS__WebKit_WKWebExtensionControllerDelegate : NSObject<WKWebExtensionControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_NSLayoutManagerDelegate : NSObject<NSLayoutManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_NSTextAttachmentContainer : NSObject<NSTextAttachmentContainer> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_NSTextContentManagerDelegate : NSObject<NSTextContentManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_NSTextContentStorageDelegate : NSObject<NSTextContentStorageDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_NSTextLayoutManagerDelegate : NSObject<NSTextLayoutManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_NSTextSelectionDataSource : NSObject<NSTextSelectionDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_NSTextStorageDelegate : NSObject<NSTextStorageDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_NSTextViewportLayoutControllerDelegate : NSObject<NSTextViewportLayoutControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIAccelerometerDelegate : NSObject<UIAccelerometerDelegate> {
}
	-(id) init;
@end

@protocol UIAccessibilityContainer
	@required -(void *) accessibilityElementCount;
	@required -(NSObject *) accessibilityElementAtIndex:(void *)p0;
	@required -(void *) indexOfAccessibilityElement:(NSObject *)p0;
	@required -(NSObject *) accessibilityElements;
	@required -(void) setAccessibilityElements:(NSObject *)p0;
	@required -(NSInteger) accessibilityContainerType;
	@required -(void) setAccessibilityContainerType:(NSInteger)p0;
@end

@interface Microsoft_iOS__UIKit_UIAccessibilityContainerDataTable : NSObject<UIAccessibilityContainerDataTable> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIActionSheetDelegate : NSObject<UIActionSheetDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIAlertViewDelegate : NSObject<UIAlertViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIAppearanceContainer : NSObject<UIAppearanceContainer> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIApplicationDelegate : NSObject<UIApplicationDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIBarPositioning : NSObject<UIBarPositioning> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIBarPositioningDelegate : NSObject<UIBarPositioningDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICalendarSelectionMultiDateDelegate : NSObject<UICalendarSelectionMultiDateDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICalendarSelectionSingleDateDelegate : NSObject<UICalendarSelectionSingleDateDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICalendarSelectionWeekOfYearDelegate : NSObject<UICalendarSelectionWeekOfYearDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICalendarViewDelegate : NSObject<UICalendarViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICGFloatTraitDefinition : NSObject<UICGFloatTraitDefinition> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICloudSharingControllerDelegate : NSObject<UICloudSharingControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICollectionViewDataSource : NSObject<UICollectionViewDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICollectionViewDragDelegate : NSObject<UICollectionViewDragDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICollectionViewDropDelegate : NSObject<UICollectionViewDropDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICollectionViewSource : NSObject<UICollectionViewDataSource, UICollectionViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICollisionBehaviorDelegate : NSObject<UICollisionBehaviorDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIColorPickerViewControllerDelegate : NSObject<UIColorPickerViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIContentContainer : NSObject<UIContentContainer> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UICoordinateSpace : NSObject<UICoordinateSpace> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIDocumentBrowserViewControllerDelegate : NSObject<UIDocumentBrowserViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIDocumentInteractionControllerDelegate : NSObject<UIDocumentInteractionControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIDocumentMenuDelegate : NSObject<UIDocumentMenuDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIDragInteractionDelegate : NSObject<UIDragInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIDropInteractionDelegate : NSObject<UIDropInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIDynamicAnimatorDelegate : NSObject<UIDynamicAnimatorDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIDynamicItem : NSObject<UIDynamicItem> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIEditMenuInteractionDelegate : NSObject<UIEditMenuInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIFindInteractionDelegate : NSObject<UIFindInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIFontPickerViewControllerDelegate : NSObject<UIFontPickerViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIIndirectScribbleInteractionDelegate : NSObject<UIIndirectScribbleInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UILargeContentViewerInteractionDelegate : NSObject<UILargeContentViewerInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UILayoutSupport : NSObject<UILayoutSupport> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIMutableTraits : NSObject<UIMutableTraits> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UINavigationBarDelegate : NSObject<UIBarPositioningDelegate, UINavigationBarDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UINavigationItemRenameDelegate : NSObject<UINavigationItemRenameDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UINSIntegerTraitDefinition : NSObject<UINSIntegerTraitDefinition> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIObjectRestoration : NSObject<UIObjectRestoration> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIObjectTraitDefinition : NSObject<UIObjectTraitDefinition> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPageControlProgressDelegate : NSObject<UIPageControlProgressDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPageControlTimerProgressDelegate : NSObject<UIPageControlTimerProgressDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPageViewControllerDataSource : NSObject<UIPageViewControllerDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPageViewControllerDelegate : NSObject<UIPageViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPencilInteractionDelegate : NSObject<UIPencilInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPickerViewDelegate : NSObject<UIPickerViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPickerViewAccessibilityDelegate : NSObject<UIPickerViewAccessibilityDelegate, UIPickerViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPickerViewDataSource : NSObject<UIPickerViewDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPointerInteractionDelegate : NSObject<UIPointerInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPopoverControllerDelegate : NSObject<UIPopoverControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPopoverPresentationControllerDelegate : NSObject<UIAdaptivePresentationControllerDelegate, UIPopoverPresentationControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPreviewInteractionDelegate : NSObject<UIPreviewInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPrinterPickerControllerDelegate : NSObject<UIPrinterPickerControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIPrintInteractionControllerDelegate : NSObject<UIPrintInteractionControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UISceneDelegate : NSObject<UISceneDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIScreenshotServiceDelegate : NSObject<UIScreenshotServiceDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIScribbleInteractionDelegate : NSObject<UIScribbleInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIScrollViewAccessibilityDelegate : NSObject<UIScrollViewAccessibilityDelegate, UIScrollViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UISearchBarDelegate : NSObject<UIBarPositioningDelegate, UISearchBarDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UISearchControllerDelegate : NSObject<UISearchControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UISearchDisplayDelegate : NSObject<UISearchDisplayDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UISearchResultsUpdating : NSObject<UISearchResultsUpdating> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UISearchTextFieldDelegate : NSObject<UISearchTextFieldDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UISheetPresentationControllerDelegate : NSObject<UISheetPresentationControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UISplitViewControllerDelegate : NSObject<UISplitViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIStateRestoring : NSObject<UIStateRestoring> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITabBarControllerDelegate : NSObject<UITabBarControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITabBarControllerSidebarDelegate : NSObject<UITabBarControllerSidebarDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITabBarDelegate : NSObject<UITabBarDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITableViewDataSource : NSObject<UITableViewDataSource> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITableViewDelegate : NSObject<UIScrollViewDelegate, UITableViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITableViewDragDelegate : NSObject<UITableViewDragDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITableViewDropDelegate : NSObject<UITableViewDropDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextDocumentProxy : NSObject<UITextDocumentProxy> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextDragDelegate : NSObject<UITextDragDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextDropDelegate : NSObject<UITextDropDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextFieldDelegate : NSObject<UITextFieldDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextFormattingCoordinatorDelegate : NSObject<UITextFormattingCoordinatorDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextFormattingViewControllerDelegate : NSObject<UITextFormattingViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextInputDelegate : NSObject<UITextInputDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextInputTokenizer : NSObject<UITextInputTokenizer> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextInteractionDelegate : NSObject<UITextInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextPasteDelegate : NSObject<UITextPasteDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextSelectionDisplayInteractionDelegate : NSObject<UITextSelectionDisplayInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITextViewDelegate : NSObject<UIScrollViewDelegate, UITextViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIToolbarDelegate : NSObject<UIBarPositioningDelegate, UIToolbarDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIToolTipInteractionDelegate : NSObject<UIToolTipInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UITraitEnvironment : NSObject<UITraitEnvironment> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIVideoEditorControllerDelegate : NSObject<UINavigationControllerDelegate, UIVideoEditorControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIViewControllerAnimatedTransitioning : NSObject<UIViewControllerAnimatedTransitioning> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIViewControllerContextTransitioning : NSObject<UIViewControllerContextTransitioning> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIViewControllerInteractiveTransitioning : NSObject<UIViewControllerInteractiveTransitioning> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIViewControllerPreviewingDelegate : NSObject<UIViewControllerPreviewingDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIViewControllerTransitioningDelegate : NSObject<UIViewControllerTransitioningDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIWebViewDelegate : NSObject<UIWebViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIWindowSceneDelegate : NSObject<UIWindowSceneDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__UIKit_UIWritingToolsCoordinatorDelegate : NSObject<UIWritingToolsCoordinatorDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__EventKitUI_EKCalendarChooserDelegate : NSObject<EKCalendarChooserDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__EventKitUI_EKEventEditViewDelegate : NSObject<EKEventEditViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__EventKitUI_EKEventViewDelegate : NSObject<EKEventViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CryptoTokenKit_TKTokenDelegate : NSObject<TKTokenDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CryptoTokenKit_TKTokenDriverDelegate : NSObject<TKTokenDriverDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CryptoTokenKit_TKTokenSessionDelegate : NSObject<TKTokenSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreTelephony_CTTelephonyNetworkInfoDelegate : NSObject<CTTelephonyNetworkInfoDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreSpotlight_CSSearchableIndexDelegate : NSObject<CSSearchableIndexDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreMotion_CMHeadphoneMotionManagerDelegate : NSObject<CMHeadphoneMotionManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreMotion_CMWaterSubmersionManagerDelegate : NSObject<CMWaterSubmersionManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreMidi_MidiCIProfileResponderDelegate : NSObject<MIDICIProfileResponderDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreML_MLCustomModel : NSObject<MLCustomModel> {
}
	-(id) init;
	-(id) initWithModelDescription:(MLModelDescription *)p0 parameterDictionary:(NSDictionary <NSString *, NSObject *>*)p1 error:(NSError **)p2;
@end

@interface CoreImage_CIAccordionFoldTransition : CIFilter<CIAccordionFoldTransition> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(float) bottomHeight;
	-(void) setBottomHeight:(float)p0;
	-(float) numberOfFolds;
	-(void) setNumberOfFolds:(float)p0;
	-(float) foldShadowAmount;
	-(void) setFoldShadowAmount:(float)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) targetImage;
	-(void) setTargetImage:(CIImage *)p0;
	-(float) time;
	-(void) setTime:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICompositingFilter : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAdditionCompositing : CoreImage_CICompositingFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAffineFilter : CIFilter<CIFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAffineClamp : CoreImage_CIAffineFilter<CIAffineClamp, CIFilter> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGAffineTransform) transform;
	-(void) setTransform:(CGAffineTransform)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAffineTile : CoreImage_CIAffineFilter<CIAffineTile, CIFilter> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGAffineTransform) transform;
	-(void) setTransform:(CGAffineTransform)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAffineTransform : CoreImage_CIAffineFilter<CIFilter> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIReductionFilter : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaAverage : CoreImage_CIReductionFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaBoundsRed : CIFilter<CIAreaBoundsRed> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaHistogram : CIFilter<CIAreaHistogram> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(void *) count;
	-(void) setCount:(void *)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaLogarithmicHistogram : CIFilter<CIAreaLogarithmicHistogram> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(void *) count;
	-(void) setCount:(void *)p0;
	-(float) minimumStop;
	-(void) setMinimumStop:(float)p0;
	-(float) maximumStop;
	-(void) setMaximumStop:(float)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaMaximum : CoreImage_CIReductionFilter<CIAreaMaximum> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaMaximumAlpha : CoreImage_CIReductionFilter<CIAreaMaximumAlpha> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaMinimum : CoreImage_CIReductionFilter<CIAreaReductionFilter> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaMinimumAlpha : CoreImage_CIReductionFilter<CIAreaReductionFilter> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaMinMax : CoreImage_CIReductionFilter<CIAreaReductionFilter> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAreaMinMaxRed : CoreImage_CIAreaMaximum<CIAreaMaximum, CIAreaMinMaxRed> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIImageGenerator : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAttributedTextImageGenerator : CoreImage_CIImageGenerator<CIAttributedTextImageGenerator> {
}
	-(NSAttributedString *) text;
	-(void) setText:(NSAttributedString *)p0;
	-(float) scaleFactor;
	-(void) setScaleFactor:(float)p0;
	-(float) padding;
	-(void) setPadding:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICodeGenerator : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIAztecCodeGenerator : CoreImage_CICodeGenerator<CIAztecCodeGenerator> {
}
	-(NSData *) message;
	-(void) setMessage:(NSData *)p0;
	-(float) correctionLevel;
	-(void) setCorrectionLevel:(float)p0;
	-(float) layers;
	-(void) setLayers:(float)p0;
	-(float) compactStyle;
	-(void) setCompactStyle:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBarcodeGenerator : CIFilter<CIBarcodeGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIBarcodeDescriptor *) barcodeDescriptor;
	-(void) setBarcodeDescriptor:(CIBarcodeDescriptor *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CITransitionFilter : CIFilter<CITransitionFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) targetImage;
	-(void) setTargetImage:(CIImage *)p0;
	-(float) time;
	-(void) setTime:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBarsSwipeTransition : CoreImage_CITransitionFilter<CITransitionFilter> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBicubicScaleTransform : CIFilter<CIBicubicScaleTransform> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(float) aspectRatio;
	-(void) setAspectRatio:(float)p0;
	-(float) parameterB;
	-(void) setParameterB:(float)p0;
	-(float) parameterC;
	-(void) setParameterC:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBlendFilter : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBlendWithMask : CoreImage_CIBlendFilter<CIBlendWithMask> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) backgroundImage;
	-(void) setBackgroundImage:(CIImage *)p0;
	-(CIImage *) maskImage;
	-(void) setMaskImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBlendWithAlphaMask : CoreImage_CIBlendWithMask<CIBlendWithMask> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBlendWithBlueMask : CoreImage_CIBlendWithMask<CIBlendWithMask> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBlendWithRedMask : CoreImage_CIBlendWithMask<CIBlendWithMask> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBloom : CIFilter<CIBloom> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) intensity;
	-(void) setIntensity:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBlurredRectangleGenerator : CIFilter<CIBlurredRectangleGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(float) sigma;
	-(void) setSigma:(float)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILinearBlur : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBokehBlur : CoreImage_CILinearBlur<CIBokehBlur> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) ringAmount;
	-(void) setRingAmount:(float)p0;
	-(float) ringSize;
	-(void) setRingSize:(float)p0;
	-(float) softness;
	-(void) setSoftness:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBoxBlur : CIFilter<CIBoxBlur> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDistortionFilter : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBumpDistortion : CoreImage_CIDistortionFilter<CIBumpDistortion> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIBumpDistortionLinear : CoreImage_CIDistortionFilter<CIBumpDistortionLinear> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICameraCalibrationLensCorrection : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICannyEdgeDetector : CIFilter<CICannyEdgeDetector> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) gaussianSigma;
	-(void) setGaussianSigma:(float)p0;
	-(BOOL) perceptual;
	-(void) setPerceptual:(BOOL)p0;
	-(float) thresholdHigh;
	-(void) setThresholdHigh:(float)p0;
	-(float) thresholdLow;
	-(void) setThresholdLow:(float)p0;
	-(void *) hysteresisPasses;
	-(void) setHysteresisPasses:(void *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICheckerboardGenerator : CIFilter<CICheckerboardGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CIColor *) color0;
	-(void) setColor0:(CIColor *)p0;
	-(CIColor *) color1;
	-(void) setColor1:(CIColor *)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) sharpness;
	-(void) setSharpness:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICircleSplashDistortion : CoreImage_CIDistortionFilter<CICircleSplashDistortion> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIScreenFilter : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICircularScreen : CoreImage_CIScreenFilter<CICircularScreen> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) sharpness;
	-(void) setSharpness:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICircularWrap : CIFilter<CICircularWrap> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIClamp : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICmykHalftone : CIFilter<CICMYKHalftone> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) sharpness;
	-(void) setSharpness:(float)p0;
	-(float) grayComponentReplacement;
	-(void) setGrayComponentReplacement:(float)p0;
	-(float) underColorRemoval;
	-(void) setUnderColorRemoval:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICode128BarcodeGenerator : CoreImage_CICodeGenerator<CICode128BarcodeGenerator> {
}
	-(NSData *) message;
	-(void) setMessage:(NSData *)p0;
	-(float) quietSpace;
	-(void) setQuietSpace:(float)p0;
	-(float) barcodeHeight;
	-(void) setBarcodeHeight:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorAbsoluteDifference : CIFilter<CIColorAbsoluteDifference> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) inputImage2;
	-(void) setInputImage2:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorBurnBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorClamp : CIFilter<CIColorClamp> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIVector *) minComponents;
	-(void) setMinComponents:(CIVector *)p0;
	-(CIVector *) maxComponents;
	-(void) setMaxComponents:(CIVector *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorControls : CIFilter<CIColorControls> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) saturation;
	-(void) setSaturation:(float)p0;
	-(float) brightness;
	-(void) setBrightness:(float)p0;
	-(float) contrast;
	-(void) setContrast:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorCrossPolynomial : CIFilter<CIColorCrossPolynomial> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIVector *) redCoefficients;
	-(void) setRedCoefficients:(CIVector *)p0;
	-(CIVector *) greenCoefficients;
	-(void) setGreenCoefficients:(CIVector *)p0;
	-(CIVector *) blueCoefficients;
	-(void) setBlueCoefficients:(CIVector *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorCube : CIFilter<CIColorCube> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) cubeDimension;
	-(void) setCubeDimension:(float)p0;
	-(NSData *) cubeData;
	-(void) setCubeData:(NSData *)p0;
	-(BOOL) extrapolate;
	-(void) setExtrapolate:(BOOL)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorCubesMixedWithMask : CIFilter<CIColorCubesMixedWithMask> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) maskImage;
	-(void) setMaskImage:(CIImage *)p0;
	-(float) cubeDimension;
	-(void) setCubeDimension:(float)p0;
	-(NSData *) cube0Data;
	-(void) setCube0Data:(NSData *)p0;
	-(NSData *) cube1Data;
	-(void) setCube1Data:(NSData *)p0;
	-(id) colorSpace;
	-(void) setColorSpace:(id)p0;
	-(BOOL) extrapolate;
	-(void) setExtrapolate:(BOOL)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorCubeWithColorSpace : CoreImage_CIColorCube<CIColorCube, CIColorCubeWithColorSpace> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) cubeDimension;
	-(void) setCubeDimension:(float)p0;
	-(NSData *) cubeData;
	-(void) setCubeData:(NSData *)p0;
	-(id) colorSpace;
	-(void) setColorSpace:(id)p0;
	-(BOOL) extrapolate;
	-(void) setExtrapolate:(BOOL)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorCurves : CIFilter<CIColorCurves> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(NSData *) curvesData;
	-(void) setCurvesData:(NSData *)p0;
	-(CIVector *) curvesDomain;
	-(void) setCurvesDomain:(CIVector *)p0;
	-(id) colorSpace;
	-(void) setColorSpace:(id)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorDodgeBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorInvert : CIFilter<CIColorInvert> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorMap : CIFilter<CIColorMap> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) gradientImage;
	-(void) setGradientImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorMatrix : CIFilter<CIColorMatrix> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIVector *) RVector;
	-(void) setRVector:(CIVector *)p0;
	-(CIVector *) GVector;
	-(void) setGVector:(CIVector *)p0;
	-(CIVector *) BVector;
	-(void) setBVector:(CIVector *)p0;
	-(CIVector *) AVector;
	-(void) setAVector:(CIVector *)p0;
	-(CIVector *) biasVector;
	-(void) setBiasVector:(CIVector *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorMonochrome : CIFilter<CIColorMonochrome> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(float) intensity;
	-(void) setIntensity:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorPolynomial : CoreImage_CIColorCrossPolynomial<CIColorCrossPolynomial, CIColorPolynomial> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIVector *) redCoefficients;
	-(void) setRedCoefficients:(CIVector *)p0;
	-(CIVector *) greenCoefficients;
	-(void) setGreenCoefficients:(CIVector *)p0;
	-(CIVector *) blueCoefficients;
	-(void) setBlueCoefficients:(CIVector *)p0;
	-(CIVector *) alphaCoefficients;
	-(void) setAlphaCoefficients:(CIVector *)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorPosterize : CIFilter<CIColorPosterize> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) levels;
	-(void) setLevels:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorThreshold : CIFilter<CIColorThreshold> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) threshold;
	-(void) setThreshold:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColorThresholdOtsu : CIFilter<CIColorThresholdOtsu> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIColumnAverage : CoreImage_CIReductionFilter<CIColumnAverage> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIComicEffect : CIFilter<CIComicEffect> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConstantColorGenerator : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolutionCore : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolution3X3 : CoreImage_CIConvolutionCore {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolution5X5 : CoreImage_CIConvolutionCore {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolution7X7 : CoreImage_CIConvolutionCore {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolution9Horizontal : CoreImage_CIConvolutionCore {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolution9Vertical : CoreImage_CIConvolutionCore {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolutionRGB3X3 : CIFilter<CIFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolutionRGB5X5 : CIFilter<CIFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolutionRGB7X7 : CIFilter<CIFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolutionRGB9Horizontal : CIFilter<CIFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIConvolutionRGB9Vertical : CIFilter<CIFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICopyMachineTransition : CoreImage_CITransitionFilter<CITransitionFilter> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICoreMLModelFilter : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICrop : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CICrystallize : CIFilter<CICrystallize> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDarkenBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDepthBlurEffect : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDepthDisparityConverter : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDepthOfField : CIFilter<CIDepthOfField> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) point0;
	-(void) setPoint0:(CGPoint)p0;
	-(CGPoint) point1;
	-(void) setPoint1:(CGPoint)p0;
	-(float) saturation;
	-(void) setSaturation:(float)p0;
	-(float) unsharpMaskRadius;
	-(void) setUnsharpMaskRadius:(float)p0;
	-(float) unsharpMaskIntensity;
	-(void) setUnsharpMaskIntensity:(float)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDepthToDisparity : CoreImage_CIDepthDisparityConverter<CIDepthToDisparity> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDifferenceBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDiscBlur : CIFilter<CIDiscBlur> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDisintegrateWithMaskTransition : CoreImage_CITransitionFilter<CIDisintegrateWithMaskTransition, CITransitionFilter> {
}
	-(CIImage *) maskImage;
	-(void) setMaskImage:(CIImage *)p0;
	-(float) shadowRadius;
	-(void) setShadowRadius:(float)p0;
	-(float) shadowDensity;
	-(void) setShadowDensity:(float)p0;
	-(CGPoint) shadowOffset;
	-(void) setShadowOffset:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDisparityToDepth : CoreImage_CIDepthDisparityConverter<CIDisparityToDepth> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDisplacementDistortion : CIFilter<CIDisplacementDistortion> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) displacementImage;
	-(void) setDisplacementImage:(CIImage *)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDissolveTransition : CoreImage_CITransitionFilter<CITransitionFilter> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDistanceGradientFromRedMask : CIFilter<CIFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDither : CIFilter<CIDither> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) intensity;
	-(void) setIntensity:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDivideBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDocumentEnhancer : CIFilter<CIDocumentEnhancer> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) amount;
	-(void) setAmount:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDotScreen : CoreImage_CIScreenFilter<CIDotScreen> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) sharpness;
	-(void) setSharpness:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIDroste : CIFilter<CIDroste> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) insetPoint0;
	-(void) setInsetPoint0:(CGPoint)p0;
	-(CGPoint) insetPoint1;
	-(void) setInsetPoint1:(CGPoint)p0;
	-(float) strands;
	-(void) setStrands:(float)p0;
	-(float) periodicity;
	-(void) setPeriodicity:(float)p0;
	-(float) rotation;
	-(void) setRotation:(float)p0;
	-(float) zoom;
	-(void) setZoom:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIEdgePreserveUpsampleFilter : CIFilter<CIEdgePreserveUpsample> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) smallImage;
	-(void) setSmallImage:(CIImage *)p0;
	-(float) spatialSigma;
	-(void) setSpatialSigma:(float)p0;
	-(float) lumaSigma;
	-(void) setLumaSigma:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIEdges : CIFilter<CIEdges> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) intensity;
	-(void) setIntensity:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIEdgeWork : CIFilter<CIEdgeWork> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CITileFilter : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIEightfoldReflectedTile : CoreImage_CITileFilter<CIEightfoldReflectedTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIExclusionBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIExposureAdjust : CIFilter<CIExposureAdjust> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) EV;
	-(void) setEV:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIFaceBalance : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIFalseColor : CIFilter<CIFalseColor> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIColor *) color0;
	-(void) setColor0:(CIColor *)p0;
	-(CIColor *) color1;
	-(void) setColor1:(CIColor *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIFlashTransition : CoreImage_CITransitionFilter<CIFlashTransition, CITransitionFilter> {
}
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(float) maxStriationRadius;
	-(void) setMaxStriationRadius:(float)p0;
	-(float) striationStrength;
	-(void) setStriationStrength:(float)p0;
	-(float) striationContrast;
	-(void) setStriationContrast:(float)p0;
	-(float) fadeThreshold;
	-(void) setFadeThreshold:(float)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) targetImage;
	-(void) setTargetImage:(CIImage *)p0;
	-(float) time;
	-(void) setTime:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIFourfoldReflectedTile : CoreImage_CITileFilter<CIFourfoldReflectedTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) acuteAngle;
	-(void) setAcuteAngle:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIFourfoldRotatedTile : CoreImage_CITileFilter<CIFourfoldRotatedTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIFourfoldTranslatedTile : CoreImage_CITileFilter<CIFourfoldTranslatedTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) acuteAngle;
	-(void) setAcuteAngle:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIGaborGradients : CIFilter<CIGaborGradients> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIGammaAdjust : CIFilter<CIGammaAdjust> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) power;
	-(void) setPower:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIGaussianBlur : CIFilter<CIGaussianBlur> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIGaussianGradient : CIFilter<CIGaussianGradient> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CIColor *) color0;
	-(void) setColor0:(CIColor *)p0;
	-(CIColor *) color1;
	-(void) setColor1:(CIColor *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIGlassDistortion : CIFilter<CIGlassDistortion> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) textureImage;
	-(void) setTextureImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIGlassLozenge : CIFilter<CIGlassLozenge> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) point0;
	-(void) setPoint0:(CGPoint)p0;
	-(CGPoint) point1;
	-(void) setPoint1:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) refraction;
	-(void) setRefraction:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIGlideReflectedTile : CoreImage_CITileFilter<CIGlideReflectedTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIGloom : CIFilter<CIGloom> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) intensity;
	-(void) setIntensity:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIGuidedFilter : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHardLightBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHatchedScreen : CoreImage_CIScreenFilter<CIHatchedScreen> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) sharpness;
	-(void) setSharpness:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHeightFieldFromMask : CIFilter<CIHeightFieldFromMask> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHexagonalPixellate : CIFilter<CIHexagonalPixellate> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHighlightShadowAdjust : CIFilter<CIHighlightShadowAdjust> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) shadowAmount;
	-(void) setShadowAmount:(float)p0;
	-(float) highlightAmount;
	-(void) setHighlightAmount:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHistogramDisplayFilter : CIFilter<CIHistogramDisplay> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) height;
	-(void) setHeight:(float)p0;
	-(float) highLimit;
	-(void) setHighLimit:(float)p0;
	-(float) lowLimit;
	-(void) setLowLimit:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHoleDistortion : CoreImage_CIDistortionFilter<CIHoleDistortion> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHueAdjust : CIFilter<CIHueAdjust> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHueBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIHueSaturationValueGradient : CIFilter<CIHueSaturationValueGradient> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(float) value;
	-(void) setValue:(float)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) softness;
	-(void) setSoftness:(float)p0;
	-(float) dither;
	-(void) setDither:(float)p0;
	-(id) colorSpace;
	-(void) setColorSpace:(id)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@protocol CIImageProvider
	@required -(void) provideImageData:(void *)p0 bytesPerRow:(void *)p1 origin:(void *)p2 :(void *)p3 size:(void *)p4 :(void *)p5 userInfo:(NSObject *)p6;
@end

@interface CoreImage_CIKaleidoscope : CIFilter<CIKaleidoscope> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(void *) count;
	-(void) setCount:(void *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIKeystoneCorrection : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIKeystoneCorrectionCombined : CoreImage_CIKeystoneCorrection<CIKeystoneCorrectionCombined> {
}
	-(float) focalLength;
	-(void) setFocalLength:(float)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) topLeft;
	-(void) setTopLeft:(CGPoint)p0;
	-(CGPoint) topRight;
	-(void) setTopRight:(CGPoint)p0;
	-(CGPoint) bottomRight;
	-(void) setBottomRight:(CGPoint)p0;
	-(CGPoint) bottomLeft;
	-(void) setBottomLeft:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIKeystoneCorrectionHorizontal : CoreImage_CIKeystoneCorrection<CIKeystoneCorrectionHorizontal> {
}
	-(float) focalLength;
	-(void) setFocalLength:(float)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) topLeft;
	-(void) setTopLeft:(CGPoint)p0;
	-(CGPoint) topRight;
	-(void) setTopRight:(CGPoint)p0;
	-(CGPoint) bottomRight;
	-(void) setBottomRight:(CGPoint)p0;
	-(CGPoint) bottomLeft;
	-(void) setBottomLeft:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIKeystoneCorrectionVertical : CoreImage_CIKeystoneCorrection<CIKeystoneCorrectionVertical> {
}
	-(float) focalLength;
	-(void) setFocalLength:(float)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) topLeft;
	-(void) setTopLeft:(CGPoint)p0;
	-(CGPoint) topRight;
	-(void) setTopRight:(CGPoint)p0;
	-(CGPoint) bottomRight;
	-(void) setBottomRight:(CGPoint)p0;
	-(CGPoint) bottomLeft;
	-(void) setBottomLeft:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIKMeans : CoreImage_CIReductionFilter<CIKMeans> {
}
	-(CIImage *) inputMeans;
	-(void) setInputMeans:(CIImage *)p0;
	-(void *) count;
	-(void) setCount:(void *)p0;
	-(float) passes;
	-(void) setPasses:(float)p0;
	-(BOOL) perceptual;
	-(void) setPerceptual:(BOOL)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILabDeltaE : CIFilter<CILabDeltaE> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) image2;
	-(void) setImage2:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILanczosScaleTransform : CIFilter<CILanczosScaleTransform> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(float) aspectRatio;
	-(void) setAspectRatio:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILenticularHaloGenerator : CIFilter<CILenticularHaloGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(float) haloRadius;
	-(void) setHaloRadius:(float)p0;
	-(float) haloWidth;
	-(void) setHaloWidth:(float)p0;
	-(float) haloOverlap;
	-(void) setHaloOverlap:(float)p0;
	-(float) striationStrength;
	-(void) setStriationStrength:(float)p0;
	-(float) striationContrast;
	-(void) setStriationContrast:(float)p0;
	-(float) time;
	-(void) setTime:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILightenBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILightTunnel : CIFilter<CILightTunnel> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) rotation;
	-(void) setRotation:(float)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILinearBurnBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILinearDodgeBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILinearGradient : CIFilter<CILinearGradient> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGPoint) point0;
	-(void) setPoint0:(CGPoint)p0;
	-(CGPoint) point1;
	-(void) setPoint1:(CGPoint)p0;
	-(CIColor *) color0;
	-(void) setColor0:(CIColor *)p0;
	-(CIColor *) color1;
	-(void) setColor1:(CIColor *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILinearLightBlendMode : CIFilter<CIFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILinearToSRGBToneCurve : CIFilter<CILinearToSRGBToneCurve> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILineOverlay : CIFilter<CILineOverlay> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) NRNoiseLevel;
	-(void) setNRNoiseLevel:(float)p0;
	-(float) NRSharpness;
	-(void) setNRSharpness:(float)p0;
	-(float) edgeIntensity;
	-(void) setEdgeIntensity:(float)p0;
	-(float) threshold;
	-(void) setThreshold:(float)p0;
	-(float) contrast;
	-(void) setContrast:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILineScreen : CoreImage_CIScreenFilter<CILineScreen> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) sharpness;
	-(void) setSharpness:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CILuminosityBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMaskedVariableBlur : CIFilter<CIMaskedVariableBlur> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) mask;
	-(void) setMask:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMaskToAlpha : CIFilter<CIMaskToAlpha> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMaximumComponent : CIFilter<CIMaximumComponent> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMaximumCompositing : CoreImage_CICompositingFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMaximumScaleTransform : CIFilter<CIMaximumScaleTransform> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(float) aspectRatio;
	-(void) setAspectRatio:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMedianFilter : CIFilter<CIMedian> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMeshGenerator : CIFilter<CIMeshGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(NSArray *) mesh;
	-(void) setMesh:(NSArray *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMinimumComponent : CIFilter<CIMinimumComponent> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMinimumCompositing : CoreImage_CICompositingFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMix : CIFilter<CIMix> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) backgroundImage;
	-(void) setBackgroundImage:(CIImage *)p0;
	-(float) amount;
	-(void) setAmount:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIModTransition : CoreImage_CITransitionFilter<CIModTransition, CITransitionFilter> {
}
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) compression;
	-(void) setCompression:(float)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) targetImage;
	-(void) setTargetImage:(CIImage *)p0;
	-(float) time;
	-(void) setTime:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMorphology : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMorphologyGradient : CoreImage_CIMorphology<CIMorphologyGradient> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMorphologyMaximum : CoreImage_CIMorphology<CIMorphologyMaximum> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMorphologyMinimum : CoreImage_CIMorphology<CIMorphologyMinimum> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMorphologyRectangle : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMorphologyRectangleMaximum : CoreImage_CIMorphologyRectangle<CIMorphologyRectangleMaximum> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) height;
	-(void) setHeight:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMorphologyRectangleMinimum : CoreImage_CIMorphologyRectangle<CIMorphologyRectangleMinimum> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) height;
	-(void) setHeight:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMotionBlur : CoreImage_CILinearBlur<CIMotionBlur> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMultiplyBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIMultiplyCompositing : CoreImage_CICompositingFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CINinePartStretched : CIFilter<CINinePartStretched> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) breakpoint0;
	-(void) setBreakpoint0:(CGPoint)p0;
	-(CGPoint) breakpoint1;
	-(void) setBreakpoint1:(CGPoint)p0;
	-(CGPoint) growAmount;
	-(void) setGrowAmount:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CINinePartTiled : CIFilter<CINinePartTiled> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) breakpoint0;
	-(void) setBreakpoint0:(CGPoint)p0;
	-(CGPoint) breakpoint1;
	-(void) setBreakpoint1:(CGPoint)p0;
	-(CGPoint) growAmount;
	-(void) setGrowAmount:(CGPoint)p0;
	-(BOOL) flipYTiles;
	-(void) setFlipYTiles:(BOOL)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CINoiseReduction : CIFilter<CINoiseReduction> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) noiseLevel;
	-(void) setNoiseLevel:(float)p0;
	-(float) sharpness;
	-(void) setSharpness:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIOpTile : CoreImage_CITileFilter<CIOpTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIOverlayBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPageCurlTransition : CoreImage_CITransitionFilter<CIPageCurlTransition, CITransitionFilter> {
}
	-(CIImage *) backsideImage;
	-(void) setBacksideImage:(CIImage *)p0;
	-(CIImage *) shadingImage;
	-(void) setShadingImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) targetImage;
	-(void) setTargetImage:(CIImage *)p0;
	-(float) time;
	-(void) setTime:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPageCurlWithShadowTransition : CIFilter<CIPageCurlWithShadowTransition> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) backsideImage;
	-(void) setBacksideImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) shadowSize;
	-(void) setShadowSize:(float)p0;
	-(float) shadowAmount;
	-(void) setShadowAmount:(float)p0;
	-(CGRect) shadowExtent;
	-(void) setShadowExtent:(CGRect)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) targetImage;
	-(void) setTargetImage:(CIImage *)p0;
	-(float) time;
	-(void) setTime:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPaletteCentroid : CIFilter<CIPaletteCentroid> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) paletteImage;
	-(void) setPaletteImage:(CIImage *)p0;
	-(BOOL) perceptual;
	-(void) setPerceptual:(BOOL)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPalettize : CIFilter<CIPalettize> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) paletteImage;
	-(void) setPaletteImage:(CIImage *)p0;
	-(BOOL) perceptual;
	-(void) setPerceptual:(BOOL)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIParallelogramTile : CoreImage_CITileFilter<CIParallelogramTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) acuteAngle;
	-(void) setAcuteAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPdf417BarcodeGenerator : CoreImage_CICodeGenerator<CIPDF417BarcodeGenerator> {
}
	-(NSData *) message;
	-(void) setMessage:(NSData *)p0;
	-(float) minWidth;
	-(void) setMinWidth:(float)p0;
	-(float) maxWidth;
	-(void) setMaxWidth:(float)p0;
	-(float) minHeight;
	-(void) setMinHeight:(float)p0;
	-(float) maxHeight;
	-(void) setMaxHeight:(float)p0;
	-(float) dataColumns;
	-(void) setDataColumns:(float)p0;
	-(float) rows;
	-(void) setRows:(float)p0;
	-(float) preferredAspectRatio;
	-(void) setPreferredAspectRatio:(float)p0;
	-(float) compactionMode;
	-(void) setCompactionMode:(float)p0;
	-(float) compactStyle;
	-(void) setCompactStyle:(float)p0;
	-(float) correctionLevel;
	-(void) setCorrectionLevel:(float)p0;
	-(float) alwaysSpecifyCompaction;
	-(void) setAlwaysSpecifyCompaction:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPersonSegmentation : CIFilter<CIPersonSegmentation> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(void *) qualityLevel;
	-(void) setQualityLevel:(void *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPerspectiveTransform : CIFilter<CIPerspectiveTransform> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) topLeft;
	-(void) setTopLeft:(CGPoint)p0;
	-(CGPoint) topRight;
	-(void) setTopRight:(CGPoint)p0;
	-(CGPoint) bottomRight;
	-(void) setBottomRight:(CGPoint)p0;
	-(CGPoint) bottomLeft;
	-(void) setBottomLeft:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPerspectiveCorrection : CoreImage_CIPerspectiveTransform<CIPerspectiveCorrection, CIPerspectiveTransform> {
}
	-(BOOL) crop;
	-(void) setCrop:(BOOL)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) topLeft;
	-(void) setTopLeft:(CGPoint)p0;
	-(CGPoint) topRight;
	-(void) setTopRight:(CGPoint)p0;
	-(CGPoint) bottomRight;
	-(void) setBottomRight:(CGPoint)p0;
	-(CGPoint) bottomLeft;
	-(void) setBottomLeft:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPerspectiveRotate : CIFilter<CIPerspectiveRotate> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) focalLength;
	-(void) setFocalLength:(float)p0;
	-(float) pitch;
	-(void) setPitch:(float)p0;
	-(float) yaw;
	-(void) setYaw:(float)p0;
	-(float) roll;
	-(void) setRoll:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPerspectiveTile : CIFilter<CIPerspectiveTile> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) topLeft;
	-(void) setTopLeft:(CGPoint)p0;
	-(CGPoint) topRight;
	-(void) setTopRight:(CGPoint)p0;
	-(CGPoint) bottomRight;
	-(void) setBottomRight:(CGPoint)p0;
	-(CGPoint) bottomLeft;
	-(void) setBottomLeft:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPerspectiveTransformWithExtent : CoreImage_CIPerspectiveTransform<CIPerspectiveTransform, CIPerspectiveTransformWithExtent> {
}
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) topLeft;
	-(void) setTopLeft:(CGPoint)p0;
	-(CGPoint) topRight;
	-(void) setTopRight:(CGPoint)p0;
	-(CGPoint) bottomRight;
	-(void) setBottomRight:(CGPoint)p0;
	-(CGPoint) bottomLeft;
	-(void) setBottomLeft:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPhotoEffect : CIFilter<CIPhotoEffect> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(BOOL) extrapolate;
	-(void) setExtrapolate:(BOOL)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPhotoEffectChrome : CoreImage_CIPhotoEffect<CIPhotoEffect> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPhotoEffectFade : CoreImage_CIPhotoEffect<CIPhotoEffect> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPhotoEffectInstant : CoreImage_CIPhotoEffect<CIPhotoEffect> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPhotoEffectMono : CoreImage_CIPhotoEffect<CIPhotoEffect> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPhotoEffectNoir : CoreImage_CIPhotoEffect<CIPhotoEffect> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPhotoEffectProcess : CoreImage_CIPhotoEffect<CIPhotoEffect> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPhotoEffectTonal : CoreImage_CIPhotoEffect<CIPhotoEffect> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPhotoEffectTransfer : CoreImage_CIPhotoEffect<CIPhotoEffect> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPinchDistortion : CoreImage_CIDistortionFilter<CIPinchDistortion> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPinLightBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPixellate : CIFilter<CIPixellate> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIPointillize : CIFilter<CIPointillize> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIQRCodeGenerator : CoreImage_CICodeGenerator<CIQRCodeGenerator> {
}
	-(NSData *) message;
	-(void) setMessage:(NSData *)p0;
	-(NSString *) correctionLevel;
	-(void) setCorrectionLevel:(NSString *)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIRadialGradient : CIFilter<CIRadialGradient> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius0;
	-(void) setRadius0:(float)p0;
	-(float) radius1;
	-(void) setRadius1:(float)p0;
	-(CIColor *) color0;
	-(void) setColor0:(CIColor *)p0;
	-(CIColor *) color1;
	-(void) setColor1:(CIColor *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIRandomGenerator : CIFilter<CIRandomGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIRippleTransition : CoreImage_CITransitionFilter<CIRippleTransition, CITransitionFilter> {
}
	-(CIImage *) shadingImage;
	-(void) setShadingImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) targetImage;
	-(void) setTargetImage:(CIImage *)p0;
	-(float) time;
	-(void) setTime:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIRoundedRectangleGenerator : CIFilter<CIRoundedRectangleGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIRoundedRectangleStrokeGenerator : CIFilter<CIRoundedRectangleStrokeGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIRowAverage : CIFilter<CIRowAverage> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGRect) extent;
	-(void) setExtent:(CGRect)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISaliencyMapFilter : CIFilter<CISaliencyMap> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISampleNearest : CIFilter {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISaturationBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIScreenBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISepiaTone : CIFilter<CISepiaTone> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) intensity;
	-(void) setIntensity:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIShadedMaterial : CIFilter<CIShadedMaterial> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) shadingImage;
	-(void) setShadingImage:(CIImage *)p0;
	-(float) scale;
	-(void) setScale:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISharpenLuminance : CIFilter<CISharpenLuminance> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) sharpness;
	-(void) setSharpness:(float)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISixfoldReflectedTile : CoreImage_CITileFilter<CISixfoldReflectedTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISixfoldRotatedTile : CoreImage_CITileFilter<CISixfoldRotatedTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISmoothLinearGradient : CoreImage_CILinearGradient<CILinearGradient, CISmoothLinearGradient> {
}
	-(CGPoint) point0;
	-(void) setPoint0:(CGPoint)p0;
	-(CGPoint) point1;
	-(void) setPoint1:(CGPoint)p0;
	-(CIColor *) color0;
	-(void) setColor0:(CIColor *)p0;
	-(CIColor *) color1;
	-(void) setColor1:(CIColor *)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISobelGradients : CIFilter<CISobelGradients> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISoftLightBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISourceAtopCompositing : CoreImage_CICompositingFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISourceInCompositing : CoreImage_CICompositingFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISourceOutCompositing : CoreImage_CICompositingFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISourceOverCompositing : CoreImage_CICompositingFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISpotColor : CIFilter<CISpotColor> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIColor *) centerColor1;
	-(void) setCenterColor1:(CIColor *)p0;
	-(CIColor *) replacementColor1;
	-(void) setReplacementColor1:(CIColor *)p0;
	-(float) closeness1;
	-(void) setCloseness1:(float)p0;
	-(float) contrast1;
	-(void) setContrast1:(float)p0;
	-(CIColor *) centerColor2;
	-(void) setCenterColor2:(CIColor *)p0;
	-(CIColor *) replacementColor2;
	-(void) setReplacementColor2:(CIColor *)p0;
	-(float) closeness2;
	-(void) setCloseness2:(float)p0;
	-(float) contrast2;
	-(void) setContrast2:(float)p0;
	-(CIColor *) centerColor3;
	-(void) setCenterColor3:(CIColor *)p0;
	-(CIColor *) replacementColor3;
	-(void) setReplacementColor3:(CIColor *)p0;
	-(float) closeness3;
	-(void) setCloseness3:(float)p0;
	-(float) contrast3;
	-(void) setContrast3:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISpotLight : CIFilter<CISpotLight> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIVector *) lightPosition;
	-(void) setLightPosition:(CIVector *)p0;
	-(CIVector *) lightPointsAt;
	-(void) setLightPointsAt:(CIVector *)p0;
	-(float) brightness;
	-(void) setBrightness:(float)p0;
	-(float) concentration;
	-(void) setConcentration:(float)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISRGBToneCurveToLinear : CIFilter<CISRGBToneCurveToLinear> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIStarShineGenerator : CIFilter<CIStarShineGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) crossScale;
	-(void) setCrossScale:(float)p0;
	-(float) crossAngle;
	-(void) setCrossAngle:(float)p0;
	-(float) crossOpacity;
	-(void) setCrossOpacity:(float)p0;
	-(float) crossWidth;
	-(void) setCrossWidth:(float)p0;
	-(float) epsilon;
	-(void) setEpsilon:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIStraightenFilter : CIFilter<CIStraighten> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIStretchCrop : CIFilter<CIStretchCrop> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) size;
	-(void) setSize:(CGPoint)p0;
	-(float) cropAmount;
	-(void) setCropAmount:(float)p0;
	-(float) centerStretchAmount;
	-(void) setCenterStretchAmount:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIStripesGenerator : CIFilter<CIStripesGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CIColor *) color0;
	-(void) setColor0:(CIColor *)p0;
	-(CIColor *) color1;
	-(void) setColor1:(CIColor *)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) sharpness;
	-(void) setSharpness:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISubtractBlendMode : CoreImage_CIBlendFilter {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISunbeamsGenerator : CIFilter<CISunbeamsGenerator> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(float) sunRadius;
	-(void) setSunRadius:(float)p0;
	-(float) maxStriationRadius;
	-(void) setMaxStriationRadius:(float)p0;
	-(float) striationStrength;
	-(void) setStriationStrength:(float)p0;
	-(float) striationContrast;
	-(void) setStriationContrast:(float)p0;
	-(float) time;
	-(void) setTime:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CISwipeTransition : CoreImage_CITransitionFilter<CITransitionFilter> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CITemperatureAndTint : CIFilter<CITemperatureAndTint> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIVector *) neutral;
	-(void) setNeutral:(CIVector *)p0;
	-(CIVector *) targetNeutral;
	-(void) setTargetNeutral:(CIVector *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CITextImageGenerator : CoreImage_CIImageGenerator<CITextImageGenerator> {
}
	-(NSString *) text;
	-(void) setText:(NSString *)p0;
	-(NSString *) fontName;
	-(void) setFontName:(NSString *)p0;
	-(float) fontSize;
	-(void) setFontSize:(float)p0;
	-(float) scaleFactor;
	-(void) setScaleFactor:(float)p0;
	-(float) padding;
	-(void) setPadding:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIThermal : CIFilter<CIThermal> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIToneCurve : CIFilter<CIToneCurve> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) point0;
	-(void) setPoint0:(CGPoint)p0;
	-(CGPoint) point1;
	-(void) setPoint1:(CGPoint)p0;
	-(CGPoint) point2;
	-(void) setPoint2:(CGPoint)p0;
	-(CGPoint) point3;
	-(void) setPoint3:(CGPoint)p0;
	-(CGPoint) point4;
	-(void) setPoint4:(CGPoint)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIToneMapHeadroom : CIFilter<CIToneMapHeadroom> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) sourceHeadroom;
	-(void) setSourceHeadroom:(float)p0;
	-(float) targetHeadroom;
	-(void) setTargetHeadroom:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CITorusLensDistortion : CIFilter<CITorusLensDistortion> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(float) refraction;
	-(void) setRefraction:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CITriangleKaleidoscope : CIFilter<CITriangleKaleidoscope> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) point;
	-(void) setPoint:(CGPoint)p0;
	-(float) size;
	-(void) setSize:(float)p0;
	-(float) rotation;
	-(void) setRotation:(float)p0;
	-(float) decay;
	-(void) setDecay:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CITriangleTile : CoreImage_CITileFilter<CITriangleTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CITwelvefoldReflectedTile : CoreImage_CITileFilter<CITwelvefoldReflectedTile> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(float) width;
	-(void) setWidth:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CITwirlDistortion : CoreImage_CIDistortionFilter<CITwirlDistortion> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIUnsharpMask : CIFilter<CIUnsharpMask> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) intensity;
	-(void) setIntensity:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIVibrance : CIFilter<CIVibrance> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) amount;
	-(void) setAmount:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIVignette : CIFilter<CIVignette> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(float) intensity;
	-(void) setIntensity:(float)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIVignetteEffect : CIFilter<CIVignetteEffect> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) intensity;
	-(void) setIntensity:(float)p0;
	-(float) falloff;
	-(void) setFalloff:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIVividLightBlendMode : CIFilter<CIFilter> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIVortexDistortion : CoreImage_CIDistortionFilter<CIVortexDistortion> {
}
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) radius;
	-(void) setRadius:(float)p0;
	-(float) angle;
	-(void) setAngle:(float)p0;
	-(CIImage *) outputImage;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIWhitePointAdjust : CIFilter<CIWhitePointAdjust> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIColor *) color;
	-(void) setColor:(CIColor *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIXRay : CIFilter<CIXRay> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface CoreImage_CIZoomBlur : CIFilter<CIZoomBlur> {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(CIImage *) inputImage;
	-(void) setInputImage:(CIImage *)p0;
	-(CGPoint) center;
	-(void) setCenter:(CGPoint)p0;
	-(float) amount;
	-(void) setAmount:(float)p0;
	-(CIImage *) outputImage;
	-(BOOL) conformsToProtocol:(void *)p0;
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface Microsoft_iOS__CoreData_NSFetchedResultsControllerDelegate : NSObject<NSFetchedResultsControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreData_NSFetchedResultsSectionInfo : NSObject<NSFetchedResultsSectionInfo> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreBluetooth_CBCentralManagerDelegate : NSObject<CBCentralManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreBluetooth_CBPeripheralDelegate : NSObject<CBPeripheralDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreBluetooth_CBPeripheralManagerDelegate : NSObject<CBPeripheralManagerDelegate> {
}
	-(id) init;
@end

@interface Foundation_NSExceptionError : NSError {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface Microsoft_iOS__Foundation_NSFileManagerDelegate : NSObject<NSFileManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSCacheDelegate : NSObject<NSCacheDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSCoding : NSObject<NSCoding> {
}
	-(id) init;
	-(id) initWithCoder:(NSCoder *)p0;
@end

@interface Microsoft_iOS__Foundation_NSCopying : NSObject<NSCopying> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSExtensionRequestHandling : NSObject<NSExtensionRequestHandling> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSFilePresenter : NSObject<NSFilePresenter> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSKeyedArchiverDelegate : NSObject<NSKeyedArchiverDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSKeyedUnarchiverDelegate : NSObject<NSKeyedUnarchiverDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSPortDelegate : NSObject<NSPortDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSMachPortDelegate : NSObject<NSMachPortDelegate, NSPortDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSMetadataQueryDelegate : NSObject<NSMetadataQueryDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSMutableCopying : NSObject<NSMutableCopying> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSNetServiceBrowserDelegate : NSObject<NSNetServiceBrowserDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSNetServiceDelegate : NSObject<NSNetServiceDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSStreamDelegate : NSObject<NSStreamDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUrlConnectionDownloadDelegate : NSObject<NSURLConnectionDelegate, NSURLConnectionDownloadDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUrlSessionDelegate : NSObject<NSURLSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUrlSessionTaskDelegate : NSObject<NSURLSessionDelegate, NSURLSessionTaskDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUrlSessionDataDelegate : NSObject<NSURLSessionDataDelegate, NSURLSessionDelegate, NSURLSessionTaskDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUrlSessionDownloadDelegate : NSObject<NSURLSessionDelegate, NSURLSessionDownloadDelegate, NSURLSessionTaskDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUrlSessionStreamDelegate : NSObject<NSURLSessionDelegate, NSURLSessionStreamDelegate, NSURLSessionTaskDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUrlSessionWebSocketDelegate : NSObject<NSURLSessionDelegate, NSURLSessionTaskDelegate, NSURLSessionWebSocketDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSUserActivityDelegate : NSObject<NSUserActivityDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__Foundation_NSXpcListenerDelegate : NSObject<NSXPCListenerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreAnimation_CALayerDelegate : NSObject<CALayerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreAnimation_CAAnimationDelegate : NSObject<CAAnimationDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreAnimation_CAMediaTiming : NSObject<CAMediaTiming> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CoreAnimation_CAMetalDisplayLinkDelegate : NSObject<CAMetalDisplayLinkDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CloudKit_CKRecordValue : NSObject<CKRecordValue> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CloudKit_CKSyncEngineDelegate : NSObject<CKSyncEngineDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ClassKit_CLSDataStoreDelegate : NSObject<CLSDataStoreDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPApplicationDelegate : NSObject<CPApplicationDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPInstrumentClusterControllerDelegate : NSObject<CPInstrumentClusterControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPInterfaceControllerDelegate : NSObject<CPInterfaceControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPListTemplateDelegate : NSObject<CPListTemplateDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPMapTemplateDelegate : NSObject<CPMapTemplateDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPPointOfInterestTemplateDelegate : NSObject<CPPointOfInterestTemplateDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPSearchTemplateDelegate : NSObject<CPSearchTemplateDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPSessionConfigurationDelegate : NSObject<CPSessionConfigurationDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPTabBarTemplateDelegate : NSObject<CPTabBarTemplateDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPTemplateApplicationDashboardSceneDelegate : NSObject<CPTemplateApplicationDashboardSceneDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPTemplateApplicationInstrumentClusterSceneDelegate : NSObject<CPTemplateApplicationInstrumentClusterSceneDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CarPlay_CPTemplateApplicationSceneDelegate : NSObject<CPTemplateApplicationSceneDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CallKit_CXCallDirectoryExtensionContextDelegate : NSObject<CXCallDirectoryExtensionContextDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CallKit_CXCallObserverDelegate : NSObject<CXCallObserverDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__CallKit_CXProviderDelegate : NSObject<CXProviderDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__BrowserEngineKit_BEDragInteractionDelegate : NSObject<BEDragInteractionDelegate, UIDragInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__BrowserEngineKit_BEScrollViewDelegate : NSObject<BEScrollViewDelegate, UIScrollViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__BrowserEngineKit_BETextInputDelegate : NSObject<BETextInputDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__BrowserEngineKit_BETextInteractionDelegate : NSObject<BETextInteractionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__BackgroundAssets_BADownloadManagerDelegate : NSObject<BADownloadManagerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AuthenticationServices_ASAccountAuthenticationModificationControllerDelegate : NSObject<ASAccountAuthenticationModificationControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AuthenticationServices_ASAuthorizationControllerDelegate : NSObject<ASAuthorizationControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AddressBookUI_ABNewPersonViewControllerDelegate : NSObject<ABNewPersonViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AddressBookUI_ABPeoplePickerNavigationControllerDelegate : NSObject<ABPeoplePickerNavigationControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AddressBookUI_ABPersonViewControllerDelegate : NSObject<ABPersonViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AddressBookUI_ABUnknownPersonViewControllerDelegate : NSObject<ABUnknownPersonViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVKit_AVCustomRoutingControllerDelegate : NSObject<AVCustomRoutingControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVKit_AVPictureInPictureControllerDelegate : NSObject<AVPictureInPictureControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVKit_AVPictureInPictureSampleBufferPlaybackDelegate : NSObject<AVPictureInPictureSampleBufferPlaybackDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVKit_AVPlayerViewControllerDelegate : NSObject<AVPlayerViewControllerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVKit_AVRoutePickerViewDelegate : NSObject<AVRoutePickerViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVAudioPlayerDelegate : NSObject<AVAudioPlayerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVAudioRecorderDelegate : NSObject<AVAudioRecorderDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVAudioSessionDelegate : NSObject<AVAudioSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVAssetDownloadDelegate : NSObject<AVAssetDownloadDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVAssetResourceLoaderDelegate : NSObject<AVAssetResourceLoaderDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVAssetWriterDelegate : NSObject<AVAssetWriterDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVAsynchronousKeyValueLoading : NSObject<AVAsynchronousKeyValueLoading> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVAudio3DMixing : NSObject<AVAudio3DMixing> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVAudioStereoMixing : NSObject<AVAudioStereoMixing> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVCaptureAudioDataOutputSampleBufferDelegate : NSObject<AVCaptureAudioDataOutputSampleBufferDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVCaptureDataOutputSynchronizerDelegate : NSObject<AVCaptureDataOutputSynchronizerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVCaptureDepthDataOutputDelegate : NSObject<AVCaptureDepthDataOutputDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVCaptureFileOutputRecordingDelegate : NSObject<AVCaptureFileOutputRecordingDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVCaptureMetadataOutputObjectsDelegate : NSObject<AVCaptureMetadataOutputObjectsDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVCapturePhotoCaptureDelegate : NSObject<AVCapturePhotoCaptureDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVCapturePhotoOutputReadinessCoordinatorDelegate : NSObject<AVCapturePhotoOutputReadinessCoordinatorDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVCaptureSessionControlsDelegate : NSObject<AVCaptureSessionControlsDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVCaptureVideoDataOutputSampleBufferDelegate : NSObject<AVCaptureVideoDataOutputSampleBufferDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVContentKeySessionDelegate : NSObject<AVContentKeySessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVPlaybackCoordinatorPlaybackControlDelegate : NSObject<AVPlaybackCoordinatorPlaybackControlDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVPlayerItemOutputPushDelegate : NSObject<AVPlayerItemOutputPushDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVPlayerItemLegibleOutputPushDelegate : NSObject<AVPlayerItemLegibleOutputPushDelegate, AVPlayerItemOutputPushDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVPlayerItemMetadataCollectorPushDelegate : NSObject<AVPlayerItemMetadataCollectorPushDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVPlayerItemMetadataOutputPushDelegate : NSObject<AVPlayerItemMetadataOutputPushDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVPlayerItemOutputPullDelegate : NSObject<AVPlayerItemOutputPullDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVPlayerItemRenderedLegibleOutputPushDelegate : NSObject<AVPlayerItemRenderedLegibleOutputPushDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVPlayerPlaybackCoordinatorDelegate : NSObject<AVPlayerPlaybackCoordinatorDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVSpeechSynthesizerDelegate : NSObject<AVSpeechSynthesizerDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__AVFoundation_AVVideoCompositing : NSObject<AVVideoCompositing> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ARKit_ARCoachingOverlayViewDelegate : NSObject<ARCoachingOverlayViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ARKit_ARSCNViewDelegate : NSObject<ARSCNViewDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ARKit_ARSessionDelegate : NSObject<ARSessionDelegate> {
}
	-(id) init;
@end

@interface Microsoft_iOS__ARKit_ARSKViewDelegate : NSObject<ARSKViewDelegate> {
}
	-(id) init;
@end

@interface UIKit_UIView_UIViewAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(UIColor *) backgroundColor;
	-(void) setBackgroundColor:(UIColor *)p0;
	-(UIColor *) tintColor;
	-(void) setTintColor:(UIColor *)p0;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface SharedWithYou_SWAttributionView_SWAttributionViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface SharedWithYou_SWCollaborationView_SWCollaborationViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface PhotosUI_PHLivePhotoView_PHLivePhotoViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIScrollView_UIScrollViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface PencilKit_PKCanvasView_PKCanvasViewAppearance : UIKit_UIScrollView_UIScrollViewAppearance {
}
@end

@interface Messages_MSStickerBrowserView_MSStickerBrowserViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface Messages_MSStickerView_MSStickerViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface LinkPresentation_LPLinkView_LPLinkViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIControl_UIControlAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIButton_UIButtonAppearance : UIKit_UIControl_UIControlAppearance {
}
	-(UIImage *) backgroundImageForState:(NSUInteger)p0;
	-(UIEdgeInsets) contentEdgeInsets;
	-(void) setContentEdgeInsets:(UIEdgeInsets)p0;
	-(UIImage *) currentBackgroundImage;
	-(UIImage *) currentImage;
	-(UIColor *) currentTitleColor;
	-(UIColor *) currentTitleShadowColor;
	-(UIImage *) imageForState:(NSUInteger)p0;
	-(void) setBackgroundImage:(UIImage *)p0 forState:(NSUInteger)p1;
	-(void) setImage:(UIImage *)p0 forState:(NSUInteger)p1;
	-(void) setPreferredSymbolConfiguration:(UIImageSymbolConfiguration *)p0 forImageInState:(NSUInteger)p1;
	-(void) setTitleColor:(UIColor *)p0 forState:(NSUInteger)p1;
	-(void) setTitleShadowColor:(UIColor *)p0 forState:(NSUInteger)p1;
	-(UIColor *) titleColorForState:(NSUInteger)p0;
	-(UIColor *) titleShadowColorForState:(NSUInteger)p0;
@end

@interface IntentsUI_INUIAddVoiceShortcutButton_INUIAddVoiceShortcutButtonAppearance : UIKit_UIButton_UIButtonAppearance {
}
@end

@interface HealthKitUI_HKActivityRingView_HKActivityRingViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface CoreLocationUI_CLLocationButton_CLLocationButtonAppearance : UIKit_UIControl_UIControlAppearance {
}
@end

@interface CoreAudioKit_CAInterAppAudioSwitcherView_CAInterAppAudioSwitcherViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface CoreAudioKit_CAInterAppAudioTransportView_CAInterAppAudioTransportViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface SpriteKit_SKView_SKViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface SceneKit_SCNView_SCNViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface ReplayKit_RPSystemBroadcastPickerView_RPSystemBroadcastPickerViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface PdfKit_PdfThumbnailView_PdfThumbnailViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface PdfKit_PdfView_PdfViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface PassKit_PKPayLaterView_PKPayLaterViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface PassKit_PKAddPassButton_PKAddPassButtonAppearance : UIKit_UIButton_UIButtonAppearance {
}
	-(NSInteger) addPassButtonStyle;
	-(void) setAddPassButtonStyle:(NSInteger)p0;
@end

@interface PassKit_PKIdentityButton_PKIdentityButtonAppearance : UIKit_UIControl_UIControlAppearance {
}
@end

@interface PassKit_PKPaymentButton_PKPaymentButtonAppearance : UIKit_UIButton_UIButtonAppearance {
}
@end

@interface MetalKit_MTKView_MTKViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface MessageUI_MFMailComposeViewController_MFMailComposeViewControllerAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface MessageUI_MFMessageComposeViewController_MFMessageComposeViewControllerAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface MediaPlayer_MPVolumeView_MPVolumeViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface MapKit_MKOverlayView_MKOverlayViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface MapKit_MKAnnotationView_MKAnnotationViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface MapKit_MKOverlayPathView_MKOverlayPathViewAppearance : MapKit_MKOverlayView_MKOverlayViewAppearance {
}
@end

@interface MapKit_MKCircleView_MKCircleViewAppearance : MapKit_MKOverlayPathView_MKOverlayPathViewAppearance {
}
@end

@interface MapKit_MKCompassButton_MKCompassButtonAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface MapKit_MKMapView_MKMapViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface MapKit_MKMarkerAnnotationView_MKMarkerAnnotationViewAppearance : MapKit_MKAnnotationView_MKAnnotationViewAppearance {
}
	-(UIImage *) glyphImage;
	-(void) setGlyphImage:(UIImage *)p0;
	-(NSString *) glyphText;
	-(void) setGlyphText:(NSString *)p0;
	-(UIColor *) glyphTintColor;
	-(void) setGlyphTintColor:(UIColor *)p0;
	-(UIColor *) markerTintColor;
	-(void) setMarkerTintColor:(UIColor *)p0;
	-(UIImage *) selectedGlyphImage;
	-(void) setSelectedGlyphImage:(UIImage *)p0;
@end

@interface MapKit_MKPinAnnotationView_MKPinAnnotationViewAppearance : MapKit_MKAnnotationView_MKAnnotationViewAppearance {
}
	-(UIColor *) pinTintColor;
	-(void) setPinTintColor:(UIColor *)p0;
@end

@interface MapKit_MKPolygonView_MKPolygonViewAppearance : MapKit_MKOverlayPathView_MKOverlayPathViewAppearance {
}
@end

@interface MapKit_MKPolylineView_MKPolylineViewAppearance : MapKit_MKOverlayPathView_MKOverlayPathViewAppearance {
}
@end

@interface MapKit_MKScaleView_MKScaleViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface MapKit_MKUserLocationView_MKUserLocationViewAppearance : MapKit_MKAnnotationView_MKAnnotationViewAppearance {
}
@end

@interface UIKit_UIBarItem_UIBarItemAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(NSDictionary *) titleTextAttributesForState:(NSUInteger)p0;
	-(void) setTitleTextAttributes:(NSDictionary *)p0 forState:(NSUInteger)p1;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface UIKit_UIBarButtonItem_UIBarButtonItemAppearance : UIKit_UIBarItem_UIBarItemAppearance {
}
	-(UIImage *) backButtonBackgroundImageForState:(NSUInteger)p0 barMetrics:(NSInteger)p1;
	-(CGFloat) backButtonBackgroundVerticalPositionAdjustmentForBarMetrics:(NSInteger)p0;
	-(UIOffset) backButtonTitlePositionAdjustmentForBarMetrics:(NSInteger)p0;
	-(UIImage *) backgroundImageForState:(NSUInteger)p0 barMetrics:(NSInteger)p1;
	-(UIImage *) backgroundImageForState:(NSUInteger)p0 style:(NSInteger)p1 barMetrics:(NSInteger)p2;
	-(CGFloat) backgroundVerticalPositionAdjustmentForBarMetrics:(NSInteger)p0;
	-(UIOffset) titlePositionAdjustmentForBarMetrics:(NSInteger)p0;
	-(void) setBackButtonBackgroundImage:(UIImage *)p0 forState:(NSUInteger)p1 barMetrics:(NSInteger)p2;
	-(void) setBackButtonBackgroundVerticalPositionAdjustment:(CGFloat)p0 forBarMetrics:(NSInteger)p1;
	-(void) setBackButtonTitlePositionAdjustment:(UIOffset)p0 forBarMetrics:(NSInteger)p1;
	-(void) setBackgroundImage:(UIImage *)p0 forState:(NSUInteger)p1 barMetrics:(NSInteger)p2;
	-(void) setBackgroundImage:(UIImage *)p0 forState:(NSUInteger)p1 style:(NSInteger)p2 barMetrics:(NSInteger)p3;
	-(void) setBackgroundVerticalPositionAdjustment:(CGFloat)p0 forBarMetrics:(NSInteger)p1;
	-(void) setTitlePositionAdjustment:(UIOffset)p0 forBarMetrics:(NSInteger)p1;
	-(UIColor *) tintColor;
	-(void) setTintColor:(UIColor *)p0;
@end

@interface MapKit_MKUserTrackingBarButtonItem_MKUserTrackingBarButtonItemAppearance : UIKit_UIBarButtonItem_UIBarButtonItemAppearance {
}
@end

@interface MapKit_MKUserTrackingButton_MKUserTrackingButtonAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface HomeKit_HMCameraView_HMCameraViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface GameKit_GKAchievementViewController_GKAchievementViewControllerAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface GameKit_GKFriendRequestComposeViewController_GKFriendRequestComposeViewControllerAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface GameKit_GKLeaderboardViewController_GKLeaderboardViewControllerAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface GameKit_GKTurnBasedMatchmakerViewController_GKTurnBasedMatchmakerViewControllerAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface GLKit_GLKView_GLKViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface WebKit_WKWebView_WKWebViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIActionSheet_UIActionSheetAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIAlertView_UIAlertViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UICollectionView_UICollectionViewAppearance : UIKit_UIScrollView_UIScrollViewAppearance {
}
@end

@interface __UIGestureRecognizerParameterlessToken : __UIGestureRecognizerToken {
}
	-(void) target;
@end

@interface __UIGestureRecognizerParametrizedToken : __UIGestureRecognizerToken {
}
	-(void) target:(UIGestureRecognizer *)p0;
@end

@interface UIKit_UINavigationBar_UINavigationBarAppearance : UIKit_UIView_UIViewAppearance {
}
	-(UIImage *) backIndicatorImage;
	-(void) setBackIndicatorImage:(UIImage *)p0;
	-(UIImage *) backIndicatorTransitionMaskImage;
	-(void) setBackIndicatorTransitionMaskImage:(UIImage *)p0;
	-(NSInteger) barStyle;
	-(void) setBarStyle:(NSInteger)p0;
	-(UIColor *) barTintColor;
	-(void) setBarTintColor:(UIColor *)p0;
	-(UINavigationBarAppearance *) compactAppearance;
	-(void) setCompactAppearance:(UINavigationBarAppearance *)p0;
	-(UINavigationBarAppearance *) compactScrollEdgeAppearance;
	-(void) setCompactScrollEdgeAppearance:(UINavigationBarAppearance *)p0;
	-(UIImage *) backgroundImageForBarMetrics:(NSInteger)p0;
	-(UIImage *) backgroundImageForBarPosition:(NSInteger)p0 barMetrics:(NSInteger)p1;
	-(CGFloat) titleVerticalPositionAdjustmentForBarMetrics:(NSInteger)p0;
	-(BOOL) prefersLargeTitles;
	-(void) setPrefersLargeTitles:(BOOL)p0;
	-(UINavigationBarAppearance *) scrollEdgeAppearance;
	-(void) setScrollEdgeAppearance:(UINavigationBarAppearance *)p0;
	-(void) setBackgroundImage:(UIImage *)p0 forBarMetrics:(NSInteger)p1;
	-(void) setBackgroundImage:(UIImage *)p0 forBarPosition:(NSInteger)p1 barMetrics:(NSInteger)p2;
	-(void) setTitleVerticalPositionAdjustment:(CGFloat)p0 forBarMetrics:(NSInteger)p1;
	-(UIImage *) shadowImage;
	-(void) setShadowImage:(UIImage *)p0;
	-(UINavigationBarAppearance *) standardAppearance;
	-(void) setStandardAppearance:(UINavigationBarAppearance *)p0;
	-(BOOL) isTranslucent;
	-(void) setTranslucent:(BOOL)p0;
	-(NSDictionary *) largeTitleTextAttributes;
	-(void) setLargeTitleTextAttributes:(NSDictionary *)p0;
	-(NSDictionary *) titleTextAttributes;
	-(void) setTitleTextAttributes:(NSDictionary *)p0;
@end

@interface UIKit_UIPickerView_UIPickerViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UISearchBar_UISearchBarAppearance : UIKit_UIView_UIViewAppearance {
}
	-(UIImage *) backgroundImage;
	-(void) setBackgroundImage:(UIImage *)p0;
	-(UIImage *) backgroundImageForBarPosition:(NSInteger)p0 barMetrics:(NSInteger)p1;
	-(UIColor *) barTintColor;
	-(void) setBarTintColor:(UIColor *)p0;
	-(UIImage *) imageForSearchBarIcon:(NSInteger)p0 state:(NSUInteger)p1;
	-(UIOffset) positionAdjustmentForSearchBarIcon:(NSInteger)p0;
	-(UIImage *) scopeBarButtonBackgroundImageForState:(NSUInteger)p0;
	-(UIImage *) scopeBarButtonDividerImageForLeftSegmentState:(NSUInteger)p0 rightSegmentState:(NSUInteger)p1;
	-(UIImage *) searchFieldBackgroundImageForState:(NSUInteger)p0;
	-(UIImage *) scopeBarBackgroundImage;
	-(void) setScopeBarBackgroundImage:(UIImage *)p0;
	-(UIOffset) searchFieldBackgroundPositionAdjustment;
	-(void) setSearchFieldBackgroundPositionAdjustment:(UIOffset)p0;
	-(UIOffset) searchTextPositionAdjustment;
	-(void) setSearchTextPositionAdjustment:(UIOffset)p0;
	-(void) setBackgroundImage:(UIImage *)p0 forBarPosition:(NSInteger)p1 barMetrics:(NSInteger)p2;
	-(void) setImage:(UIImage *)p0 forSearchBarIcon:(NSInteger)p1 state:(NSUInteger)p2;
	-(void) setPositionAdjustment:(UIOffset)p0 forSearchBarIcon:(NSInteger)p1;
	-(void) setScopeBarButtonBackgroundImage:(UIImage *)p0 forState:(NSUInteger)p1;
	-(void) setScopeBarButtonDividerImage:(UIImage *)p0 forLeftSegmentState:(NSUInteger)p1 rightSegmentState:(NSUInteger)p2;
	-(void) setSearchFieldBackgroundImage:(UIImage *)p0 forState:(NSUInteger)p1;
	-(NSDictionary *) scopeBarButtonTitleTextAttributesForState:(NSUInteger)p0;
	-(void) setScopeBarButtonTitleTextAttributes:(NSDictionary *)p0 forState:(NSUInteger)p1;
@end

@interface UIKit_UISegmentedControl_UISegmentedControlAppearance : UIKit_UIControl_UIControlAppearance {
}
	-(UIOffset) contentPositionAdjustmentForSegmentType:(NSInteger)p0 barMetrics:(NSInteger)p1;
	-(UIImage *) backgroundImageForState:(NSUInteger)p0 barMetrics:(NSInteger)p1;
	-(UIImage *) dividerImageForLeftSegmentState:(NSUInteger)p0 rightSegmentState:(NSUInteger)p1 barMetrics:(NSInteger)p2;
	-(NSDictionary *) titleTextAttributesForState:(NSUInteger)p0;
	-(UIColor *) selectedSegmentTintColor;
	-(void) setSelectedSegmentTintColor:(UIColor *)p0;
	-(void) setBackgroundImage:(UIImage *)p0 forState:(NSUInteger)p1 barMetrics:(NSInteger)p2;
	-(void) setContentPositionAdjustment:(UIOffset)p0 forSegmentType:(NSInteger)p1 barMetrics:(NSInteger)p2;
	-(void) setDividerImage:(UIImage *)p0 forLeftSegmentState:(NSUInteger)p1 rightSegmentState:(NSUInteger)p2 barMetrics:(NSInteger)p3;
	-(void) setTitleTextAttributes:(NSDictionary *)p0 forState:(NSUInteger)p1;
@end

@interface UIKit_UITableView_UITableViewAppearance : UIKit_UIScrollView_UIScrollViewAppearance {
}
	-(UIColor *) sectionIndexBackgroundColor;
	-(void) setSectionIndexBackgroundColor:(UIColor *)p0;
	-(UIColor *) sectionIndexColor;
	-(void) setSectionIndexColor:(UIColor *)p0;
	-(UIColor *) sectionIndexTrackingBackgroundColor;
	-(void) setSectionIndexTrackingBackgroundColor:(UIColor *)p0;
	-(UIColor *) separatorColor;
	-(void) setSeparatorColor:(UIColor *)p0;
	-(UIVisualEffect *) separatorEffect;
	-(void) setSeparatorEffect:(UIVisualEffect *)p0;
	-(UIEdgeInsets) separatorInset;
	-(void) setSeparatorInset:(UIEdgeInsets)p0;
@end

@interface UIKit_UITableViewCell_UITableViewCellAppearance : UIKit_UIView_UIViewAppearance {
}
	-(NSInteger) focusStyle;
	-(void) setFocusStyle:(NSInteger)p0;
	-(UIEdgeInsets) separatorInset;
	-(void) setSeparatorInset:(UIEdgeInsets)p0;
@end

@interface UIKit_UITextField_UITextFieldAppearance : UIKit_UIControl_UIControlAppearance {
}
@end

@interface UIKit_UITextView_UITextViewAppearance : UIKit_UIScrollView_UIScrollViewAppearance {
}
@end

@interface UIKit_UIToolbar_UIToolbarAppearance : UIKit_UIView_UIViewAppearance {
}
	-(NSInteger) barStyle;
	-(void) setBarStyle:(NSInteger)p0;
	-(UIColor *) barTintColor;
	-(void) setBarTintColor:(UIColor *)p0;
	-(UIToolbarAppearance *) compactAppearance;
	-(void) setCompactAppearance:(UIToolbarAppearance *)p0;
	-(UIToolbarAppearance *) compactScrollEdgeAppearance;
	-(void) setCompactScrollEdgeAppearance:(UIToolbarAppearance *)p0;
	-(UIImage *) backgroundImageForToolbarPosition:(NSInteger)p0 barMetrics:(NSInteger)p1;
	-(UIImage *) shadowImageForToolbarPosition:(NSInteger)p0;
	-(UIToolbarAppearance *) scrollEdgeAppearance;
	-(void) setScrollEdgeAppearance:(UIToolbarAppearance *)p0;
	-(void) setBackgroundImage:(UIImage *)p0 forToolbarPosition:(NSInteger)p1 barMetrics:(NSInteger)p2;
	-(void) setShadowImage:(UIImage *)p0 forToolbarPosition:(NSInteger)p1;
	-(UIToolbarAppearance *) standardAppearance;
	-(void) setStandardAppearance:(UIToolbarAppearance *)p0;
	-(BOOL) isTranslucent;
	-(void) setTranslucent:(BOOL)p0;
@end

@interface UIKit_UIActivityIndicatorView_UIActivityIndicatorViewAppearance : UIKit_UIView_UIViewAppearance {
}
	-(UIColor *) color;
	-(void) setColor:(UIColor *)p0;
@end

@interface UIKit_UICalendarView_UICalendarViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UICollectionReusableView_UICollectionReusableViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UICollectionViewCell_UICollectionViewCellAppearance : UIKit_UICollectionReusableView_UICollectionReusableViewAppearance {
}
@end

@interface UIKit_UICollectionViewListCell_UICollectionViewListCellAppearance : UIKit_UICollectionViewCell_UICollectionViewCellAppearance {
}
@end

@interface UIKit_UIColorWell_UIColorWellAppearance : UIKit_UIControl_UIControlAppearance {
}
@end

@interface UIKit_UIContentUnavailableView_UIContentUnavailableViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIDatePicker_UIDatePickerAppearance : UIKit_UIControl_UIControlAppearance {
}
@end

@interface UIKit_UIEventAttributionView_UIEventAttributionViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIImageView_UIImageViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIInputView_UIInputViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UILabel_UILabelAppearance : UIKit_UIView_UIViewAppearance {
}
	-(UIFont *) font;
	-(void) setFont:(UIFont *)p0;
	-(UIColor *) highlightedTextColor;
	-(void) setHighlightedTextColor:(UIColor *)p0;
	-(NSInteger) preferredVibrancy;
	-(void) setPreferredVibrancy:(NSInteger)p0;
	-(UIColor *) shadowColor;
	-(void) setShadowColor:(UIColor *)p0;
	-(CGSize) shadowOffset;
	-(void) setShadowOffset:(CGSize)p0;
	-(UIColor *) textColor;
	-(void) setTextColor:(UIColor *)p0;
@end

@interface UIKit_UIListContentView_UIListContentViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIPageControl_UIPageControlAppearance : UIKit_UIControl_UIControlAppearance {
}
	-(UIColor *) currentPageIndicatorTintColor;
	-(void) setCurrentPageIndicatorTintColor:(UIColor *)p0;
	-(UIColor *) pageIndicatorTintColor;
	-(void) setPageIndicatorTintColor:(UIColor *)p0;
@end

@interface UIKit_UIPasteControl_UIPasteControlAppearance : UIKit_UIControl_UIControlAppearance {
}
@end

@interface UIKit_UIPopoverBackgroundView_UIPopoverBackgroundViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIProgressView_UIProgressViewAppearance : UIKit_UIView_UIViewAppearance {
}
	-(UIImage *) progressImage;
	-(void) setProgressImage:(UIImage *)p0;
	-(UIColor *) progressTintColor;
	-(void) setProgressTintColor:(UIColor *)p0;
	-(UIImage *) trackImage;
	-(void) setTrackImage:(UIImage *)p0;
	-(UIColor *) trackTintColor;
	-(void) setTrackTintColor:(UIColor *)p0;
@end

@interface UIKit_UIRefreshControl_UIRefreshControlAppearance : UIKit_UIControl_UIControlAppearance {
}
	-(NSAttributedString *) attributedTitle;
	-(void) setAttributedTitle:(NSAttributedString *)p0;
@end

@interface UIKit_UISearchTextField_UISearchTextFieldAppearance : UIKit_UITextField_UITextFieldAppearance {
}
@end

@interface UIKit_UISlider_UISliderAppearance : UIKit_UIControl_UIControlAppearance {
}
	-(UIImage *) maximumTrackImageForState:(NSUInteger)p0;
	-(UIImage *) maximumValueImage;
	-(void) setMaximumValueImage:(UIImage *)p0;
	-(UIColor *) maximumTrackTintColor;
	-(void) setMaximumTrackTintColor:(UIColor *)p0;
	-(UIImage *) minimumTrackImageForState:(NSUInteger)p0;
	-(UIImage *) minimumValueImage;
	-(void) setMinimumValueImage:(UIImage *)p0;
	-(UIColor *) minimumTrackTintColor;
	-(void) setMinimumTrackTintColor:(UIColor *)p0;
	-(void) setMaximumTrackImage:(UIImage *)p0 forState:(NSUInteger)p1;
	-(void) setMinimumTrackImage:(UIImage *)p0 forState:(NSUInteger)p1;
	-(void) setThumbImage:(UIImage *)p0 forState:(NSUInteger)p1;
	-(UIImage *) thumbImageForState:(NSUInteger)p0;
	-(UIColor *) thumbTintColor;
	-(void) setThumbTintColor:(UIColor *)p0;
@end

@interface UIKit_UIStackView_UIStackViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIStandardTextCursorView_UIStandardTextCursorViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIStepper_UIStepperAppearance : UIKit_UIControl_UIControlAppearance {
}
	-(UIImage *) backgroundImageForState:(NSUInteger)p0;
	-(UIImage *) decrementImageForState:(NSUInteger)p0;
	-(UIImage *) dividerImageForLeftSegmentState:(NSUInteger)p0 rightSegmentState:(NSUInteger)p1;
	-(UIImage *) incrementImageForState:(NSUInteger)p0;
	-(void) setBackgroundImage:(UIImage *)p0 forState:(NSUInteger)p1;
	-(void) setDecrementImage:(UIImage *)p0 forState:(NSUInteger)p1;
	-(void) setDividerImage:(UIImage *)p0 forLeftSegmentState:(NSUInteger)p1 rightSegmentState:(NSUInteger)p2;
	-(void) setIncrementImage:(UIImage *)p0 forState:(NSUInteger)p1;
@end

@interface UIKit_UISwitch_UISwitchAppearance : UIKit_UIControl_UIControlAppearance {
}
	-(UIImage *) offImage;
	-(void) setOffImage:(UIImage *)p0;
	-(UIImage *) onImage;
	-(void) setOnImage:(UIImage *)p0;
	-(UIColor *) onTintColor;
	-(void) setOnTintColor:(UIColor *)p0;
	-(UIColor *) thumbTintColor;
	-(void) setThumbTintColor:(UIColor *)p0;
@end

@interface UIKit_UITabBar_UITabBarAppearance : UIKit_UIView_UIViewAppearance {
}
	-(UIImage *) backgroundImage;
	-(void) setBackgroundImage:(UIImage *)p0;
	-(NSInteger) barStyle;
	-(void) setBarStyle:(NSInteger)p0;
	-(UIColor *) barTintColor;
	-(void) setBarTintColor:(UIColor *)p0;
	-(NSInteger) itemPositioning;
	-(void) setItemPositioning:(NSInteger)p0;
	-(CGFloat) itemSpacing;
	-(void) setItemSpacing:(CGFloat)p0;
	-(CGFloat) itemWidth;
	-(void) setItemWidth:(CGFloat)p0;
	-(UITabBarAppearance *) scrollEdgeAppearance;
	-(void) setScrollEdgeAppearance:(UITabBarAppearance *)p0;
	-(UIColor *) selectedImageTintColor;
	-(void) setSelectedImageTintColor:(UIColor *)p0;
	-(UIImage *) selectionIndicatorImage;
	-(void) setSelectionIndicatorImage:(UIImage *)p0;
	-(UIImage *) shadowImage;
	-(void) setShadowImage:(UIImage *)p0;
	-(UITabBarAppearance *) standardAppearance;
	-(void) setStandardAppearance:(UITabBarAppearance *)p0;
	-(UIColor *) unselectedItemTintColor;
	-(void) setUnselectedItemTintColor:(UIColor *)p0;
@end

@interface UIKit_UITabBarItem_UITabBarItemAppearance : UIKit_UIBarItem_UIBarItemAppearance {
}
	-(UIColor *) badgeColor;
	-(void) setBadgeColor:(UIColor *)p0;
	-(NSDictionary <NSString *, NSObject *>*) badgeTextAttributesForState:(NSUInteger)p0;
	-(UITabBarAppearance *) scrollEdgeAppearance;
	-(void) setScrollEdgeAppearance:(UITabBarAppearance *)p0;
	-(void) setBadgeTextAttributes:(NSDictionary *)p0 forState:(NSUInteger)p1;
	-(UITabBarAppearance *) standardAppearance;
	-(void) setStandardAppearance:(UITabBarAppearance *)p0;
	-(UIOffset) titlePositionAdjustment;
	-(void) setTitlePositionAdjustment:(UIOffset)p0;
@end

@interface UIKit_UITableViewHeaderFooterView_UITableViewHeaderFooterViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIVisualEffectView_UIVisualEffectViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIWebView_UIWebViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface UIKit_UIWindow_UIWindowAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface EventKitUI_EKEventEditViewController_EKEventEditViewControllerAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface CarPlay_CPWindow_CPWindowAppearance : UIKit_UIWindow_UIWindowAppearance {
}
@end

@interface BusinessChat_BCChatButton_BCChatButtonAppearance : UIKit_UIControl_UIControlAppearance {
}
@end

@interface BrowserEngineKit_BELayerHierarchyHostingView_BELayerHierarchyHostingViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface BrowserEngineKit_BEScrollView_BEScrollViewAppearance : UIKit_UIScrollView_UIScrollViewAppearance {
}
@end

@interface AuthenticationServices_ASAuthorizationAppleIdButton_ASAuthorizationAppleIdButtonAppearance : UIKit_UIControl_UIControlAppearance {
}
@end

@interface AddressBookUI_ABPeoplePickerNavigationController_ABPeoplePickerNavigationControllerAppearance : NSObject {
}
	-(void) release;
	-(id) retain;
	-(GCHandle) xamarinGetGCHandle;
	-(bool) xamarinSetGCHandle: (GCHandle) gchandle flags: (enum XamarinGCHandleFlags) flags;
	-(enum XamarinGCHandleFlags) xamarinGetFlags;
	-(void) xamarinSetFlags: (enum XamarinGCHandleFlags) flags;
	-(BOOL) conformsToProtocol:(void *)p0;
@end

@interface AVKit_AVRoutePickerView_AVRoutePickerViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface ARKit_ARCoachingOverlayView_ARCoachingOverlayViewAppearance : UIKit_UIView_UIViewAppearance {
}
@end

@interface ARKit_ARSCNView_ARSCNViewAppearance : SceneKit_SCNView_SCNViewAppearance {
}
@end

@interface ARKit_ARSKView_ARSKViewAppearance : SpriteKit_SKView_SKViewAppearance {
}
@end


