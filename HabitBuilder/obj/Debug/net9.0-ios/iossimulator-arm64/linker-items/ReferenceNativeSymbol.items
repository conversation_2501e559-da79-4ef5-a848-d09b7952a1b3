﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ReferenceNativeSymbol Include="_xamarin_CGPoint__VNNormalizedFaceBoundingBoxPointForLandmarkPoint_Vector2_CGRect_nuint_nuint_string">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_CGPoint__VNImagePointForFaceLandmarkPoint_Vector2_CGRect_nuint_nuint_string">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_OBJC_CLASS_$_XamarinSwiftFunctions">
      <SymbolType>ObjectiveCClass</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_UIApplicationMain">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_os_log">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_release_managed_ref">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_set_gchandle_with_flags_safe">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format_1">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format_2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format_3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format_4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format_5">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format_6">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format_7">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format_8">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_localized_string_format_9">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_init_nsthread">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_locate_assembly_resource">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_switch_gchandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_mono_object_retain">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_find_protocol_wrapper_type">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_is_user_type">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_log">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_release_block_on_main_thread">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_get_original_working_directory_path">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_get_runtime_arch">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_start_wwan">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_get_block_descriptor">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_is_object_valid">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_free">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix3_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix3_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix3_objc_msgSend_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix3_objc_msgSendSuper_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__CGPoint_objc_msgSend_NVector3_IntPtr_CGSize">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__CGPoint_objc_msgSendSuper_NVector3_IntPtr_CGSize">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3_objc_msgSend_CGPoint_NMatrix4_IntPtr_CGSize">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3_objc_msgSendSuper_CGPoint_NMatrix4_IntPtr_CGSize">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_IntPtr_CGSize_nfloat_nfloat">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_IntPtr_CGSize_nfloat_nfloat">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_stret_IntPtr_CGSize_nfloat_nfloat">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_stret_IntPtr_CGSize_nfloat_nfloat">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_stret_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_stret_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NMatrix4_NVector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NMatrix4_NVector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NMatrix4_NVector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NMatrix4_NVector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NVector3_NVector3_IntPtr_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector3_NVector3_IntPtr_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3_objc_msgSend_CGPoint_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3_objc_msgSendSuper_CGPoint_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NMatrix4_NVector3_NVector3_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NMatrix4_NVector3_NVector3_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NVector3_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NVector3_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSend_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSendSuper_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_stret_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_stret_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4x3_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4x3_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4x3_objc_msgSend_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4x3_objc_msgSendSuper_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_NativeHandle_objc_msgSend_NativeHandle_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_NativeHandle_objc_msgSendSuper_NativeHandle_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_NativeHandle_objc_msgSend_NativeHandle_NativeHandle_UIntPtr_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_NativeHandle_objc_msgSendSuper_NativeHandle_NativeHandle_UIntPtr_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_NativeHandle_objc_msgSend_NativeHandle_IntPtr_IntPtr_IntPtr_UIntPtr_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_NativeHandle_objc_msgSendSuper_NativeHandle_IntPtr_IntPtr_IntPtr_UIntPtr_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_NativeHandle_objc_msgSend_NativeHandle_UIntPtr_IntPtr_UIntPtr_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_IntPtr_UIntPtr_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NMatrix3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NMatrix3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector2i_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector2i_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NVector2i_int_int_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2i_int_int_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NVector2i_int_int_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2i_int_int_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_float_Vector2_Vector2_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_float_Vector2_Vector2_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_float_Vector2_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_float_Vector2_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKTriangle_objc_msgSend_UIntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKTriangle_objc_msgSendSuper_UIntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKTriangle_objc_msgSend_stret_UIntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKTriangle_objc_msgSendSuper_stret_UIntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__float_objc_msgSend_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__float_objc_msgSendSuper_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NVector3d">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NVector3d">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector2d_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector2d_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NVector2d_NVector2d_NVector2i_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NVector2d_NVector2d_NVector2i_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__float_objc_msgSend_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__float_objc_msgSendSuper_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_float_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_float_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKBox_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKBox_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKBox_objc_msgSend_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKBox_objc_msgSendSuper_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_GKBox_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_GKBox_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_GKBox">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_GKBox">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_GKBox">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_GKBox">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSend_UIntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSendSuper_UIntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSend_UIntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSendSuper_UIntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_GKQuad_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_GKQuad_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Vector2_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Vector2_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_GKQuad">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_GKQuad">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_GKQuad">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_GKQuad">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKQuad_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__GKQuad_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NativeHandle_Vector2_Vector2_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NativeHandle_Vector2_Vector2_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NativeHandle_Vector2_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NativeHandle_Vector2_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_Vector2_Vector2_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector2_Vector2_exception">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MPSAxisAlignedBoundingBox_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MPSAxisAlignedBoundingBox_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MPSAxisAlignedBoundingBox_objc_msgSend_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MPSAxisAlignedBoundingBox_objc_msgSendSuper_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MPSImageHistogramInfo_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MPSImageHistogramInfo_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MPSImageHistogramInfo_objc_msgSend_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MPSImageHistogramInfo_objc_msgSendSuper_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector4_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector4_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_ref_MPSImageHistogramInfo">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_ref_MPSImageHistogramInfo">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NMatrix4_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NMatrix4_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NMatrix4d_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NMatrix4d_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4d_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4d_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4d_objc_msgSend_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4d_objc_msgSendSuper_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_Quaternion_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_Quaternion_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NQuaterniond_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NQuaterniond_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Quaternion_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Quaternion_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NQuaterniond_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NQuaterniond_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NQuaterniond_objc_msgSend_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NQuaterniond_objc_msgSendSuper_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_Vector2_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_Vector2_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NVector2d_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NVector2d_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector2d_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector2d_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NVector3_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NVector3_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NVector3d_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NVector3d_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3d_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3d_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3d_objc_msgSend_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector3d_objc_msgSendSuper_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_Vector4_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_Vector4_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NVector4d_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NVector4d_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector4_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector4_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector4d_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector4d_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector4d_objc_msgSend_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector4d_objc_msgSendSuper_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4d_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4d_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4d_objc_msgSend_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4d_objc_msgSendSuper_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NMatrix4d">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NMatrix4d">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NVector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NVector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_stret_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_MDLAxisAlignedBoundingBox_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_MDLAxisAlignedBoundingBox_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_Vector3_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_Vector3_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSend_NVector2i_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSendSuper_NVector2i_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_bool_NativeHandle_NVector2i_IntPtr_UIntPtr_IntPtr_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_bool_NativeHandle_NVector2i_IntPtr_UIntPtr_IntPtr_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_float_NativeHandle_NVector2i_int_IntPtr_NativeHandle_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_float_NativeHandle_NVector2i_int_IntPtr_NativeHandle_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_float_float_NativeHandle_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_float_float_NativeHandle_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NativeHandle_NativeHandle_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NativeHandle_NativeHandle_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_Vector3_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector3_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_UIntPtr_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_UIntPtr_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_UIntPtr_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_UIntPtr_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_UIntPtr_NMatrix4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector3i_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector3i_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_bool_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_bool_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_bool_bool_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_bool_bool_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_int_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_int_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_Vector3_NVector2i_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_NVector2i_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_Vector3_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_Vector3_bool_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_NativeHandle_int_UInt32_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_NativeHandle_int_UInt32_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_Vector3_NVector3i_IntPtr_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector3_NVector3i_IntPtr_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_Vector2_NVector2i_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector2_NVector2i_IntPtr_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_Vector3_UIntPtr_UIntPtr_IntPtr_bool_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector3_UIntPtr_UIntPtr_IntPtr_bool_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_float_Vector2_UIntPtr_UIntPtr_IntPtr_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_float_Vector2_UIntPtr_UIntPtr_IntPtr_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_float_Vector2_UIntPtr_UIntPtr_UIntPtr_IntPtr_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_float_Vector2_UIntPtr_UIntPtr_UIntPtr_IntPtr_bool_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__bool_objc_msgSend_NVector2i_IntPtr_float_NativeHandle_NativeHandle_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__bool_objc_msgSendSuper_NVector2i_IntPtr_float_NativeHandle_NativeHandle_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__bool_objc_msgSend_NVector2i_NativeHandle_NativeHandle_NativeHandle_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__bool_objc_msgSendSuper_NVector2i_NativeHandle_NativeHandle_NativeHandle_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSend_float_NativeHandle_NVector2i_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__IntPtr_objc_msgSendSuper_float_NativeHandle_NVector2i_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_float_NativeHandle_NVector2i_int_IntPtr_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_float_NativeHandle_NVector2i_int_IntPtr_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_IntPtr_NVector2i_float_float_float_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_IntPtr_NVector2i_float_float_float_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_IntPtr_NVector2i_float_float_float_float_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_IntPtr_NVector2i_float_float_float_float_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NativeHandle_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NativeHandle_NVector2i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NativeHandle_NVector2i_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NativeHandle_NVector2i_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NMatrix4_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NMatrix4_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSend_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSendSuper_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_Vector3_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_Vector3_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_NativeHandle_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_NativeHandle_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSend_stret_NativeHandle_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix4_objc_msgSendSuper_stret_NativeHandle_Double">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLVoxelIndexExtent_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLVoxelIndexExtent_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLVoxelIndexExtent_objc_msgSend_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLVoxelIndexExtent_objc_msgSendSuper_stret">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_MDLAxisAlignedBoundingBox_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_MDLAxisAlignedBoundingBox_float">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__bool_objc_msgSend_NVector4i_bool_bool_bool_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__bool_objc_msgSendSuper_NVector4i_bool_bool_bool_bool">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NVector4i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NVector4i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_MDLVoxelIndexExtent">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_MDLVoxelIndexExtent">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector4i_objc_msgSend_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NVector4i_objc_msgSendSuper_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSend_NVector4i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSendSuper_NVector4i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_NVector4i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_NVector4i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSend_stret_NVector4i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__MDLAxisAlignedBoundingBox_objc_msgSendSuper_stret_NVector4i">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Quaternion_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Quaternion_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Quaternion_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Quaternion_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Quaternion">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Quaternion">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NVector2d_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2d_NativeHandle">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NVector2d">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NVector2d">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NVector2d_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NVector2d_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSend_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector3_objc_msgSendSuper_Vector3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_vector_float3__Vector4_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_vector_float3__Vector4_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_vector_float3__void_objc_msgSend_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_vector_float3__void_objc_msgSendSuper_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_Quaternion">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_Quaternion">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix2_objc_msgSend">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NMatrix2_objc_msgSendSuper">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSend_NMatrix2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__void_objc_msgSendSuper_NMatrix2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Vector2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_Vector4">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NMatrix2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NMatrix2">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSend_NativeHandle_NMatrix3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__NativeHandle_objc_msgSendSuper_NativeHandle_NMatrix3">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSend_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
    <ReferenceNativeSymbol Include="_xamarin_simd__Vector2_objc_msgSendSuper_IntPtr">
      <SymbolType>Function</SymbolType>
    </ReferenceNativeSymbol>
  </ItemGroup>
</Project>