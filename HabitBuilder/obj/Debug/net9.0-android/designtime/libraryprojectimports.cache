<Paths><Jars><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/97/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/98/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/99/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/100/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/101/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/102/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/104/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/105/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/106/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/107/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/108/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/109/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/110/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/111/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/112/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/113/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/115/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/116/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/117/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/118/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/119/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/120/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/121/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/122/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/123/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/124/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/125/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/126/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/127/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/128/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/129/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/130/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/130/jl/libs/repackaged.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/131/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/132/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/133/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/134/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/135/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/136/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/137/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/138/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/139/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/140/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/141/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/142/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/143/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/144/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/145/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/146/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/147/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/149/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/150/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/151/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/152/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/153/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/154/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/155/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/156/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/157/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/158/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/159/jl/libs/ED64959F88B22E6D.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/160/jl/libs/2E7FD15AFA9B216B.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/163/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/166/jl/libs/659D48BEBA477FDD.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/167/jl/libs/95D547F40BE4687C.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/168/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/169/jl/classes.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/170/jl/libs/F975D0960055A5E3.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/171/jl/libs/B71CFF5D5A0B3AEB.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/172/jl/libs/8956669C0037225B.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/173/jl/libs/05D34BA5CA8637CF.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/174/jl/libs/F91D79B39A269A7E.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/175/jl/libs/327D95CCFE836D7B.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/176/jl/libs/C7DE71AD77BC3A1F.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/177/jl/libs/F01C7EADE990A004.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/178/jl/libs/F6DB49E0CC905A9A.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/179/jl/libs/A1A754E9E8693F85.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/180/jl/libs/B7446A23346A729D.jar</Jar><Jar>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/181/jl/libs/623E6E29E2058512.jar</Jar></Jars><ResolvedResourceDirectories><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.versionedparcelable/*******/aar/androidx.versionedparcelable.versionedparcelable.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/97/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/97/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.tracing.tracing/*******/aar/androidx.tracing.tracing.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/98/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/98/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.startup.startupruntime/*******/aar/androidx.startup.startup-runtime.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/99/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/99/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.profileinstaller.profileinstaller/*******/aar/androidx.profileinstaller.profileinstaller.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/100/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/100/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.runtime/********/aar/androidx.arch.core.core-runtime.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/101/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/101/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.android/*******/aar/androidx.lifecycle.lifecycle-runtime-android.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/102/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/102/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.interpolator/********/aar/androidx.interpolator.interpolator.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/104/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/104/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.experimental/*******/aar/androidx.annotation.annotation-experimental.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/105/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/105/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core/********/aar/androidx.core.core.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/106/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/106/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview/********/aar/androidx.customview.customview.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/107/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/107/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager/*******/aar/androidx.viewpager.viewpager.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/108/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/108/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate/********/aar/androidx.savedstate.savedstate.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/109/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/109/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core.core.ktx/********/aar/androidx.core.core-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/110/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/110/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview.poolingcontainer/********/aar/androidx.customview.customview-poolingcontainer.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/111/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/111/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.recyclerview/********/aar/androidx.recyclerview.recyclerview.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/112/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/112/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.android/*******/aar/androidx.lifecycle.lifecycle-viewmodel-android.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/113/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/113/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core/*******/aar/androidx.lifecycle.lifecycle-livedata-core.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/115/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/115/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.loader/********/aar/androidx.loader.loader.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/116/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/116/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodelsavedstate/*******/aar/androidx.lifecycle.lifecycle-viewmodel-savedstate.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/117/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/117/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity/*******/aar/androidx.activity.activity.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/118/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/118/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment/*******/aar/androidx.fragment.fragment.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/119/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/119/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager2/*******/aar/androidx.viewpager2.viewpager2.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/120/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/120/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable/*******/aar/androidx.vectordrawable.vectordrawable.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/121/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/121/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable.animated/*******/aar/androidx.vectordrawable.vectordrawable-animated.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/122/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/122/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.print/********/aar/androidx.print.print.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/123/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/123/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.localbroadcastmanager/*******9/aar/androidx.localbroadcastmanager.localbroadcastmanager.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/124/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/124/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.documentfile/********/aar/androidx.documentfile.documentfile.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/125/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/125/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.legacy.support.core.utils/********/aar/androidx.legacy.legacy-support-core-utils.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/126/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/126/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.dynamicanimation/********/aar/androidx.dynamicanimation.dynamicanimation.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/127/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/127/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.transition/*******/aar/androidx.transition.transition.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/128/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/128/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.process/*******/aar/androidx.lifecycle.lifecycle-process.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/129/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/129/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2/*******/aar/androidx.emoji2.emoji2.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/130/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/130/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2.viewshelper/*******/aar/androidx.emoji2.emoji2-views-helper.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/131/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/131/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.drawerlayout/********/aar/androidx.drawerlayout.drawerlayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/132/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/132/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cursoradapter/********/aar/androidx.cursoradapter.cursoradapter.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/133/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/133/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.coordinatorlayout/********/aar/androidx.coordinatorlayout.coordinatorlayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/134/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/134/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat.appcompatresources/*******/aar/androidx.appcompat.appcompat-resources.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/135/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/135/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat/*******/aar/androidx.appcompat.appcompat.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/136/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/136/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout/*******/aar/androidx.constraintlayout.constraintlayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/137/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/137/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cardview/********/aar/androidx.cardview.cardview.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/138/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/138/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.android.material/********/aar/com.google.android.material.material.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/139/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/139/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window.extensions.core.core/********/aar/androidx.window.extensions.core.core.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/140/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/140/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window/*******/aar/androidx.window.window.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/141/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/141/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.swiperefreshlayout/********/aar/androidx.swiperefreshlayout.swiperefreshlayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/142/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/142/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.slidingpanelayout/********/aar/androidx.slidingpanelayout.slidingpanelayout.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/143/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/143/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.security.securitycrypto/*******-alpha06/aar/androidx.security.security-crypto.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/144/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/144/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate.savedstate.ktx/********/aar/androidx.savedstate.savedstate-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/145/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/145/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.ktx/*******/aar/androidx.lifecycle.lifecycle-viewmodel-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/146/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/146/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx.android/*******/aar/androidx.lifecycle.lifecycle-runtime-ktx-android.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/147/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/147/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.common/*******/aar/androidx.navigation.navigation-common.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/149/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/149/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity.ktx/*******/aar/androidx.activity.activity-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/150/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/150/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.runtime/*******/aar/androidx.navigation.navigation-runtime.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/151/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/151/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.ui/*******/aar/androidx.navigation.navigation-ui.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/152/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/152/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core.ktx/*******/aar/androidx.lifecycle.lifecycle-livedata-core-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/153/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/153/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment.ktx/*******/aar/androidx.fragment.fragment-ktx.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/154/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/154/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.fragment/*******/aar/androidx.navigation.navigation-fragment.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/155/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/155/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata/*******/aar/androidx.lifecycle.lifecycle-livedata.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/156/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/156/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.exifinterface/*******/aar/androidx.exifinterface.exifinterface.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/157/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/157/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.browser/*******/aar/androidx.browser.browser.aar" AndroidSkipResourceProcessing="true" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/158/jl/res.zip">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/158/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.controls.core/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.Controls.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/162/jl/res.zip" NuGetPackageId="Microsoft.Maui.Controls.Core" NuGetPackageVersion="9.0.51">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/162/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.core/9.0.51/lib/net9.0-android35.0/maui.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/163/jl/res.zip" NuGetPackageId="Microsoft.Maui.Core" NuGetPackageVersion="9.0.51">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/163/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.core/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/164/jl/res.zip" NuGetPackageId="Microsoft.Maui.Core" NuGetPackageVersion="9.0.51">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/164/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.essentials/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.Essentials.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/165/jl/res.zip" NuGetPackageId="Microsoft.Maui.Essentials" NuGetPackageVersion="9.0.51">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/165/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide/*********/lib/net8.0-android34.0/glide.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/168/jl/res.zip" NuGetPackageId="Xamarin.Android.Glide" NuGetPackageVersion="*********">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/168/jl/res</ResolvedResourceDirectory><ResolvedResourceDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.gifdecoder/*********/lib/net8.0-android34.0/gifdecoder.aar" AndroidSkipResourceProcessing="false" ResourceDirectoryArchive="/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/169/jl/res.zip" NuGetPackageId="Xamarin.Android.Glide.GifDecoder" NuGetPackageVersion="*********">/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/169/jl/res</ResolvedResourceDirectory></ResolvedResourceDirectories><ResolvedAssetDirectories /><ResolvedEnvironmentFiles /><ResolvedResourceDirectoryStamps><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/97.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/98.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/99.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/100.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/101.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/102.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/104.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/105.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/106.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/107.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/108.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/109.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/110.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/111.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/112.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/113.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/115.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/116.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/117.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/118.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/119.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/120.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/121.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/122.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/123.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/124.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/125.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/126.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/127.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/128.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/129.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/130.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/131.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/132.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/133.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/134.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/135.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/136.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/137.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/138.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/139.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/140.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/141.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/142.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/143.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/144.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/145.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/146.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/147.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/149.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/150.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/151.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/152.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/153.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/154.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/155.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/156.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/157.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/158.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/162.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/163.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/164.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/165.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/168.stamp</ResolvedResourceDirectoryStamp><ResolvedResourceDirectoryStamp>/Users/<USER>/Documents/src/habit-builder/HabitBuilder/obj/Debug/net9.0-android/lp/169.stamp</ResolvedResourceDirectoryStamp></ResolvedResourceDirectoryStamps><ProguardConfigFiles /><ExtractedDirectories><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/googlegson/2.11.0.5/lib/net8.0-android34.0/GoogleGson.dll" NuGetPackageId="GoogleGson" NuGetPackageVersion="2.11.0.5">obj/Debug/net9.0-android/lp/0</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.code.findbugs.jsr305/3.0.2.18/lib/net8.0-android34.0/Jsr305Binding.dll" NuGetPackageId="Xamarin.Google.Code.FindBugs.JSR305" NuGetPackageVersion="3.0.2.18">obj/Debug/net9.0-android/lp/1</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.identity.client/4.73.1/lib/net8.0-android34.0/Microsoft.Identity.Client.dll" NuGetPackageId="Microsoft.Identity.Client" NuGetPackageVersion="4.73.1">obj/Debug/net9.0-android/lp/2</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.controls.core/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.Controls.dll" NuGetPackageId="Microsoft.Maui.Controls.Core" NuGetPackageVersion="9.0.51">obj/Debug/net9.0-android/lp/3</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.controls.xaml/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.Controls.Xaml.dll" NuGetPackageId="Microsoft.Maui.Controls.Xaml" NuGetPackageVersion="9.0.51">obj/Debug/net9.0-android/lp/4</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.core/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.dll" NuGetPackageId="Microsoft.Maui.Core" NuGetPackageVersion="9.0.51">obj/Debug/net9.0-android/lp/5</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.essentials/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.Essentials.dll" NuGetPackageId="Microsoft.Maui.Essentials" NuGetPackageVersion="9.0.51">obj/Debug/net9.0-android/lp/6</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.graphics/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.Graphics.dll" NuGetPackageId="Microsoft.Maui.Graphics" NuGetPackageVersion="9.0.51">obj/Debug/net9.0-android/lp/7</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.annotations/*********/lib/net8.0-android34.0/Xamarin.Android.Glide.Annotations.dll" NuGetPackageId="Xamarin.Android.Glide.Annotations" NuGetPackageVersion="*********">obj/Debug/net9.0-android/lp/8</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.disklrucache/*********/lib/net8.0-android34.0/Xamarin.Android.Glide.DiskLruCache.dll" NuGetPackageId="Xamarin.Android.Glide.DiskLruCache" NuGetPackageVersion="*********">obj/Debug/net9.0-android/lp/9</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide/*********/lib/net8.0-android34.0/Xamarin.Android.Glide.dll" NuGetPackageId="Xamarin.Android.Glide" NuGetPackageVersion="*********">obj/Debug/net9.0-android/lp/10</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.gifdecoder/*********/lib/net8.0-android34.0/Xamarin.Android.Glide.GifDecoder.dll" NuGetPackageId="Xamarin.Android.Glide.GifDecoder" NuGetPackageVersion="*********">obj/Debug/net9.0-android/lp/11</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Activity.dll" NuGetPackageId="Xamarin.AndroidX.Activity" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/12</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity.ktx/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Activity.Ktx.dll" NuGetPackageId="Xamarin.AndroidX.Activity.Ktx" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/13</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.annotation/1.9.1.2/lib/net8.0-android34.0/Xamarin.AndroidX.Annotation.dll" NuGetPackageId="Xamarin.AndroidX.Annotation" NuGetPackageVersion="1.9.1.2">obj/Debug/net9.0-android/lp/14</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.experimental/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Annotation.Experimental.dll" NuGetPackageId="Xamarin.AndroidX.Annotation.Experimental" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/15</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.jvm/1.9.1.2/lib/net8.0-android34.0/Xamarin.AndroidX.Annotation.Jvm.dll" NuGetPackageId="Xamarin.AndroidX.Annotation.Jvm" NuGetPackageVersion="1.9.1.2">obj/Debug/net9.0-android/lp/16</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat.appcompatresources/*******/lib/net8.0-android34.0/Xamarin.AndroidX.AppCompat.AppCompatResources.dll" NuGetPackageId="Xamarin.AndroidX.AppCompat.AppCompatResources" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/17</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat/*******/lib/net8.0-android34.0/Xamarin.AndroidX.AppCompat.dll" NuGetPackageId="Xamarin.AndroidX.AppCompat" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/18</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.common/********/lib/net8.0-android34.0/Xamarin.AndroidX.Arch.Core.Common.dll" NuGetPackageId="Xamarin.AndroidX.Arch.Core.Common" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/19</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.runtime/********/lib/net8.0-android34.0/Xamarin.AndroidX.Arch.Core.Runtime.dll" NuGetPackageId="Xamarin.AndroidX.Arch.Core.Runtime" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/20</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.browser/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Browser.dll" NuGetPackageId="Xamarin.AndroidX.Browser" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/21</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cardview/********/lib/net8.0-android34.0/Xamarin.AndroidX.CardView.dll" NuGetPackageId="Xamarin.AndroidX.CardView" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/22</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.collection/1.4.5.2/lib/net8.0-android34.0/Xamarin.AndroidX.Collection.dll" NuGetPackageId="Xamarin.AndroidX.Collection" NuGetPackageVersion="1.4.5.2">obj/Debug/net9.0-android/lp/23</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.collection.jvm/1.4.5.2/lib/net8.0-android34.0/Xamarin.AndroidX.Collection.Jvm.dll" NuGetPackageId="Xamarin.AndroidX.Collection.Jvm" NuGetPackageVersion="1.4.5.2">obj/Debug/net9.0-android/lp/24</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.collection.ktx/1.4.5.2/lib/net8.0-android34.0/Xamarin.AndroidX.Collection.Ktx.dll" NuGetPackageId="Xamarin.AndroidX.Collection.Ktx" NuGetPackageVersion="1.4.5.2">obj/Debug/net9.0-android/lp/25</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.concurrent.futures/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Concurrent.Futures.dll" NuGetPackageId="Xamarin.AndroidX.Concurrent.Futures" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/26</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout.core/*******/lib/net8.0-android34.0/Xamarin.AndroidX.ConstraintLayout.Core.dll" NuGetPackageId="Xamarin.AndroidX.ConstraintLayout.Core" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/27</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout/*******/lib/net8.0-android34.0/Xamarin.AndroidX.ConstraintLayout.dll" NuGetPackageId="Xamarin.AndroidX.ConstraintLayout" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/28</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.coordinatorlayout/********/lib/net8.0-android34.0/Xamarin.AndroidX.CoordinatorLayout.dll" NuGetPackageId="Xamarin.AndroidX.CoordinatorLayout" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/29</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core.core.ktx/********/lib/net8.0-android34.0/Xamarin.AndroidX.Core.Core.Ktx.dll" NuGetPackageId="Xamarin.AndroidX.Core.Core.Ktx" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/30</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core/********/lib/net8.0-android34.0/Xamarin.AndroidX.Core.dll" NuGetPackageId="Xamarin.AndroidX.Core" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/31</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cursoradapter/********/lib/net8.0-android34.0/Xamarin.AndroidX.CursorAdapter.dll" NuGetPackageId="Xamarin.AndroidX.CursorAdapter" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/32</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview/********/lib/net8.0-android34.0/Xamarin.AndroidX.CustomView.dll" NuGetPackageId="Xamarin.AndroidX.CustomView" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/33</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview.poolingcontainer/********/lib/net8.0-android34.0/Xamarin.AndroidX.CustomView.PoolingContainer.dll" NuGetPackageId="Xamarin.AndroidX.CustomView.PoolingContainer" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/34</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.documentfile/********/lib/net8.0-android34.0/Xamarin.AndroidX.DocumentFile.dll" NuGetPackageId="Xamarin.AndroidX.DocumentFile" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/35</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.drawerlayout/********/lib/net8.0-android34.0/Xamarin.AndroidX.DrawerLayout.dll" NuGetPackageId="Xamarin.AndroidX.DrawerLayout" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/36</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.dynamicanimation/********/lib/net8.0-android34.0/Xamarin.AndroidX.DynamicAnimation.dll" NuGetPackageId="Xamarin.AndroidX.DynamicAnimation" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/37</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Emoji2.dll" NuGetPackageId="Xamarin.AndroidX.Emoji2" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/38</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2.viewshelper/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Emoji2.ViewsHelper.dll" NuGetPackageId="Xamarin.AndroidX.Emoji2.ViewsHelper" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/39</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.exifinterface/*******/lib/net8.0-android34.0/Xamarin.AndroidX.ExifInterface.dll" NuGetPackageId="Xamarin.AndroidX.ExifInterface" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/40</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Fragment.dll" NuGetPackageId="Xamarin.AndroidX.Fragment" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/41</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment.ktx/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Fragment.Ktx.dll" NuGetPackageId="Xamarin.AndroidX.Fragment.Ktx" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/42</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.interpolator/********/lib/net8.0-android34.0/Xamarin.AndroidX.Interpolator.dll" NuGetPackageId="Xamarin.AndroidX.Interpolator" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/43</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.legacy.support.core.utils/********/lib/net8.0-android34.0/Xamarin.AndroidX.Legacy.Support.Core.Utils.dll" NuGetPackageId="Xamarin.AndroidX.Legacy.Support.Core.Utils" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/44</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.common/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.Common.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.Common" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/45</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.common.jvm/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.Common.Jvm.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.Common.Jvm" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/46</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.LiveData.Core.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.LiveData.Core" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/47</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core.ktx/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/48</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.LiveData.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.LiveData" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/49</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.process/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.Process.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.Process" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/50</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.android/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.Runtime.Android.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.Runtime.Android" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/51</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.Runtime.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.Runtime" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/52</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx.android/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.Runtime.Ktx.Android.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.Runtime.Ktx.Android" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/53</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.Runtime.Ktx.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.Runtime.Ktx" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/54</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.android/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.ViewModel.Android.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.ViewModel.Android" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/55</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.ViewModel.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.ViewModel" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/56</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.ktx/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.ViewModel.Ktx" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/57</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodelsavedstate/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Lifecycle.ViewModelSavedState.dll" NuGetPackageId="Xamarin.AndroidX.Lifecycle.ViewModelSavedState" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/58</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.loader/********/lib/net8.0-android34.0/Xamarin.AndroidX.Loader.dll" NuGetPackageId="Xamarin.AndroidX.Loader" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/59</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.localbroadcastmanager/*******9/lib/net8.0-android34.0/Xamarin.AndroidX.LocalBroadcastManager.dll" NuGetPackageId="Xamarin.AndroidX.LocalBroadcastManager" NuGetPackageVersion="*******9">obj/Debug/net9.0-android/lp/60</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.common/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Navigation.Common.dll" NuGetPackageId="Xamarin.AndroidX.Navigation.Common" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/61</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.fragment/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Navigation.Fragment.dll" NuGetPackageId="Xamarin.AndroidX.Navigation.Fragment" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/62</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.runtime/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Navigation.Runtime.dll" NuGetPackageId="Xamarin.AndroidX.Navigation.Runtime" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/63</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.ui/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Navigation.UI.dll" NuGetPackageId="Xamarin.AndroidX.Navigation.UI" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/64</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.print/********/lib/net8.0-android34.0/Xamarin.AndroidX.Print.dll" NuGetPackageId="Xamarin.AndroidX.Print" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/65</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.profileinstaller.profileinstaller/*******/lib/net8.0-android34.0/Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.dll" NuGetPackageId="Xamarin.AndroidX.ProfileInstaller.ProfileInstaller" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/66</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.recyclerview/********/lib/net8.0-android34.0/Xamarin.AndroidX.RecyclerView.dll" NuGetPackageId="Xamarin.AndroidX.RecyclerView" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/67</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.resourceinspection.annotation/1.0.1.19/lib/net8.0-android34.0/Xamarin.AndroidX.ResourceInspection.Annotation.dll" NuGetPackageId="Xamarin.AndroidX.ResourceInspection.Annotation" NuGetPackageVersion="1.0.1.19">obj/Debug/net9.0-android/lp/68</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate/********/lib/net8.0-android34.0/Xamarin.AndroidX.SavedState.dll" NuGetPackageId="Xamarin.AndroidX.SavedState" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/69</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate.savedstate.ktx/********/lib/net8.0-android34.0/Xamarin.AndroidX.SavedState.SavedState.Ktx.dll" NuGetPackageId="Xamarin.AndroidX.SavedState.SavedState.Ktx" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/70</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.security.securitycrypto/*******-alpha06/lib/net8.0-android34.0/Xamarin.AndroidX.Security.SecurityCrypto.dll" NuGetPackageId="Xamarin.AndroidX.Security.SecurityCrypto" NuGetPackageVersion="*******-alpha06">obj/Debug/net9.0-android/lp/71</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.slidingpanelayout/********/lib/net8.0-android34.0/Xamarin.AndroidX.SlidingPaneLayout.dll" NuGetPackageId="Xamarin.AndroidX.SlidingPaneLayout" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/72</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.startup.startupruntime/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Startup.StartupRuntime.dll" NuGetPackageId="Xamarin.AndroidX.Startup.StartupRuntime" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/73</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.swiperefreshlayout/********/lib/net8.0-android34.0/Xamarin.AndroidX.SwipeRefreshLayout.dll" NuGetPackageId="Xamarin.AndroidX.SwipeRefreshLayout" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/74</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.tracing.tracing/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Tracing.Tracing.dll" NuGetPackageId="Xamarin.AndroidX.Tracing.Tracing" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/75</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.transition/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Transition.dll" NuGetPackageId="Xamarin.AndroidX.Transition" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/76</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable.animated/*******/lib/net8.0-android34.0/Xamarin.AndroidX.VectorDrawable.Animated.dll" NuGetPackageId="Xamarin.AndroidX.VectorDrawable.Animated" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/77</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable/*******/lib/net8.0-android34.0/Xamarin.AndroidX.VectorDrawable.dll" NuGetPackageId="Xamarin.AndroidX.VectorDrawable" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/78</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.versionedparcelable/*******/lib/net8.0-android34.0/Xamarin.AndroidX.VersionedParcelable.dll" NuGetPackageId="Xamarin.AndroidX.VersionedParcelable" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/79</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager/*******/lib/net8.0-android34.0/Xamarin.AndroidX.ViewPager.dll" NuGetPackageId="Xamarin.AndroidX.ViewPager" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/80</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager2/*******/lib/net8.0-android34.0/Xamarin.AndroidX.ViewPager2.dll" NuGetPackageId="Xamarin.AndroidX.ViewPager2" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/81</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window/*******/lib/net8.0-android34.0/Xamarin.AndroidX.Window.dll" NuGetPackageId="Xamarin.AndroidX.Window" NuGetPackageVersion="*******">obj/Debug/net9.0-android/lp/82</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window.extensions.core.core/********/lib/net8.0-android34.0/Xamarin.AndroidX.Window.Extensions.Core.Core.dll" NuGetPackageId="Xamarin.AndroidX.Window.Extensions.Core.Core" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/83</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.android.material/********/lib/net8.0-android34.0/Xamarin.Google.Android.Material.dll" NuGetPackageId="Xamarin.Google.Android.Material" NuGetPackageVersion="********">obj/Debug/net9.0-android/lp/84</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.crypto.tink.android/1.16.0.1/lib/net8.0-android34.0/Xamarin.Google.Crypto.Tink.Android.dll" NuGetPackageId="Xamarin.Google.Crypto.Tink.Android" NuGetPackageVersion="1.16.0.1">obj/Debug/net9.0-android/lp/85</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.errorprone.annotations/2.36.0.1/lib/net8.0-android34.0/Xamarin.Google.ErrorProne.Annotations.dll" NuGetPackageId="Xamarin.Google.ErrorProne.Annotations" NuGetPackageVersion="2.36.0.1">obj/Debug/net9.0-android/lp/86</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.guava.listenablefuture/1.0.0.26/lib/net8.0-android34.0/Xamarin.Google.Guava.ListenableFuture.dll" NuGetPackageId="Xamarin.Google.Guava.ListenableFuture" NuGetPackageVersion="1.0.0.26">obj/Debug/net9.0-android/lp/87</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.jetbrains.annotations/26.0.1.2/lib/net8.0-android34.0/Xamarin.Jetbrains.Annotations.dll" NuGetPackageId="Xamarin.Jetbrains.Annotations" NuGetPackageVersion="26.0.1.2">obj/Debug/net9.0-android/lp/88</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlin.stdlib/2.0.21.2/lib/net8.0-android34.0/Xamarin.Kotlin.StdLib.dll" NuGetPackageId="Xamarin.Kotlin.StdLib" NuGetPackageVersion="2.0.21.2">obj/Debug/net9.0-android/lp/89</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.atomicfu/0.26.1.1/lib/net8.0-android34.0/Xamarin.KotlinX.AtomicFU.dll" NuGetPackageId="Xamarin.KotlinX.AtomicFU" NuGetPackageVersion="0.26.1.1">obj/Debug/net9.0-android/lp/90</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.atomicfu.jvm/0.26.1.1/lib/net8.0-android34.0/Xamarin.KotlinX.AtomicFU.Jvm.dll" NuGetPackageId="Xamarin.KotlinX.AtomicFU.Jvm" NuGetPackageVersion="0.26.1.1">obj/Debug/net9.0-android/lp/91</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.android/1.9.0.2/lib/net8.0-android34.0/Xamarin.KotlinX.Coroutines.Android.dll" NuGetPackageId="Xamarin.KotlinX.Coroutines.Android" NuGetPackageVersion="1.9.0.2">obj/Debug/net9.0-android/lp/92</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.core/1.9.0.2/lib/net8.0-android34.0/Xamarin.KotlinX.Coroutines.Core.dll" NuGetPackageId="Xamarin.KotlinX.Coroutines.Core" NuGetPackageVersion="1.9.0.2">obj/Debug/net9.0-android/lp/93</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.core.jvm/1.9.0.2/lib/net8.0-android34.0/Xamarin.KotlinX.Coroutines.Core.Jvm.dll" NuGetPackageId="Xamarin.KotlinX.Coroutines.Core.Jvm" NuGetPackageVersion="1.9.0.2">obj/Debug/net9.0-android/lp/94</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.serialization.core/1.7.3.2/lib/net8.0-android34.0/Xamarin.KotlinX.Serialization.Core.dll" NuGetPackageId="Xamarin.KotlinX.Serialization.Core" NuGetPackageVersion="1.7.3.2">obj/Debug/net9.0-android/lp/95</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.serialization.core.jvm/1.7.3.2/lib/net8.0-android34.0/Xamarin.KotlinX.Serialization.Core.Jvm.dll" NuGetPackageId="Xamarin.KotlinX.Serialization.Core.Jvm" NuGetPackageVersion="1.7.3.2">obj/Debug/net9.0-android/lp/96</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.versionedparcelable/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.versionedparcelable.versionedparcelable.aar">obj/Debug/net9.0-android/lp/97</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.tracing.tracing/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.tracing.tracing.aar">obj/Debug/net9.0-android/lp/98</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.startup.startupruntime/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.startup.startup-runtime.aar">obj/Debug/net9.0-android/lp/99</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.profileinstaller.profileinstaller/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.profileinstaller.profileinstaller.aar">obj/Debug/net9.0-android/lp/100</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.runtime/********/buildTransitive/net8.0-android34.0/../../aar/androidx.arch.core.core-runtime.aar">obj/Debug/net9.0-android/lp/101</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.android/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime-android.aar">obj/Debug/net9.0-android/lp/102</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime.aar">obj/Debug/net9.0-android/lp/103</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.interpolator/********/buildTransitive/net8.0-android34.0/../../aar/androidx.interpolator.interpolator.aar">obj/Debug/net9.0-android/lp/104</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.experimental/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.annotation.annotation-experimental.aar">obj/Debug/net9.0-android/lp/105</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core/********/buildTransitive/net8.0-android34.0/../../aar/androidx.core.core.aar">obj/Debug/net9.0-android/lp/106</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview/********/buildTransitive/net8.0-android34.0/../../aar/androidx.customview.customview.aar">obj/Debug/net9.0-android/lp/107</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.viewpager.viewpager.aar">obj/Debug/net9.0-android/lp/108</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate/********/buildTransitive/net8.0-android34.0/../../aar/androidx.savedstate.savedstate.aar">obj/Debug/net9.0-android/lp/109</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.core.core.ktx/********/buildTransitive/net8.0-android34.0/../../aar/androidx.core.core-ktx.aar">obj/Debug/net9.0-android/lp/110</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.customview.poolingcontainer/********/buildTransitive/net8.0-android34.0/../../aar/androidx.customview.customview-poolingcontainer.aar">obj/Debug/net9.0-android/lp/111</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.recyclerview/********/buildTransitive/net8.0-android34.0/../../aar/androidx.recyclerview.recyclerview.aar">obj/Debug/net9.0-android/lp/112</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.android/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel-android.aar">obj/Debug/net9.0-android/lp/113</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel.aar">obj/Debug/net9.0-android/lp/114</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-livedata-core.aar">obj/Debug/net9.0-android/lp/115</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.loader/********/buildTransitive/net8.0-android34.0/../../aar/androidx.loader.loader.aar">obj/Debug/net9.0-android/lp/116</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodelsavedstate/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel-savedstate.aar">obj/Debug/net9.0-android/lp/117</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.activity.activity.aar">obj/Debug/net9.0-android/lp/118</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.fragment.fragment.aar">obj/Debug/net9.0-android/lp/119</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager2/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.viewpager2.viewpager2.aar">obj/Debug/net9.0-android/lp/120</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.vectordrawable.vectordrawable.aar">obj/Debug/net9.0-android/lp/121</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable.animated/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.vectordrawable.vectordrawable-animated.aar">obj/Debug/net9.0-android/lp/122</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.print/********/buildTransitive/net8.0-android34.0/../../aar/androidx.print.print.aar">obj/Debug/net9.0-android/lp/123</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.localbroadcastmanager/*******9/buildTransitive/net8.0-android34.0/../../aar/androidx.localbroadcastmanager.localbroadcastmanager.aar">obj/Debug/net9.0-android/lp/124</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.documentfile/********/buildTransitive/net8.0-android34.0/../../aar/androidx.documentfile.documentfile.aar">obj/Debug/net9.0-android/lp/125</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.legacy.support.core.utils/********/buildTransitive/net8.0-android34.0/../../aar/androidx.legacy.legacy-support-core-utils.aar">obj/Debug/net9.0-android/lp/126</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.dynamicanimation/********/buildTransitive/net8.0-android34.0/../../aar/androidx.dynamicanimation.dynamicanimation.aar">obj/Debug/net9.0-android/lp/127</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.transition/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.transition.transition.aar">obj/Debug/net9.0-android/lp/128</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.process/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-process.aar">obj/Debug/net9.0-android/lp/129</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.emoji2.emoji2.aar">obj/Debug/net9.0-android/lp/130</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2.viewshelper/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.emoji2.emoji2-views-helper.aar">obj/Debug/net9.0-android/lp/131</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.drawerlayout/********/buildTransitive/net8.0-android34.0/../../aar/androidx.drawerlayout.drawerlayout.aar">obj/Debug/net9.0-android/lp/132</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cursoradapter/********/buildTransitive/net8.0-android34.0/../../aar/androidx.cursoradapter.cursoradapter.aar">obj/Debug/net9.0-android/lp/133</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.coordinatorlayout/********/buildTransitive/net8.0-android34.0/../../aar/androidx.coordinatorlayout.coordinatorlayout.aar">obj/Debug/net9.0-android/lp/134</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat.appcompatresources/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.appcompat.appcompat-resources.aar">obj/Debug/net9.0-android/lp/135</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.appcompat.appcompat.aar">obj/Debug/net9.0-android/lp/136</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.constraintlayout.constraintlayout.aar">obj/Debug/net9.0-android/lp/137</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.cardview/********/buildTransitive/net8.0-android34.0/../../aar/androidx.cardview.cardview.aar">obj/Debug/net9.0-android/lp/138</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.android.material/********/buildTransitive/net8.0-android34.0/../../aar/com.google.android.material.material.aar">obj/Debug/net9.0-android/lp/139</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window.extensions.core.core/********/buildTransitive/net8.0-android34.0/../../aar/androidx.window.extensions.core.core.aar">obj/Debug/net9.0-android/lp/140</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.window/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.window.window.aar">obj/Debug/net9.0-android/lp/141</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.swiperefreshlayout/********/buildTransitive/net8.0-android34.0/../../aar/androidx.swiperefreshlayout.swiperefreshlayout.aar">obj/Debug/net9.0-android/lp/142</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.slidingpanelayout/********/buildTransitive/net8.0-android34.0/../../aar/androidx.slidingpanelayout.slidingpanelayout.aar">obj/Debug/net9.0-android/lp/143</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.security.securitycrypto/*******-alpha06/buildTransitive/net8.0-android34.0/../../aar/androidx.security.security-crypto.aar">obj/Debug/net9.0-android/lp/144</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate.savedstate.ktx/********/buildTransitive/net8.0-android34.0/../../aar/androidx.savedstate.savedstate-ktx.aar">obj/Debug/net9.0-android/lp/145</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-viewmodel-ktx.aar">obj/Debug/net9.0-android/lp/146</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx.android/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime-ktx-android.aar">obj/Debug/net9.0-android/lp/147</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-runtime-ktx.aar">obj/Debug/net9.0-android/lp/148</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.common/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-common.aar">obj/Debug/net9.0-android/lp/149</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.activity.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.activity.activity-ktx.aar">obj/Debug/net9.0-android/lp/150</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.runtime/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-runtime.aar">obj/Debug/net9.0-android/lp/151</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.ui/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-ui.aar">obj/Debug/net9.0-android/lp/152</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-livedata-core-ktx.aar">obj/Debug/net9.0-android/lp/153</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.fragment.ktx/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.fragment.fragment-ktx.aar">obj/Debug/net9.0-android/lp/154</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.fragment/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.navigation.navigation-fragment.aar">obj/Debug/net9.0-android/lp/155</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.lifecycle.lifecycle-livedata.aar">obj/Debug/net9.0-android/lp/156</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.exifinterface/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.exifinterface.exifinterface.aar">obj/Debug/net9.0-android/lp/157</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.androidx.browser/*******/buildTransitive/net8.0-android34.0/../../aar/androidx.browser.browser.aar">obj/Debug/net9.0-android/lp/158</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/googlegson/2.11.0.5/lib/net8.0-android34.0/GoogleGson.aar" NuGetPackageId="GoogleGson" NuGetPackageVersion="2.11.0.5">obj/Debug/net9.0-android/lp/159</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.code.findbugs.jsr305/3.0.2.18/lib/net8.0-android34.0/Jsr305Binding.aar" NuGetPackageId="Xamarin.Google.Code.FindBugs.JSR305" NuGetPackageVersion="3.0.2.18">obj/Debug/net9.0-android/lp/160</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.identity.client/4.73.1/lib/net8.0-android34.0/Microsoft.Identity.Client.aar" NuGetPackageId="Microsoft.Identity.Client" NuGetPackageVersion="4.73.1">obj/Debug/net9.0-android/lp/161</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.controls.core/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.Controls.aar" NuGetPackageId="Microsoft.Maui.Controls.Core" NuGetPackageVersion="9.0.51">obj/Debug/net9.0-android/lp/162</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.core/9.0.51/lib/net9.0-android35.0/maui.aar" NuGetPackageId="Microsoft.Maui.Core" NuGetPackageVersion="9.0.51">obj/Debug/net9.0-android/lp/163</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.core/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.aar" NuGetPackageId="Microsoft.Maui.Core" NuGetPackageVersion="9.0.51">obj/Debug/net9.0-android/lp/164</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/microsoft.maui.essentials/9.0.51/lib/net9.0-android35.0/Microsoft.Maui.Essentials.aar" NuGetPackageId="Microsoft.Maui.Essentials" NuGetPackageVersion="9.0.51">obj/Debug/net9.0-android/lp/165</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.annotations/*********/lib/net8.0-android34.0/Xamarin.Android.Glide.Annotations.aar" NuGetPackageId="Xamarin.Android.Glide.Annotations" NuGetPackageVersion="*********">obj/Debug/net9.0-android/lp/166</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.disklrucache/*********/lib/net8.0-android34.0/Xamarin.Android.Glide.DiskLruCache.aar" NuGetPackageId="Xamarin.Android.Glide.DiskLruCache" NuGetPackageVersion="*********">obj/Debug/net9.0-android/lp/167</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide/*********/lib/net8.0-android34.0/glide.aar" NuGetPackageId="Xamarin.Android.Glide" NuGetPackageVersion="*********">obj/Debug/net9.0-android/lp/168</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.android.glide.gifdecoder/*********/lib/net8.0-android34.0/gifdecoder.aar" NuGetPackageId="Xamarin.Android.Glide.GifDecoder" NuGetPackageVersion="*********">obj/Debug/net9.0-android/lp/169</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.crypto.tink.android/1.16.0.1/lib/net8.0-android34.0/Xamarin.Google.Crypto.Tink.Android.aar" NuGetPackageId="Xamarin.Google.Crypto.Tink.Android" NuGetPackageVersion="1.16.0.1">obj/Debug/net9.0-android/lp/170</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.errorprone.annotations/2.36.0.1/lib/net8.0-android34.0/Xamarin.Google.ErrorProne.Annotations.aar" NuGetPackageId="Xamarin.Google.ErrorProne.Annotations" NuGetPackageVersion="2.36.0.1">obj/Debug/net9.0-android/lp/171</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.google.guava.listenablefuture/1.0.0.26/lib/net8.0-android34.0/Xamarin.Google.Guava.ListenableFuture.aar" NuGetPackageId="Xamarin.Google.Guava.ListenableFuture" NuGetPackageVersion="1.0.0.26">obj/Debug/net9.0-android/lp/172</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.jetbrains.annotations/26.0.1.2/lib/net8.0-android34.0/Xamarin.Jetbrains.Annotations.aar" NuGetPackageId="Xamarin.Jetbrains.Annotations" NuGetPackageVersion="26.0.1.2">obj/Debug/net9.0-android/lp/173</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlin.stdlib/2.0.21.2/lib/net8.0-android34.0/Xamarin.Kotlin.StdLib.aar" NuGetPackageId="Xamarin.Kotlin.StdLib" NuGetPackageVersion="2.0.21.2">obj/Debug/net9.0-android/lp/174</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.atomicfu/0.26.1.1/lib/net8.0-android34.0/Xamarin.KotlinX.AtomicFU.aar" NuGetPackageId="Xamarin.KotlinX.AtomicFU" NuGetPackageVersion="0.26.1.1">obj/Debug/net9.0-android/lp/175</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.atomicfu.jvm/0.26.1.1/lib/net8.0-android34.0/Xamarin.KotlinX.AtomicFU.Jvm.aar" NuGetPackageId="Xamarin.KotlinX.AtomicFU.Jvm" NuGetPackageVersion="0.26.1.1">obj/Debug/net9.0-android/lp/176</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.android/1.9.0.2/lib/net8.0-android34.0/Xamarin.KotlinX.Coroutines.Android.aar" NuGetPackageId="Xamarin.KotlinX.Coroutines.Android" NuGetPackageVersion="1.9.0.2">obj/Debug/net9.0-android/lp/177</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.core/1.9.0.2/lib/net8.0-android34.0/Xamarin.KotlinX.Coroutines.Core.aar" NuGetPackageId="Xamarin.KotlinX.Coroutines.Core" NuGetPackageVersion="1.9.0.2">obj/Debug/net9.0-android/lp/178</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.core.jvm/1.9.0.2/lib/net8.0-android34.0/Xamarin.KotlinX.Coroutines.Core.Jvm.aar" NuGetPackageId="Xamarin.KotlinX.Coroutines.Core.Jvm" NuGetPackageVersion="1.9.0.2">obj/Debug/net9.0-android/lp/179</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.serialization.core/1.7.3.2/lib/net8.0-android34.0/Xamarin.KotlinX.Serialization.Core.aar" NuGetPackageId="Xamarin.KotlinX.Serialization.Core" NuGetPackageVersion="1.7.3.2">obj/Debug/net9.0-android/lp/180</ExtractedDirectory><ExtractedDirectory OriginalFile="/Users/<USER>/.nuget/packages/xamarin.kotlinx.serialization.core.jvm/1.7.3.2/lib/net8.0-android34.0/Xamarin.KotlinX.Serialization.Core.Jvm.aar" NuGetPackageId="Xamarin.KotlinX.Serialization.Core.Jvm" NuGetPackageVersion="1.7.3.2">obj/Debug/net9.0-android/lp/181</ExtractedDirectory></ExtractedDirectories></Paths>