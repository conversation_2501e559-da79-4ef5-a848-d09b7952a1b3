aotassemblies=false
androidaddkeepalives=
androidaotmode=interpreter
androidembedprofilers=
androidenableprofiledaot=
androiddextool=d8
androidlinktool=
androidlinkresources=
androidpackageformat=apk
embedassembliesintoapk=false
androidlinkmode=none
androidlinkskip=
androidsdkbuildtoolsversion=35.0.0
androidsdkpath=/users/shashank/library/developer/xamarin/android-sdk-macosx/
androidndkpath=
javasdkpath=/library/java/javavirtualmachines/microsoft-11.jdk/contents/home/
androidsequencepointsmode=none
androidnetsdkversion=35.0.78
monosymbolarchive=false
androiduselatestplatformsdk=false
targetframeworkversion=v9.0
androidcreatepackageperabi=
androidgeneratejnimarshalmethods=false
os=unix
androidincludedebugsymbols=true
androidpackagenamingpolicy=lowercasecrc64
_nugetassetsfilehash=9a33c4eca6d9e28ca13f398adad840880554391aa1e6f1360241680f093174ff
typemapkind=strings-asm
androidmanifestplaceholders=
projectfullpath=/users/shashank/documents/src/habit-builder/habitbuilder/habitbuilder.csproj
androidusedesignerassembly=true
