<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2021 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
  <!-- Checked. -->

  <!-- The ripple color matches the color of the text / iconography on the element it's applied to.
       If the text / iconography changes color during a state change, the ripple color matches the end state's color.

       Pressing on a checked card will return it to the unchecked state at which point the text /
       iconography will use "colorOnSurfaceVariant". Therefore, the ripple color for the pressed,
       checked state is based on "colorOnSurfaceVariant". -->
  <item android:alpha="@dimen/m3_comp_filled_card_pressed_state_layer_opacity"
        android:color="?attr/colorOnSurfaceVariant"
        android:state_pressed="true" android:state_checked="true"/>
  <item android:alpha="@dimen/m3_comp_filled_card_focus_state_layer_opacity"
        android:color="?attr/colorSecondary"
        android:state_focused="true" android:state_checked="true"/>
  <item android:alpha="@dimen/m3_comp_filled_card_hover_state_layer_opacity"
        android:color="?attr/colorSecondary"
        android:state_hovered="true" android:state_checked="true"/>
  <item android:alpha="@dimen/m3_ripple_default_alpha"
        android:color="?attr/colorSecondary"
        android:state_checked="true"/>

  <!-- Unchecked. -->

  <!-- Pressing on an unchecked, checkable card will check it at which point the text / iconography
       will use "colorSecondary". Therefore, the ripple color for the pressed, checkable state is
       based on "colorSecondary". -->
  <item android:alpha="@dimen/m3_comp_filled_card_pressed_state_layer_opacity"
        android:color="?attr/colorSecondary"
        android:state_checkable="true" android:state_pressed="true"/>
  <!-- Pressing on an uncheckable card will remain in the unchecked state at which point the text /
       iconography will continue to use "colorOnSurfaceVariant". Therefore, the ripple color for the
       pressed, uncheckable state is based on "colorOnSurfaceVariant". -->
  <item android:alpha="@dimen/m3_comp_filled_card_pressed_state_layer_opacity"
        android:color="?attr/colorOnSurfaceVariant"
        android:state_checkable="false" android:state_pressed="true"/>
  <item android:alpha="@dimen/m3_comp_filled_card_focus_state_layer_opacity"
        android:color="?attr/colorOnSurfaceVariant"
        android:state_focused="true"/>
  <item android:alpha="@dimen/m3_comp_filled_card_hover_state_layer_opacity"
        android:color="?attr/colorOnSurfaceVariant"
        android:state_hovered="true"/>
  <item android:alpha="@dimen/m3_comp_filled_card_dragged_state_layer_opacity"
        android:color="?attr/colorOnSurfaceVariant"/>

</selector>
