<androidx.coordinatorlayout.widget.CoordinatorLayout android:layout_width="match_parent" android:layout_height="match_parent" android:id="@+id/navigation_layout" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:android="http://schemas.android.com/apk/res/android"><com.google.android.material.appbar.AppBarLayout android:id="@+id/navigationlayout_appbar" android:layout_width="match_parent" android:layout_height="wrap_content" android:background="?attr/colorPrimary" android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"><androidx.fragment.app.FragmentContainerView android:id="@+id/navigationlayout_toptabs" android:layout_width="match_parent" android:layout_height="wrap_content" /></com.google.android.material.appbar.AppBarLayout><androidx.fragment.app.FragmentContainerView android:id="@+id/navigationlayout_content" android:layout_width="match_parent" android:layout_height="match_parent" app:layout_behavior="@string/appbar_scrolling_view_behavior" /><androidx.fragment.app.FragmentContainerView android:id="@+id/navigationlayout_bottomtabs" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_gravity="bottom" /></androidx.coordinatorlayout.widget.CoordinatorLayout>