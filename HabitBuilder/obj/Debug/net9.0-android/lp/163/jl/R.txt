int anim abc_fade_in 0x0
int anim abc_fade_out 0x0
int anim abc_grow_fade_in_from_bottom 0x0
int anim abc_popup_enter 0x0
int anim abc_popup_exit 0x0
int anim abc_shrink_fade_out_from_bottom 0x0
int anim abc_slide_in_bottom 0x0
int anim abc_slide_in_top 0x0
int anim abc_slide_out_bottom 0x0
int anim abc_slide_out_top 0x0
int anim abc_tooltip_enter 0x0
int anim abc_tooltip_exit 0x0
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x0
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x0
int anim btn_checkbox_to_checked_icon_null_animation 0x0
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x0
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x0
int anim btn_checkbox_to_unchecked_icon_null_animation 0x0
int anim btn_radio_to_off_mtrl_dot_group_animation 0x0
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x0
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x0
int anim btn_radio_to_on_mtrl_dot_group_animation 0x0
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x0
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x0
int anim design_bottom_sheet_slide_in 0x0
int anim design_bottom_sheet_slide_out 0x0
int anim design_snackbar_in 0x0
int anim design_snackbar_out 0x0
int anim fragment_fast_out_extra_slow_in 0x0
int anim linear_indeterminate_line1_head_interpolator 0x0
int anim linear_indeterminate_line1_tail_interpolator 0x0
int anim linear_indeterminate_line2_head_interpolator 0x0
int anim linear_indeterminate_line2_tail_interpolator 0x0
int anim m3_bottom_sheet_slide_in 0x0
int anim m3_bottom_sheet_slide_out 0x0
int anim m3_motion_fade_enter 0x0
int anim m3_motion_fade_exit 0x0
int anim m3_side_sheet_enter_from_left 0x0
int anim m3_side_sheet_enter_from_right 0x0
int anim m3_side_sheet_exit_to_left 0x0
int anim m3_side_sheet_exit_to_right 0x0
int anim mtrl_bottom_sheet_slide_in 0x0
int anim mtrl_bottom_sheet_slide_out 0x0
int anim mtrl_card_lowers_interpolator 0x0
int animator design_appbar_state_list_animator 0x0
int animator design_fab_hide_motion_spec 0x0
int animator design_fab_show_motion_spec 0x0
int animator fragment_close_enter 0x0
int animator fragment_close_exit 0x0
int animator fragment_fade_enter 0x0
int animator fragment_fade_exit 0x0
int animator fragment_open_enter 0x0
int animator fragment_open_exit 0x0
int animator m3_appbar_state_list_animator 0x0
int animator m3_btn_elevated_btn_state_list_anim 0x0
int animator m3_btn_state_list_anim 0x0
int animator m3_card_elevated_state_list_anim 0x0
int animator m3_card_state_list_anim 0x0
int animator m3_chip_state_list_anim 0x0
int animator m3_elevated_chip_state_list_anim 0x0
int animator m3_extended_fab_change_size_collapse_motion_spec 0x0
int animator m3_extended_fab_change_size_expand_motion_spec 0x0
int animator m3_extended_fab_hide_motion_spec 0x0
int animator m3_extended_fab_show_motion_spec 0x0
int animator m3_extended_fab_state_list_animator 0x0
int animator mtrl_btn_state_list_anim 0x0
int animator mtrl_btn_unelevated_state_list_anim 0x0
int animator mtrl_card_state_list_anim 0x0
int animator mtrl_chip_state_list_anim 0x0
int animator mtrl_extended_fab_change_size_collapse_motion_spec 0x0
int animator mtrl_extended_fab_change_size_expand_motion_spec 0x0
int animator mtrl_extended_fab_hide_motion_spec 0x0
int animator mtrl_extended_fab_show_motion_spec 0x0
int animator mtrl_extended_fab_state_list_animator 0x0
int animator mtrl_fab_hide_motion_spec 0x0
int animator mtrl_fab_show_motion_spec 0x0
int animator mtrl_fab_transformation_sheet_collapse_spec 0x0
int animator mtrl_fab_transformation_sheet_expand_spec 0x0
int attr actionBarDivider 0x0
int attr actionBarItemBackground 0x0
int attr actionBarPopupTheme 0x0
int attr actionBarSize 0x0
int attr actionBarSplitStyle 0x0
int attr actionBarStyle 0x0
int attr actionBarTabBarStyle 0x0
int attr actionBarTabStyle 0x0
int attr actionBarTabTextStyle 0x0
int attr actionBarTheme 0x0
int attr actionBarWidgetTheme 0x0
int attr actionButtonStyle 0x0
int attr actionDropDownStyle 0x0
int attr actionLayout 0x0
int attr actionMenuTextAppearance 0x0
int attr actionMenuTextColor 0x0
int attr actionModeBackground 0x0
int attr actionModeCloseButtonStyle 0x0
int attr actionModeCloseContentDescription 0x0
int attr actionModeCloseDrawable 0x0
int attr actionModeCopyDrawable 0x0
int attr actionModeCutDrawable 0x0
int attr actionModeFindDrawable 0x0
int attr actionModePasteDrawable 0x0
int attr actionModePopupWindowStyle 0x0
int attr actionModeSelectAllDrawable 0x0
int attr actionModeShareDrawable 0x0
int attr actionModeSplitBackground 0x0
int attr actionModeStyle 0x0
int attr actionModeTheme 0x0
int attr actionModeWebSearchDrawable 0x0
int attr actionOverflowButtonStyle 0x0
int attr actionOverflowMenuStyle 0x0
int attr actionProviderClass 0x0
int attr actionTextColorAlpha 0x0
int attr actionViewClass 0x0
int attr activeIndicatorLabelPadding 0x0
int attr activityAction 0x0
int attr activityChooserViewStyle 0x0
int attr activityName 0x0
int attr addElevationShadow 0x0
int attr alertDialogButtonGroupStyle 0x0
int attr alertDialogCenterButtons 0x0
int attr alertDialogStyle 0x0
int attr alertDialogTheme 0x0
int attr allowStacking 0x0
int attr alpha 0x0
int attr alphabeticModifiers 0x0
int attr altSrc 0x0
int attr alwaysExpand 0x0
int attr animateMenuItems 0x0
int attr animateNavigationIcon 0x0
int attr animate_relativeTo 0x0
int attr animationBackgroundColor 0x0
int attr animationMode 0x0
int attr appBarLayoutStyle 0x0
int attr applyMotionScene 0x0
int attr arcMode 0x0
int attr arrowHeadLength 0x0
int attr arrowShaftLength 0x0
int attr attributeName 0x0
int attr autoAdjustToWithinGrandparentBounds 0x0
int attr autoCompleteTextViewStyle 0x0
int attr autoShowKeyboard 0x0
int attr autoSizeMaxTextSize 0x0
int attr autoSizeMinTextSize 0x0
int attr autoSizePresetSizes 0x0
int attr autoSizeStepGranularity 0x0
int attr autoSizeTextType 0x0
int attr autoTransition 0x0
int attr backHandlingEnabled 0x0
int attr background 0x0
int attr backgroundColor 0x0
int attr backgroundInsetBottom 0x0
int attr backgroundInsetEnd 0x0
int attr backgroundInsetStart 0x0
int attr backgroundInsetTop 0x0
int attr backgroundOverlayColorAlpha 0x0
int attr backgroundSplit 0x0
int attr backgroundStacked 0x0
int attr backgroundTint 0x0
int attr backgroundTintMode 0x0
int attr badgeGravity 0x0
int attr badgeHeight 0x0
int attr badgeRadius 0x0
int attr badgeShapeAppearance 0x0
int attr badgeShapeAppearanceOverlay 0x0
int attr badgeStyle 0x0
int attr badgeText 0x0
int attr badgeTextAppearance 0x0
int attr badgeTextColor 0x0
int attr badgeVerticalPadding 0x0
int attr badgeWidePadding 0x0
int attr badgeWidth 0x0
int attr badgeWithTextHeight 0x0
int attr badgeWithTextRadius 0x0
int attr badgeWithTextShapeAppearance 0x0
int attr badgeWithTextShapeAppearanceOverlay 0x0
int attr badgeWithTextWidth 0x0
int attr barLength 0x0
int attr barrierAllowsGoneWidgets 0x0
int attr barrierDirection 0x0
int attr barrierMargin 0x0
int attr behavior_autoHide 0x0
int attr behavior_autoShrink 0x0
int attr behavior_draggable 0x0
int attr behavior_expandedOffset 0x0
int attr behavior_fitToContents 0x0
int attr behavior_halfExpandedRatio 0x0
int attr behavior_hideable 0x0
int attr behavior_overlapTop 0x0
int attr behavior_peekHeight 0x0
int attr behavior_saveFlags 0x0
int attr behavior_significantVelocityThreshold 0x0
int attr behavior_skipCollapsed 0x0
int attr borderWidth 0x0
int attr borderlessButtonStyle 0x0
int attr bottomAppBarStyle 0x0
int attr bottomInsetScrimEnabled 0x0
int attr bottomNavigationStyle 0x0
int attr bottomSheetDialogTheme 0x0
int attr bottomSheetDragHandleStyle 0x0
int attr bottomSheetStyle 0x0
int attr boxBackgroundColor 0x0
int attr boxBackgroundMode 0x0
int attr boxCollapsedPaddingTop 0x0
int attr boxCornerRadiusBottomEnd 0x0
int attr boxCornerRadiusBottomStart 0x0
int attr boxCornerRadiusTopEnd 0x0
int attr boxCornerRadiusTopStart 0x0
int attr boxStrokeColor 0x0
int attr boxStrokeErrorColor 0x0
int attr boxStrokeWidth 0x0
int attr boxStrokeWidthFocused 0x0
int attr brightness 0x0
int attr buttonBarButtonStyle 0x0
int attr buttonBarNegativeButtonStyle 0x0
int attr buttonBarNeutralButtonStyle 0x0
int attr buttonBarPositiveButtonStyle 0x0
int attr buttonBarStyle 0x0
int attr buttonCompat 0x0
int attr buttonGravity 0x0
int attr buttonIcon 0x0
int attr buttonIconDimen 0x0
int attr buttonIconTint 0x0
int attr buttonIconTintMode 0x0
int attr buttonPanelSideLayout 0x0
int attr buttonStyle 0x0
int attr buttonStyleSmall 0x0
int attr buttonTint 0x0
int attr buttonTintMode 0x0
int attr cardBackgroundColor 0x0
int attr cardCornerRadius 0x0
int attr cardElevation 0x0
int attr cardForegroundColor 0x0
int attr cardMaxElevation 0x0
int attr cardPreventCornerOverlap 0x0
int attr cardUseCompatPadding 0x0
int attr cardViewStyle 0x0
int attr carousel_alignment 0x0
int attr centerIfNoTextEnabled 0x0
int attr chainUseRtl 0x0
int attr checkMarkCompat 0x0
int attr checkMarkTint 0x0
int attr checkMarkTintMode 0x0
int attr checkboxStyle 0x0
int attr checkedButton 0x0
int attr checkedChip 0x0
int attr checkedIcon 0x0
int attr checkedIconEnabled 0x0
int attr checkedIconGravity 0x0
int attr checkedIconMargin 0x0
int attr checkedIconSize 0x0
int attr checkedIconTint 0x0
int attr checkedIconVisible 0x0
int attr checkedState 0x0
int attr checkedTextViewStyle 0x0
int attr chipBackgroundColor 0x0
int attr chipCornerRadius 0x0
int attr chipEndPadding 0x0
int attr chipGroupStyle 0x0
int attr chipIcon 0x0
int attr chipIconEnabled 0x0
int attr chipIconSize 0x0
int attr chipIconTint 0x0
int attr chipIconVisible 0x0
int attr chipMinHeight 0x0
int attr chipMinTouchTargetSize 0x0
int attr chipSpacing 0x0
int attr chipSpacingHorizontal 0x0
int attr chipSpacingVertical 0x0
int attr chipStandaloneStyle 0x0
int attr chipStartPadding 0x0
int attr chipStrokeColor 0x0
int attr chipStrokeWidth 0x0
int attr chipStyle 0x0
int attr chipSurfaceColor 0x0
int attr circleRadius 0x0
int attr circularProgressIndicatorStyle 0x0
int attr clearTop 0x0
int attr clickAction 0x0
int attr clockFaceBackgroundColor 0x0
int attr clockHandColor 0x0
int attr clockIcon 0x0
int attr clockNumberTextColor 0x0
int attr closeIcon 0x0
int attr closeIconEnabled 0x0
int attr closeIconEndPadding 0x0
int attr closeIconSize 0x0
int attr closeIconStartPadding 0x0
int attr closeIconTint 0x0
int attr closeIconVisible 0x0
int attr closeItemLayout 0x0
int attr collapseContentDescription 0x0
int attr collapseIcon 0x0
int attr collapsedSize 0x0
int attr collapsedTitleGravity 0x0
int attr collapsedTitleTextAppearance 0x0
int attr collapsedTitleTextColor 0x0
int attr collapsingToolbarLayoutLargeSize 0x0
int attr collapsingToolbarLayoutLargeStyle 0x0
int attr collapsingToolbarLayoutMediumSize 0x0
int attr collapsingToolbarLayoutMediumStyle 0x0
int attr collapsingToolbarLayoutStyle 0x0
int attr color 0x0
int attr colorAccent 0x0
int attr colorBackgroundFloating 0x0
int attr colorButtonNormal 0x0
int attr colorContainer 0x0
int attr colorControlActivated 0x0
int attr colorControlHighlight 0x0
int attr colorControlNormal 0x0
int attr colorError 0x0
int attr colorErrorContainer 0x0
int attr colorOnBackground 0x0
int attr colorOnContainer 0x0
int attr colorOnContainerUnchecked 0x0
int attr colorOnError 0x0
int attr colorOnErrorContainer 0x0
int attr colorOnPrimary 0x0
int attr colorOnPrimaryContainer 0x0
int attr colorOnPrimaryFixed 0x0
int attr colorOnPrimaryFixedVariant 0x0
int attr colorOnPrimarySurface 0x0
int attr colorOnSecondary 0x0
int attr colorOnSecondaryContainer 0x0
int attr colorOnSecondaryFixed 0x0
int attr colorOnSecondaryFixedVariant 0x0
int attr colorOnSurface 0x0
int attr colorOnSurfaceInverse 0x0
int attr colorOnSurfaceVariant 0x0
int attr colorOnTertiary 0x0
int attr colorOnTertiaryContainer 0x0
int attr colorOnTertiaryFixed 0x0
int attr colorOnTertiaryFixedVariant 0x0
int attr colorOutline 0x0
int attr colorOutlineVariant 0x0
int attr colorPrimary 0x0
int attr colorPrimaryContainer 0x0
int attr colorPrimaryDark 0x0
int attr colorPrimaryFixed 0x0
int attr colorPrimaryFixedDim 0x0
int attr colorPrimaryInverse 0x0
int attr colorPrimarySurface 0x0
int attr colorPrimaryVariant 0x0
int attr colorSecondary 0x0
int attr colorSecondaryContainer 0x0
int attr colorSecondaryFixed 0x0
int attr colorSecondaryFixedDim 0x0
int attr colorSecondaryVariant 0x0
int attr colorSurface 0x0
int attr colorSurfaceBright 0x0
int attr colorSurfaceContainer 0x0
int attr colorSurfaceContainerHigh 0x0
int attr colorSurfaceContainerHighest 0x0
int attr colorSurfaceContainerLow 0x0
int attr colorSurfaceContainerLowest 0x0
int attr colorSurfaceDim 0x0
int attr colorSurfaceInverse 0x0
int attr colorSurfaceVariant 0x0
int attr colorSwitchThumbNormal 0x0
int attr colorTertiary 0x0
int attr colorTertiaryContainer 0x0
int attr colorTertiaryFixed 0x0
int attr colorTertiaryFixedDim 0x0
int attr commitIcon 0x0
int attr compatShadowEnabled 0x0
int attr constraintSet 0x0
int attr constraintSetEnd 0x0
int attr constraintSetStart 0x0
int attr constraint_referenced_ids 0x0
int attr constraints 0x0
int attr content 0x0
int attr contentDescription 0x0
int attr contentInsetEnd 0x0
int attr contentInsetEndWithActions 0x0
int attr contentInsetLeft 0x0
int attr contentInsetRight 0x0
int attr contentInsetStart 0x0
int attr contentInsetStartWithNavigation 0x0
int attr contentPadding 0x0
int attr contentPaddingBottom 0x0
int attr contentPaddingEnd 0x0
int attr contentPaddingLeft 0x0
int attr contentPaddingRight 0x0
int attr contentPaddingStart 0x0
int attr contentPaddingTop 0x0
int attr contentScrim 0x0
int attr contrast 0x0
int attr controlBackground 0x0
int attr coordinatorLayoutStyle 0x0
int attr coplanarSiblingViewId 0x0
int attr cornerFamily 0x0
int attr cornerFamilyBottomLeft 0x0
int attr cornerFamilyBottomRight 0x0
int attr cornerFamilyTopLeft 0x0
int attr cornerFamilyTopRight 0x0
int attr cornerRadius 0x0
int attr cornerSize 0x0
int attr cornerSizeBottomLeft 0x0
int attr cornerSizeBottomRight 0x0
int attr cornerSizeTopLeft 0x0
int attr cornerSizeTopRight 0x0
int attr counterEnabled 0x0
int attr counterMaxLength 0x0
int attr counterOverflowTextAppearance 0x0
int attr counterOverflowTextColor 0x0
int attr counterTextAppearance 0x0
int attr counterTextColor 0x0
int attr crossfade 0x0
int attr currentState 0x0
int attr cursorColor 0x0
int attr cursorErrorColor 0x0
int attr curveFit 0x0
int attr customBoolean 0x0
int attr customColorDrawableValue 0x0
int attr customColorValue 0x0
int attr customDimension 0x0
int attr customFloatValue 0x0
int attr customIntegerValue 0x0
int attr customNavigationLayout 0x0
int attr customPixelDimension 0x0
int attr customStringValue 0x0
int attr dayInvalidStyle 0x0
int attr daySelectedStyle 0x0
int attr dayStyle 0x0
int attr dayTodayStyle 0x0
int attr defaultDuration 0x0
int attr defaultMarginsEnabled 0x0
int attr defaultQueryHint 0x0
int attr defaultScrollFlagsEnabled 0x0
int attr defaultState 0x0
int attr deltaPolarAngle 0x0
int attr deltaPolarRadius 0x0
int attr deriveConstraintsFrom 0x0
int attr dialogCornerRadius 0x0
int attr dialogPreferredPadding 0x0
int attr dialogTheme 0x0
int attr displayOptions 0x0
int attr divider 0x0
int attr dividerColor 0x0
int attr dividerHorizontal 0x0
int attr dividerInsetEnd 0x0
int attr dividerInsetStart 0x0
int attr dividerPadding 0x0
int attr dividerThickness 0x0
int attr dividerVertical 0x0
int attr dragDirection 0x0
int attr dragScale 0x0
int attr dragThreshold 0x0
int attr drawPath 0x0
int attr drawableBottomCompat 0x0
int attr drawableEndCompat 0x0
int attr drawableLeftCompat 0x0
int attr drawableRightCompat 0x0
int attr drawableSize 0x0
int attr drawableStartCompat 0x0
int attr drawableTint 0x0
int attr drawableTintMode 0x0
int attr drawableTopCompat 0x0
int attr drawerArrowStyle 0x0
int attr drawerLayoutCornerSize 0x0
int attr drawerLayoutStyle 0x0
int attr dropDownBackgroundTint 0x0
int attr dropDownListViewStyle 0x0
int attr dropdownListPreferredItemHeight 0x0
int attr duration 0x0
int attr dynamicColorThemeOverlay 0x0
int attr editTextBackground 0x0
int attr editTextColor 0x0
int attr editTextStyle 0x0
int attr elevation 0x0
int attr elevationOverlayAccentColor 0x0
int attr elevationOverlayColor 0x0
int attr elevationOverlayEnabled 0x0
int attr emojiCompatEnabled 0x0
int attr enableEdgeToEdge 0x0
int attr endIconCheckable 0x0
int attr endIconContentDescription 0x0
int attr endIconDrawable 0x0
int attr endIconMinSize 0x0
int attr endIconMode 0x0
int attr endIconScaleType 0x0
int attr endIconTint 0x0
int attr endIconTintMode 0x0
int attr enforceMaterialTheme 0x0
int attr enforceTextAppearance 0x0
int attr ensureMinTouchTargetSize 0x0
int attr errorAccessibilityLabel 0x0
int attr errorAccessibilityLiveRegion 0x0
int attr errorContentDescription 0x0
int attr errorEnabled 0x0
int attr errorIconDrawable 0x0
int attr errorIconTint 0x0
int attr errorIconTintMode 0x0
int attr errorShown 0x0
int attr errorTextAppearance 0x0
int attr errorTextColor 0x0
int attr expandActivityOverflowButtonDrawable 0x0
int attr expanded 0x0
int attr expandedHintEnabled 0x0
int attr expandedTitleGravity 0x0
int attr expandedTitleMargin 0x0
int attr expandedTitleMarginBottom 0x0
int attr expandedTitleMarginEnd 0x0
int attr expandedTitleMarginStart 0x0
int attr expandedTitleMarginTop 0x0
int attr expandedTitleTextAppearance 0x0
int attr expandedTitleTextColor 0x0
int attr extendMotionSpec 0x0
int attr extendStrategy 0x0
int attr extendedFloatingActionButtonPrimaryStyle 0x0
int attr extendedFloatingActionButtonSecondaryStyle 0x0
int attr extendedFloatingActionButtonStyle 0x0
int attr extendedFloatingActionButtonSurfaceStyle 0x0
int attr extendedFloatingActionButtonTertiaryStyle 0x0
int attr extraMultilineHeightEnabled 0x0
int attr fabAlignmentMode 0x0
int attr fabAlignmentModeEndMargin 0x0
int attr fabAnchorMode 0x0
int attr fabAnimationMode 0x0
int attr fabCradleMargin 0x0
int attr fabCradleRoundedCornerRadius 0x0
int attr fabCradleVerticalOffset 0x0
int attr fabCustomSize 0x0
int attr fabSize 0x0
int attr fastScrollEnabled 0x0
int attr fastScrollHorizontalThumbDrawable 0x0
int attr fastScrollHorizontalTrackDrawable 0x0
int attr fastScrollVerticalThumbDrawable 0x0
int attr fastScrollVerticalTrackDrawable 0x0
int attr finishPrimaryWithPlaceholder 0x0
int attr finishPrimaryWithSecondary 0x0
int attr finishSecondaryWithPrimary 0x0
int attr firstBaselineToTopHeight 0x0
int attr floatingActionButtonLargePrimaryStyle 0x0
int attr floatingActionButtonLargeSecondaryStyle 0x0
int attr floatingActionButtonLargeStyle 0x0
int attr floatingActionButtonLargeSurfaceStyle 0x0
int attr floatingActionButtonLargeTertiaryStyle 0x0
int attr floatingActionButtonPrimaryStyle 0x0
int attr floatingActionButtonSecondaryStyle 0x0
int attr floatingActionButtonSmallPrimaryStyle 0x0
int attr floatingActionButtonSmallSecondaryStyle 0x0
int attr floatingActionButtonSmallStyle 0x0
int attr floatingActionButtonSmallSurfaceStyle 0x0
int attr floatingActionButtonSmallTertiaryStyle 0x0
int attr floatingActionButtonStyle 0x0
int attr floatingActionButtonSurfaceStyle 0x0
int attr floatingActionButtonTertiaryStyle 0x0
int attr flow_firstHorizontalBias 0x0
int attr flow_firstHorizontalStyle 0x0
int attr flow_firstVerticalBias 0x0
int attr flow_firstVerticalStyle 0x0
int attr flow_horizontalAlign 0x0
int attr flow_horizontalBias 0x0
int attr flow_horizontalGap 0x0
int attr flow_horizontalStyle 0x0
int attr flow_lastHorizontalBias 0x0
int attr flow_lastHorizontalStyle 0x0
int attr flow_lastVerticalBias 0x0
int attr flow_lastVerticalStyle 0x0
int attr flow_maxElementsWrap 0x0
int attr flow_padding 0x0
int attr flow_verticalAlign 0x0
int attr flow_verticalBias 0x0
int attr flow_verticalGap 0x0
int attr flow_verticalStyle 0x0
int attr flow_wrapMode 0x0
int attr font 0x0
int attr fontFamily 0x0
int attr fontProviderAuthority 0x0
int attr fontProviderCerts 0x0
int attr fontProviderFetchStrategy 0x0
int attr fontProviderFetchTimeout 0x0
int attr fontProviderPackage 0x0
int attr fontProviderQuery 0x0
int attr fontProviderSystemFontFamily 0x0
int attr fontStyle 0x0
int attr fontVariationSettings 0x0
int attr fontWeight 0x0
int attr forceApplySystemWindowInsetTop 0x0
int attr forceDefaultNavigationOnClickListener 0x0
int attr foregroundInsidePadding 0x0
int attr framePosition 0x0
int attr gapBetweenBars 0x0
int attr gestureInsetBottomIgnored 0x0
int attr goIcon 0x0
int attr haloColor 0x0
int attr haloRadius 0x0
int attr headerLayout 0x0
int attr height 0x0
int attr helperText 0x0
int attr helperTextEnabled 0x0
int attr helperTextTextAppearance 0x0
int attr helperTextTextColor 0x0
int attr hideAnimationBehavior 0x0
int attr hideMotionSpec 0x0
int attr hideNavigationIcon 0x0
int attr hideOnContentScroll 0x0
int attr hideOnScroll 0x0
int attr hintAnimationEnabled 0x0
int attr hintEnabled 0x0
int attr hintTextAppearance 0x0
int attr hintTextColor 0x0
int attr homeAsUpIndicator 0x0
int attr homeLayout 0x0
int attr horizontalOffset 0x0
int attr horizontalOffsetWithText 0x0
int attr hoveredFocusedTranslationZ 0x0
int attr icon 0x0
int attr iconEndPadding 0x0
int attr iconGravity 0x0
int attr iconPadding 0x0
int attr iconSize 0x0
int attr iconStartPadding 0x0
int attr iconTint 0x0
int attr iconTintMode 0x0
int attr iconifiedByDefault 0x0
int attr imageButtonStyle 0x0
int attr indeterminateAnimationType 0x0
int attr indeterminateProgressStyle 0x0
int attr indicatorColor 0x0
int attr indicatorDirectionCircular 0x0
int attr indicatorDirectionLinear 0x0
int attr indicatorInset 0x0
int attr indicatorSize 0x0
int attr initialActivityCount 0x0
int attr insetForeground 0x0
int attr isLightTheme 0x0
int attr isMaterial3DynamicColorApplied 0x0
int attr isMaterial3Theme 0x0
int attr isMaterialTheme 0x0
int attr itemActiveIndicatorStyle 0x0
int attr itemBackground 0x0
int attr itemFillColor 0x0
int attr itemHorizontalPadding 0x0
int attr itemHorizontalTranslationEnabled 0x0
int attr itemIconPadding 0x0
int attr itemIconSize 0x0
int attr itemIconTint 0x0
int attr itemMaxLines 0x0
int attr itemMinHeight 0x0
int attr itemPadding 0x0
int attr itemPaddingBottom 0x0
int attr itemPaddingTop 0x0
int attr itemRippleColor 0x0
int attr itemShapeAppearance 0x0
int attr itemShapeAppearanceOverlay 0x0
int attr itemShapeFillColor 0x0
int attr itemShapeInsetBottom 0x0
int attr itemShapeInsetEnd 0x0
int attr itemShapeInsetStart 0x0
int attr itemShapeInsetTop 0x0
int attr itemSpacing 0x0
int attr itemStrokeColor 0x0
int attr itemStrokeWidth 0x0
int attr itemTextAppearance 0x0
int attr itemTextAppearanceActive 0x0
int attr itemTextAppearanceActiveBoldEnabled 0x0
int attr itemTextAppearanceInactive 0x0
int attr itemTextColor 0x0
int attr itemVerticalPadding 0x0
int attr keyPositionType 0x0
int attr keyboardIcon 0x0
int attr keylines 0x0
int attr lStar 0x0
int attr labelBehavior 0x0
int attr labelStyle 0x0
int attr labelVisibilityMode 0x0
int attr largeFontVerticalOffsetAdjustment 0x0
int attr lastBaselineToBottomHeight 0x0
int attr lastItemDecorated 0x0
int attr layout 0x0
int attr layoutDescription 0x0
int attr layoutDuringTransition 0x0
int attr layoutManager 0x0
int attr layout_anchor 0x0
int attr layout_anchorGravity 0x0
int attr layout_behavior 0x0
int attr layout_collapseMode 0x0
int attr layout_collapseParallaxMultiplier 0x0
int attr layout_constrainedHeight 0x0
int attr layout_constrainedWidth 0x0
int attr layout_constraintBaseline_creator 0x0
int attr layout_constraintBaseline_toBaselineOf 0x0
int attr layout_constraintBottom_creator 0x0
int attr layout_constraintBottom_toBottomOf 0x0
int attr layout_constraintBottom_toTopOf 0x0
int attr layout_constraintCircle 0x0
int attr layout_constraintCircleAngle 0x0
int attr layout_constraintCircleRadius 0x0
int attr layout_constraintDimensionRatio 0x0
int attr layout_constraintEnd_toEndOf 0x0
int attr layout_constraintEnd_toStartOf 0x0
int attr layout_constraintGuide_begin 0x0
int attr layout_constraintGuide_end 0x0
int attr layout_constraintGuide_percent 0x0
int attr layout_constraintHeight_default 0x0
int attr layout_constraintHeight_max 0x0
int attr layout_constraintHeight_min 0x0
int attr layout_constraintHeight_percent 0x0
int attr layout_constraintHorizontal_bias 0x0
int attr layout_constraintHorizontal_chainStyle 0x0
int attr layout_constraintHorizontal_weight 0x0
int attr layout_constraintLeft_creator 0x0
int attr layout_constraintLeft_toLeftOf 0x0
int attr layout_constraintLeft_toRightOf 0x0
int attr layout_constraintRight_creator 0x0
int attr layout_constraintRight_toLeftOf 0x0
int attr layout_constraintRight_toRightOf 0x0
int attr layout_constraintStart_toEndOf 0x0
int attr layout_constraintStart_toStartOf 0x0
int attr layout_constraintTag 0x0
int attr layout_constraintTop_creator 0x0
int attr layout_constraintTop_toBottomOf 0x0
int attr layout_constraintTop_toTopOf 0x0
int attr layout_constraintVertical_bias 0x0
int attr layout_constraintVertical_chainStyle 0x0
int attr layout_constraintVertical_weight 0x0
int attr layout_constraintWidth_default 0x0
int attr layout_constraintWidth_max 0x0
int attr layout_constraintWidth_min 0x0
int attr layout_constraintWidth_percent 0x0
int attr layout_dodgeInsetEdges 0x0
int attr layout_editor_absoluteX 0x0
int attr layout_editor_absoluteY 0x0
int attr layout_goneMarginBottom 0x0
int attr layout_goneMarginEnd 0x0
int attr layout_goneMarginLeft 0x0
int attr layout_goneMarginRight 0x0
int attr layout_goneMarginStart 0x0
int attr layout_goneMarginTop 0x0
int attr layout_insetEdge 0x0
int attr layout_keyline 0x0
int attr layout_optimizationLevel 0x0
int attr layout_scrollEffect 0x0
int attr layout_scrollFlags 0x0
int attr layout_scrollInterpolator 0x0
int attr liftOnScroll 0x0
int attr liftOnScrollColor 0x0
int attr liftOnScrollTargetViewId 0x0
int attr limitBoundsTo 0x0
int attr lineHeight 0x0
int attr lineSpacing 0x0
int attr linearProgressIndicatorStyle 0x0
int attr listChoiceBackgroundIndicator 0x0
int attr listChoiceIndicatorMultipleAnimated 0x0
int attr listChoiceIndicatorSingleAnimated 0x0
int attr listDividerAlertDialog 0x0
int attr listItemLayout 0x0
int attr listLayout 0x0
int attr listMenuViewStyle 0x0
int attr listPopupWindowStyle 0x0
int attr listPreferredItemHeight 0x0
int attr listPreferredItemHeightLarge 0x0
int attr listPreferredItemHeightSmall 0x0
int attr listPreferredItemPaddingEnd 0x0
int attr listPreferredItemPaddingLeft 0x0
int attr listPreferredItemPaddingRight 0x0
int attr listPreferredItemPaddingStart 0x0
int attr logo 0x0
int attr logoAdjustViewBounds 0x0
int attr logoDescription 0x0
int attr logoScaleType 0x0
int attr marginHorizontal 0x0
int attr marginLeftSystemWindowInsets 0x0
int attr marginRightSystemWindowInsets 0x0
int attr marginTopSystemWindowInsets 0x0
int attr materialAlertDialogBodyTextStyle 0x0
int attr materialAlertDialogButtonSpacerVisibility 0x0
int attr materialAlertDialogTheme 0x0
int attr materialAlertDialogTitleIconStyle 0x0
int attr materialAlertDialogTitlePanelStyle 0x0
int attr materialAlertDialogTitleTextStyle 0x0
int attr materialButtonOutlinedStyle 0x0
int attr materialButtonStyle 0x0
int attr materialButtonToggleGroupStyle 0x0
int attr materialCalendarDay 0x0
int attr materialCalendarDayOfWeekLabel 0x0
int attr materialCalendarFullscreenTheme 0x0
int attr materialCalendarHeaderCancelButton 0x0
int attr materialCalendarHeaderConfirmButton 0x0
int attr materialCalendarHeaderDivider 0x0
int attr materialCalendarHeaderLayout 0x0
int attr materialCalendarHeaderSelection 0x0
int attr materialCalendarHeaderTitle 0x0
int attr materialCalendarHeaderToggleButton 0x0
int attr materialCalendarMonth 0x0
int attr materialCalendarMonthNavigationButton 0x0
int attr materialCalendarStyle 0x0
int attr materialCalendarTheme 0x0
int attr materialCalendarYearNavigationButton 0x0
int attr materialCardViewElevatedStyle 0x0
int attr materialCardViewFilledStyle 0x0
int attr materialCardViewOutlinedStyle 0x0
int attr materialCardViewStyle 0x0
int attr materialCircleRadius 0x0
int attr materialClockStyle 0x0
int attr materialDisplayDividerStyle 0x0
int attr materialDividerHeavyStyle 0x0
int attr materialDividerStyle 0x0
int attr materialIconButtonFilledStyle 0x0
int attr materialIconButtonFilledTonalStyle 0x0
int attr materialIconButtonOutlinedStyle 0x0
int attr materialIconButtonStyle 0x0
int attr materialSearchBarStyle 0x0
int attr materialSearchViewPrefixStyle 0x0
int attr materialSearchViewStyle 0x0
int attr materialSearchViewToolbarHeight 0x0
int attr materialSearchViewToolbarStyle 0x0
int attr materialSwitchStyle 0x0
int attr materialThemeOverlay 0x0
int attr materialTimePickerStyle 0x0
int attr materialTimePickerTheme 0x0
int attr materialTimePickerTitleStyle 0x0
int attr maxAcceleration 0x0
int attr maxActionInlineWidth 0x0
int attr maxButtonHeight 0x0
int attr maxCharacterCount 0x0
int attr maxHeight 0x0
int attr maxImageSize 0x0
int attr maxLines 0x0
int attr maxNumber 0x0
int attr maxVelocity 0x0
int attr maxWidth 0x0
int attr measureWithLargestChild 0x0
int attr menu 0x0
int attr menuAlignmentMode 0x0
int attr menuGravity 0x0
int attr minHeight 0x0
int attr minHideDelay 0x0
int attr minSeparation 0x0
int attr minTouchTargetSize 0x0
int attr minWidth 0x0
int attr mock_diagonalsColor 0x0
int attr mock_label 0x0
int attr mock_labelBackgroundColor 0x0
int attr mock_labelColor 0x0
int attr mock_showDiagonals 0x0
int attr mock_showLabel 0x0
int attr motionDebug 0x0
int attr motionDurationExtraLong1 0x0
int attr motionDurationExtraLong2 0x0
int attr motionDurationExtraLong3 0x0
int attr motionDurationExtraLong4 0x0
int attr motionDurationLong1 0x0
int attr motionDurationLong2 0x0
int attr motionDurationLong3 0x0
int attr motionDurationLong4 0x0
int attr motionDurationMedium1 0x0
int attr motionDurationMedium2 0x0
int attr motionDurationMedium3 0x0
int attr motionDurationMedium4 0x0
int attr motionDurationShort1 0x0
int attr motionDurationShort2 0x0
int attr motionDurationShort3 0x0
int attr motionDurationShort4 0x0
int attr motionEasingAccelerated 0x0
int attr motionEasingDecelerated 0x0
int attr motionEasingEmphasized 0x0
int attr motionEasingEmphasizedAccelerateInterpolator 0x0
int attr motionEasingEmphasizedDecelerateInterpolator 0x0
int attr motionEasingEmphasizedInterpolator 0x0
int attr motionEasingLinear 0x0
int attr motionEasingLinearInterpolator 0x0
int attr motionEasingStandard 0x0
int attr motionEasingStandardAccelerateInterpolator 0x0
int attr motionEasingStandardDecelerateInterpolator 0x0
int attr motionEasingStandardInterpolator 0x0
int attr motionInterpolator 0x0
int attr motionPath 0x0
int attr motionPathRotate 0x0
int attr motionProgress 0x0
int attr motionStagger 0x0
int attr motionTarget 0x0
int attr motion_postLayoutCollision 0x0
int attr motion_triggerOnCollision 0x0
int attr moveWhenScrollAtTop 0x0
int attr multiChoiceItemLayout 0x0
int attr navigationContentDescription 0x0
int attr navigationIcon 0x0
int attr navigationIconTint 0x0
int attr navigationMode 0x0
int attr navigationRailStyle 0x0
int attr navigationViewStyle 0x0
int attr nestedScrollFlags 0x0
int attr nestedScrollViewStyle 0x0
int attr nestedScrollable 0x0
int attr number 0x0
int attr numericModifiers 0x0
int attr offsetAlignmentMode 0x0
int attr onCross 0x0
int attr onHide 0x0
int attr onNegativeCross 0x0
int attr onPositiveCross 0x0
int attr onShow 0x0
int attr onTouchUp 0x0
int attr overlapAnchor 0x0
int attr overlay 0x0
int attr paddingBottomNoButtons 0x0
int attr paddingBottomSystemWindowInsets 0x0
int attr paddingEnd 0x0
int attr paddingLeftSystemWindowInsets 0x0
int attr paddingRightSystemWindowInsets 0x0
int attr paddingStart 0x0
int attr paddingStartSystemWindowInsets 0x0
int attr paddingTopNoTitle 0x0
int attr paddingTopSystemWindowInsets 0x0
int attr panelBackground 0x0
int attr panelMenuListTheme 0x0
int attr panelMenuListWidth 0x0
int attr passwordToggleContentDescription 0x0
int attr passwordToggleDrawable 0x0
int attr passwordToggleEnabled 0x0
int attr passwordToggleTint 0x0
int attr passwordToggleTintMode 0x0
int attr pathMotionArc 0x0
int attr path_percent 0x0
int attr percentHeight 0x0
int attr percentWidth 0x0
int attr percentX 0x0
int attr percentY 0x0
int attr perpendicularPath_percent 0x0
int attr pivotAnchor 0x0
int attr placeholderActivityName 0x0
int attr placeholderText 0x0
int attr placeholderTextAppearance 0x0
int attr placeholderTextColor 0x0
int attr placeholder_emptyVisibility 0x0
int attr popupMenuBackground 0x0
int attr popupMenuStyle 0x0
int attr popupTheme 0x0
int attr popupWindowStyle 0x0
int attr prefixText 0x0
int attr prefixTextAppearance 0x0
int attr prefixTextColor 0x0
int attr preserveIconSpacing 0x0
int attr pressedTranslationZ 0x0
int attr primaryActivityName 0x0
int attr progressBarPadding 0x0
int attr progressBarStyle 0x0
int attr queryBackground 0x0
int attr queryHint 0x0
int attr queryPatterns 0x0
int attr radioButtonStyle 0x0
int attr rangeFillColor 0x0
int attr ratingBarStyle 0x0
int attr ratingBarStyleIndicator 0x0
int attr ratingBarStyleSmall 0x0
int attr recyclerViewStyle 0x0
int attr region_heightLessThan 0x0
int attr region_heightMoreThan 0x0
int attr region_widthLessThan 0x0
int attr region_widthMoreThan 0x0
int attr removeEmbeddedFabElevation 0x0
int attr reverseLayout 0x0
int attr rippleColor 0x0
int attr round 0x0
int attr roundPercent 0x0
int attr saturation 0x0
int attr scrimAnimationDuration 0x0
int attr scrimBackground 0x0
int attr scrimVisibleHeightTrigger 0x0
int attr searchHintIcon 0x0
int attr searchIcon 0x0
int attr searchPrefixText 0x0
int attr searchViewStyle 0x0
int attr secondaryActivityAction 0x0
int attr secondaryActivityName 0x0
int attr seekBarStyle 0x0
int attr selectableItemBackground 0x0
int attr selectableItemBackgroundBorderless 0x0
int attr selectionRequired 0x0
int attr selectorSize 0x0
int attr shapeAppearance 0x0
int attr shapeAppearanceCornerExtraLarge 0x0
int attr shapeAppearanceCornerExtraSmall 0x0
int attr shapeAppearanceCornerLarge 0x0
int attr shapeAppearanceCornerMedium 0x0
int attr shapeAppearanceCornerSmall 0x0
int attr shapeAppearanceLargeComponent 0x0
int attr shapeAppearanceMediumComponent 0x0
int attr shapeAppearanceOverlay 0x0
int attr shapeAppearanceSmallComponent 0x0
int attr shapeCornerFamily 0x0
int attr shortcutMatchRequired 0x0
int attr shouldRemoveExpandedCorners 0x0
int attr showAnimationBehavior 0x0
int attr showAsAction 0x0
int attr showDelay 0x0
int attr showDividers 0x0
int attr showMotionSpec 0x0
int attr showPaths 0x0
int attr showText 0x0
int attr showTitle 0x0
int attr shrinkMotionSpec 0x0
int attr sideSheetDialogTheme 0x0
int attr sideSheetModalStyle 0x0
int attr simpleItemLayout 0x0
int attr simpleItemSelectedColor 0x0
int attr simpleItemSelectedRippleColor 0x0
int attr simpleItems 0x0
int attr singleChoiceItemLayout 0x0
int attr singleLine 0x0
int attr singleSelection 0x0
int attr sizePercent 0x0
int attr sliderStyle 0x0
int attr snackbarButtonStyle 0x0
int attr snackbarStyle 0x0
int attr snackbarTextViewStyle 0x0
int attr spanCount 0x0
int attr spinBars 0x0
int attr spinnerDropDownItemStyle 0x0
int attr spinnerStyle 0x0
int attr splitLayoutDirection 0x0
int attr splitMaxAspectRatioInLandscape 0x0
int attr splitMaxAspectRatioInPortrait 0x0
int attr splitMinHeightDp 0x0
int attr splitMinSmallestWidthDp 0x0
int attr splitMinWidthDp 0x0
int attr splitRatio 0x0
int attr splitTrack 0x0
int attr srcCompat 0x0
int attr stackFromEnd 0x0
int attr staggered 0x0
int attr startIconCheckable 0x0
int attr startIconContentDescription 0x0
int attr startIconDrawable 0x0
int attr startIconMinSize 0x0
int attr startIconScaleType 0x0
int attr startIconTint 0x0
int attr startIconTintMode 0x0
int attr state_above_anchor 0x0
int attr state_collapsed 0x0
int attr state_collapsible 0x0
int attr state_dragged 0x0
int attr state_error 0x0
int attr state_indeterminate 0x0
int attr state_liftable 0x0
int attr state_lifted 0x0
int attr state_with_icon 0x0
int attr statusBarBackground 0x0
int attr statusBarForeground 0x0
int attr statusBarScrim 0x0
int attr stickyPlaceholder 0x0
int attr strokeColor 0x0
int attr strokeWidth 0x0
int attr subMenuArrow 0x0
int attr subheaderColor 0x0
int attr subheaderInsetEnd 0x0
int attr subheaderInsetStart 0x0
int attr subheaderTextAppearance 0x0
int attr submitBackground 0x0
int attr subtitle 0x0
int attr subtitleCentered 0x0
int attr subtitleTextAppearance 0x0
int attr subtitleTextColor 0x0
int attr subtitleTextStyle 0x0
int attr suffixText 0x0
int attr suffixTextAppearance 0x0
int attr suffixTextColor 0x0
int attr suggestionRowLayout 0x0
int attr switchMinWidth 0x0
int attr switchPadding 0x0
int attr switchStyle 0x0
int attr switchTextAppearance 0x0
int attr tabBackground 0x0
int attr tabContentStart 0x0
int attr tabGravity 0x0
int attr tabIconTint 0x0
int attr tabIconTintMode 0x0
int attr tabIndicator 0x0
int attr tabIndicatorAnimationDuration 0x0
int attr tabIndicatorAnimationMode 0x0
int attr tabIndicatorColor 0x0
int attr tabIndicatorFullWidth 0x0
int attr tabIndicatorGravity 0x0
int attr tabIndicatorHeight 0x0
int attr tabInlineLabel 0x0
int attr tabMaxWidth 0x0
int attr tabMinWidth 0x0
int attr tabMode 0x0
int attr tabPadding 0x0
int attr tabPaddingBottom 0x0
int attr tabPaddingEnd 0x0
int attr tabPaddingStart 0x0
int attr tabPaddingTop 0x0
int attr tabRippleColor 0x0
int attr tabSecondaryStyle 0x0
int attr tabSelectedTextAppearance 0x0
int attr tabSelectedTextColor 0x0
int attr tabStyle 0x0
int attr tabTextAppearance 0x0
int attr tabTextColor 0x0
int attr tabUnboundedRipple 0x0
int attr tag 0x0
int attr targetId 0x0
int attr telltales_tailColor 0x0
int attr telltales_tailScale 0x0
int attr telltales_velocityMode 0x0
int attr textAllCaps 0x0
int attr textAppearanceBody1 0x0
int attr textAppearanceBody2 0x0
int attr textAppearanceBodyLarge 0x0
int attr textAppearanceBodyMedium 0x0
int attr textAppearanceBodySmall 0x0
int attr textAppearanceButton 0x0
int attr textAppearanceCaption 0x0
int attr textAppearanceDisplayLarge 0x0
int attr textAppearanceDisplayMedium 0x0
int attr textAppearanceDisplaySmall 0x0
int attr textAppearanceHeadline1 0x0
int attr textAppearanceHeadline2 0x0
int attr textAppearanceHeadline3 0x0
int attr textAppearanceHeadline4 0x0
int attr textAppearanceHeadline5 0x0
int attr textAppearanceHeadline6 0x0
int attr textAppearanceHeadlineLarge 0x0
int attr textAppearanceHeadlineMedium 0x0
int attr textAppearanceHeadlineSmall 0x0
int attr textAppearanceLabelLarge 0x0
int attr textAppearanceLabelMedium 0x0
int attr textAppearanceLabelSmall 0x0
int attr textAppearanceLargePopupMenu 0x0
int attr textAppearanceLineHeightEnabled 0x0
int attr textAppearanceListItem 0x0
int attr textAppearanceListItemSecondary 0x0
int attr textAppearanceListItemSmall 0x0
int attr textAppearanceOverline 0x0
int attr textAppearancePopupMenuHeader 0x0
int attr textAppearanceSearchResultSubtitle 0x0
int attr textAppearanceSearchResultTitle 0x0
int attr textAppearanceSmallPopupMenu 0x0
int attr textAppearanceSubtitle1 0x0
int attr textAppearanceSubtitle2 0x0
int attr textAppearanceTitleLarge 0x0
int attr textAppearanceTitleMedium 0x0
int attr textAppearanceTitleSmall 0x0
int attr textColorAlertDialogListItem 0x0
int attr textColorSearchUrl 0x0
int attr textEndPadding 0x0
int attr textInputFilledDenseStyle 0x0
int attr textInputFilledExposedDropdownMenuStyle 0x0
int attr textInputFilledStyle 0x0
int attr textInputLayoutFocusedRectEnabled 0x0
int attr textInputOutlinedDenseStyle 0x0
int attr textInputOutlinedExposedDropdownMenuStyle 0x0
int attr textInputOutlinedStyle 0x0
int attr textInputStyle 0x0
int attr textLocale 0x0
int attr textStartPadding 0x0
int attr theme 0x0
int attr thickness 0x0
int attr thumbColor 0x0
int attr thumbElevation 0x0
int attr thumbIcon 0x0
int attr thumbIconSize 0x0
int attr thumbIconTint 0x0
int attr thumbIconTintMode 0x0
int attr thumbRadius 0x0
int attr thumbStrokeColor 0x0
int attr thumbStrokeWidth 0x0
int attr thumbTextPadding 0x0
int attr thumbTint 0x0
int attr thumbTintMode 0x0
int attr tickColor 0x0
int attr tickColorActive 0x0
int attr tickColorInactive 0x0
int attr tickMark 0x0
int attr tickMarkTint 0x0
int attr tickMarkTintMode 0x0
int attr tickRadiusActive 0x0
int attr tickRadiusInactive 0x0
int attr tickVisible 0x0
int attr tint 0x0
int attr tintMode 0x0
int attr tintNavigationIcon 0x0
int attr title 0x0
int attr titleCentered 0x0
int attr titleCollapseMode 0x0
int attr titleEnabled 0x0
int attr titleMargin 0x0
int attr titleMarginBottom 0x0
int attr titleMarginEnd 0x0
int attr titleMarginStart 0x0
int attr titleMarginTop 0x0
int attr titleMargins 0x0
int attr titlePositionInterpolator 0x0
int attr titleTextAppearance 0x0
int attr titleTextColor 0x0
int attr titleTextEllipsize 0x0
int attr titleTextStyle 0x0
int attr toggleCheckedStateOnClick 0x0
int attr toolbarId 0x0
int attr toolbarNavigationButtonStyle 0x0
int attr toolbarStyle 0x0
int attr toolbarSurfaceStyle 0x0
int attr tooltipForegroundColor 0x0
int attr tooltipFrameBackground 0x0
int attr tooltipStyle 0x0
int attr tooltipText 0x0
int attr topInsetScrimEnabled 0x0
int attr touchAnchorId 0x0
int attr touchAnchorSide 0x0
int attr touchRegionId 0x0
int attr track 0x0
int attr trackColor 0x0
int attr trackColorActive 0x0
int attr trackColorInactive 0x0
int attr trackCornerRadius 0x0
int attr trackDecoration 0x0
int attr trackDecorationTint 0x0
int attr trackDecorationTintMode 0x0
int attr trackHeight 0x0
int attr trackThickness 0x0
int attr trackTint 0x0
int attr trackTintMode 0x0
int attr transitionDisable 0x0
int attr transitionEasing 0x0
int attr transitionFlags 0x0
int attr transitionPathRotate 0x0
int attr transitionShapeAppearance 0x0
int attr triggerId 0x0
int attr triggerReceiver 0x0
int attr triggerSlack 0x0
int attr ttcIndex 0x0
int attr useCompatPadding 0x0
int attr useDrawerArrowDrawable 0x0
int attr useMaterialThemeColors 0x0
int attr values 0x0
int attr verticalOffset 0x0
int attr verticalOffsetWithText 0x0
int attr viewInflaterClass 0x0
int attr visibilityMode 0x0
int attr voiceIcon 0x0
int attr warmth 0x0
int attr waveDecay 0x0
int attr waveOffset 0x0
int attr wavePeriod 0x0
int attr waveShape 0x0
int attr waveVariesBy 0x0
int attr windowActionBar 0x0
int attr windowActionBarOverlay 0x0
int attr windowActionModeOverlay 0x0
int attr windowFixedHeightMajor 0x0
int attr windowFixedHeightMinor 0x0
int attr windowFixedWidthMajor 0x0
int attr windowFixedWidthMinor 0x0
int attr windowMinWidthMajor 0x0
int attr windowMinWidthMinor 0x0
int attr windowNoTitle 0x0
int attr yearSelectedStyle 0x0
int attr yearStyle 0x0
int attr yearTodayStyle 0x0
int bool abc_action_bar_embed_tabs 0x0
int bool abc_allow_stacked_button_bar 0x0
int bool abc_config_actionMenuItemAllCaps 0x0
int bool mtrl_btn_textappearance_all_caps 0x0
int color abc_background_cache_hint_selector_material_dark 0x0
int color abc_background_cache_hint_selector_material_light 0x0
int color abc_btn_colored_borderless_text_material 0x0
int color abc_btn_colored_text_material 0x0
int color abc_color_highlight_material 0x0
int color abc_decor_view_status_guard 0x0
int color abc_decor_view_status_guard_light 0x0
int color abc_hint_foreground_material_dark 0x0
int color abc_hint_foreground_material_light 0x0
int color abc_primary_text_disable_only_material_dark 0x0
int color abc_primary_text_disable_only_material_light 0x0
int color abc_primary_text_material_dark 0x0
int color abc_primary_text_material_light 0x0
int color abc_search_url_text 0x0
int color abc_search_url_text_normal 0x0
int color abc_search_url_text_pressed 0x0
int color abc_search_url_text_selected 0x0
int color abc_secondary_text_material_dark 0x0
int color abc_secondary_text_material_light 0x0
int color abc_tint_btn_checkable 0x0
int color abc_tint_default 0x0
int color abc_tint_edittext 0x0
int color abc_tint_seek_thumb 0x0
int color abc_tint_spinner 0x0
int color abc_tint_switch_track 0x0
int color accent_material_dark 0x0
int color accent_material_light 0x0
int color androidx_core_ripple_material_light 0x0
int color androidx_core_secondary_text_default_material_light 0x0
int color background_floating_material_dark 0x0
int color background_floating_material_light 0x0
int color background_material_dark 0x0
int color background_material_light 0x0
int color bright_foreground_disabled_material_dark 0x0
int color bright_foreground_disabled_material_light 0x0
int color bright_foreground_inverse_material_dark 0x0
int color bright_foreground_inverse_material_light 0x0
int color bright_foreground_material_dark 0x0
int color bright_foreground_material_light 0x0
int color button_material_dark 0x0
int color button_material_light 0x0
int color call_notification_answer_color 0x0
int color call_notification_decline_color 0x0
int color cardview_dark_background 0x0
int color cardview_light_background 0x0
int color cardview_shadow_end_color 0x0
int color cardview_shadow_start_color 0x0
int color design_bottom_navigation_shadow_color 0x0
int color design_box_stroke_color 0x0
int color design_dark_default_color_background 0x0
int color design_dark_default_color_error 0x0
int color design_dark_default_color_on_background 0x0
int color design_dark_default_color_on_error 0x0
int color design_dark_default_color_on_primary 0x0
int color design_dark_default_color_on_secondary 0x0
int color design_dark_default_color_on_surface 0x0
int color design_dark_default_color_primary 0x0
int color design_dark_default_color_primary_dark 0x0
int color design_dark_default_color_primary_variant 0x0
int color design_dark_default_color_secondary 0x0
int color design_dark_default_color_secondary_variant 0x0
int color design_dark_default_color_surface 0x0
int color design_default_color_background 0x0
int color design_default_color_error 0x0
int color design_default_color_on_background 0x0
int color design_default_color_on_error 0x0
int color design_default_color_on_primary 0x0
int color design_default_color_on_secondary 0x0
int color design_default_color_on_surface 0x0
int color design_default_color_primary 0x0
int color design_default_color_primary_dark 0x0
int color design_default_color_primary_variant 0x0
int color design_default_color_secondary 0x0
int color design_default_color_secondary_variant 0x0
int color design_default_color_surface 0x0
int color design_error 0x0
int color design_fab_shadow_end_color 0x0
int color design_fab_shadow_mid_color 0x0
int color design_fab_shadow_start_color 0x0
int color design_fab_stroke_end_inner_color 0x0
int color design_fab_stroke_end_outer_color 0x0
int color design_fab_stroke_top_inner_color 0x0
int color design_fab_stroke_top_outer_color 0x0
int color design_icon_tint 0x0
int color design_snackbar_background_color 0x0
int color dim_foreground_disabled_material_dark 0x0
int color dim_foreground_disabled_material_light 0x0
int color dim_foreground_material_dark 0x0
int color dim_foreground_material_light 0x0
int color error_color_material_dark 0x0
int color error_color_material_light 0x0
int color foreground_material_dark 0x0
int color foreground_material_light 0x0
int color highlighted_text_material_dark 0x0
int color highlighted_text_material_light 0x0
int color m3_appbar_overlay_color 0x0
int color m3_assist_chip_icon_tint_color 0x0
int color m3_assist_chip_stroke_color 0x0
int color m3_bottom_sheet_drag_handle_color 0x0
int color m3_button_background_color_selector 0x0
int color m3_button_foreground_color_selector 0x0
int color m3_button_outline_color_selector 0x0
int color m3_button_ripple_color 0x0
int color m3_button_ripple_color_selector 0x0
int color m3_calendar_item_disabled_text 0x0
int color m3_calendar_item_stroke_color 0x0
int color m3_card_foreground_color 0x0
int color m3_card_ripple_color 0x0
int color m3_card_stroke_color 0x0
int color m3_checkbox_button_icon_tint 0x0
int color m3_checkbox_button_tint 0x0
int color m3_chip_assist_text_color 0x0
int color m3_chip_background_color 0x0
int color m3_chip_ripple_color 0x0
int color m3_chip_stroke_color 0x0
int color m3_chip_text_color 0x0
int color m3_dark_default_color_primary_text 0x0
int color m3_dark_default_color_secondary_text 0x0
int color m3_dark_highlighted_text 0x0
int color m3_dark_hint_foreground 0x0
int color m3_dark_primary_text_disable_only 0x0
int color m3_default_color_primary_text 0x0
int color m3_default_color_secondary_text 0x0
int color m3_dynamic_dark_default_color_primary_text 0x0
int color m3_dynamic_dark_default_color_secondary_text 0x0
int color m3_dynamic_dark_highlighted_text 0x0
int color m3_dynamic_dark_hint_foreground 0x0
int color m3_dynamic_dark_primary_text_disable_only 0x0
int color m3_dynamic_default_color_primary_text 0x0
int color m3_dynamic_default_color_secondary_text 0x0
int color m3_dynamic_highlighted_text 0x0
int color m3_dynamic_hint_foreground 0x0
int color m3_dynamic_primary_text_disable_only 0x0
int color m3_efab_ripple_color_selector 0x0
int color m3_elevated_chip_background_color 0x0
int color m3_fab_efab_background_color_selector 0x0
int color m3_fab_efab_foreground_color_selector 0x0
int color m3_fab_ripple_color_selector 0x0
int color m3_filled_icon_button_container_color_selector 0x0
int color m3_highlighted_text 0x0
int color m3_hint_foreground 0x0
int color m3_icon_button_icon_color_selector 0x0
int color m3_navigation_bar_item_with_indicator_icon_tint 0x0
int color m3_navigation_bar_item_with_indicator_label_tint 0x0
int color m3_navigation_bar_ripple_color_selector 0x0
int color m3_navigation_item_background_color 0x0
int color m3_navigation_item_icon_tint 0x0
int color m3_navigation_item_ripple_color 0x0
int color m3_navigation_item_text_color 0x0
int color m3_navigation_rail_item_with_indicator_icon_tint 0x0
int color m3_navigation_rail_item_with_indicator_label_tint 0x0
int color m3_navigation_rail_ripple_color_selector 0x0
int color m3_popupmenu_overlay_color 0x0
int color m3_primary_text_disable_only 0x0
int color m3_radiobutton_button_tint 0x0
int color m3_radiobutton_ripple_tint 0x0
int color m3_ref_palette_black 0x0
int color m3_ref_palette_dynamic_neutral0 0x0
int color m3_ref_palette_dynamic_neutral10 0x0
int color m3_ref_palette_dynamic_neutral100 0x0
int color m3_ref_palette_dynamic_neutral12 0x0
int color m3_ref_palette_dynamic_neutral17 0x0
int color m3_ref_palette_dynamic_neutral20 0x0
int color m3_ref_palette_dynamic_neutral22 0x0
int color m3_ref_palette_dynamic_neutral24 0x0
int color m3_ref_palette_dynamic_neutral30 0x0
int color m3_ref_palette_dynamic_neutral4 0x0
int color m3_ref_palette_dynamic_neutral40 0x0
int color m3_ref_palette_dynamic_neutral50 0x0
int color m3_ref_palette_dynamic_neutral6 0x0
int color m3_ref_palette_dynamic_neutral60 0x0
int color m3_ref_palette_dynamic_neutral70 0x0
int color m3_ref_palette_dynamic_neutral80 0x0
int color m3_ref_palette_dynamic_neutral87 0x0
int color m3_ref_palette_dynamic_neutral90 0x0
int color m3_ref_palette_dynamic_neutral92 0x0
int color m3_ref_palette_dynamic_neutral94 0x0
int color m3_ref_palette_dynamic_neutral95 0x0
int color m3_ref_palette_dynamic_neutral96 0x0
int color m3_ref_palette_dynamic_neutral98 0x0
int color m3_ref_palette_dynamic_neutral99 0x0
int color m3_ref_palette_dynamic_neutral_variant0 0x0
int color m3_ref_palette_dynamic_neutral_variant10 0x0
int color m3_ref_palette_dynamic_neutral_variant100 0x0
int color m3_ref_palette_dynamic_neutral_variant12 0x0
int color m3_ref_palette_dynamic_neutral_variant17 0x0
int color m3_ref_palette_dynamic_neutral_variant20 0x0
int color m3_ref_palette_dynamic_neutral_variant22 0x0
int color m3_ref_palette_dynamic_neutral_variant24 0x0
int color m3_ref_palette_dynamic_neutral_variant30 0x0
int color m3_ref_palette_dynamic_neutral_variant4 0x0
int color m3_ref_palette_dynamic_neutral_variant40 0x0
int color m3_ref_palette_dynamic_neutral_variant50 0x0
int color m3_ref_palette_dynamic_neutral_variant6 0x0
int color m3_ref_palette_dynamic_neutral_variant60 0x0
int color m3_ref_palette_dynamic_neutral_variant70 0x0
int color m3_ref_palette_dynamic_neutral_variant80 0x0
int color m3_ref_palette_dynamic_neutral_variant87 0x0
int color m3_ref_palette_dynamic_neutral_variant90 0x0
int color m3_ref_palette_dynamic_neutral_variant92 0x0
int color m3_ref_palette_dynamic_neutral_variant94 0x0
int color m3_ref_palette_dynamic_neutral_variant95 0x0
int color m3_ref_palette_dynamic_neutral_variant96 0x0
int color m3_ref_palette_dynamic_neutral_variant98 0x0
int color m3_ref_palette_dynamic_neutral_variant99 0x0
int color m3_ref_palette_dynamic_primary0 0x0
int color m3_ref_palette_dynamic_primary10 0x0
int color m3_ref_palette_dynamic_primary100 0x0
int color m3_ref_palette_dynamic_primary20 0x0
int color m3_ref_palette_dynamic_primary30 0x0
int color m3_ref_palette_dynamic_primary40 0x0
int color m3_ref_palette_dynamic_primary50 0x0
int color m3_ref_palette_dynamic_primary60 0x0
int color m3_ref_palette_dynamic_primary70 0x0
int color m3_ref_palette_dynamic_primary80 0x0
int color m3_ref_palette_dynamic_primary90 0x0
int color m3_ref_palette_dynamic_primary95 0x0
int color m3_ref_palette_dynamic_primary99 0x0
int color m3_ref_palette_dynamic_secondary0 0x0
int color m3_ref_palette_dynamic_secondary10 0x0
int color m3_ref_palette_dynamic_secondary100 0x0
int color m3_ref_palette_dynamic_secondary20 0x0
int color m3_ref_palette_dynamic_secondary30 0x0
int color m3_ref_palette_dynamic_secondary40 0x0
int color m3_ref_palette_dynamic_secondary50 0x0
int color m3_ref_palette_dynamic_secondary60 0x0
int color m3_ref_palette_dynamic_secondary70 0x0
int color m3_ref_palette_dynamic_secondary80 0x0
int color m3_ref_palette_dynamic_secondary90 0x0
int color m3_ref_palette_dynamic_secondary95 0x0
int color m3_ref_palette_dynamic_secondary99 0x0
int color m3_ref_palette_dynamic_tertiary0 0x0
int color m3_ref_palette_dynamic_tertiary10 0x0
int color m3_ref_palette_dynamic_tertiary100 0x0
int color m3_ref_palette_dynamic_tertiary20 0x0
int color m3_ref_palette_dynamic_tertiary30 0x0
int color m3_ref_palette_dynamic_tertiary40 0x0
int color m3_ref_palette_dynamic_tertiary50 0x0
int color m3_ref_palette_dynamic_tertiary60 0x0
int color m3_ref_palette_dynamic_tertiary70 0x0
int color m3_ref_palette_dynamic_tertiary80 0x0
int color m3_ref_palette_dynamic_tertiary90 0x0
int color m3_ref_palette_dynamic_tertiary95 0x0
int color m3_ref_palette_dynamic_tertiary99 0x0
int color m3_ref_palette_error0 0x0
int color m3_ref_palette_error10 0x0
int color m3_ref_palette_error100 0x0
int color m3_ref_palette_error20 0x0
int color m3_ref_palette_error30 0x0
int color m3_ref_palette_error40 0x0
int color m3_ref_palette_error50 0x0
int color m3_ref_palette_error60 0x0
int color m3_ref_palette_error70 0x0
int color m3_ref_palette_error80 0x0
int color m3_ref_palette_error90 0x0
int color m3_ref_palette_error95 0x0
int color m3_ref_palette_error99 0x0
int color m3_ref_palette_neutral0 0x0
int color m3_ref_palette_neutral10 0x0
int color m3_ref_palette_neutral100 0x0
int color m3_ref_palette_neutral12 0x0
int color m3_ref_palette_neutral17 0x0
int color m3_ref_palette_neutral20 0x0
int color m3_ref_palette_neutral22 0x0
int color m3_ref_palette_neutral24 0x0
int color m3_ref_palette_neutral30 0x0
int color m3_ref_palette_neutral4 0x0
int color m3_ref_palette_neutral40 0x0
int color m3_ref_palette_neutral50 0x0
int color m3_ref_palette_neutral6 0x0
int color m3_ref_palette_neutral60 0x0
int color m3_ref_palette_neutral70 0x0
int color m3_ref_palette_neutral80 0x0
int color m3_ref_palette_neutral87 0x0
int color m3_ref_palette_neutral90 0x0
int color m3_ref_palette_neutral92 0x0
int color m3_ref_palette_neutral94 0x0
int color m3_ref_palette_neutral95 0x0
int color m3_ref_palette_neutral96 0x0
int color m3_ref_palette_neutral98 0x0
int color m3_ref_palette_neutral99 0x0
int color m3_ref_palette_neutral_variant0 0x0
int color m3_ref_palette_neutral_variant10 0x0
int color m3_ref_palette_neutral_variant100 0x0
int color m3_ref_palette_neutral_variant20 0x0
int color m3_ref_palette_neutral_variant30 0x0
int color m3_ref_palette_neutral_variant40 0x0
int color m3_ref_palette_neutral_variant50 0x0
int color m3_ref_palette_neutral_variant60 0x0
int color m3_ref_palette_neutral_variant70 0x0
int color m3_ref_palette_neutral_variant80 0x0
int color m3_ref_palette_neutral_variant90 0x0
int color m3_ref_palette_neutral_variant95 0x0
int color m3_ref_palette_neutral_variant99 0x0
int color m3_ref_palette_primary0 0x0
int color m3_ref_palette_primary10 0x0
int color m3_ref_palette_primary100 0x0
int color m3_ref_palette_primary20 0x0
int color m3_ref_palette_primary30 0x0
int color m3_ref_palette_primary40 0x0
int color m3_ref_palette_primary50 0x0
int color m3_ref_palette_primary60 0x0
int color m3_ref_palette_primary70 0x0
int color m3_ref_palette_primary80 0x0
int color m3_ref_palette_primary90 0x0
int color m3_ref_palette_primary95 0x0
int color m3_ref_palette_primary99 0x0
int color m3_ref_palette_secondary0 0x0
int color m3_ref_palette_secondary10 0x0
int color m3_ref_palette_secondary100 0x0
int color m3_ref_palette_secondary20 0x0
int color m3_ref_palette_secondary30 0x0
int color m3_ref_palette_secondary40 0x0
int color m3_ref_palette_secondary50 0x0
int color m3_ref_palette_secondary60 0x0
int color m3_ref_palette_secondary70 0x0
int color m3_ref_palette_secondary80 0x0
int color m3_ref_palette_secondary90 0x0
int color m3_ref_palette_secondary95 0x0
int color m3_ref_palette_secondary99 0x0
int color m3_ref_palette_tertiary0 0x0
int color m3_ref_palette_tertiary10 0x0
int color m3_ref_palette_tertiary100 0x0
int color m3_ref_palette_tertiary20 0x0
int color m3_ref_palette_tertiary30 0x0
int color m3_ref_palette_tertiary40 0x0
int color m3_ref_palette_tertiary50 0x0
int color m3_ref_palette_tertiary60 0x0
int color m3_ref_palette_tertiary70 0x0
int color m3_ref_palette_tertiary80 0x0
int color m3_ref_palette_tertiary90 0x0
int color m3_ref_palette_tertiary95 0x0
int color m3_ref_palette_tertiary99 0x0
int color m3_ref_palette_white 0x0
int color m3_selection_control_ripple_color_selector 0x0
int color m3_simple_item_ripple_color 0x0
int color m3_slider_active_track_color 0x0
int color m3_slider_halo_color 0x0
int color m3_slider_inactive_track_color 0x0
int color m3_slider_thumb_color 0x0
int color m3_switch_thumb_tint 0x0
int color m3_switch_track_tint 0x0
int color m3_sys_color_dark_background 0x0
int color m3_sys_color_dark_error 0x0
int color m3_sys_color_dark_error_container 0x0
int color m3_sys_color_dark_inverse_on_surface 0x0
int color m3_sys_color_dark_inverse_primary 0x0
int color m3_sys_color_dark_inverse_surface 0x0
int color m3_sys_color_dark_on_background 0x0
int color m3_sys_color_dark_on_error 0x0
int color m3_sys_color_dark_on_error_container 0x0
int color m3_sys_color_dark_on_primary 0x0
int color m3_sys_color_dark_on_primary_container 0x0
int color m3_sys_color_dark_on_secondary 0x0
int color m3_sys_color_dark_on_secondary_container 0x0
int color m3_sys_color_dark_on_surface 0x0
int color m3_sys_color_dark_on_surface_variant 0x0
int color m3_sys_color_dark_on_tertiary 0x0
int color m3_sys_color_dark_on_tertiary_container 0x0
int color m3_sys_color_dark_outline 0x0
int color m3_sys_color_dark_outline_variant 0x0
int color m3_sys_color_dark_primary 0x0
int color m3_sys_color_dark_primary_container 0x0
int color m3_sys_color_dark_secondary 0x0
int color m3_sys_color_dark_secondary_container 0x0
int color m3_sys_color_dark_surface 0x0
int color m3_sys_color_dark_surface_bright 0x0
int color m3_sys_color_dark_surface_container 0x0
int color m3_sys_color_dark_surface_container_high 0x0
int color m3_sys_color_dark_surface_container_highest 0x0
int color m3_sys_color_dark_surface_container_low 0x0
int color m3_sys_color_dark_surface_container_lowest 0x0
int color m3_sys_color_dark_surface_dim 0x0
int color m3_sys_color_dark_surface_variant 0x0
int color m3_sys_color_dark_tertiary 0x0
int color m3_sys_color_dark_tertiary_container 0x0
int color m3_sys_color_dynamic_dark_background 0x0
int color m3_sys_color_dynamic_dark_error 0x0
int color m3_sys_color_dynamic_dark_error_container 0x0
int color m3_sys_color_dynamic_dark_inverse_on_surface 0x0
int color m3_sys_color_dynamic_dark_inverse_primary 0x0
int color m3_sys_color_dynamic_dark_inverse_surface 0x0
int color m3_sys_color_dynamic_dark_on_background 0x0
int color m3_sys_color_dynamic_dark_on_error 0x0
int color m3_sys_color_dynamic_dark_on_error_container 0x0
int color m3_sys_color_dynamic_dark_on_primary 0x0
int color m3_sys_color_dynamic_dark_on_primary_container 0x0
int color m3_sys_color_dynamic_dark_on_secondary 0x0
int color m3_sys_color_dynamic_dark_on_secondary_container 0x0
int color m3_sys_color_dynamic_dark_on_surface 0x0
int color m3_sys_color_dynamic_dark_on_surface_variant 0x0
int color m3_sys_color_dynamic_dark_on_tertiary 0x0
int color m3_sys_color_dynamic_dark_on_tertiary_container 0x0
int color m3_sys_color_dynamic_dark_outline 0x0
int color m3_sys_color_dynamic_dark_outline_variant 0x0
int color m3_sys_color_dynamic_dark_primary 0x0
int color m3_sys_color_dynamic_dark_primary_container 0x0
int color m3_sys_color_dynamic_dark_secondary 0x0
int color m3_sys_color_dynamic_dark_secondary_container 0x0
int color m3_sys_color_dynamic_dark_surface 0x0
int color m3_sys_color_dynamic_dark_surface_bright 0x0
int color m3_sys_color_dynamic_dark_surface_container 0x0
int color m3_sys_color_dynamic_dark_surface_container_high 0x0
int color m3_sys_color_dynamic_dark_surface_container_highest 0x0
int color m3_sys_color_dynamic_dark_surface_container_low 0x0
int color m3_sys_color_dynamic_dark_surface_container_lowest 0x0
int color m3_sys_color_dynamic_dark_surface_dim 0x0
int color m3_sys_color_dynamic_dark_surface_variant 0x0
int color m3_sys_color_dynamic_dark_tertiary 0x0
int color m3_sys_color_dynamic_dark_tertiary_container 0x0
int color m3_sys_color_dynamic_light_background 0x0
int color m3_sys_color_dynamic_light_error 0x0
int color m3_sys_color_dynamic_light_error_container 0x0
int color m3_sys_color_dynamic_light_inverse_on_surface 0x0
int color m3_sys_color_dynamic_light_inverse_primary 0x0
int color m3_sys_color_dynamic_light_inverse_surface 0x0
int color m3_sys_color_dynamic_light_on_background 0x0
int color m3_sys_color_dynamic_light_on_error 0x0
int color m3_sys_color_dynamic_light_on_error_container 0x0
int color m3_sys_color_dynamic_light_on_primary 0x0
int color m3_sys_color_dynamic_light_on_primary_container 0x0
int color m3_sys_color_dynamic_light_on_secondary 0x0
int color m3_sys_color_dynamic_light_on_secondary_container 0x0
int color m3_sys_color_dynamic_light_on_surface 0x0
int color m3_sys_color_dynamic_light_on_surface_variant 0x0
int color m3_sys_color_dynamic_light_on_tertiary 0x0
int color m3_sys_color_dynamic_light_on_tertiary_container 0x0
int color m3_sys_color_dynamic_light_outline 0x0
int color m3_sys_color_dynamic_light_outline_variant 0x0
int color m3_sys_color_dynamic_light_primary 0x0
int color m3_sys_color_dynamic_light_primary_container 0x0
int color m3_sys_color_dynamic_light_secondary 0x0
int color m3_sys_color_dynamic_light_secondary_container 0x0
int color m3_sys_color_dynamic_light_surface 0x0
int color m3_sys_color_dynamic_light_surface_bright 0x0
int color m3_sys_color_dynamic_light_surface_container 0x0
int color m3_sys_color_dynamic_light_surface_container_high 0x0
int color m3_sys_color_dynamic_light_surface_container_highest 0x0
int color m3_sys_color_dynamic_light_surface_container_low 0x0
int color m3_sys_color_dynamic_light_surface_container_lowest 0x0
int color m3_sys_color_dynamic_light_surface_dim 0x0
int color m3_sys_color_dynamic_light_surface_variant 0x0
int color m3_sys_color_dynamic_light_tertiary 0x0
int color m3_sys_color_dynamic_light_tertiary_container 0x0
int color m3_sys_color_dynamic_on_primary_fixed 0x0
int color m3_sys_color_dynamic_on_primary_fixed_variant 0x0
int color m3_sys_color_dynamic_on_secondary_fixed 0x0
int color m3_sys_color_dynamic_on_secondary_fixed_variant 0x0
int color m3_sys_color_dynamic_on_tertiary_fixed 0x0
int color m3_sys_color_dynamic_on_tertiary_fixed_variant 0x0
int color m3_sys_color_dynamic_primary_fixed 0x0
int color m3_sys_color_dynamic_primary_fixed_dim 0x0
int color m3_sys_color_dynamic_secondary_fixed 0x0
int color m3_sys_color_dynamic_secondary_fixed_dim 0x0
int color m3_sys_color_dynamic_tertiary_fixed 0x0
int color m3_sys_color_dynamic_tertiary_fixed_dim 0x0
int color m3_sys_color_light_background 0x0
int color m3_sys_color_light_error 0x0
int color m3_sys_color_light_error_container 0x0
int color m3_sys_color_light_inverse_on_surface 0x0
int color m3_sys_color_light_inverse_primary 0x0
int color m3_sys_color_light_inverse_surface 0x0
int color m3_sys_color_light_on_background 0x0
int color m3_sys_color_light_on_error 0x0
int color m3_sys_color_light_on_error_container 0x0
int color m3_sys_color_light_on_primary 0x0
int color m3_sys_color_light_on_primary_container 0x0
int color m3_sys_color_light_on_secondary 0x0
int color m3_sys_color_light_on_secondary_container 0x0
int color m3_sys_color_light_on_surface 0x0
int color m3_sys_color_light_on_surface_variant 0x0
int color m3_sys_color_light_on_tertiary 0x0
int color m3_sys_color_light_on_tertiary_container 0x0
int color m3_sys_color_light_outline 0x0
int color m3_sys_color_light_outline_variant 0x0
int color m3_sys_color_light_primary 0x0
int color m3_sys_color_light_primary_container 0x0
int color m3_sys_color_light_secondary 0x0
int color m3_sys_color_light_secondary_container 0x0
int color m3_sys_color_light_surface 0x0
int color m3_sys_color_light_surface_bright 0x0
int color m3_sys_color_light_surface_container 0x0
int color m3_sys_color_light_surface_container_high 0x0
int color m3_sys_color_light_surface_container_highest 0x0
int color m3_sys_color_light_surface_container_low 0x0
int color m3_sys_color_light_surface_container_lowest 0x0
int color m3_sys_color_light_surface_dim 0x0
int color m3_sys_color_light_surface_variant 0x0
int color m3_sys_color_light_tertiary 0x0
int color m3_sys_color_light_tertiary_container 0x0
int color m3_sys_color_on_primary_fixed 0x0
int color m3_sys_color_on_primary_fixed_variant 0x0
int color m3_sys_color_on_secondary_fixed 0x0
int color m3_sys_color_on_secondary_fixed_variant 0x0
int color m3_sys_color_on_tertiary_fixed 0x0
int color m3_sys_color_on_tertiary_fixed_variant 0x0
int color m3_sys_color_primary_fixed 0x0
int color m3_sys_color_primary_fixed_dim 0x0
int color m3_sys_color_secondary_fixed 0x0
int color m3_sys_color_secondary_fixed_dim 0x0
int color m3_sys_color_tertiary_fixed 0x0
int color m3_sys_color_tertiary_fixed_dim 0x0
int color m3_tabs_icon_color 0x0
int color m3_tabs_icon_color_secondary 0x0
int color m3_tabs_ripple_color 0x0
int color m3_tabs_ripple_color_secondary 0x0
int color m3_tabs_text_color 0x0
int color m3_tabs_text_color_secondary 0x0
int color m3_text_button_background_color_selector 0x0
int color m3_text_button_foreground_color_selector 0x0
int color m3_text_button_ripple_color_selector 0x0
int color m3_textfield_filled_background_color 0x0
int color m3_textfield_indicator_text_color 0x0
int color m3_textfield_input_text_color 0x0
int color m3_textfield_label_color 0x0
int color m3_textfield_stroke_color 0x0
int color m3_timepicker_button_background_color 0x0
int color m3_timepicker_button_ripple_color 0x0
int color m3_timepicker_button_text_color 0x0
int color m3_timepicker_clock_text_color 0x0
int color m3_timepicker_display_background_color 0x0
int color m3_timepicker_display_ripple_color 0x0
int color m3_timepicker_display_text_color 0x0
int color m3_timepicker_secondary_text_button_ripple_color 0x0
int color m3_timepicker_secondary_text_button_text_color 0x0
int color m3_timepicker_time_input_stroke_color 0x0
int color m3_tonal_button_ripple_color_selector 0x0
int color material_blue_grey_800 0x0
int color material_blue_grey_900 0x0
int color material_blue_grey_950 0x0
int color material_cursor_color 0x0
int color material_deep_teal_200 0x0
int color material_deep_teal_500 0x0
int color material_divider_color 0x0
int color material_dynamic_color_dark_error 0x0
int color material_dynamic_color_dark_error_container 0x0
int color material_dynamic_color_dark_on_error 0x0
int color material_dynamic_color_dark_on_error_container 0x0
int color material_dynamic_color_light_error 0x0
int color material_dynamic_color_light_error_container 0x0
int color material_dynamic_color_light_on_error 0x0
int color material_dynamic_color_light_on_error_container 0x0
int color material_dynamic_neutral0 0x0
int color material_dynamic_neutral10 0x0
int color material_dynamic_neutral100 0x0
int color material_dynamic_neutral20 0x0
int color material_dynamic_neutral30 0x0
int color material_dynamic_neutral40 0x0
int color material_dynamic_neutral50 0x0
int color material_dynamic_neutral60 0x0
int color material_dynamic_neutral70 0x0
int color material_dynamic_neutral80 0x0
int color material_dynamic_neutral90 0x0
int color material_dynamic_neutral95 0x0
int color material_dynamic_neutral99 0x0
int color material_dynamic_neutral_variant0 0x0
int color material_dynamic_neutral_variant10 0x0
int color material_dynamic_neutral_variant100 0x0
int color material_dynamic_neutral_variant20 0x0
int color material_dynamic_neutral_variant30 0x0
int color material_dynamic_neutral_variant40 0x0
int color material_dynamic_neutral_variant50 0x0
int color material_dynamic_neutral_variant60 0x0
int color material_dynamic_neutral_variant70 0x0
int color material_dynamic_neutral_variant80 0x0
int color material_dynamic_neutral_variant90 0x0
int color material_dynamic_neutral_variant95 0x0
int color material_dynamic_neutral_variant99 0x0
int color material_dynamic_primary0 0x0
int color material_dynamic_primary10 0x0
int color material_dynamic_primary100 0x0
int color material_dynamic_primary20 0x0
int color material_dynamic_primary30 0x0
int color material_dynamic_primary40 0x0
int color material_dynamic_primary50 0x0
int color material_dynamic_primary60 0x0
int color material_dynamic_primary70 0x0
int color material_dynamic_primary80 0x0
int color material_dynamic_primary90 0x0
int color material_dynamic_primary95 0x0
int color material_dynamic_primary99 0x0
int color material_dynamic_secondary0 0x0
int color material_dynamic_secondary10 0x0
int color material_dynamic_secondary100 0x0
int color material_dynamic_secondary20 0x0
int color material_dynamic_secondary30 0x0
int color material_dynamic_secondary40 0x0
int color material_dynamic_secondary50 0x0
int color material_dynamic_secondary60 0x0
int color material_dynamic_secondary70 0x0
int color material_dynamic_secondary80 0x0
int color material_dynamic_secondary90 0x0
int color material_dynamic_secondary95 0x0
int color material_dynamic_secondary99 0x0
int color material_dynamic_tertiary0 0x0
int color material_dynamic_tertiary10 0x0
int color material_dynamic_tertiary100 0x0
int color material_dynamic_tertiary20 0x0
int color material_dynamic_tertiary30 0x0
int color material_dynamic_tertiary40 0x0
int color material_dynamic_tertiary50 0x0
int color material_dynamic_tertiary60 0x0
int color material_dynamic_tertiary70 0x0
int color material_dynamic_tertiary80 0x0
int color material_dynamic_tertiary90 0x0
int color material_dynamic_tertiary95 0x0
int color material_dynamic_tertiary99 0x0
int color material_grey_100 0x0
int color material_grey_300 0x0
int color material_grey_50 0x0
int color material_grey_600 0x0
int color material_grey_800 0x0
int color material_grey_850 0x0
int color material_grey_900 0x0
int color material_harmonized_color_error 0x0
int color material_harmonized_color_error_container 0x0
int color material_harmonized_color_on_error 0x0
int color material_harmonized_color_on_error_container 0x0
int color material_on_background_disabled 0x0
int color material_on_background_emphasis_high_type 0x0
int color material_on_background_emphasis_medium 0x0
int color material_on_primary_disabled 0x0
int color material_on_primary_emphasis_high_type 0x0
int color material_on_primary_emphasis_medium 0x0
int color material_on_surface_disabled 0x0
int color material_on_surface_emphasis_high_type 0x0
int color material_on_surface_emphasis_medium 0x0
int color material_on_surface_stroke 0x0
int color material_personalized__highlighted_text 0x0
int color material_personalized__highlighted_text_inverse 0x0
int color material_personalized_color_background 0x0
int color material_personalized_color_control_activated 0x0
int color material_personalized_color_control_highlight 0x0
int color material_personalized_color_control_normal 0x0
int color material_personalized_color_error 0x0
int color material_personalized_color_error_container 0x0
int color material_personalized_color_on_background 0x0
int color material_personalized_color_on_error 0x0
int color material_personalized_color_on_error_container 0x0
int color material_personalized_color_on_primary 0x0
int color material_personalized_color_on_primary_container 0x0
int color material_personalized_color_on_secondary 0x0
int color material_personalized_color_on_secondary_container 0x0
int color material_personalized_color_on_surface 0x0
int color material_personalized_color_on_surface_inverse 0x0
int color material_personalized_color_on_surface_variant 0x0
int color material_personalized_color_on_tertiary 0x0
int color material_personalized_color_on_tertiary_container 0x0
int color material_personalized_color_outline 0x0
int color material_personalized_color_outline_variant 0x0
int color material_personalized_color_primary 0x0
int color material_personalized_color_primary_container 0x0
int color material_personalized_color_primary_inverse 0x0
int color material_personalized_color_primary_text 0x0
int color material_personalized_color_primary_text_inverse 0x0
int color material_personalized_color_secondary 0x0
int color material_personalized_color_secondary_container 0x0
int color material_personalized_color_secondary_text 0x0
int color material_personalized_color_secondary_text_inverse 0x0
int color material_personalized_color_surface 0x0
int color material_personalized_color_surface_bright 0x0
int color material_personalized_color_surface_container 0x0
int color material_personalized_color_surface_container_high 0x0
int color material_personalized_color_surface_container_highest 0x0
int color material_personalized_color_surface_container_low 0x0
int color material_personalized_color_surface_container_lowest 0x0
int color material_personalized_color_surface_dim 0x0
int color material_personalized_color_surface_inverse 0x0
int color material_personalized_color_surface_variant 0x0
int color material_personalized_color_tertiary 0x0
int color material_personalized_color_tertiary_container 0x0
int color material_personalized_color_text_hint_foreground_inverse 0x0
int color material_personalized_color_text_primary_inverse 0x0
int color material_personalized_color_text_primary_inverse_disable_only 0x0
int color material_personalized_color_text_secondary_and_tertiary_inverse 0x0
int color material_personalized_color_text_secondary_and_tertiary_inverse_disabled 0x0
int color material_personalized_hint_foreground 0x0
int color material_personalized_hint_foreground_inverse 0x0
int color material_personalized_primary_inverse_text_disable_only 0x0
int color material_personalized_primary_text_disable_only 0x0
int color material_slider_active_tick_marks_color 0x0
int color material_slider_active_track_color 0x0
int color material_slider_halo_color 0x0
int color material_slider_inactive_tick_marks_color 0x0
int color material_slider_inactive_track_color 0x0
int color material_slider_thumb_color 0x0
int color material_timepicker_button_background 0x0
int color material_timepicker_button_stroke 0x0
int color material_timepicker_clock_text_color 0x0
int color material_timepicker_clockface 0x0
int color material_timepicker_modebutton_tint 0x0
int color mtrl_btn_bg_color_selector 0x0
int color mtrl_btn_ripple_color 0x0
int color mtrl_btn_stroke_color_selector 0x0
int color mtrl_btn_text_btn_bg_color_selector 0x0
int color mtrl_btn_text_btn_ripple_color 0x0
int color mtrl_btn_text_color_disabled 0x0
int color mtrl_btn_text_color_selector 0x0
int color mtrl_btn_transparent_bg_color 0x0
int color mtrl_calendar_item_stroke_color 0x0
int color mtrl_calendar_selected_range 0x0
int color mtrl_card_view_foreground 0x0
int color mtrl_card_view_ripple 0x0
int color mtrl_chip_background_color 0x0
int color mtrl_chip_close_icon_tint 0x0
int color mtrl_chip_surface_color 0x0
int color mtrl_chip_text_color 0x0
int color mtrl_choice_chip_background_color 0x0
int color mtrl_choice_chip_ripple_color 0x0
int color mtrl_choice_chip_text_color 0x0
int color mtrl_error 0x0
int color mtrl_fab_bg_color_selector 0x0
int color mtrl_fab_icon_text_color_selector 0x0
int color mtrl_fab_ripple_color 0x0
int color mtrl_filled_background_color 0x0
int color mtrl_filled_icon_tint 0x0
int color mtrl_filled_stroke_color 0x0
int color mtrl_indicator_text_color 0x0
int color mtrl_navigation_bar_colored_item_tint 0x0
int color mtrl_navigation_bar_colored_ripple_color 0x0
int color mtrl_navigation_bar_item_tint 0x0
int color mtrl_navigation_bar_ripple_color 0x0
int color mtrl_navigation_item_background_color 0x0
int color mtrl_navigation_item_icon_tint 0x0
int color mtrl_navigation_item_text_color 0x0
int color mtrl_on_primary_text_btn_text_color_selector 0x0
int color mtrl_on_surface_ripple_color 0x0
int color mtrl_outlined_icon_tint 0x0
int color mtrl_outlined_stroke_color 0x0
int color mtrl_popupmenu_overlay_color 0x0
int color mtrl_scrim_color 0x0
int color mtrl_switch_thumb_icon_tint 0x0
int color mtrl_switch_thumb_tint 0x0
int color mtrl_switch_track_decoration_tint 0x0
int color mtrl_switch_track_tint 0x0
int color mtrl_tabs_colored_ripple_color 0x0
int color mtrl_tabs_icon_color_selector 0x0
int color mtrl_tabs_icon_color_selector_colored 0x0
int color mtrl_tabs_legacy_text_color_selector 0x0
int color mtrl_tabs_ripple_color 0x0
int color mtrl_text_btn_text_color_selector 0x0
int color mtrl_textinput_default_box_stroke_color 0x0
int color mtrl_textinput_disabled_color 0x0
int color mtrl_textinput_filled_box_default_background_color 0x0
int color mtrl_textinput_focused_box_stroke_color 0x0
int color mtrl_textinput_hovered_box_stroke_color 0x0
int color notification_action_color_filter 0x0
int color notification_icon_bg_color 0x0
int color primary_dark_material_dark 0x0
int color primary_dark_material_light 0x0
int color primary_material_dark 0x0
int color primary_material_light 0x0
int color primary_text_default_material_dark 0x0
int color primary_text_default_material_light 0x0
int color primary_text_disabled_material_dark 0x0
int color primary_text_disabled_material_light 0x0
int color ripple_material_dark 0x0
int color ripple_material_light 0x0
int color secondary_text_default_material_dark 0x0
int color secondary_text_default_material_light 0x0
int color secondary_text_disabled_material_dark 0x0
int color secondary_text_disabled_material_light 0x0
int color switch_thumb_disabled_material_dark 0x0
int color switch_thumb_disabled_material_light 0x0
int color switch_thumb_material_dark 0x0
int color switch_thumb_material_light 0x0
int color switch_thumb_normal_material_dark 0x0
int color switch_thumb_normal_material_light 0x0
int color tooltip_background_dark 0x0
int color tooltip_background_light 0x0
int dimen abc_action_bar_content_inset_material 0x0
int dimen abc_action_bar_content_inset_with_nav 0x0
int dimen abc_action_bar_default_height_material 0x0
int dimen abc_action_bar_default_padding_end_material 0x0
int dimen abc_action_bar_default_padding_start_material 0x0
int dimen abc_action_bar_elevation_material 0x0
int dimen abc_action_bar_icon_vertical_padding_material 0x0
int dimen abc_action_bar_overflow_padding_end_material 0x0
int dimen abc_action_bar_overflow_padding_start_material 0x0
int dimen abc_action_bar_stacked_max_height 0x0
int dimen abc_action_bar_stacked_tab_max_width 0x0
int dimen abc_action_bar_subtitle_bottom_margin_material 0x0
int dimen abc_action_bar_subtitle_top_margin_material 0x0
int dimen abc_action_button_min_height_material 0x0
int dimen abc_action_button_min_width_material 0x0
int dimen abc_action_button_min_width_overflow_material 0x0
int dimen abc_alert_dialog_button_bar_height 0x0
int dimen abc_alert_dialog_button_dimen 0x0
int dimen abc_button_inset_horizontal_material 0x0
int dimen abc_button_inset_vertical_material 0x0
int dimen abc_button_padding_horizontal_material 0x0
int dimen abc_button_padding_vertical_material 0x0
int dimen abc_cascading_menus_min_smallest_width 0x0
int dimen abc_config_prefDialogWidth 0x0
int dimen abc_control_corner_material 0x0
int dimen abc_control_inset_material 0x0
int dimen abc_control_padding_material 0x0
int dimen abc_dialog_corner_radius_material 0x0
int dimen abc_dialog_fixed_height_major 0x0
int dimen abc_dialog_fixed_height_minor 0x0
int dimen abc_dialog_fixed_width_major 0x0
int dimen abc_dialog_fixed_width_minor 0x0
int dimen abc_dialog_list_padding_bottom_no_buttons 0x0
int dimen abc_dialog_list_padding_top_no_title 0x0
int dimen abc_dialog_min_width_major 0x0
int dimen abc_dialog_min_width_minor 0x0
int dimen abc_dialog_padding_material 0x0
int dimen abc_dialog_padding_top_material 0x0
int dimen abc_dialog_title_divider_material 0x0
int dimen abc_disabled_alpha_material_dark 0x0
int dimen abc_disabled_alpha_material_light 0x0
int dimen abc_dropdownitem_icon_width 0x0
int dimen abc_dropdownitem_text_padding_left 0x0
int dimen abc_dropdownitem_text_padding_right 0x0
int dimen abc_edit_text_inset_bottom_material 0x0
int dimen abc_edit_text_inset_horizontal_material 0x0
int dimen abc_edit_text_inset_top_material 0x0
int dimen abc_floating_window_z 0x0
int dimen abc_list_item_height_large_material 0x0
int dimen abc_list_item_height_material 0x0
int dimen abc_list_item_height_small_material 0x0
int dimen abc_list_item_padding_horizontal_material 0x0
int dimen abc_panel_menu_list_width 0x0
int dimen abc_progress_bar_height_material 0x0
int dimen abc_search_view_preferred_height 0x0
int dimen abc_search_view_preferred_width 0x0
int dimen abc_seekbar_track_background_height_material 0x0
int dimen abc_seekbar_track_progress_height_material 0x0
int dimen abc_select_dialog_padding_start_material 0x0
int dimen abc_star_big 0x0
int dimen abc_star_medium 0x0
int dimen abc_star_small 0x0
int dimen abc_switch_padding 0x0
int dimen abc_text_size_body_1_material 0x0
int dimen abc_text_size_body_2_material 0x0
int dimen abc_text_size_button_material 0x0
int dimen abc_text_size_caption_material 0x0
int dimen abc_text_size_display_1_material 0x0
int dimen abc_text_size_display_2_material 0x0
int dimen abc_text_size_display_3_material 0x0
int dimen abc_text_size_display_4_material 0x0
int dimen abc_text_size_headline_material 0x0
int dimen abc_text_size_large_material 0x0
int dimen abc_text_size_medium_material 0x0
int dimen abc_text_size_menu_header_material 0x0
int dimen abc_text_size_menu_material 0x0
int dimen abc_text_size_small_material 0x0
int dimen abc_text_size_subhead_material 0x0
int dimen abc_text_size_subtitle_material_toolbar 0x0
int dimen abc_text_size_title_material 0x0
int dimen abc_text_size_title_material_toolbar 0x0
int dimen appcompat_dialog_background_inset 0x0
int dimen cardview_compat_inset_shadow 0x0
int dimen cardview_default_elevation 0x0
int dimen cardview_default_radius 0x0
int dimen clock_face_margin_start 0x0
int dimen compat_button_inset_horizontal_material 0x0
int dimen compat_button_inset_vertical_material 0x0
int dimen compat_button_padding_horizontal_material 0x0
int dimen compat_button_padding_vertical_material 0x0
int dimen compat_control_corner_material 0x0
int dimen compat_notification_large_icon_max_height 0x0
int dimen compat_notification_large_icon_max_width 0x0
int dimen def_drawer_elevation 0x0
int dimen design_appbar_elevation 0x0
int dimen design_bottom_navigation_active_item_max_width 0x0
int dimen design_bottom_navigation_active_item_min_width 0x0
int dimen design_bottom_navigation_active_text_size 0x0
int dimen design_bottom_navigation_elevation 0x0
int dimen design_bottom_navigation_height 0x0
int dimen design_bottom_navigation_icon_size 0x0
int dimen design_bottom_navigation_item_max_width 0x0
int dimen design_bottom_navigation_item_min_width 0x0
int dimen design_bottom_navigation_label_padding 0x0
int dimen design_bottom_navigation_margin 0x0
int dimen design_bottom_navigation_shadow_height 0x0
int dimen design_bottom_navigation_text_size 0x0
int dimen design_bottom_sheet_elevation 0x0
int dimen design_bottom_sheet_modal_elevation 0x0
int dimen design_bottom_sheet_peek_height_min 0x0
int dimen design_fab_border_width 0x0
int dimen design_fab_elevation 0x0
int dimen design_fab_image_size 0x0
int dimen design_fab_size_mini 0x0
int dimen design_fab_size_normal 0x0
int dimen design_fab_translation_z_hovered_focused 0x0
int dimen design_fab_translation_z_pressed 0x0
int dimen design_navigation_elevation 0x0
int dimen design_navigation_icon_padding 0x0
int dimen design_navigation_icon_size 0x0
int dimen design_navigation_item_horizontal_padding 0x0
int dimen design_navigation_item_icon_padding 0x0
int dimen design_navigation_item_vertical_padding 0x0
int dimen design_navigation_max_width 0x0
int dimen design_navigation_padding_bottom 0x0
int dimen design_navigation_separator_vertical_padding 0x0
int dimen design_snackbar_action_inline_max_width 0x0
int dimen design_snackbar_action_text_color_alpha 0x0
int dimen design_snackbar_background_corner_radius 0x0
int dimen design_snackbar_elevation 0x0
int dimen design_snackbar_extra_spacing_horizontal 0x0
int dimen design_snackbar_max_width 0x0
int dimen design_snackbar_min_width 0x0
int dimen design_snackbar_padding_horizontal 0x0
int dimen design_snackbar_padding_vertical 0x0
int dimen design_snackbar_padding_vertical_2lines 0x0
int dimen design_snackbar_text_size 0x0
int dimen design_tab_max_width 0x0
int dimen design_tab_scrollable_min_width 0x0
int dimen design_tab_text_size 0x0
int dimen design_tab_text_size_2line 0x0
int dimen design_textinput_caption_translate_y 0x0
int dimen disabled_alpha_material_dark 0x0
int dimen disabled_alpha_material_light 0x0
int dimen fastscroll_default_thickness 0x0
int dimen fastscroll_margin 0x0
int dimen fastscroll_minimum_range 0x0
int dimen highlight_alpha_material_colored 0x0
int dimen highlight_alpha_material_dark 0x0
int dimen highlight_alpha_material_light 0x0
int dimen hint_alpha_material_dark 0x0
int dimen hint_alpha_material_light 0x0
int dimen hint_pressed_alpha_material_dark 0x0
int dimen hint_pressed_alpha_material_light 0x0
int dimen item_touch_helper_max_drag_scroll_per_frame 0x0
int dimen item_touch_helper_swipe_escape_max_velocity 0x0
int dimen item_touch_helper_swipe_escape_velocity 0x0
int dimen m3_alert_dialog_action_bottom_padding 0x0
int dimen m3_alert_dialog_action_top_padding 0x0
int dimen m3_alert_dialog_corner_size 0x0
int dimen m3_alert_dialog_elevation 0x0
int dimen m3_alert_dialog_icon_margin 0x0
int dimen m3_alert_dialog_icon_size 0x0
int dimen m3_alert_dialog_title_bottom_margin 0x0
int dimen m3_appbar_expanded_title_margin_bottom 0x0
int dimen m3_appbar_expanded_title_margin_horizontal 0x0
int dimen m3_appbar_scrim_height_trigger 0x0
int dimen m3_appbar_scrim_height_trigger_large 0x0
int dimen m3_appbar_scrim_height_trigger_medium 0x0
int dimen m3_appbar_size_compact 0x0
int dimen m3_appbar_size_large 0x0
int dimen m3_appbar_size_medium 0x0
int dimen m3_back_progress_bottom_container_max_scale_x_distance 0x0
int dimen m3_back_progress_bottom_container_max_scale_y_distance 0x0
int dimen m3_back_progress_main_container_max_translation_y 0x0
int dimen m3_back_progress_main_container_min_edge_gap 0x0
int dimen m3_back_progress_side_container_max_scale_x_distance_grow 0x0
int dimen m3_back_progress_side_container_max_scale_x_distance_shrink 0x0
int dimen m3_back_progress_side_container_max_scale_y_distance 0x0
int dimen m3_badge_horizontal_offset 0x0
int dimen m3_badge_offset 0x0
int dimen m3_badge_size 0x0
int dimen m3_badge_vertical_offset 0x0
int dimen m3_badge_with_text_horizontal_offset 0x0
int dimen m3_badge_with_text_offset 0x0
int dimen m3_badge_with_text_size 0x0
int dimen m3_badge_with_text_vertical_offset 0x0
int dimen m3_badge_with_text_vertical_padding 0x0
int dimen m3_bottom_nav_item_active_indicator_height 0x0
int dimen m3_bottom_nav_item_active_indicator_margin_horizontal 0x0
int dimen m3_bottom_nav_item_active_indicator_width 0x0
int dimen m3_bottom_nav_item_padding_bottom 0x0
int dimen m3_bottom_nav_item_padding_top 0x0
int dimen m3_bottom_nav_min_height 0x0
int dimen m3_bottom_sheet_drag_handle_bottom_padding 0x0
int dimen m3_bottom_sheet_elevation 0x0
int dimen m3_bottom_sheet_modal_elevation 0x0
int dimen m3_bottomappbar_fab_cradle_margin 0x0
int dimen m3_bottomappbar_fab_cradle_rounded_corner_radius 0x0
int dimen m3_bottomappbar_fab_cradle_vertical_offset 0x0
int dimen m3_bottomappbar_fab_end_margin 0x0
int dimen m3_bottomappbar_height 0x0
int dimen m3_bottomappbar_horizontal_padding 0x0
int dimen m3_btn_dialog_btn_min_width 0x0
int dimen m3_btn_dialog_btn_spacing 0x0
int dimen m3_btn_disabled_elevation 0x0
int dimen m3_btn_disabled_translation_z 0x0
int dimen m3_btn_elevated_btn_elevation 0x0
int dimen m3_btn_elevation 0x0
int dimen m3_btn_icon_btn_padding_left 0x0
int dimen m3_btn_icon_btn_padding_right 0x0
int dimen m3_btn_icon_only_default_padding 0x0
int dimen m3_btn_icon_only_default_size 0x0
int dimen m3_btn_icon_only_icon_padding 0x0
int dimen m3_btn_icon_only_min_width 0x0
int dimen m3_btn_inset 0x0
int dimen m3_btn_max_width 0x0
int dimen m3_btn_padding_bottom 0x0
int dimen m3_btn_padding_left 0x0
int dimen m3_btn_padding_right 0x0
int dimen m3_btn_padding_top 0x0
int dimen m3_btn_stroke_size 0x0
int dimen m3_btn_text_btn_icon_padding_left 0x0
int dimen m3_btn_text_btn_icon_padding_right 0x0
int dimen m3_btn_text_btn_padding_left 0x0
int dimen m3_btn_text_btn_padding_right 0x0
int dimen m3_btn_translation_z_base 0x0
int dimen m3_btn_translation_z_hovered 0x0
int dimen m3_card_disabled_z 0x0
int dimen m3_card_dragged_z 0x0
int dimen m3_card_elevated_disabled_z 0x0
int dimen m3_card_elevated_dragged_z 0x0
int dimen m3_card_elevated_elevation 0x0
int dimen m3_card_elevated_hovered_z 0x0
int dimen m3_card_elevation 0x0
int dimen m3_card_hovered_z 0x0
int dimen m3_card_stroke_width 0x0
int dimen m3_carousel_debug_keyline_width 0x0
int dimen m3_carousel_extra_small_item_size 0x0
int dimen m3_carousel_gone_size 0x0
int dimen m3_carousel_small_item_default_corner_size 0x0
int dimen m3_carousel_small_item_size_max 0x0
int dimen m3_carousel_small_item_size_min 0x0
int dimen m3_chip_checked_hovered_translation_z 0x0
int dimen m3_chip_corner_size 0x0
int dimen m3_chip_disabled_translation_z 0x0
int dimen m3_chip_dragged_translation_z 0x0
int dimen m3_chip_elevated_elevation 0x0
int dimen m3_chip_hovered_translation_z 0x0
int dimen m3_chip_icon_size 0x0
int dimen m3_comp_assist_chip_container_height 0x0
int dimen m3_comp_assist_chip_elevated_container_elevation 0x0
int dimen m3_comp_assist_chip_flat_container_elevation 0x0
int dimen m3_comp_assist_chip_flat_outline_width 0x0
int dimen m3_comp_assist_chip_with_icon_icon_size 0x0
int dimen m3_comp_badge_large_size 0x0
int dimen m3_comp_badge_size 0x0
int dimen m3_comp_bottom_app_bar_container_elevation 0x0
int dimen m3_comp_bottom_app_bar_container_height 0x0
int dimen m3_comp_checkbox_selected_disabled_container_opacity 0x0
int dimen m3_comp_circular_progress_indicator_active_indicator_width 0x0
int dimen m3_comp_date_picker_modal_date_today_container_outline_width 0x0
int dimen m3_comp_date_picker_modal_header_container_height 0x0
int dimen m3_comp_date_picker_modal_range_selection_header_container_height 0x0
int dimen m3_comp_divider_thickness 0x0
int dimen m3_comp_elevated_button_container_elevation 0x0
int dimen m3_comp_elevated_button_disabled_container_elevation 0x0
int dimen m3_comp_elevated_card_container_elevation 0x0
int dimen m3_comp_elevated_card_icon_size 0x0
int dimen m3_comp_extended_fab_primary_container_elevation 0x0
int dimen m3_comp_extended_fab_primary_container_height 0x0
int dimen m3_comp_extended_fab_primary_focus_container_elevation 0x0
int dimen m3_comp_extended_fab_primary_focus_state_layer_opacity 0x0
int dimen m3_comp_extended_fab_primary_hover_container_elevation 0x0
int dimen m3_comp_extended_fab_primary_hover_state_layer_opacity 0x0
int dimen m3_comp_extended_fab_primary_icon_size 0x0
int dimen m3_comp_extended_fab_primary_pressed_container_elevation 0x0
int dimen m3_comp_extended_fab_primary_pressed_state_layer_opacity 0x0
int dimen m3_comp_fab_primary_container_elevation 0x0
int dimen m3_comp_fab_primary_container_height 0x0
int dimen m3_comp_fab_primary_focus_state_layer_opacity 0x0
int dimen m3_comp_fab_primary_hover_container_elevation 0x0
int dimen m3_comp_fab_primary_hover_state_layer_opacity 0x0
int dimen m3_comp_fab_primary_icon_size 0x0
int dimen m3_comp_fab_primary_large_container_height 0x0
int dimen m3_comp_fab_primary_large_icon_size 0x0
int dimen m3_comp_fab_primary_pressed_container_elevation 0x0
int dimen m3_comp_fab_primary_pressed_state_layer_opacity 0x0
int dimen m3_comp_fab_primary_small_container_height 0x0
int dimen m3_comp_fab_primary_small_icon_size 0x0
int dimen m3_comp_filled_autocomplete_menu_container_elevation 0x0
int dimen m3_comp_filled_button_container_elevation 0x0
int dimen m3_comp_filled_button_with_icon_icon_size 0x0
int dimen m3_comp_filled_card_container_elevation 0x0
int dimen m3_comp_filled_card_dragged_state_layer_opacity 0x0
int dimen m3_comp_filled_card_focus_state_layer_opacity 0x0
int dimen m3_comp_filled_card_hover_state_layer_opacity 0x0
int dimen m3_comp_filled_card_icon_size 0x0
int dimen m3_comp_filled_card_pressed_state_layer_opacity 0x0
int dimen m3_comp_filled_text_field_disabled_active_indicator_opacity 0x0
int dimen m3_comp_filter_chip_container_height 0x0
int dimen m3_comp_filter_chip_elevated_container_elevation 0x0
int dimen m3_comp_filter_chip_flat_container_elevation 0x0
int dimen m3_comp_filter_chip_flat_unselected_outline_width 0x0
int dimen m3_comp_filter_chip_with_icon_icon_size 0x0
int dimen m3_comp_input_chip_container_elevation 0x0
int dimen m3_comp_input_chip_container_height 0x0
int dimen m3_comp_input_chip_unselected_outline_width 0x0
int dimen m3_comp_input_chip_with_avatar_avatar_size 0x0
int dimen m3_comp_input_chip_with_leading_icon_leading_icon_size 0x0
int dimen m3_comp_linear_progress_indicator_active_indicator_height 0x0
int dimen m3_comp_menu_container_elevation 0x0
int dimen m3_comp_navigation_bar_active_indicator_height 0x0
int dimen m3_comp_navigation_bar_active_indicator_width 0x0
int dimen m3_comp_navigation_bar_container_elevation 0x0
int dimen m3_comp_navigation_bar_container_height 0x0
int dimen m3_comp_navigation_bar_focus_state_layer_opacity 0x0
int dimen m3_comp_navigation_bar_hover_state_layer_opacity 0x0
int dimen m3_comp_navigation_bar_icon_size 0x0
int dimen m3_comp_navigation_bar_pressed_state_layer_opacity 0x0
int dimen m3_comp_navigation_drawer_container_width 0x0
int dimen m3_comp_navigation_drawer_focus_state_layer_opacity 0x0
int dimen m3_comp_navigation_drawer_hover_state_layer_opacity 0x0
int dimen m3_comp_navigation_drawer_icon_size 0x0
int dimen m3_comp_navigation_drawer_modal_container_elevation 0x0
int dimen m3_comp_navigation_drawer_pressed_state_layer_opacity 0x0
int dimen m3_comp_navigation_drawer_standard_container_elevation 0x0
int dimen m3_comp_navigation_rail_active_indicator_height 0x0
int dimen m3_comp_navigation_rail_active_indicator_width 0x0
int dimen m3_comp_navigation_rail_container_elevation 0x0
int dimen m3_comp_navigation_rail_container_width 0x0
int dimen m3_comp_navigation_rail_focus_state_layer_opacity 0x0
int dimen m3_comp_navigation_rail_hover_state_layer_opacity 0x0
int dimen m3_comp_navigation_rail_icon_size 0x0
int dimen m3_comp_navigation_rail_pressed_state_layer_opacity 0x0
int dimen m3_comp_outlined_autocomplete_menu_container_elevation 0x0
int dimen m3_comp_outlined_button_disabled_outline_opacity 0x0
int dimen m3_comp_outlined_button_outline_width 0x0
int dimen m3_comp_outlined_card_container_elevation 0x0
int dimen m3_comp_outlined_card_disabled_outline_opacity 0x0
int dimen m3_comp_outlined_card_icon_size 0x0
int dimen m3_comp_outlined_card_outline_width 0x0
int dimen m3_comp_outlined_icon_button_unselected_outline_width 0x0
int dimen m3_comp_outlined_text_field_disabled_input_text_opacity 0x0
int dimen m3_comp_outlined_text_field_disabled_label_text_opacity 0x0
int dimen m3_comp_outlined_text_field_disabled_supporting_text_opacity 0x0
int dimen m3_comp_outlined_text_field_focus_outline_width 0x0
int dimen m3_comp_outlined_text_field_outline_width 0x0
int dimen m3_comp_primary_navigation_tab_active_focus_state_layer_opacity 0x0
int dimen m3_comp_primary_navigation_tab_active_hover_state_layer_opacity 0x0
int dimen m3_comp_primary_navigation_tab_active_indicator_height 0x0
int dimen m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity 0x0
int dimen m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity 0x0
int dimen m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity 0x0
int dimen m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity 0x0
int dimen m3_comp_primary_navigation_tab_with_icon_icon_size 0x0
int dimen m3_comp_radio_button_disabled_selected_icon_opacity 0x0
int dimen m3_comp_radio_button_disabled_unselected_icon_opacity 0x0
int dimen m3_comp_radio_button_selected_focus_state_layer_opacity 0x0
int dimen m3_comp_radio_button_selected_hover_state_layer_opacity 0x0
int dimen m3_comp_radio_button_selected_pressed_state_layer_opacity 0x0
int dimen m3_comp_radio_button_unselected_focus_state_layer_opacity 0x0
int dimen m3_comp_radio_button_unselected_hover_state_layer_opacity 0x0
int dimen m3_comp_radio_button_unselected_pressed_state_layer_opacity 0x0
int dimen m3_comp_search_bar_avatar_size 0x0
int dimen m3_comp_search_bar_container_elevation 0x0
int dimen m3_comp_search_bar_container_height 0x0
int dimen m3_comp_search_bar_hover_state_layer_opacity 0x0
int dimen m3_comp_search_bar_pressed_state_layer_opacity 0x0
int dimen m3_comp_search_view_container_elevation 0x0
int dimen m3_comp_search_view_docked_header_container_height 0x0
int dimen m3_comp_search_view_full_screen_header_container_height 0x0
int dimen m3_comp_secondary_navigation_tab_active_indicator_height 0x0
int dimen m3_comp_secondary_navigation_tab_focus_state_layer_opacity 0x0
int dimen m3_comp_secondary_navigation_tab_hover_state_layer_opacity 0x0
int dimen m3_comp_secondary_navigation_tab_pressed_state_layer_opacity 0x0
int dimen m3_comp_sheet_bottom_docked_drag_handle_height 0x0
int dimen m3_comp_sheet_bottom_docked_drag_handle_width 0x0
int dimen m3_comp_sheet_bottom_docked_modal_container_elevation 0x0
int dimen m3_comp_sheet_bottom_docked_standard_container_elevation 0x0
int dimen m3_comp_sheet_side_docked_container_width 0x0
int dimen m3_comp_sheet_side_docked_modal_container_elevation 0x0
int dimen m3_comp_sheet_side_docked_standard_container_elevation 0x0
int dimen m3_comp_slider_disabled_active_track_opacity 0x0
int dimen m3_comp_slider_disabled_handle_opacity 0x0
int dimen m3_comp_slider_disabled_inactive_track_opacity 0x0
int dimen m3_comp_slider_inactive_track_height 0x0
int dimen m3_comp_snackbar_container_elevation 0x0
int dimen m3_comp_suggestion_chip_container_height 0x0
int dimen m3_comp_suggestion_chip_elevated_container_elevation 0x0
int dimen m3_comp_suggestion_chip_flat_container_elevation 0x0
int dimen m3_comp_suggestion_chip_flat_outline_width 0x0
int dimen m3_comp_suggestion_chip_with_leading_icon_leading_icon_size 0x0
int dimen m3_comp_switch_disabled_selected_handle_opacity 0x0
int dimen m3_comp_switch_disabled_selected_icon_opacity 0x0
int dimen m3_comp_switch_disabled_track_opacity 0x0
int dimen m3_comp_switch_disabled_unselected_handle_opacity 0x0
int dimen m3_comp_switch_disabled_unselected_icon_opacity 0x0
int dimen m3_comp_switch_selected_focus_state_layer_opacity 0x0
int dimen m3_comp_switch_selected_hover_state_layer_opacity 0x0
int dimen m3_comp_switch_selected_pressed_state_layer_opacity 0x0
int dimen m3_comp_switch_track_height 0x0
int dimen m3_comp_switch_track_width 0x0
int dimen m3_comp_switch_unselected_focus_state_layer_opacity 0x0
int dimen m3_comp_switch_unselected_hover_state_layer_opacity 0x0
int dimen m3_comp_switch_unselected_pressed_state_layer_opacity 0x0
int dimen m3_comp_text_button_focus_state_layer_opacity 0x0
int dimen m3_comp_text_button_hover_state_layer_opacity 0x0
int dimen m3_comp_text_button_pressed_state_layer_opacity 0x0
int dimen m3_comp_time_input_time_input_field_focus_outline_width 0x0
int dimen m3_comp_time_picker_container_elevation 0x0
int dimen m3_comp_time_picker_period_selector_focus_state_layer_opacity 0x0
int dimen m3_comp_time_picker_period_selector_hover_state_layer_opacity 0x0
int dimen m3_comp_time_picker_period_selector_outline_width 0x0
int dimen m3_comp_time_picker_period_selector_pressed_state_layer_opacity 0x0
int dimen m3_comp_time_picker_time_selector_focus_state_layer_opacity 0x0
int dimen m3_comp_time_picker_time_selector_hover_state_layer_opacity 0x0
int dimen m3_comp_time_picker_time_selector_pressed_state_layer_opacity 0x0
int dimen m3_comp_top_app_bar_large_container_height 0x0
int dimen m3_comp_top_app_bar_medium_container_height 0x0
int dimen m3_comp_top_app_bar_small_container_elevation 0x0
int dimen m3_comp_top_app_bar_small_container_height 0x0
int dimen m3_comp_top_app_bar_small_on_scroll_container_elevation 0x0
int dimen m3_datepicker_elevation 0x0
int dimen m3_divider_heavy_thickness 0x0
int dimen m3_extended_fab_bottom_padding 0x0
int dimen m3_extended_fab_end_padding 0x0
int dimen m3_extended_fab_icon_padding 0x0
int dimen m3_extended_fab_min_height 0x0
int dimen m3_extended_fab_start_padding 0x0
int dimen m3_extended_fab_top_padding 0x0
int dimen m3_fab_border_width 0x0
int dimen m3_fab_corner_size 0x0
int dimen m3_fab_translation_z_hovered_focused 0x0
int dimen m3_fab_translation_z_pressed 0x0
int dimen m3_large_fab_max_image_size 0x0
int dimen m3_large_fab_size 0x0
int dimen m3_large_text_vertical_offset_adjustment 0x0
int dimen m3_menu_elevation 0x0
int dimen m3_nav_badge_with_text_vertical_offset 0x0
int dimen m3_navigation_drawer_layout_corner_size 0x0
int dimen m3_navigation_item_active_indicator_label_padding 0x0
int dimen m3_navigation_item_horizontal_padding 0x0
int dimen m3_navigation_item_icon_padding 0x0
int dimen m3_navigation_item_shape_inset_bottom 0x0
int dimen m3_navigation_item_shape_inset_end 0x0
int dimen m3_navigation_item_shape_inset_start 0x0
int dimen m3_navigation_item_shape_inset_top 0x0
int dimen m3_navigation_item_vertical_padding 0x0
int dimen m3_navigation_menu_divider_horizontal_padding 0x0
int dimen m3_navigation_menu_headline_horizontal_padding 0x0
int dimen m3_navigation_rail_default_width 0x0
int dimen m3_navigation_rail_elevation 0x0
int dimen m3_navigation_rail_icon_size 0x0
int dimen m3_navigation_rail_item_active_indicator_height 0x0
int dimen m3_navigation_rail_item_active_indicator_margin_horizontal 0x0
int dimen m3_navigation_rail_item_active_indicator_width 0x0
int dimen m3_navigation_rail_item_min_height 0x0
int dimen m3_navigation_rail_item_padding_bottom 0x0
int dimen m3_navigation_rail_item_padding_bottom_with_large_font 0x0
int dimen m3_navigation_rail_item_padding_top 0x0
int dimen m3_navigation_rail_item_padding_top_with_large_font 0x0
int dimen m3_ripple_default_alpha 0x0
int dimen m3_ripple_focused_alpha 0x0
int dimen m3_ripple_hovered_alpha 0x0
int dimen m3_ripple_pressed_alpha 0x0
int dimen m3_ripple_selectable_pressed_alpha 0x0
int dimen m3_searchbar_elevation 0x0
int dimen m3_searchbar_height 0x0
int dimen m3_searchbar_margin_horizontal 0x0
int dimen m3_searchbar_margin_vertical 0x0
int dimen m3_searchbar_outlined_stroke_width 0x0
int dimen m3_searchbar_padding_start 0x0
int dimen m3_searchbar_text_margin_start_no_navigation_icon 0x0
int dimen m3_searchbar_text_size 0x0
int dimen m3_searchview_divider_size 0x0
int dimen m3_searchview_elevation 0x0
int dimen m3_searchview_height 0x0
int dimen m3_side_sheet_margin_detached 0x0
int dimen m3_side_sheet_modal_elevation 0x0
int dimen m3_side_sheet_standard_elevation 0x0
int dimen m3_side_sheet_width 0x0
int dimen m3_simple_item_color_hovered_alpha 0x0
int dimen m3_simple_item_color_selected_alpha 0x0
int dimen m3_slider_inactive_track_height 0x0
int dimen m3_slider_thumb_elevation 0x0
int dimen m3_small_fab_max_image_size 0x0
int dimen m3_small_fab_size 0x0
int dimen m3_snackbar_action_text_color_alpha 0x0
int dimen m3_snackbar_margin 0x0
int dimen m3_sys_elevation_level0 0x0
int dimen m3_sys_elevation_level1 0x0
int dimen m3_sys_elevation_level2 0x0
int dimen m3_sys_elevation_level3 0x0
int dimen m3_sys_elevation_level4 0x0
int dimen m3_sys_elevation_level5 0x0
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x1 0x0
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x2 0x0
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y1 0x0
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y2 0x0
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x1 0x0
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x2 0x0
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y1 0x0
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y2 0x0
int dimen m3_sys_motion_easing_legacy_accelerate_control_x1 0x0
int dimen m3_sys_motion_easing_legacy_accelerate_control_x2 0x0
int dimen m3_sys_motion_easing_legacy_accelerate_control_y1 0x0
int dimen m3_sys_motion_easing_legacy_accelerate_control_y2 0x0
int dimen m3_sys_motion_easing_legacy_control_x1 0x0
int dimen m3_sys_motion_easing_legacy_control_x2 0x0
int dimen m3_sys_motion_easing_legacy_control_y1 0x0
int dimen m3_sys_motion_easing_legacy_control_y2 0x0
int dimen m3_sys_motion_easing_legacy_decelerate_control_x1 0x0
int dimen m3_sys_motion_easing_legacy_decelerate_control_x2 0x0
int dimen m3_sys_motion_easing_legacy_decelerate_control_y1 0x0
int dimen m3_sys_motion_easing_legacy_decelerate_control_y2 0x0
int dimen m3_sys_motion_easing_linear_control_x1 0x0
int dimen m3_sys_motion_easing_linear_control_x2 0x0
int dimen m3_sys_motion_easing_linear_control_y1 0x0
int dimen m3_sys_motion_easing_linear_control_y2 0x0
int dimen m3_sys_motion_easing_standard_accelerate_control_x1 0x0
int dimen m3_sys_motion_easing_standard_accelerate_control_x2 0x0
int dimen m3_sys_motion_easing_standard_accelerate_control_y1 0x0
int dimen m3_sys_motion_easing_standard_accelerate_control_y2 0x0
int dimen m3_sys_motion_easing_standard_control_x1 0x0
int dimen m3_sys_motion_easing_standard_control_x2 0x0
int dimen m3_sys_motion_easing_standard_control_y1 0x0
int dimen m3_sys_motion_easing_standard_control_y2 0x0
int dimen m3_sys_motion_easing_standard_decelerate_control_x1 0x0
int dimen m3_sys_motion_easing_standard_decelerate_control_x2 0x0
int dimen m3_sys_motion_easing_standard_decelerate_control_y1 0x0
int dimen m3_sys_motion_easing_standard_decelerate_control_y2 0x0
int dimen m3_sys_state_dragged_state_layer_opacity 0x0
int dimen m3_sys_state_focus_state_layer_opacity 0x0
int dimen m3_sys_state_hover_state_layer_opacity 0x0
int dimen m3_sys_state_pressed_state_layer_opacity 0x0
int dimen m3_timepicker_display_stroke_width 0x0
int dimen m3_timepicker_window_elevation 0x0
int dimen m3_toolbar_text_size_title 0x0
int dimen material_bottom_sheet_max_width 0x0
int dimen material_clock_display_height 0x0
int dimen material_clock_display_padding 0x0
int dimen material_clock_display_width 0x0
int dimen material_clock_face_margin_bottom 0x0
int dimen material_clock_face_margin_top 0x0
int dimen material_clock_hand_center_dot_radius 0x0
int dimen material_clock_hand_padding 0x0
int dimen material_clock_hand_stroke_width 0x0
int dimen material_clock_number_text_size 0x0
int dimen material_clock_period_toggle_height 0x0
int dimen material_clock_period_toggle_horizontal_gap 0x0
int dimen material_clock_period_toggle_vertical_gap 0x0
int dimen material_clock_period_toggle_width 0x0
int dimen material_clock_size 0x0
int dimen material_cursor_inset 0x0
int dimen material_cursor_width 0x0
int dimen material_divider_thickness 0x0
int dimen material_emphasis_disabled 0x0
int dimen material_emphasis_disabled_background 0x0
int dimen material_emphasis_high_type 0x0
int dimen material_emphasis_medium 0x0
int dimen material_filled_edittext_font_1_3_padding_bottom 0x0
int dimen material_filled_edittext_font_1_3_padding_top 0x0
int dimen material_filled_edittext_font_2_0_padding_bottom 0x0
int dimen material_filled_edittext_font_2_0_padding_top 0x0
int dimen material_font_1_3_box_collapsed_padding_top 0x0
int dimen material_font_2_0_box_collapsed_padding_top 0x0
int dimen material_helper_text_default_padding_top 0x0
int dimen material_helper_text_font_1_3_padding_horizontal 0x0
int dimen material_helper_text_font_1_3_padding_top 0x0
int dimen material_input_text_to_prefix_suffix_padding 0x0
int dimen material_textinput_default_width 0x0
int dimen material_textinput_max_width 0x0
int dimen material_textinput_min_width 0x0
int dimen material_time_picker_minimum_screen_height 0x0
int dimen material_time_picker_minimum_screen_width 0x0
int dimen mtrl_alert_dialog_background_inset_bottom 0x0
int dimen mtrl_alert_dialog_background_inset_end 0x0
int dimen mtrl_alert_dialog_background_inset_start 0x0
int dimen mtrl_alert_dialog_background_inset_top 0x0
int dimen mtrl_alert_dialog_picker_background_inset 0x0
int dimen mtrl_badge_horizontal_edge_offset 0x0
int dimen mtrl_badge_long_text_horizontal_padding 0x0
int dimen mtrl_badge_size 0x0
int dimen mtrl_badge_text_horizontal_edge_offset 0x0
int dimen mtrl_badge_text_size 0x0
int dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset 0x0
int dimen mtrl_badge_toolbar_action_menu_item_vertical_offset 0x0
int dimen mtrl_badge_with_text_size 0x0
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x0
int dimen mtrl_bottomappbar_fab_bottom_margin 0x0
int dimen mtrl_bottomappbar_fab_cradle_margin 0x0
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x0
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x0
int dimen mtrl_bottomappbar_height 0x0
int dimen mtrl_btn_corner_radius 0x0
int dimen mtrl_btn_dialog_btn_min_width 0x0
int dimen mtrl_btn_disabled_elevation 0x0
int dimen mtrl_btn_disabled_z 0x0
int dimen mtrl_btn_elevation 0x0
int dimen mtrl_btn_focused_z 0x0
int dimen mtrl_btn_hovered_z 0x0
int dimen mtrl_btn_icon_btn_padding_left 0x0
int dimen mtrl_btn_icon_padding 0x0
int dimen mtrl_btn_inset 0x0
int dimen mtrl_btn_letter_spacing 0x0
int dimen mtrl_btn_max_width 0x0
int dimen mtrl_btn_padding_bottom 0x0
int dimen mtrl_btn_padding_left 0x0
int dimen mtrl_btn_padding_right 0x0
int dimen mtrl_btn_padding_top 0x0
int dimen mtrl_btn_pressed_z 0x0
int dimen mtrl_btn_snackbar_margin_horizontal 0x0
int dimen mtrl_btn_stroke_size 0x0
int dimen mtrl_btn_text_btn_icon_padding 0x0
int dimen mtrl_btn_text_btn_padding_left 0x0
int dimen mtrl_btn_text_btn_padding_right 0x0
int dimen mtrl_btn_text_size 0x0
int dimen mtrl_btn_z 0x0
int dimen mtrl_calendar_action_confirm_button_min_width 0x0
int dimen mtrl_calendar_action_height 0x0
int dimen mtrl_calendar_action_padding 0x0
int dimen mtrl_calendar_bottom_padding 0x0
int dimen mtrl_calendar_content_padding 0x0
int dimen mtrl_calendar_day_corner 0x0
int dimen mtrl_calendar_day_height 0x0
int dimen mtrl_calendar_day_horizontal_padding 0x0
int dimen mtrl_calendar_day_today_stroke 0x0
int dimen mtrl_calendar_day_vertical_padding 0x0
int dimen mtrl_calendar_day_width 0x0
int dimen mtrl_calendar_days_of_week_height 0x0
int dimen mtrl_calendar_dialog_background_inset 0x0
int dimen mtrl_calendar_header_content_padding 0x0
int dimen mtrl_calendar_header_content_padding_fullscreen 0x0
int dimen mtrl_calendar_header_divider_thickness 0x0
int dimen mtrl_calendar_header_height 0x0
int dimen mtrl_calendar_header_height_fullscreen 0x0
int dimen mtrl_calendar_header_selection_line_height 0x0
int dimen mtrl_calendar_header_text_padding 0x0
int dimen mtrl_calendar_header_toggle_margin_bottom 0x0
int dimen mtrl_calendar_header_toggle_margin_top 0x0
int dimen mtrl_calendar_landscape_header_width 0x0
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x0
int dimen mtrl_calendar_month_horizontal_padding 0x0
int dimen mtrl_calendar_month_vertical_padding 0x0
int dimen mtrl_calendar_navigation_bottom_padding 0x0
int dimen mtrl_calendar_navigation_height 0x0
int dimen mtrl_calendar_navigation_top_padding 0x0
int dimen mtrl_calendar_pre_l_text_clip_padding 0x0
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x0
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x0
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x0
int dimen mtrl_calendar_selection_text_baseline_to_top 0x0
int dimen mtrl_calendar_text_input_padding_top 0x0
int dimen mtrl_calendar_title_baseline_to_top 0x0
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x0
int dimen mtrl_calendar_year_corner 0x0
int dimen mtrl_calendar_year_height 0x0
int dimen mtrl_calendar_year_horizontal_padding 0x0
int dimen mtrl_calendar_year_vertical_padding 0x0
int dimen mtrl_calendar_year_width 0x0
int dimen mtrl_card_checked_icon_margin 0x0
int dimen mtrl_card_checked_icon_size 0x0
int dimen mtrl_card_corner_radius 0x0
int dimen mtrl_card_dragged_z 0x0
int dimen mtrl_card_elevation 0x0
int dimen mtrl_card_spacing 0x0
int dimen mtrl_chip_pressed_translation_z 0x0
int dimen mtrl_chip_text_size 0x0
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x0
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x0
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x0
int dimen mtrl_extended_fab_bottom_padding 0x0
int dimen mtrl_extended_fab_disabled_elevation 0x0
int dimen mtrl_extended_fab_disabled_translation_z 0x0
int dimen mtrl_extended_fab_elevation 0x0
int dimen mtrl_extended_fab_end_padding 0x0
int dimen mtrl_extended_fab_end_padding_icon 0x0
int dimen mtrl_extended_fab_icon_size 0x0
int dimen mtrl_extended_fab_icon_text_spacing 0x0
int dimen mtrl_extended_fab_min_height 0x0
int dimen mtrl_extended_fab_min_width 0x0
int dimen mtrl_extended_fab_start_padding 0x0
int dimen mtrl_extended_fab_start_padding_icon 0x0
int dimen mtrl_extended_fab_top_padding 0x0
int dimen mtrl_extended_fab_translation_z_base 0x0
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x0
int dimen mtrl_extended_fab_translation_z_pressed 0x0
int dimen mtrl_fab_elevation 0x0
int dimen mtrl_fab_min_touch_target 0x0
int dimen mtrl_fab_translation_z_hovered_focused 0x0
int dimen mtrl_fab_translation_z_pressed 0x0
int dimen mtrl_high_ripple_default_alpha 0x0
int dimen mtrl_high_ripple_focused_alpha 0x0
int dimen mtrl_high_ripple_hovered_alpha 0x0
int dimen mtrl_high_ripple_pressed_alpha 0x0
int dimen mtrl_low_ripple_default_alpha 0x0
int dimen mtrl_low_ripple_focused_alpha 0x0
int dimen mtrl_low_ripple_hovered_alpha 0x0
int dimen mtrl_low_ripple_pressed_alpha 0x0
int dimen mtrl_min_touch_target_size 0x0
int dimen mtrl_navigation_bar_item_default_icon_size 0x0
int dimen mtrl_navigation_bar_item_default_margin 0x0
int dimen mtrl_navigation_elevation 0x0
int dimen mtrl_navigation_item_horizontal_padding 0x0
int dimen mtrl_navigation_item_icon_padding 0x0
int dimen mtrl_navigation_item_icon_size 0x0
int dimen mtrl_navigation_item_shape_horizontal_margin 0x0
int dimen mtrl_navigation_item_shape_vertical_margin 0x0
int dimen mtrl_navigation_rail_active_text_size 0x0
int dimen mtrl_navigation_rail_compact_width 0x0
int dimen mtrl_navigation_rail_default_width 0x0
int dimen mtrl_navigation_rail_elevation 0x0
int dimen mtrl_navigation_rail_icon_margin 0x0
int dimen mtrl_navigation_rail_icon_size 0x0
int dimen mtrl_navigation_rail_margin 0x0
int dimen mtrl_navigation_rail_text_bottom_margin 0x0
int dimen mtrl_navigation_rail_text_size 0x0
int dimen mtrl_progress_circular_inset 0x0
int dimen mtrl_progress_circular_inset_extra_small 0x0
int dimen mtrl_progress_circular_inset_medium 0x0
int dimen mtrl_progress_circular_inset_small 0x0
int dimen mtrl_progress_circular_radius 0x0
int dimen mtrl_progress_circular_size 0x0
int dimen mtrl_progress_circular_size_extra_small 0x0
int dimen mtrl_progress_circular_size_medium 0x0
int dimen mtrl_progress_circular_size_small 0x0
int dimen mtrl_progress_circular_track_thickness_extra_small 0x0
int dimen mtrl_progress_circular_track_thickness_medium 0x0
int dimen mtrl_progress_circular_track_thickness_small 0x0
int dimen mtrl_progress_indicator_full_rounded_corner_radius 0x0
int dimen mtrl_progress_track_thickness 0x0
int dimen mtrl_shape_corner_size_large_component 0x0
int dimen mtrl_shape_corner_size_medium_component 0x0
int dimen mtrl_shape_corner_size_small_component 0x0
int dimen mtrl_slider_halo_radius 0x0
int dimen mtrl_slider_label_padding 0x0
int dimen mtrl_slider_label_radius 0x0
int dimen mtrl_slider_label_square_side 0x0
int dimen mtrl_slider_thumb_elevation 0x0
int dimen mtrl_slider_thumb_radius 0x0
int dimen mtrl_slider_tick_radius 0x0
int dimen mtrl_slider_track_height 0x0
int dimen mtrl_slider_track_side_padding 0x0
int dimen mtrl_slider_widget_height 0x0
int dimen mtrl_snackbar_action_text_color_alpha 0x0
int dimen mtrl_snackbar_background_corner_radius 0x0
int dimen mtrl_snackbar_background_overlay_color_alpha 0x0
int dimen mtrl_snackbar_margin 0x0
int dimen mtrl_snackbar_message_margin_horizontal 0x0
int dimen mtrl_snackbar_padding_horizontal 0x0
int dimen mtrl_switch_text_padding 0x0
int dimen mtrl_switch_thumb_elevation 0x0
int dimen mtrl_switch_thumb_icon_size 0x0
int dimen mtrl_switch_thumb_size 0x0
int dimen mtrl_switch_track_height 0x0
int dimen mtrl_switch_track_width 0x0
int dimen mtrl_textinput_box_corner_radius_medium 0x0
int dimen mtrl_textinput_box_corner_radius_small 0x0
int dimen mtrl_textinput_box_label_cutout_padding 0x0
int dimen mtrl_textinput_box_stroke_width_default 0x0
int dimen mtrl_textinput_box_stroke_width_focused 0x0
int dimen mtrl_textinput_counter_margin_start 0x0
int dimen mtrl_textinput_end_icon_margin_start 0x0
int dimen mtrl_textinput_outline_box_expanded_padding 0x0
int dimen mtrl_textinput_start_icon_margin_end 0x0
int dimen mtrl_toolbar_default_height 0x0
int dimen mtrl_tooltip_arrowSize 0x0
int dimen mtrl_tooltip_cornerSize 0x0
int dimen mtrl_tooltip_minHeight 0x0
int dimen mtrl_tooltip_minWidth 0x0
int dimen mtrl_tooltip_padding 0x0
int dimen mtrl_transition_shared_axis_slide_distance 0x0
int dimen notification_action_icon_size 0x0
int dimen notification_action_text_size 0x0
int dimen notification_big_circle_margin 0x0
int dimen notification_content_margin_start 0x0
int dimen notification_large_icon_height 0x0
int dimen notification_large_icon_width 0x0
int dimen notification_main_column_padding_top 0x0
int dimen notification_media_narrow_margin 0x0
int dimen notification_right_icon_size 0x0
int dimen notification_right_side_padding_top 0x0
int dimen notification_small_icon_background_padding 0x0
int dimen notification_small_icon_size_as_large 0x0
int dimen notification_subtext_size 0x0
int dimen notification_top_pad 0x0
int dimen notification_top_pad_large_text 0x0
int dimen tooltip_corner_radius 0x0
int dimen tooltip_horizontal_padding 0x0
int dimen tooltip_margin 0x0
int dimen tooltip_precise_anchor_extra_offset 0x0
int dimen tooltip_precise_anchor_threshold 0x0
int dimen tooltip_vertical_padding 0x0
int dimen tooltip_y_offset_non_touch 0x0
int dimen tooltip_y_offset_touch 0x0
int drawable abc_ab_share_pack_mtrl_alpha 0x0
int drawable abc_action_bar_item_background_material 0x0
int drawable abc_btn_borderless_material 0x0
int drawable abc_btn_check_material 0x0
int drawable abc_btn_check_material_anim 0x0
int drawable abc_btn_check_to_on_mtrl_000 0x0
int drawable abc_btn_check_to_on_mtrl_015 0x0
int drawable abc_btn_colored_material 0x0
int drawable abc_btn_default_mtrl_shape 0x0
int drawable abc_btn_radio_material 0x0
int drawable abc_btn_radio_material_anim 0x0
int drawable abc_btn_radio_to_on_mtrl_000 0x0
int drawable abc_btn_radio_to_on_mtrl_015 0x0
int drawable abc_btn_switch_to_on_mtrl_00001 0x0
int drawable abc_btn_switch_to_on_mtrl_00012 0x0
int drawable abc_cab_background_internal_bg 0x0
int drawable abc_cab_background_top_material 0x0
int drawable abc_cab_background_top_mtrl_alpha 0x0
int drawable abc_control_background_material 0x0
int drawable abc_dialog_material_background 0x0
int drawable abc_edit_text_material 0x0
int drawable abc_ic_ab_back_material 0x0
int drawable abc_ic_arrow_drop_right_black_24dp 0x0
int drawable abc_ic_clear_material 0x0
int drawable abc_ic_commit_search_api_mtrl_alpha 0x0
int drawable abc_ic_go_search_api_material 0x0
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x0
int drawable abc_ic_menu_cut_mtrl_alpha 0x0
int drawable abc_ic_menu_overflow_material 0x0
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x0
int drawable abc_ic_menu_selectall_mtrl_alpha 0x0
int drawable abc_ic_menu_share_mtrl_alpha 0x0
int drawable abc_ic_search_api_material 0x0
int drawable abc_ic_star_black_16dp 0x0
int drawable abc_ic_star_black_36dp 0x0
int drawable abc_ic_star_black_48dp 0x0
int drawable abc_ic_star_half_black_16dp 0x0
int drawable abc_ic_star_half_black_36dp 0x0
int drawable abc_ic_star_half_black_48dp 0x0
int drawable abc_ic_voice_search_api_material 0x0
int drawable abc_item_background_holo_dark 0x0
int drawable abc_item_background_holo_light 0x0
int drawable abc_list_divider_material 0x0
int drawable abc_list_divider_mtrl_alpha 0x0
int drawable abc_list_focused_holo 0x0
int drawable abc_list_longpressed_holo 0x0
int drawable abc_list_pressed_holo_dark 0x0
int drawable abc_list_pressed_holo_light 0x0
int drawable abc_list_selector_background_transition_holo_dark 0x0
int drawable abc_list_selector_background_transition_holo_light 0x0
int drawable abc_list_selector_disabled_holo_dark 0x0
int drawable abc_list_selector_disabled_holo_light 0x0
int drawable abc_list_selector_holo_dark 0x0
int drawable abc_list_selector_holo_light 0x0
int drawable abc_menu_hardkey_panel_mtrl_mult 0x0
int drawable abc_popup_background_mtrl_mult 0x0
int drawable abc_ratingbar_indicator_material 0x0
int drawable abc_ratingbar_material 0x0
int drawable abc_ratingbar_small_material 0x0
int drawable abc_scrubber_control_off_mtrl_alpha 0x0
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x0
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x0
int drawable abc_scrubber_primary_mtrl_alpha 0x0
int drawable abc_scrubber_track_mtrl_alpha 0x0
int drawable abc_seekbar_thumb_material 0x0
int drawable abc_seekbar_tick_mark_material 0x0
int drawable abc_seekbar_track_material 0x0
int drawable abc_spinner_mtrl_am_alpha 0x0
int drawable abc_spinner_textfield_background_material 0x0
int drawable abc_star_black_48dp 0x0
int drawable abc_star_half_black_48dp 0x0
int drawable abc_switch_thumb_material 0x0
int drawable abc_switch_track_mtrl_alpha 0x0
int drawable abc_tab_indicator_material 0x0
int drawable abc_tab_indicator_mtrl_alpha 0x0
int drawable abc_text_cursor_material 0x0
int drawable abc_text_select_handle_left_mtrl 0x0
int drawable abc_text_select_handle_left_mtrl_dark 0x0
int drawable abc_text_select_handle_left_mtrl_light 0x0
int drawable abc_text_select_handle_middle_mtrl 0x0
int drawable abc_text_select_handle_middle_mtrl_dark 0x0
int drawable abc_text_select_handle_middle_mtrl_light 0x0
int drawable abc_text_select_handle_right_mtrl 0x0
int drawable abc_text_select_handle_right_mtrl_dark 0x0
int drawable abc_text_select_handle_right_mtrl_light 0x0
int drawable abc_textfield_activated_mtrl_alpha 0x0
int drawable abc_textfield_default_mtrl_alpha 0x0
int drawable abc_textfield_search_activated_mtrl_alpha 0x0
int drawable abc_textfield_search_default_mtrl_alpha 0x0
int drawable abc_textfield_search_material 0x0
int drawable abc_vector_test 0x0
int drawable avd_hide_password 0x0
int drawable avd_show_password 0x0
int drawable btn_checkbox_checked_mtrl 0x0
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x0
int drawable btn_checkbox_unchecked_mtrl 0x0
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x0
int drawable btn_radio_off_mtrl 0x0
int drawable btn_radio_off_to_on_mtrl_animation 0x0
int drawable btn_radio_on_mtrl 0x0
int drawable btn_radio_on_to_off_mtrl_animation 0x0
int drawable design_fab_background 0x0
int drawable design_ic_visibility 0x0
int drawable design_ic_visibility_off 0x0
int drawable design_password_eye 0x0
int drawable design_snackbar_background 0x0
int drawable ic_arrow_back_black_24 0x0
int drawable ic_call_answer 0x0
int drawable ic_call_answer_low 0x0
int drawable ic_call_answer_video 0x0
int drawable ic_call_answer_video_low 0x0
int drawable ic_call_decline 0x0
int drawable ic_call_decline_low 0x0
int drawable ic_clear_black_24 0x0
int drawable ic_clock_black_24dp 0x0
int drawable ic_keyboard_black_24dp 0x0
int drawable ic_m3_chip_check 0x0
int drawable ic_m3_chip_checked_circle 0x0
int drawable ic_m3_chip_close 0x0
int drawable ic_mtrl_checked_circle 0x0
int drawable ic_mtrl_chip_checked_black 0x0
int drawable ic_mtrl_chip_checked_circle 0x0
int drawable ic_mtrl_chip_close_circle 0x0
int drawable ic_search_black_24 0x0
int drawable m3_avd_hide_password 0x0
int drawable m3_avd_show_password 0x0
int drawable m3_bottom_sheet_drag_handle 0x0
int drawable m3_password_eye 0x0
int drawable m3_popupmenu_background_overlay 0x0
int drawable m3_radiobutton_ripple 0x0
int drawable m3_selection_control_ripple 0x0
int drawable m3_tabs_background 0x0
int drawable m3_tabs_line_indicator 0x0
int drawable m3_tabs_rounded_line_indicator 0x0
int drawable m3_tabs_transparent_background 0x0
int drawable material_cursor_drawable 0x0
int drawable material_ic_calendar_black_24dp 0x0
int drawable material_ic_clear_black_24dp 0x0
int drawable material_ic_edit_black_24dp 0x0
int drawable material_ic_keyboard_arrow_left_black_24dp 0x0
int drawable material_ic_keyboard_arrow_next_black_24dp 0x0
int drawable material_ic_keyboard_arrow_previous_black_24dp 0x0
int drawable material_ic_keyboard_arrow_right_black_24dp 0x0
int drawable material_ic_menu_arrow_down_black_24dp 0x0
int drawable material_ic_menu_arrow_up_black_24dp 0x0
int drawable mtrl_bottomsheet_drag_handle 0x0
int drawable mtrl_checkbox_button 0x0
int drawable mtrl_checkbox_button_checked_unchecked 0x0
int drawable mtrl_checkbox_button_icon 0x0
int drawable mtrl_checkbox_button_icon_checked_indeterminate 0x0
int drawable mtrl_checkbox_button_icon_checked_unchecked 0x0
int drawable mtrl_checkbox_button_icon_indeterminate_checked 0x0
int drawable mtrl_checkbox_button_icon_indeterminate_unchecked 0x0
int drawable mtrl_checkbox_button_icon_unchecked_checked 0x0
int drawable mtrl_checkbox_button_icon_unchecked_indeterminate 0x0
int drawable mtrl_checkbox_button_unchecked_checked 0x0
int drawable mtrl_dialog_background 0x0
int drawable mtrl_dropdown_arrow 0x0
int drawable mtrl_ic_arrow_drop_down 0x0
int drawable mtrl_ic_arrow_drop_up 0x0
int drawable mtrl_ic_cancel 0x0
int drawable mtrl_ic_check_mark 0x0
int drawable mtrl_ic_checkbox_checked 0x0
int drawable mtrl_ic_checkbox_unchecked 0x0
int drawable mtrl_ic_error 0x0
int drawable mtrl_ic_indeterminate 0x0
int drawable mtrl_navigation_bar_item_background 0x0
int drawable mtrl_popupmenu_background 0x0
int drawable mtrl_popupmenu_background_overlay 0x0
int drawable mtrl_switch_thumb 0x0
int drawable mtrl_switch_thumb_checked 0x0
int drawable mtrl_switch_thumb_checked_pressed 0x0
int drawable mtrl_switch_thumb_checked_unchecked 0x0
int drawable mtrl_switch_thumb_pressed 0x0
int drawable mtrl_switch_thumb_pressed_checked 0x0
int drawable mtrl_switch_thumb_pressed_unchecked 0x0
int drawable mtrl_switch_thumb_unchecked 0x0
int drawable mtrl_switch_thumb_unchecked_checked 0x0
int drawable mtrl_switch_thumb_unchecked_pressed 0x0
int drawable mtrl_switch_track 0x0
int drawable mtrl_switch_track_decoration 0x0
int drawable mtrl_tabs_default_indicator 0x0
int drawable navigation_empty_icon 0x0
int drawable notification_action_background 0x0
int drawable notification_bg 0x0
int drawable notification_bg_low 0x0
int drawable notification_bg_low_normal 0x0
int drawable notification_bg_low_pressed 0x0
int drawable notification_bg_normal 0x0
int drawable notification_bg_normal_pressed 0x0
int drawable notification_icon_background 0x0
int drawable notification_oversize_large_icon_bg 0x0
int drawable notification_template_icon_bg 0x0
int drawable notification_template_icon_low_bg 0x0
int drawable notification_tile_bg 0x0
int drawable notify_panel_notification_icon_bg 0x0
int drawable test_level_drawable 0x0
int drawable tooltip_frame_dark 0x0
int drawable tooltip_frame_light 0x0
int id BOTTOM_END 0x0
int id BOTTOM_START 0x0
int id NO_DEBUG 0x0
int id SHOW_ALL 0x0
int id SHOW_PATH 0x0
int id SHOW_PROGRESS 0x0
int id TOP_END 0x0
int id TOP_START 0x0
int id accelerate 0x0
int id accessibility_action_clickable_span 0x0
int id accessibility_custom_action_0 0x0
int id accessibility_custom_action_1 0x0
int id accessibility_custom_action_10 0x0
int id accessibility_custom_action_11 0x0
int id accessibility_custom_action_12 0x0
int id accessibility_custom_action_13 0x0
int id accessibility_custom_action_14 0x0
int id accessibility_custom_action_15 0x0
int id accessibility_custom_action_16 0x0
int id accessibility_custom_action_17 0x0
int id accessibility_custom_action_18 0x0
int id accessibility_custom_action_19 0x0
int id accessibility_custom_action_2 0x0
int id accessibility_custom_action_20 0x0
int id accessibility_custom_action_21 0x0
int id accessibility_custom_action_22 0x0
int id accessibility_custom_action_23 0x0
int id accessibility_custom_action_24 0x0
int id accessibility_custom_action_25 0x0
int id accessibility_custom_action_26 0x0
int id accessibility_custom_action_27 0x0
int id accessibility_custom_action_28 0x0
int id accessibility_custom_action_29 0x0
int id accessibility_custom_action_3 0x0
int id accessibility_custom_action_30 0x0
int id accessibility_custom_action_31 0x0
int id accessibility_custom_action_4 0x0
int id accessibility_custom_action_5 0x0
int id accessibility_custom_action_6 0x0
int id accessibility_custom_action_7 0x0
int id accessibility_custom_action_8 0x0
int id accessibility_custom_action_9 0x0
int id action_bar 0x0
int id action_bar_activity_content 0x0
int id action_bar_container 0x0
int id action_bar_root 0x0
int id action_bar_spinner 0x0
int id action_bar_subtitle 0x0
int id action_bar_title 0x0
int id action_container 0x0
int id action_context_bar 0x0
int id action_divider 0x0
int id action_image 0x0
int id action_menu_divider 0x0
int id action_menu_presenter 0x0
int id action_mode_bar 0x0
int id action_mode_bar_stub 0x0
int id action_mode_close_button 0x0
int id action_text 0x0
int id actions 0x0
int id activity_chooser_view_content 0x0
int id add 0x0
int id adjacent 0x0
int id alertTitle 0x0
int id aligned 0x0
int id always 0x0
int id alwaysAllow 0x0
int id alwaysDisallow 0x0
int id androidx_window_activity_scope 0x0
int id animateToEnd 0x0
int id animateToStart 0x0
int id arc 0x0
int id asConfigured 0x0
int id async 0x0
int id auto 0x0
int id autoComplete 0x0
int id autoCompleteToEnd 0x0
int id autoCompleteToStart 0x0
int id baseline 0x0
int id blocking 0x0
int id bottom 0x0
int id bottomToTop 0x0
int id bounce 0x0
int id buttonPanel 0x0
int id cancel_button 0x0
int id center 0x0
int id centerCrop 0x0
int id centerInside 0x0
int id chain 0x0
int id checkbox 0x0
int id checked 0x0
int id chronometer 0x0
int id circle_center 0x0
int id clear_text 0x0
int id clockwise 0x0
int id compress 0x0
int id confirm_button 0x0
int id container 0x0
int id content 0x0
int id contentPanel 0x0
int id contiguous 0x0
int id coordinator 0x0
int id cos 0x0
int id counterclockwise 0x0
int id cradle 0x0
int id custom 0x0
int id customPanel 0x0
int id cut 0x0
int id date_picker_actions 0x0
int id decelerate 0x0
int id decelerateAndComplete 0x0
int id decor_content_parent 0x0
int id default_activity_button 0x0
int id deltaRelative 0x0
int id design_bottom_sheet 0x0
int id design_menu_item_action_area 0x0
int id design_menu_item_action_area_stub 0x0
int id design_menu_item_text 0x0
int id design_navigation_view 0x0
int id dialog_button 0x0
int id disjoint 0x0
int id dragDown 0x0
int id dragEnd 0x0
int id dragLeft 0x0
int id dragRight 0x0
int id dragStart 0x0
int id dragUp 0x0
int id dropdown_menu 0x0
int id easeIn 0x0
int id easeInOut 0x0
int id easeOut 0x0
int id edge 0x0
int id edit_query 0x0
int id edit_text_id 0x0
int id elastic 0x0
int id embed 0x0
int id end 0x0
int id endToStart 0x0
int id expand_activities_button 0x0
int id expanded_menu 0x0
int id fade 0x0
int id fill 0x0
int id filled 0x0
int id fitCenter 0x0
int id fitEnd 0x0
int id fitStart 0x0
int id fitXY 0x0
int id fixed 0x0
int id flip 0x0
int id floating 0x0
int id forever 0x0
int id fragment_container_view_tag 0x0
int id fullscreen_header 0x0
int id ghost_view 0x0
int id ghost_view_holder 0x0
int id glide_custom_view_target_tag 0x0
int id gone 0x0
int id group_divider 0x0
int id header_title 0x0
int id hide_ime_id 0x0
int id home 0x0
int id honorRequest 0x0
int id icon 0x0
int id icon_group 0x0
int id ignore 0x0
int id ignoreRequest 0x0
int id image 0x0
int id indeterminate 0x0
int id info 0x0
int id invisible 0x0
int id inward 0x0
int id italic 0x0
int id item_touch_helper_previous_elevation 0x0
int id jumpToEnd 0x0
int id jumpToStart 0x0
int id labeled 0x0
int id layout 0x0
int id left 0x0
int id leftToRight 0x0
int id legacy 0x0
int id line1 0x0
int id line3 0x0
int id linear 0x0
int id listMode 0x0
int id list_item 0x0
int id locale 0x0
int id ltr 0x0
int id m3_side_sheet 0x0
int id marquee 0x0
int id masked 0x0
int id match_parent 0x0
int id material_clock_display 0x0
int id material_clock_display_and_toggle 0x0
int id material_clock_face 0x0
int id material_clock_hand 0x0
int id material_clock_level 0x0
int id material_clock_period_am_button 0x0
int id material_clock_period_pm_button 0x0
int id material_clock_period_toggle 0x0
int id material_hour_text_input 0x0
int id material_hour_tv 0x0
int id material_label 0x0
int id material_minute_text_input 0x0
int id material_minute_tv 0x0
int id material_textinput_timepicker 0x0
int id material_timepicker_cancel_button 0x0
int id material_timepicker_container 0x0
int id material_timepicker_mode_button 0x0
int id material_timepicker_ok_button 0x0
int id material_timepicker_view 0x0
int id material_value_index 0x0
int id matrix 0x0
int id maui_custom_view_target_running_callbacks_tag 0x0
int id message 0x0
int id middle 0x0
int id mini 0x0
int id month_grid 0x0
int id month_navigation_bar 0x0
int id month_navigation_fragment_toggle 0x0
int id month_navigation_next 0x0
int id month_navigation_previous 0x0
int id month_title 0x0
int id motion_base 0x0
int id mtrl_anchor_parent 0x0
int id mtrl_calendar_day_selector_frame 0x0
int id mtrl_calendar_days_of_week 0x0
int id mtrl_calendar_frame 0x0
int id mtrl_calendar_main_pane 0x0
int id mtrl_calendar_months 0x0
int id mtrl_calendar_selection_frame 0x0
int id mtrl_calendar_text_input_frame 0x0
int id mtrl_calendar_year_selector_frame 0x0
int id mtrl_card_checked_layer_id 0x0
int id mtrl_child_content_container 0x0
int id mtrl_internal_children_alpha_tag 0x0
int id mtrl_motion_snapshot_view 0x0
int id mtrl_picker_fullscreen 0x0
int id mtrl_picker_header 0x0
int id mtrl_picker_header_selection_text 0x0
int id mtrl_picker_header_title_and_selection 0x0
int id mtrl_picker_header_toggle 0x0
int id mtrl_picker_text_input_date 0x0
int id mtrl_picker_text_input_range_end 0x0
int id mtrl_picker_text_input_range_start 0x0
int id mtrl_picker_title_text 0x0
int id mtrl_view_tag_bottom_padding 0x0
int id multiply 0x0
int id navigation_bar_item_active_indicator_view 0x0
int id navigation_bar_item_icon_container 0x0
int id navigation_bar_item_icon_view 0x0
int id navigation_bar_item_labels_group 0x0
int id navigation_bar_item_large_label_view 0x0
int id navigation_bar_item_small_label_view 0x0
int id navigation_header_container 0x0
int id never 0x0
int id none 0x0
int id normal 0x0
int id notification_background 0x0
int id notification_main_column 0x0
int id notification_main_column_container 0x0
int id off 0x0
int id on 0x0
int id open_search_bar_text_view 0x0
int id open_search_view_background 0x0
int id open_search_view_clear_button 0x0
int id open_search_view_content_container 0x0
int id open_search_view_divider 0x0
int id open_search_view_dummy_toolbar 0x0
int id open_search_view_edit_text 0x0
int id open_search_view_header_container 0x0
int id open_search_view_root 0x0
int id open_search_view_scrim 0x0
int id open_search_view_search_prefix 0x0
int id open_search_view_status_bar_spacer 0x0
int id open_search_view_toolbar 0x0
int id open_search_view_toolbar_container 0x0
int id outline 0x0
int id outward 0x0
int id packed 0x0
int id parallax 0x0
int id parent 0x0
int id parentPanel 0x0
int id parentRelative 0x0
int id parent_matrix 0x0
int id password_toggle 0x0
int id path 0x0
int id pathRelative 0x0
int id percent 0x0
int id pin 0x0
int id position 0x0
int id postLayout 0x0
int id pressed 0x0
int id progress_circular 0x0
int id progress_horizontal 0x0
int id radio 0x0
int id rectangles 0x0
int id report_drawn 0x0
int id reverseSawtooth 0x0
int id right 0x0
int id rightToLeft 0x0
int id right_icon 0x0
int id right_side 0x0
int id rounded 0x0
int id row_index_key 0x0
int id rtl 0x0
int id save_non_transition_alpha 0x0
int id save_overlay_view 0x0
int id sawtooth 0x0
int id scale 0x0
int id screen 0x0
int id scrollIndicatorDown 0x0
int id scrollIndicatorUp 0x0
int id scrollView 0x0
int id scrollable 0x0
int id search_badge 0x0
int id search_bar 0x0
int id search_button 0x0
int id search_close_btn 0x0
int id search_edit_frame 0x0
int id search_go_btn 0x0
int id search_mag_icon 0x0
int id search_plate 0x0
int id search_src_text 0x0
int id search_voice_btn 0x0
int id select_dialog_listview 0x0
int id selected 0x0
int id selection_type 0x0
int id shortcut 0x0
int id sin 0x0
int id slide 0x0
int id snackbar_action 0x0
int id snackbar_text 0x0
int id spacer 0x0
int id special_effects_controller_view_tag 0x0
int id spline 0x0
int id split_action_bar 0x0
int id spread 0x0
int id spread_inside 0x0
int id square 0x0
int id src_atop 0x0
int id src_in 0x0
int id src_over 0x0
int id standard 0x0
int id start 0x0
int id startHorizontal 0x0
int id startToEnd 0x0
int id startVertical 0x0
int id staticLayout 0x0
int id staticPostLayout 0x0
int id stop 0x0
int id stretch 0x0
int id submenuarrow 0x0
int id submit_area 0x0
int id tabMode 0x0
int id tag_accessibility_actions 0x0
int id tag_accessibility_clickable_spans 0x0
int id tag_accessibility_heading 0x0
int id tag_accessibility_pane_title 0x0
int id tag_on_apply_window_listener 0x0
int id tag_on_receive_content_listener 0x0
int id tag_on_receive_content_mime_types 0x0
int id tag_screen_reader_focusable 0x0
int id tag_state_description 0x0
int id tag_transition_group 0x0
int id tag_unhandled_key_event_manager 0x0
int id tag_unhandled_key_listeners 0x0
int id tag_window_insets_animation_callback 0x0
int id text 0x0
int id text2 0x0
int id textSpacerNoButtons 0x0
int id textSpacerNoTitle 0x0
int id text_input_end_icon 0x0
int id text_input_error_icon 0x0
int id text_input_start_icon 0x0
int id textinput_counter 0x0
int id textinput_error 0x0
int id textinput_helper_text 0x0
int id textinput_placeholder 0x0
int id textinput_prefix_text 0x0
int id textinput_suffix_text 0x0
int id time 0x0
int id title 0x0
int id titleDividerNoCustom 0x0
int id title_template 0x0
int id top 0x0
int id topPanel 0x0
int id topToBottom 0x0
int id touch_outside 0x0
int id transition_current_scene 0x0
int id transition_layout_save 0x0
int id transition_position 0x0
int id transition_scene_layoutid_cache 0x0
int id transition_transform 0x0
int id triangle 0x0
int id unchecked 0x0
int id uniform 0x0
int id unlabeled 0x0
int id up 0x0
int id view_offset_helper 0x0
int id view_tree_lifecycle_owner 0x0
int id view_tree_on_back_pressed_dispatcher_owner 0x0
int id view_tree_saved_state_registry_owner 0x0
int id view_tree_view_model_store_owner 0x0
int id visible 0x0
int id visible_removing_fragment_view_tag 0x0
int id with_icon 0x0
int id withinBounds 0x0
int id wrap 0x0
int id wrap_content 0x0
int integer abc_config_activityDefaultDur 0x0
int integer abc_config_activityShortDur 0x0
int integer app_bar_elevation_anim_duration 0x0
int integer bottom_sheet_slide_duration 0x0
int integer cancel_button_image_alpha 0x0
int integer config_tooltipAnimTime 0x0
int integer design_snackbar_text_max_lines 0x0
int integer design_tab_indicator_anim_duration_ms 0x0
int integer hide_password_duration 0x0
int integer m3_badge_max_number 0x0
int integer m3_btn_anim_delay_ms 0x0
int integer m3_btn_anim_duration_ms 0x0
int integer m3_card_anim_delay_ms 0x0
int integer m3_card_anim_duration_ms 0x0
int integer m3_chip_anim_duration 0x0
int integer m3_sys_motion_duration_extra_long1 0x0
int integer m3_sys_motion_duration_extra_long2 0x0
int integer m3_sys_motion_duration_extra_long3 0x0
int integer m3_sys_motion_duration_extra_long4 0x0
int integer m3_sys_motion_duration_long1 0x0
int integer m3_sys_motion_duration_long2 0x0
int integer m3_sys_motion_duration_long3 0x0
int integer m3_sys_motion_duration_long4 0x0
int integer m3_sys_motion_duration_medium1 0x0
int integer m3_sys_motion_duration_medium2 0x0
int integer m3_sys_motion_duration_medium3 0x0
int integer m3_sys_motion_duration_medium4 0x0
int integer m3_sys_motion_duration_short1 0x0
int integer m3_sys_motion_duration_short2 0x0
int integer m3_sys_motion_duration_short3 0x0
int integer m3_sys_motion_duration_short4 0x0
int integer m3_sys_motion_path 0x0
int integer m3_sys_shape_corner_extra_large_corner_family 0x0
int integer m3_sys_shape_corner_extra_small_corner_family 0x0
int integer m3_sys_shape_corner_full_corner_family 0x0
int integer m3_sys_shape_corner_large_corner_family 0x0
int integer m3_sys_shape_corner_medium_corner_family 0x0
int integer m3_sys_shape_corner_small_corner_family 0x0
int integer material_motion_duration_long_1 0x0
int integer material_motion_duration_long_2 0x0
int integer material_motion_duration_medium_1 0x0
int integer material_motion_duration_medium_2 0x0
int integer material_motion_duration_short_1 0x0
int integer material_motion_duration_short_2 0x0
int integer material_motion_path 0x0
int integer mtrl_badge_max_character_count 0x0
int integer mtrl_btn_anim_delay_ms 0x0
int integer mtrl_btn_anim_duration_ms 0x0
int integer mtrl_calendar_header_orientation 0x0
int integer mtrl_calendar_selection_text_lines 0x0
int integer mtrl_calendar_year_selector_span 0x0
int integer mtrl_card_anim_delay_ms 0x0
int integer mtrl_card_anim_duration_ms 0x0
int integer mtrl_chip_anim_duration 0x0
int integer mtrl_switch_thumb_motion_duration 0x0
int integer mtrl_switch_thumb_post_morphing_duration 0x0
int integer mtrl_switch_thumb_pre_morphing_duration 0x0
int integer mtrl_switch_thumb_pressed_duration 0x0
int integer mtrl_switch_thumb_viewport_center_coordinate 0x0
int integer mtrl_switch_thumb_viewport_size 0x0
int integer mtrl_switch_track_viewport_height 0x0
int integer mtrl_switch_track_viewport_width 0x0
int integer mtrl_tab_indicator_anim_duration_ms 0x0
int integer mtrl_view_gone 0x0
int integer mtrl_view_invisible 0x0
int integer mtrl_view_visible 0x0
int integer show_password_duration 0x0
int integer status_bar_notification_info_maxnum 0x0
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x0
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x0
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x0
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x0
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x0
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x0
int interpolator fast_out_slow_in 0x0
int interpolator m3_sys_motion_easing_emphasized 0x0
int interpolator m3_sys_motion_easing_emphasized_accelerate 0x0
int interpolator m3_sys_motion_easing_emphasized_decelerate 0x0
int interpolator m3_sys_motion_easing_linear 0x0
int interpolator m3_sys_motion_easing_standard 0x0
int interpolator m3_sys_motion_easing_standard_accelerate 0x0
int interpolator m3_sys_motion_easing_standard_decelerate 0x0
int interpolator mtrl_fast_out_linear_in 0x0
int interpolator mtrl_fast_out_slow_in 0x0
int interpolator mtrl_linear 0x0
int interpolator mtrl_linear_out_slow_in 0x0
int layout abc_action_bar_title_item 0x0
int layout abc_action_bar_up_container 0x0
int layout abc_action_menu_item_layout 0x0
int layout abc_action_menu_layout 0x0
int layout abc_action_mode_bar 0x0
int layout abc_action_mode_close_item_material 0x0
int layout abc_activity_chooser_view 0x0
int layout abc_activity_chooser_view_list_item 0x0
int layout abc_alert_dialog_button_bar_material 0x0
int layout abc_alert_dialog_material 0x0
int layout abc_alert_dialog_title_material 0x0
int layout abc_cascading_menu_item_layout 0x0
int layout abc_dialog_title_material 0x0
int layout abc_expanded_menu_layout 0x0
int layout abc_list_menu_item_checkbox 0x0
int layout abc_list_menu_item_icon 0x0
int layout abc_list_menu_item_layout 0x0
int layout abc_list_menu_item_radio 0x0
int layout abc_popup_menu_header_item_layout 0x0
int layout abc_popup_menu_item_layout 0x0
int layout abc_screen_content_include 0x0
int layout abc_screen_simple 0x0
int layout abc_screen_simple_overlay_action_mode 0x0
int layout abc_screen_toolbar 0x0
int layout abc_search_dropdown_item_icons_2line 0x0
int layout abc_search_view 0x0
int layout abc_select_dialog_material 0x0
int layout abc_tooltip 0x0
int layout custom_dialog 0x0
int layout design_bottom_navigation_item 0x0
int layout design_bottom_sheet_dialog 0x0
int layout design_layout_snackbar 0x0
int layout design_layout_snackbar_include 0x0
int layout design_layout_tab_icon 0x0
int layout design_layout_tab_text 0x0
int layout design_menu_item_action_area 0x0
int layout design_navigation_item 0x0
int layout design_navigation_item_header 0x0
int layout design_navigation_item_separator 0x0
int layout design_navigation_item_subheader 0x0
int layout design_navigation_menu 0x0
int layout design_navigation_menu_item 0x0
int layout design_text_input_end_icon 0x0
int layout design_text_input_start_icon 0x0
int layout ime_base_split_test_activity 0x0
int layout ime_secondary_split_test_activity 0x0
int layout m3_alert_dialog 0x0
int layout m3_alert_dialog_actions 0x0
int layout m3_alert_dialog_title 0x0
int layout m3_auto_complete_simple_item 0x0
int layout m3_side_sheet_dialog 0x0
int layout material_chip_input_combo 0x0
int layout material_clock_display 0x0
int layout material_clock_display_divider 0x0
int layout material_clock_period_toggle 0x0
int layout material_clock_period_toggle_land 0x0
int layout material_clockface_textview 0x0
int layout material_clockface_view 0x0
int layout material_radial_view_group 0x0
int layout material_textinput_timepicker 0x0
int layout material_time_chip 0x0
int layout material_time_input 0x0
int layout material_timepicker 0x0
int layout material_timepicker_dialog 0x0
int layout material_timepicker_textinput_display 0x0
int layout mtrl_alert_dialog 0x0
int layout mtrl_alert_dialog_actions 0x0
int layout mtrl_alert_dialog_title 0x0
int layout mtrl_alert_select_dialog_item 0x0
int layout mtrl_alert_select_dialog_multichoice 0x0
int layout mtrl_alert_select_dialog_singlechoice 0x0
int layout mtrl_auto_complete_simple_item 0x0
int layout mtrl_calendar_day 0x0
int layout mtrl_calendar_day_of_week 0x0
int layout mtrl_calendar_days_of_week 0x0
int layout mtrl_calendar_horizontal 0x0
int layout mtrl_calendar_month 0x0
int layout mtrl_calendar_month_labeled 0x0
int layout mtrl_calendar_month_navigation 0x0
int layout mtrl_calendar_months 0x0
int layout mtrl_calendar_vertical 0x0
int layout mtrl_calendar_year 0x0
int layout mtrl_layout_snackbar 0x0
int layout mtrl_layout_snackbar_include 0x0
int layout mtrl_navigation_rail_item 0x0
int layout mtrl_picker_actions 0x0
int layout mtrl_picker_dialog 0x0
int layout mtrl_picker_fullscreen 0x0
int layout mtrl_picker_header_dialog 0x0
int layout mtrl_picker_header_fullscreen 0x0
int layout mtrl_picker_header_selection_text 0x0
int layout mtrl_picker_header_title_text 0x0
int layout mtrl_picker_header_toggle 0x0
int layout mtrl_picker_text_input_date 0x0
int layout mtrl_picker_text_input_date_range 0x0
int layout mtrl_search_bar 0x0
int layout mtrl_search_view 0x0
int layout notification_action 0x0
int layout notification_action_tombstone 0x0
int layout notification_template_custom_big 0x0
int layout notification_template_icon_group 0x0
int layout notification_template_part_chronometer 0x0
int layout notification_template_part_time 0x0
int layout select_dialog_item_material 0x0
int layout select_dialog_multichoice_material 0x0
int layout select_dialog_singlechoice_material 0x0
int layout support_simple_spinner_dropdown_item 0x0
int plurals mtrl_badge_content_description 0x0
int string abc_action_bar_home_description 0x0
int string abc_action_bar_up_description 0x0
int string abc_action_menu_overflow_description 0x0
int string abc_action_mode_done 0x0
int string abc_activity_chooser_view_see_all 0x0
int string abc_activitychooserview_choose_application 0x0
int string abc_capital_off 0x0
int string abc_capital_on 0x0
int string abc_menu_alt_shortcut_label 0x0
int string abc_menu_ctrl_shortcut_label 0x0
int string abc_menu_delete_shortcut_label 0x0
int string abc_menu_enter_shortcut_label 0x0
int string abc_menu_function_shortcut_label 0x0
int string abc_menu_meta_shortcut_label 0x0
int string abc_menu_shift_shortcut_label 0x0
int string abc_menu_space_shortcut_label 0x0
int string abc_menu_sym_shortcut_label 0x0
int string abc_prepend_shortcut_label 0x0
int string abc_search_hint 0x0
int string abc_searchview_description_clear 0x0
int string abc_searchview_description_query 0x0
int string abc_searchview_description_search 0x0
int string abc_searchview_description_submit 0x0
int string abc_searchview_description_voice 0x0
int string abc_shareactionprovider_share_with 0x0
int string abc_shareactionprovider_share_with_application 0x0
int string abc_toolbar_collapse_description 0x0
int string appbar_scrolling_view_behavior 0x0
int string bottom_sheet_behavior 0x0
int string bottomsheet_action_collapse 0x0
int string bottomsheet_action_expand 0x0
int string bottomsheet_action_expand_halfway 0x0
int string bottomsheet_drag_handle_clicked 0x0
int string bottomsheet_drag_handle_content_description 0x0
int string call_notification_answer_action 0x0
int string call_notification_answer_video_action 0x0
int string call_notification_decline_action 0x0
int string call_notification_hang_up_action 0x0
int string call_notification_incoming_text 0x0
int string call_notification_ongoing_text 0x0
int string call_notification_screening_text 0x0
int string character_counter_content_description 0x0
int string character_counter_overflowed_content_description 0x0
int string character_counter_pattern 0x0
int string clear_text_end_icon_content_description 0x0
int string error_a11y_label 0x0
int string error_icon_content_description 0x0
int string exposed_dropdown_menu_content_description 0x0
int string fab_transformation_scrim_behavior 0x0
int string fab_transformation_sheet_behavior 0x0
int string hide_bottom_view_on_scroll_behavior 0x0
int string icon_content_description 0x0
int string item_view_role_description 0x0
int string m3_exceed_max_badge_text_suffix 0x0
int string m3_ref_typeface_brand_medium 0x0
int string m3_ref_typeface_brand_regular 0x0
int string m3_ref_typeface_plain_medium 0x0
int string m3_ref_typeface_plain_regular 0x0
int string m3_sys_motion_easing_emphasized 0x0
int string m3_sys_motion_easing_emphasized_accelerate 0x0
int string m3_sys_motion_easing_emphasized_decelerate 0x0
int string m3_sys_motion_easing_emphasized_path_data 0x0
int string m3_sys_motion_easing_legacy 0x0
int string m3_sys_motion_easing_legacy_accelerate 0x0
int string m3_sys_motion_easing_legacy_decelerate 0x0
int string m3_sys_motion_easing_linear 0x0
int string m3_sys_motion_easing_standard 0x0
int string m3_sys_motion_easing_standard_accelerate 0x0
int string m3_sys_motion_easing_standard_decelerate 0x0
int string material_clock_display_divider 0x0
int string material_clock_toggle_content_description 0x0
int string material_hour_24h_suffix 0x0
int string material_hour_selection 0x0
int string material_hour_suffix 0x0
int string material_minute_selection 0x0
int string material_minute_suffix 0x0
int string material_motion_easing_accelerated 0x0
int string material_motion_easing_decelerated 0x0
int string material_motion_easing_emphasized 0x0
int string material_motion_easing_linear 0x0
int string material_motion_easing_standard 0x0
int string material_slider_range_end 0x0
int string material_slider_range_start 0x0
int string material_slider_value 0x0
int string material_timepicker_am 0x0
int string material_timepicker_clock_mode_description 0x0
int string material_timepicker_hour 0x0
int string material_timepicker_minute 0x0
int string material_timepicker_pm 0x0
int string material_timepicker_select_time 0x0
int string material_timepicker_text_input_mode_description 0x0
int string maui_empty_unused 0x0
int string mtrl_badge_numberless_content_description 0x0
int string mtrl_checkbox_button_icon_path_checked 0x0
int string mtrl_checkbox_button_icon_path_group_name 0x0
int string mtrl_checkbox_button_icon_path_indeterminate 0x0
int string mtrl_checkbox_button_icon_path_name 0x0
int string mtrl_checkbox_button_path_checked 0x0
int string mtrl_checkbox_button_path_group_name 0x0
int string mtrl_checkbox_button_path_name 0x0
int string mtrl_checkbox_button_path_unchecked 0x0
int string mtrl_checkbox_state_description_checked 0x0
int string mtrl_checkbox_state_description_indeterminate 0x0
int string mtrl_checkbox_state_description_unchecked 0x0
int string mtrl_chip_close_icon_content_description 0x0
int string mtrl_exceed_max_badge_number_content_description 0x0
int string mtrl_exceed_max_badge_number_suffix 0x0
int string mtrl_picker_a11y_next_month 0x0
int string mtrl_picker_a11y_prev_month 0x0
int string mtrl_picker_announce_current_range_selection 0x0
int string mtrl_picker_announce_current_selection 0x0
int string mtrl_picker_announce_current_selection_none 0x0
int string mtrl_picker_cancel 0x0
int string mtrl_picker_confirm 0x0
int string mtrl_picker_date_header_selected 0x0
int string mtrl_picker_date_header_title 0x0
int string mtrl_picker_date_header_unselected 0x0
int string mtrl_picker_day_of_week_column_header 0x0
int string mtrl_picker_end_date_description 0x0
int string mtrl_picker_invalid_format 0x0
int string mtrl_picker_invalid_format_example 0x0
int string mtrl_picker_invalid_format_use 0x0
int string mtrl_picker_invalid_range 0x0
int string mtrl_picker_navigate_to_current_year_description 0x0
int string mtrl_picker_navigate_to_year_description 0x0
int string mtrl_picker_out_of_range 0x0
int string mtrl_picker_range_header_only_end_selected 0x0
int string mtrl_picker_range_header_only_start_selected 0x0
int string mtrl_picker_range_header_selected 0x0
int string mtrl_picker_range_header_title 0x0
int string mtrl_picker_range_header_unselected 0x0
int string mtrl_picker_save 0x0
int string mtrl_picker_start_date_description 0x0
int string mtrl_picker_text_input_date_hint 0x0
int string mtrl_picker_text_input_date_range_end_hint 0x0
int string mtrl_picker_text_input_date_range_start_hint 0x0
int string mtrl_picker_text_input_day_abbr 0x0
int string mtrl_picker_text_input_month_abbr 0x0
int string mtrl_picker_text_input_year_abbr 0x0
int string mtrl_picker_today_description 0x0
int string mtrl_picker_toggle_to_calendar_input_mode 0x0
int string mtrl_picker_toggle_to_day_selection 0x0
int string mtrl_picker_toggle_to_text_input_mode 0x0
int string mtrl_picker_toggle_to_year_selection 0x0
int string mtrl_switch_thumb_group_name 0x0
int string mtrl_switch_thumb_path_checked 0x0
int string mtrl_switch_thumb_path_morphing 0x0
int string mtrl_switch_thumb_path_name 0x0
int string mtrl_switch_thumb_path_pressed 0x0
int string mtrl_switch_thumb_path_unchecked 0x0
int string mtrl_switch_track_decoration_path 0x0
int string mtrl_switch_track_path 0x0
int string mtrl_timepicker_cancel 0x0
int string mtrl_timepicker_confirm 0x0
int string password_toggle_content_description 0x0
int string path_password_eye 0x0
int string path_password_eye_mask_strike_through 0x0
int string path_password_eye_mask_visible 0x0
int string path_password_strike_through 0x0
int string search_menu_title 0x0
int string searchbar_scrolling_view_behavior 0x0
int string searchview_clear_text_content_description 0x0
int string searchview_navigation_content_description 0x0
int string side_sheet_accessibility_pane_title 0x0
int string side_sheet_behavior 0x0
int string status_bar_notification_info_overflow 0x0
int style AlertDialog_AppCompat 0x0
int style AlertDialog_AppCompat_Light 0x0
int style Animation_AppCompat_Dialog 0x0
int style Animation_AppCompat_DropDownUp 0x0
int style Animation_AppCompat_Tooltip 0x0
int style Animation_Design_BottomSheetDialog 0x0
int style Animation_Material3_BottomSheetDialog 0x0
int style Animation_Material3_SideSheetDialog 0x0
int style Animation_Material3_SideSheetDialog_Left 0x0
int style Animation_Material3_SideSheetDialog_Right 0x0
int style Animation_MaterialComponents_BottomSheetDialog 0x0
int style Base_AlertDialog_AppCompat 0x0
int style Base_AlertDialog_AppCompat_Light 0x0
int style Base_Animation_AppCompat_Dialog 0x0
int style Base_Animation_AppCompat_DropDownUp 0x0
int style Base_Animation_AppCompat_Tooltip 0x0
int style Base_CardView 0x0
int style Base_DialogWindowTitleBackground_AppCompat 0x0
int style Base_DialogWindowTitle_AppCompat 0x0
int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x0
int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x0
int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x0
int style Base_TextAppearance_AppCompat 0x0
int style Base_TextAppearance_AppCompat_Body1 0x0
int style Base_TextAppearance_AppCompat_Body2 0x0
int style Base_TextAppearance_AppCompat_Button 0x0
int style Base_TextAppearance_AppCompat_Caption 0x0
int style Base_TextAppearance_AppCompat_Display1 0x0
int style Base_TextAppearance_AppCompat_Display2 0x0
int style Base_TextAppearance_AppCompat_Display3 0x0
int style Base_TextAppearance_AppCompat_Display4 0x0
int style Base_TextAppearance_AppCompat_Headline 0x0
int style Base_TextAppearance_AppCompat_Inverse 0x0
int style Base_TextAppearance_AppCompat_Large 0x0
int style Base_TextAppearance_AppCompat_Large_Inverse 0x0
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
int style Base_TextAppearance_AppCompat_Medium 0x0
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x0
int style Base_TextAppearance_AppCompat_Menu 0x0
int style Base_TextAppearance_AppCompat_SearchResult 0x0
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x0
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x0
int style Base_TextAppearance_AppCompat_Small 0x0
int style Base_TextAppearance_AppCompat_Small_Inverse 0x0
int style Base_TextAppearance_AppCompat_Subhead 0x0
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x0
int style Base_TextAppearance_AppCompat_Title 0x0
int style Base_TextAppearance_AppCompat_Title_Inverse 0x0
int style Base_TextAppearance_AppCompat_Tooltip 0x0
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
int style Base_TextAppearance_AppCompat_Widget_Button 0x0
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x0
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x0
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x0
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
int style Base_TextAppearance_AppCompat_Widget_Switch 0x0
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
int style Base_TextAppearance_Material3_Search 0x0
int style Base_TextAppearance_MaterialComponents_Badge 0x0
int style Base_TextAppearance_MaterialComponents_Button 0x0
int style Base_TextAppearance_MaterialComponents_Headline6 0x0
int style Base_TextAppearance_MaterialComponents_Subtitle2 0x0
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
int style Base_ThemeOverlay_AppCompat 0x0
int style Base_ThemeOverlay_AppCompat_ActionBar 0x0
int style Base_ThemeOverlay_AppCompat_Dark 0x0
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x0
int style Base_ThemeOverlay_AppCompat_Dialog 0x0
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x0
int style Base_ThemeOverlay_AppCompat_Light 0x0
int style Base_ThemeOverlay_Material3_AutoCompleteTextView 0x0
int style Base_ThemeOverlay_Material3_BottomSheetDialog 0x0
int style Base_ThemeOverlay_Material3_Dialog 0x0
int style Base_ThemeOverlay_Material3_SideSheetDialog 0x0
int style Base_ThemeOverlay_Material3_TextInputEditText 0x0
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x0
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x0
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x0
int style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x0
int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x0
int style Base_Theme_AppCompat 0x0
int style Base_Theme_AppCompat_CompactMenu 0x0
int style Base_Theme_AppCompat_Dialog 0x0
int style Base_Theme_AppCompat_DialogWhenLarge 0x0
int style Base_Theme_AppCompat_Dialog_Alert 0x0
int style Base_Theme_AppCompat_Dialog_FixedSize 0x0
int style Base_Theme_AppCompat_Dialog_MinWidth 0x0
int style Base_Theme_AppCompat_Light 0x0
int style Base_Theme_AppCompat_Light_DarkActionBar 0x0
int style Base_Theme_AppCompat_Light_Dialog 0x0
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x0
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x0
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x0
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x0
int style Base_Theme_Material3_Dark 0x0
int style Base_Theme_Material3_Dark_BottomSheetDialog 0x0
int style Base_Theme_Material3_Dark_Dialog 0x0
int style Base_Theme_Material3_Dark_DialogWhenLarge 0x0
int style Base_Theme_Material3_Dark_Dialog_FixedSize 0x0
int style Base_Theme_Material3_Dark_SideSheetDialog 0x0
int style Base_Theme_Material3_Light 0x0
int style Base_Theme_Material3_Light_BottomSheetDialog 0x0
int style Base_Theme_Material3_Light_Dialog 0x0
int style Base_Theme_Material3_Light_DialogWhenLarge 0x0
int style Base_Theme_Material3_Light_Dialog_FixedSize 0x0
int style Base_Theme_Material3_Light_SideSheetDialog 0x0
int style Base_Theme_MaterialComponents 0x0
int style Base_Theme_MaterialComponents_Bridge 0x0
int style Base_Theme_MaterialComponents_CompactMenu 0x0
int style Base_Theme_MaterialComponents_Dialog 0x0
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x0
int style Base_Theme_MaterialComponents_Dialog_Alert 0x0
int style Base_Theme_MaterialComponents_Dialog_Bridge 0x0
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x0
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x0
int style Base_Theme_MaterialComponents_Light 0x0
int style Base_Theme_MaterialComponents_Light_Bridge 0x0
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x0
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x0
int style Base_Theme_MaterialComponents_Light_Dialog 0x0
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x0
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x0
int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x0
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x0
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x0
int style Base_V14_ThemeOverlay_Material3_BottomSheetDialog 0x0
int style Base_V14_ThemeOverlay_Material3_SideSheetDialog 0x0
int style Base_V14_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x0
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x0
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x0
int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x0
int style Base_V14_Theme_Material3_Dark 0x0
int style Base_V14_Theme_Material3_Dark_BottomSheetDialog 0x0
int style Base_V14_Theme_Material3_Dark_Dialog 0x0
int style Base_V14_Theme_Material3_Dark_SideSheetDialog 0x0
int style Base_V14_Theme_Material3_Light 0x0
int style Base_V14_Theme_Material3_Light_BottomSheetDialog 0x0
int style Base_V14_Theme_Material3_Light_Dialog 0x0
int style Base_V14_Theme_Material3_Light_SideSheetDialog 0x0
int style Base_V14_Theme_MaterialComponents 0x0
int style Base_V14_Theme_MaterialComponents_Bridge 0x0
int style Base_V14_Theme_MaterialComponents_Dialog 0x0
int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x0
int style Base_V14_Theme_MaterialComponents_Light 0x0
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x0
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x0
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x0
int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x0
int style Base_V14_Widget_MaterialComponents_AutoCompleteTextView 0x0
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x0
int style Base_V21_ThemeOverlay_Material3_BottomSheetDialog 0x0
int style Base_V21_ThemeOverlay_Material3_SideSheetDialog 0x0
int style Base_V21_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x0
int style Base_V21_Theme_AppCompat 0x0
int style Base_V21_Theme_AppCompat_Dialog 0x0
int style Base_V21_Theme_AppCompat_Light 0x0
int style Base_V21_Theme_AppCompat_Light_Dialog 0x0
int style Base_V21_Theme_MaterialComponents 0x0
int style Base_V21_Theme_MaterialComponents_Dialog 0x0
int style Base_V21_Theme_MaterialComponents_Light 0x0
int style Base_V21_Theme_MaterialComponents_Light_Dialog 0x0
int style Base_V22_Theme_AppCompat 0x0
int style Base_V22_Theme_AppCompat_Light 0x0
int style Base_V23_Theme_AppCompat 0x0
int style Base_V23_Theme_AppCompat_Light 0x0
int style Base_V24_Theme_Material3_Dark 0x0
int style Base_V24_Theme_Material3_Dark_Dialog 0x0
int style Base_V24_Theme_Material3_Light 0x0
int style Base_V24_Theme_Material3_Light_Dialog 0x0
int style Base_V26_Theme_AppCompat 0x0
int style Base_V26_Theme_AppCompat_Light 0x0
int style Base_V26_Widget_AppCompat_Toolbar 0x0
int style Base_V28_Theme_AppCompat 0x0
int style Base_V28_Theme_AppCompat_Light 0x0
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x0
int style Base_V7_Theme_AppCompat 0x0
int style Base_V7_Theme_AppCompat_Dialog 0x0
int style Base_V7_Theme_AppCompat_Light 0x0
int style Base_V7_Theme_AppCompat_Light_Dialog 0x0
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x0
int style Base_V7_Widget_AppCompat_EditText 0x0
int style Base_V7_Widget_AppCompat_Toolbar 0x0
int style Base_Widget_AppCompat_ActionBar 0x0
int style Base_Widget_AppCompat_ActionBar_Solid 0x0
int style Base_Widget_AppCompat_ActionBar_TabBar 0x0
int style Base_Widget_AppCompat_ActionBar_TabText 0x0
int style Base_Widget_AppCompat_ActionBar_TabView 0x0
int style Base_Widget_AppCompat_ActionButton 0x0
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x0
int style Base_Widget_AppCompat_ActionButton_Overflow 0x0
int style Base_Widget_AppCompat_ActionMode 0x0
int style Base_Widget_AppCompat_ActivityChooserView 0x0
int style Base_Widget_AppCompat_AutoCompleteTextView 0x0
int style Base_Widget_AppCompat_Button 0x0
int style Base_Widget_AppCompat_ButtonBar 0x0
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x0
int style Base_Widget_AppCompat_Button_Borderless 0x0
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x0
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
int style Base_Widget_AppCompat_Button_Colored 0x0
int style Base_Widget_AppCompat_Button_Small 0x0
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x0
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x0
int style Base_Widget_AppCompat_CompoundButton_Switch 0x0
int style Base_Widget_AppCompat_DrawerArrowToggle 0x0
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x0
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x0
int style Base_Widget_AppCompat_EditText 0x0
int style Base_Widget_AppCompat_ImageButton 0x0
int style Base_Widget_AppCompat_Light_ActionBar 0x0
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x0
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x0
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x0
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x0
int style Base_Widget_AppCompat_Light_PopupMenu 0x0
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x0
int style Base_Widget_AppCompat_ListMenuView 0x0
int style Base_Widget_AppCompat_ListPopupWindow 0x0
int style Base_Widget_AppCompat_ListView 0x0
int style Base_Widget_AppCompat_ListView_DropDown 0x0
int style Base_Widget_AppCompat_ListView_Menu 0x0
int style Base_Widget_AppCompat_PopupMenu 0x0
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x0
int style Base_Widget_AppCompat_PopupWindow 0x0
int style Base_Widget_AppCompat_ProgressBar 0x0
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x0
int style Base_Widget_AppCompat_RatingBar 0x0
int style Base_Widget_AppCompat_RatingBar_Indicator 0x0
int style Base_Widget_AppCompat_RatingBar_Small 0x0
int style Base_Widget_AppCompat_SearchView 0x0
int style Base_Widget_AppCompat_SearchView_ActionBar 0x0
int style Base_Widget_AppCompat_SeekBar 0x0
int style Base_Widget_AppCompat_SeekBar_Discrete 0x0
int style Base_Widget_AppCompat_Spinner 0x0
int style Base_Widget_AppCompat_Spinner_Underlined 0x0
int style Base_Widget_AppCompat_TextView 0x0
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x0
int style Base_Widget_AppCompat_Toolbar 0x0
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x0
int style Base_Widget_Design_TabLayout 0x0
int style Base_Widget_Material3_ActionBar_Solid 0x0
int style Base_Widget_Material3_ActionMode 0x0
int style Base_Widget_Material3_BottomNavigationView 0x0
int style Base_Widget_Material3_CardView 0x0
int style Base_Widget_Material3_Chip 0x0
int style Base_Widget_Material3_CollapsingToolbar 0x0
int style Base_Widget_Material3_CompoundButton_CheckBox 0x0
int style Base_Widget_Material3_CompoundButton_RadioButton 0x0
int style Base_Widget_Material3_CompoundButton_Switch 0x0
int style Base_Widget_Material3_ExtendedFloatingActionButton 0x0
int style Base_Widget_Material3_ExtendedFloatingActionButton_Icon 0x0
int style Base_Widget_Material3_FloatingActionButton 0x0
int style Base_Widget_Material3_FloatingActionButton_Large 0x0
int style Base_Widget_Material3_FloatingActionButton_Small 0x0
int style Base_Widget_Material3_Light_ActionBar_Solid 0x0
int style Base_Widget_Material3_MaterialCalendar_NavigationButton 0x0
int style Base_Widget_Material3_Snackbar 0x0
int style Base_Widget_Material3_TabLayout 0x0
int style Base_Widget_Material3_TabLayout_OnSurface 0x0
int style Base_Widget_Material3_TabLayout_Secondary 0x0
int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x0
int style Base_Widget_MaterialComponents_CheckedTextView 0x0
int style Base_Widget_MaterialComponents_Chip 0x0
int style Base_Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x0
int style Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton 0x0
int style Base_Widget_MaterialComponents_PopupMenu 0x0
int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x0
int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x0
int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x0
int style Base_Widget_MaterialComponents_Slider 0x0
int style Base_Widget_MaterialComponents_Snackbar 0x0
int style Base_Widget_MaterialComponents_TextInputEditText 0x0
int style Base_Widget_MaterialComponents_TextInputLayout 0x0
int style Base_Widget_MaterialComponents_TextView 0x0
int style CardView 0x0
int style CardView_Dark 0x0
int style CardView_Light 0x0
int style MaterialAlertDialog_Material3 0x0
int style MaterialAlertDialog_Material3_Animation 0x0
int style MaterialAlertDialog_Material3_Body_Text 0x0
int style MaterialAlertDialog_Material3_Body_Text_CenterStacked 0x0
int style MaterialAlertDialog_Material3_Title_Icon 0x0
int style MaterialAlertDialog_Material3_Title_Icon_CenterStacked 0x0
int style MaterialAlertDialog_Material3_Title_Panel 0x0
int style MaterialAlertDialog_Material3_Title_Panel_CenterStacked 0x0
int style MaterialAlertDialog_Material3_Title_Text 0x0
int style MaterialAlertDialog_Material3_Title_Text_CenterStacked 0x0
int style MaterialAlertDialog_MaterialComponents 0x0
int style MaterialAlertDialog_MaterialComponents_Body_Text 0x0
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x0
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x0
int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x0
int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x0
int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x0
int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x0
int style MaterialAlertDialog_MaterialComponents_Title_Text 0x0
int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x0
int style Platform_AppCompat 0x0
int style Platform_AppCompat_Light 0x0
int style Platform_MaterialComponents 0x0
int style Platform_MaterialComponents_Dialog 0x0
int style Platform_MaterialComponents_Light 0x0
int style Platform_MaterialComponents_Light_Dialog 0x0
int style Platform_ThemeOverlay_AppCompat 0x0
int style Platform_ThemeOverlay_AppCompat_Dark 0x0
int style Platform_ThemeOverlay_AppCompat_Light 0x0
int style Platform_V21_AppCompat 0x0
int style Platform_V21_AppCompat_Light 0x0
int style Platform_V25_AppCompat 0x0
int style Platform_V25_AppCompat_Light 0x0
int style Platform_Widget_AppCompat_Spinner 0x0
int style RtlOverlay_DialogWindowTitle_AppCompat 0x0
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x0
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x0
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x0
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x0
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x0
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x0
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x0
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x0
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x0
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x0
int style ShapeAppearanceOverlay_Material3_Button 0x0
int style ShapeAppearanceOverlay_Material3_Chip 0x0
int style ShapeAppearanceOverlay_Material3_Corner_Bottom 0x0
int style ShapeAppearanceOverlay_Material3_Corner_Left 0x0
int style ShapeAppearanceOverlay_Material3_Corner_Right 0x0
int style ShapeAppearanceOverlay_Material3_Corner_Top 0x0
int style ShapeAppearanceOverlay_Material3_FloatingActionButton 0x0
int style ShapeAppearanceOverlay_Material3_NavigationView_Item 0x0
int style ShapeAppearanceOverlay_Material3_SearchBar 0x0
int style ShapeAppearanceOverlay_Material3_SearchView 0x0
int style ShapeAppearanceOverlay_MaterialAlertDialog_Material3 0x0
int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x0
int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x0
int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x0
int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x0
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x0
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x0
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x0
int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x0
int style ShapeAppearance_M3_Comp_Badge_Large_Shape 0x0
int style ShapeAppearance_M3_Comp_Badge_Shape 0x0
int style ShapeAppearance_M3_Comp_BottomAppBar_Container_Shape 0x0
int style ShapeAppearance_M3_Comp_DatePicker_Modal_Date_Container_Shape 0x0
int style ShapeAppearance_M3_Comp_FilledButton_Container_Shape 0x0
int style ShapeAppearance_M3_Comp_NavigationBar_ActiveIndicator_Shape 0x0
int style ShapeAppearance_M3_Comp_NavigationBar_Container_Shape 0x0
int style ShapeAppearance_M3_Comp_NavigationDrawer_ActiveIndicator_Shape 0x0
int style ShapeAppearance_M3_Comp_NavigationRail_ActiveIndicator_Shape 0x0
int style ShapeAppearance_M3_Comp_NavigationRail_Container_Shape 0x0
int style ShapeAppearance_M3_Comp_SearchBar_Avatar_Shape 0x0
int style ShapeAppearance_M3_Comp_SearchBar_Container_Shape 0x0
int style ShapeAppearance_M3_Comp_SearchView_FullScreen_Container_Shape 0x0
int style ShapeAppearance_M3_Comp_Sheet_Side_Docked_Container_Shape 0x0
int style ShapeAppearance_M3_Comp_Switch_Handle_Shape 0x0
int style ShapeAppearance_M3_Comp_Switch_StateLayer_Shape 0x0
int style ShapeAppearance_M3_Comp_Switch_Track_Shape 0x0
int style ShapeAppearance_M3_Comp_TextButton_Container_Shape 0x0
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraLarge 0x0
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraSmall 0x0
int style ShapeAppearance_M3_Sys_Shape_Corner_Full 0x0
int style ShapeAppearance_M3_Sys_Shape_Corner_Large 0x0
int style ShapeAppearance_M3_Sys_Shape_Corner_Medium 0x0
int style ShapeAppearance_M3_Sys_Shape_Corner_None 0x0
int style ShapeAppearance_M3_Sys_Shape_Corner_Small 0x0
int style ShapeAppearance_Material3_Corner_ExtraLarge 0x0
int style ShapeAppearance_Material3_Corner_ExtraSmall 0x0
int style ShapeAppearance_Material3_Corner_Full 0x0
int style ShapeAppearance_Material3_Corner_Large 0x0
int style ShapeAppearance_Material3_Corner_Medium 0x0
int style ShapeAppearance_Material3_Corner_None 0x0
int style ShapeAppearance_Material3_Corner_Small 0x0
int style ShapeAppearance_Material3_LargeComponent 0x0
int style ShapeAppearance_Material3_MediumComponent 0x0
int style ShapeAppearance_Material3_NavigationBarView_ActiveIndicator 0x0
int style ShapeAppearance_Material3_SmallComponent 0x0
int style ShapeAppearance_Material3_Tooltip 0x0
int style ShapeAppearance_MaterialComponents 0x0
int style ShapeAppearance_MaterialComponents_Badge 0x0
int style ShapeAppearance_MaterialComponents_LargeComponent 0x0
int style ShapeAppearance_MaterialComponents_MediumComponent 0x0
int style ShapeAppearance_MaterialComponents_SmallComponent 0x0
int style ShapeAppearance_MaterialComponents_Tooltip 0x0
int style TextAppearance_AppCompat 0x0
int style TextAppearance_AppCompat_Body1 0x0
int style TextAppearance_AppCompat_Body2 0x0
int style TextAppearance_AppCompat_Button 0x0
int style TextAppearance_AppCompat_Caption 0x0
int style TextAppearance_AppCompat_Display1 0x0
int style TextAppearance_AppCompat_Display2 0x0
int style TextAppearance_AppCompat_Display3 0x0
int style TextAppearance_AppCompat_Display4 0x0
int style TextAppearance_AppCompat_Headline 0x0
int style TextAppearance_AppCompat_Inverse 0x0
int style TextAppearance_AppCompat_Large 0x0
int style TextAppearance_AppCompat_Large_Inverse 0x0
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x0
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x0
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
int style TextAppearance_AppCompat_Medium 0x0
int style TextAppearance_AppCompat_Medium_Inverse 0x0
int style TextAppearance_AppCompat_Menu 0x0
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x0
int style TextAppearance_AppCompat_SearchResult_Title 0x0
int style TextAppearance_AppCompat_Small 0x0
int style TextAppearance_AppCompat_Small_Inverse 0x0
int style TextAppearance_AppCompat_Subhead 0x0
int style TextAppearance_AppCompat_Subhead_Inverse 0x0
int style TextAppearance_AppCompat_Title 0x0
int style TextAppearance_AppCompat_Title_Inverse 0x0
int style TextAppearance_AppCompat_Tooltip 0x0
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x0
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x0
int style TextAppearance_AppCompat_Widget_Button 0x0
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
int style TextAppearance_AppCompat_Widget_Button_Colored 0x0
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x0
int style TextAppearance_AppCompat_Widget_DropDownItem 0x0
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
int style TextAppearance_AppCompat_Widget_Switch 0x0
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
int style TextAppearance_Compat_Notification 0x0
int style TextAppearance_Compat_Notification_Info 0x0
int style TextAppearance_Compat_Notification_Line2 0x0
int style TextAppearance_Compat_Notification_Time 0x0
int style TextAppearance_Compat_Notification_Title 0x0
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x0
int style TextAppearance_Design_Counter 0x0
int style TextAppearance_Design_Counter_Overflow 0x0
int style TextAppearance_Design_Error 0x0
int style TextAppearance_Design_HelperText 0x0
int style TextAppearance_Design_Hint 0x0
int style TextAppearance_Design_Placeholder 0x0
int style TextAppearance_Design_Prefix 0x0
int style TextAppearance_Design_Snackbar_Message 0x0
int style TextAppearance_Design_Suffix 0x0
int style TextAppearance_Design_Tab 0x0
int style TextAppearance_M3_Sys_Typescale_BodyLarge 0x0
int style TextAppearance_M3_Sys_Typescale_BodyMedium 0x0
int style TextAppearance_M3_Sys_Typescale_BodySmall 0x0
int style TextAppearance_M3_Sys_Typescale_DisplayLarge 0x0
int style TextAppearance_M3_Sys_Typescale_DisplayMedium 0x0
int style TextAppearance_M3_Sys_Typescale_DisplaySmall 0x0
int style TextAppearance_M3_Sys_Typescale_HeadlineLarge 0x0
int style TextAppearance_M3_Sys_Typescale_HeadlineMedium 0x0
int style TextAppearance_M3_Sys_Typescale_HeadlineSmall 0x0
int style TextAppearance_M3_Sys_Typescale_LabelLarge 0x0
int style TextAppearance_M3_Sys_Typescale_LabelMedium 0x0
int style TextAppearance_M3_Sys_Typescale_LabelSmall 0x0
int style TextAppearance_M3_Sys_Typescale_TitleLarge 0x0
int style TextAppearance_M3_Sys_Typescale_TitleMedium 0x0
int style TextAppearance_M3_Sys_Typescale_TitleSmall 0x0
int style TextAppearance_Material3_ActionBar_Subtitle 0x0
int style TextAppearance_Material3_ActionBar_Title 0x0
int style TextAppearance_Material3_BodyLarge 0x0
int style TextAppearance_Material3_BodyMedium 0x0
int style TextAppearance_Material3_BodySmall 0x0
int style TextAppearance_Material3_DisplayLarge 0x0
int style TextAppearance_Material3_DisplayMedium 0x0
int style TextAppearance_Material3_DisplaySmall 0x0
int style TextAppearance_Material3_HeadlineLarge 0x0
int style TextAppearance_Material3_HeadlineMedium 0x0
int style TextAppearance_Material3_HeadlineSmall 0x0
int style TextAppearance_Material3_LabelLarge 0x0
int style TextAppearance_Material3_LabelMedium 0x0
int style TextAppearance_Material3_LabelSmall 0x0
int style TextAppearance_Material3_MaterialTimePicker_Title 0x0
int style TextAppearance_Material3_SearchBar 0x0
int style TextAppearance_Material3_SearchView 0x0
int style TextAppearance_Material3_SearchView_Prefix 0x0
int style TextAppearance_Material3_TitleLarge 0x0
int style TextAppearance_Material3_TitleMedium 0x0
int style TextAppearance_Material3_TitleSmall 0x0
int style TextAppearance_MaterialComponents_Badge 0x0
int style TextAppearance_MaterialComponents_Body1 0x0
int style TextAppearance_MaterialComponents_Body2 0x0
int style TextAppearance_MaterialComponents_Button 0x0
int style TextAppearance_MaterialComponents_Caption 0x0
int style TextAppearance_MaterialComponents_Chip 0x0
int style TextAppearance_MaterialComponents_Headline1 0x0
int style TextAppearance_MaterialComponents_Headline2 0x0
int style TextAppearance_MaterialComponents_Headline3 0x0
int style TextAppearance_MaterialComponents_Headline4 0x0
int style TextAppearance_MaterialComponents_Headline5 0x0
int style TextAppearance_MaterialComponents_Headline6 0x0
int style TextAppearance_MaterialComponents_Overline 0x0
int style TextAppearance_MaterialComponents_Subtitle1 0x0
int style TextAppearance_MaterialComponents_Subtitle2 0x0
int style TextAppearance_MaterialComponents_TimePicker_Title 0x0
int style TextAppearance_MaterialComponents_Tooltip 0x0
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
int style ThemeOverlay_AppCompat 0x0
int style ThemeOverlay_AppCompat_ActionBar 0x0
int style ThemeOverlay_AppCompat_Dark 0x0
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x0
int style ThemeOverlay_AppCompat_DayNight 0x0
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x0
int style ThemeOverlay_AppCompat_Dialog 0x0
int style ThemeOverlay_AppCompat_Dialog_Alert 0x0
int style ThemeOverlay_AppCompat_Light 0x0
int style ThemeOverlay_Design_TextInputEditText 0x0
int style ThemeOverlay_Material3 0x0
int style ThemeOverlay_Material3_ActionBar 0x0
int style ThemeOverlay_Material3_AutoCompleteTextView 0x0
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox 0x0
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox_Dense 0x0
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox 0x0
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x0
int style ThemeOverlay_Material3_BottomAppBar 0x0
int style ThemeOverlay_Material3_BottomAppBar_Legacy 0x0
int style ThemeOverlay_Material3_BottomNavigationView 0x0
int style ThemeOverlay_Material3_BottomSheetDialog 0x0
int style ThemeOverlay_Material3_Button 0x0
int style ThemeOverlay_Material3_Button_ElevatedButton 0x0
int style ThemeOverlay_Material3_Button_IconButton 0x0
int style ThemeOverlay_Material3_Button_IconButton_Filled 0x0
int style ThemeOverlay_Material3_Button_IconButton_Filled_Tonal 0x0
int style ThemeOverlay_Material3_Button_TextButton 0x0
int style ThemeOverlay_Material3_Button_TextButton_Snackbar 0x0
int style ThemeOverlay_Material3_Button_TonalButton 0x0
int style ThemeOverlay_Material3_Chip 0x0
int style ThemeOverlay_Material3_Chip_Assist 0x0
int style ThemeOverlay_Material3_Dark 0x0
int style ThemeOverlay_Material3_Dark_ActionBar 0x0
int style ThemeOverlay_Material3_DayNight_BottomSheetDialog 0x0
int style ThemeOverlay_Material3_DayNight_SideSheetDialog 0x0
int style ThemeOverlay_Material3_Dialog 0x0
int style ThemeOverlay_Material3_Dialog_Alert 0x0
int style ThemeOverlay_Material3_Dialog_Alert_Framework 0x0
int style ThemeOverlay_Material3_DynamicColors_Dark 0x0
int style ThemeOverlay_Material3_DynamicColors_DayNight 0x0
int style ThemeOverlay_Material3_DynamicColors_Light 0x0
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Primary 0x0
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Secondary 0x0
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Surface 0x0
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Tertiary 0x0
int style ThemeOverlay_Material3_FloatingActionButton_Primary 0x0
int style ThemeOverlay_Material3_FloatingActionButton_Secondary 0x0
int style ThemeOverlay_Material3_FloatingActionButton_Surface 0x0
int style ThemeOverlay_Material3_FloatingActionButton_Tertiary 0x0
int style ThemeOverlay_Material3_HarmonizedColors 0x0
int style ThemeOverlay_Material3_HarmonizedColors_Empty 0x0
int style ThemeOverlay_Material3_Light 0x0
int style ThemeOverlay_Material3_Light_Dialog_Alert_Framework 0x0
int style ThemeOverlay_Material3_MaterialAlertDialog 0x0
int style ThemeOverlay_Material3_MaterialAlertDialog_Centered 0x0
int style ThemeOverlay_Material3_MaterialCalendar 0x0
int style ThemeOverlay_Material3_MaterialCalendar_Fullscreen 0x0
int style ThemeOverlay_Material3_MaterialCalendar_HeaderCancelButton 0x0
int style ThemeOverlay_Material3_MaterialTimePicker 0x0
int style ThemeOverlay_Material3_MaterialTimePicker_Display_TextInputEditText 0x0
int style ThemeOverlay_Material3_NavigationRailView 0x0
int style ThemeOverlay_Material3_NavigationView 0x0
int style ThemeOverlay_Material3_PersonalizedColors 0x0
int style ThemeOverlay_Material3_Search 0x0
int style ThemeOverlay_Material3_SideSheetDialog 0x0
int style ThemeOverlay_Material3_Snackbar 0x0
int style ThemeOverlay_Material3_TabLayout 0x0
int style ThemeOverlay_Material3_TextInputEditText 0x0
int style ThemeOverlay_Material3_TextInputEditText_FilledBox 0x0
int style ThemeOverlay_Material3_TextInputEditText_FilledBox_Dense 0x0
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox 0x0
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox_Dense 0x0
int style ThemeOverlay_Material3_Toolbar_Surface 0x0
int style ThemeOverlay_MaterialAlertDialog_Material3_Title_Icon 0x0
int style ThemeOverlay_MaterialComponents 0x0
int style ThemeOverlay_MaterialComponents_ActionBar 0x0
int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x0
int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x0
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x0
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x0
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x0
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x0
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x0
int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x0
int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x0
int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x0
int style ThemeOverlay_MaterialComponents_Dark 0x0
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x0
int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x0
int style ThemeOverlay_MaterialComponents_Dialog 0x0
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x0
int style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x0
int style ThemeOverlay_MaterialComponents_Light 0x0
int style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x0
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x0
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x0
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x0
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x0
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x0
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x0
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x0
int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x0
int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x0
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x0
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x0
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x0
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x0
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x0
int style ThemeOverlay_MaterialComponents_TimePicker 0x0
int style ThemeOverlay_MaterialComponents_TimePicker_Display 0x0
int style ThemeOverlay_MaterialComponents_TimePicker_Display_TextInputEditText 0x0
int style ThemeOverlay_MaterialComponents_Toolbar_Popup_Primary 0x0
int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x0
int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x0
int style Theme_AppCompat 0x0
int style Theme_AppCompat_CompactMenu 0x0
int style Theme_AppCompat_DayNight 0x0
int style Theme_AppCompat_DayNight_DarkActionBar 0x0
int style Theme_AppCompat_DayNight_Dialog 0x0
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x0
int style Theme_AppCompat_DayNight_Dialog_Alert 0x0
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x0
int style Theme_AppCompat_DayNight_NoActionBar 0x0
int style Theme_AppCompat_Dialog 0x0
int style Theme_AppCompat_DialogWhenLarge 0x0
int style Theme_AppCompat_Dialog_Alert 0x0
int style Theme_AppCompat_Dialog_MinWidth 0x0
int style Theme_AppCompat_Empty 0x0
int style Theme_AppCompat_Light 0x0
int style Theme_AppCompat_Light_DarkActionBar 0x0
int style Theme_AppCompat_Light_Dialog 0x0
int style Theme_AppCompat_Light_DialogWhenLarge 0x0
int style Theme_AppCompat_Light_Dialog_Alert 0x0
int style Theme_AppCompat_Light_Dialog_MinWidth 0x0
int style Theme_AppCompat_Light_NoActionBar 0x0
int style Theme_AppCompat_NoActionBar 0x0
int style Theme_Design 0x0
int style Theme_Design_BottomSheetDialog 0x0
int style Theme_Design_Light 0x0
int style Theme_Design_Light_BottomSheetDialog 0x0
int style Theme_Design_Light_NoActionBar 0x0
int style Theme_Design_NoActionBar 0x0
int style Theme_Material3_Dark 0x0
int style Theme_Material3_Dark_BottomSheetDialog 0x0
int style Theme_Material3_Dark_Dialog 0x0
int style Theme_Material3_Dark_DialogWhenLarge 0x0
int style Theme_Material3_Dark_Dialog_Alert 0x0
int style Theme_Material3_Dark_Dialog_MinWidth 0x0
int style Theme_Material3_Dark_NoActionBar 0x0
int style Theme_Material3_Dark_SideSheetDialog 0x0
int style Theme_Material3_DayNight 0x0
int style Theme_Material3_DayNight_BottomSheetDialog 0x0
int style Theme_Material3_DayNight_Dialog 0x0
int style Theme_Material3_DayNight_DialogWhenLarge 0x0
int style Theme_Material3_DayNight_Dialog_Alert 0x0
int style Theme_Material3_DayNight_Dialog_MinWidth 0x0
int style Theme_Material3_DayNight_NoActionBar 0x0
int style Theme_Material3_DayNight_SideSheetDialog 0x0
int style Theme_Material3_DynamicColors_Dark 0x0
int style Theme_Material3_DynamicColors_DayNight 0x0
int style Theme_Material3_DynamicColors_Light 0x0
int style Theme_Material3_Light 0x0
int style Theme_Material3_Light_BottomSheetDialog 0x0
int style Theme_Material3_Light_Dialog 0x0
int style Theme_Material3_Light_DialogWhenLarge 0x0
int style Theme_Material3_Light_Dialog_Alert 0x0
int style Theme_Material3_Light_Dialog_MinWidth 0x0
int style Theme_Material3_Light_NoActionBar 0x0
int style Theme_Material3_Light_SideSheetDialog 0x0
int style Theme_MaterialComponents 0x0
int style Theme_MaterialComponents_BottomSheetDialog 0x0
int style Theme_MaterialComponents_Bridge 0x0
int style Theme_MaterialComponents_CompactMenu 0x0
int style Theme_MaterialComponents_DayNight 0x0
int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x0
int style Theme_MaterialComponents_DayNight_Bridge 0x0
int style Theme_MaterialComponents_DayNight_DarkActionBar 0x0
int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x0
int style Theme_MaterialComponents_DayNight_Dialog 0x0
int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x0
int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x0
int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x0
int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x0
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x0
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x0
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x0
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x0
int style Theme_MaterialComponents_DayNight_NoActionBar 0x0
int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x0
int style Theme_MaterialComponents_Dialog 0x0
int style Theme_MaterialComponents_DialogWhenLarge 0x0
int style Theme_MaterialComponents_Dialog_Alert 0x0
int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x0
int style Theme_MaterialComponents_Dialog_Bridge 0x0
int style Theme_MaterialComponents_Dialog_FixedSize 0x0
int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x0
int style Theme_MaterialComponents_Dialog_MinWidth 0x0
int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x0
int style Theme_MaterialComponents_Light 0x0
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x0
int style Theme_MaterialComponents_Light_Bridge 0x0
int style Theme_MaterialComponents_Light_DarkActionBar 0x0
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x0
int style Theme_MaterialComponents_Light_Dialog 0x0
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x0
int style Theme_MaterialComponents_Light_Dialog_Alert 0x0
int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x0
int style Theme_MaterialComponents_Light_Dialog_Bridge 0x0
int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x0
int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x0
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x0
int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x0
int style Theme_MaterialComponents_Light_NoActionBar 0x0
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x0
int style Theme_MaterialComponents_NoActionBar 0x0
int style Theme_MaterialComponents_NoActionBar_Bridge 0x0
int style Widget_AppCompat_ActionBar 0x0
int style Widget_AppCompat_ActionBar_Solid 0x0
int style Widget_AppCompat_ActionBar_TabBar 0x0
int style Widget_AppCompat_ActionBar_TabText 0x0
int style Widget_AppCompat_ActionBar_TabView 0x0
int style Widget_AppCompat_ActionButton 0x0
int style Widget_AppCompat_ActionButton_CloseMode 0x0
int style Widget_AppCompat_ActionButton_Overflow 0x0
int style Widget_AppCompat_ActionMode 0x0
int style Widget_AppCompat_ActivityChooserView 0x0
int style Widget_AppCompat_AutoCompleteTextView 0x0
int style Widget_AppCompat_Button 0x0
int style Widget_AppCompat_ButtonBar 0x0
int style Widget_AppCompat_ButtonBar_AlertDialog 0x0
int style Widget_AppCompat_Button_Borderless 0x0
int style Widget_AppCompat_Button_Borderless_Colored 0x0
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
int style Widget_AppCompat_Button_Colored 0x0
int style Widget_AppCompat_Button_Small 0x0
int style Widget_AppCompat_CompoundButton_CheckBox 0x0
int style Widget_AppCompat_CompoundButton_RadioButton 0x0
int style Widget_AppCompat_CompoundButton_Switch 0x0
int style Widget_AppCompat_DrawerArrowToggle 0x0
int style Widget_AppCompat_DropDownItem_Spinner 0x0
int style Widget_AppCompat_EditText 0x0
int style Widget_AppCompat_ImageButton 0x0
int style Widget_AppCompat_Light_ActionBar 0x0
int style Widget_AppCompat_Light_ActionBar_Solid 0x0
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x0
int style Widget_AppCompat_Light_ActionBar_TabBar 0x0
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x0
int style Widget_AppCompat_Light_ActionBar_TabText 0x0
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
int style Widget_AppCompat_Light_ActionBar_TabView 0x0
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x0
int style Widget_AppCompat_Light_ActionButton 0x0
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x0
int style Widget_AppCompat_Light_ActionButton_Overflow 0x0
int style Widget_AppCompat_Light_ActionMode_Inverse 0x0
int style Widget_AppCompat_Light_ActivityChooserView 0x0
int style Widget_AppCompat_Light_AutoCompleteTextView 0x0
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x0
int style Widget_AppCompat_Light_ListPopupWindow 0x0
int style Widget_AppCompat_Light_ListView_DropDown 0x0
int style Widget_AppCompat_Light_PopupMenu 0x0
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x0
int style Widget_AppCompat_Light_SearchView 0x0
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x0
int style Widget_AppCompat_ListMenuView 0x0
int style Widget_AppCompat_ListPopupWindow 0x0
int style Widget_AppCompat_ListView 0x0
int style Widget_AppCompat_ListView_DropDown 0x0
int style Widget_AppCompat_ListView_Menu 0x0
int style Widget_AppCompat_PopupMenu 0x0
int style Widget_AppCompat_PopupMenu_Overflow 0x0
int style Widget_AppCompat_PopupWindow 0x0
int style Widget_AppCompat_ProgressBar 0x0
int style Widget_AppCompat_ProgressBar_Horizontal 0x0
int style Widget_AppCompat_RatingBar 0x0
int style Widget_AppCompat_RatingBar_Indicator 0x0
int style Widget_AppCompat_RatingBar_Small 0x0
int style Widget_AppCompat_SearchView 0x0
int style Widget_AppCompat_SearchView_ActionBar 0x0
int style Widget_AppCompat_SeekBar 0x0
int style Widget_AppCompat_SeekBar_Discrete 0x0
int style Widget_AppCompat_Spinner 0x0
int style Widget_AppCompat_Spinner_DropDown 0x0
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x0
int style Widget_AppCompat_Spinner_Underlined 0x0
int style Widget_AppCompat_TextView 0x0
int style Widget_AppCompat_TextView_SpinnerItem 0x0
int style Widget_AppCompat_Toolbar 0x0
int style Widget_AppCompat_Toolbar_Button_Navigation 0x0
int style Widget_Compat_NotificationActionContainer 0x0
int style Widget_Compat_NotificationActionText 0x0
int style Widget_Design_AppBarLayout 0x0
int style Widget_Design_BottomNavigationView 0x0
int style Widget_Design_BottomSheet_Modal 0x0
int style Widget_Design_CollapsingToolbar 0x0
int style Widget_Design_FloatingActionButton 0x0
int style Widget_Design_NavigationView 0x0
int style Widget_Design_ScrimInsetsFrameLayout 0x0
int style Widget_Design_Snackbar 0x0
int style Widget_Design_TabLayout 0x0
int style Widget_Design_TextInputEditText 0x0
int style Widget_Design_TextInputLayout 0x0
int style Widget_Material3_ActionBar_Solid 0x0
int style Widget_Material3_ActionMode 0x0
int style Widget_Material3_AppBarLayout 0x0
int style Widget_Material3_AutoCompleteTextView_FilledBox 0x0
int style Widget_Material3_AutoCompleteTextView_FilledBox_Dense 0x0
int style Widget_Material3_AutoCompleteTextView_OutlinedBox 0x0
int style Widget_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x0
int style Widget_Material3_Badge 0x0
int style Widget_Material3_Badge_AdjustToBounds 0x0
int style Widget_Material3_BottomAppBar 0x0
int style Widget_Material3_BottomAppBar_Button_Navigation 0x0
int style Widget_Material3_BottomAppBar_Legacy 0x0
int style Widget_Material3_BottomNavigationView 0x0
int style Widget_Material3_BottomNavigationView_ActiveIndicator 0x0
int style Widget_Material3_BottomNavigation_Badge 0x0
int style Widget_Material3_BottomSheet 0x0
int style Widget_Material3_BottomSheet_DragHandle 0x0
int style Widget_Material3_BottomSheet_Modal 0x0
int style Widget_Material3_Button 0x0
int style Widget_Material3_Button_ElevatedButton 0x0
int style Widget_Material3_Button_ElevatedButton_Icon 0x0
int style Widget_Material3_Button_Icon 0x0
int style Widget_Material3_Button_IconButton 0x0
int style Widget_Material3_Button_IconButton_Filled 0x0
int style Widget_Material3_Button_IconButton_Filled_Tonal 0x0
int style Widget_Material3_Button_IconButton_Outlined 0x0
int style Widget_Material3_Button_OutlinedButton 0x0
int style Widget_Material3_Button_OutlinedButton_Icon 0x0
int style Widget_Material3_Button_TextButton 0x0
int style Widget_Material3_Button_TextButton_Dialog 0x0
int style Widget_Material3_Button_TextButton_Dialog_Flush 0x0
int style Widget_Material3_Button_TextButton_Dialog_Icon 0x0
int style Widget_Material3_Button_TextButton_Icon 0x0
int style Widget_Material3_Button_TextButton_Snackbar 0x0
int style Widget_Material3_Button_TonalButton 0x0
int style Widget_Material3_Button_TonalButton_Icon 0x0
int style Widget_Material3_Button_UnelevatedButton 0x0
int style Widget_Material3_CardView_Elevated 0x0
int style Widget_Material3_CardView_Filled 0x0
int style Widget_Material3_CardView_Outlined 0x0
int style Widget_Material3_CheckedTextView 0x0
int style Widget_Material3_ChipGroup 0x0
int style Widget_Material3_Chip_Assist 0x0
int style Widget_Material3_Chip_Assist_Elevated 0x0
int style Widget_Material3_Chip_Filter 0x0
int style Widget_Material3_Chip_Filter_Elevated 0x0
int style Widget_Material3_Chip_Input 0x0
int style Widget_Material3_Chip_Input_Elevated 0x0
int style Widget_Material3_Chip_Input_Icon 0x0
int style Widget_Material3_Chip_Input_Icon_Elevated 0x0
int style Widget_Material3_Chip_Suggestion 0x0
int style Widget_Material3_Chip_Suggestion_Elevated 0x0
int style Widget_Material3_CircularProgressIndicator 0x0
int style Widget_Material3_CircularProgressIndicator_ExtraSmall 0x0
int style Widget_Material3_CircularProgressIndicator_Medium 0x0
int style Widget_Material3_CircularProgressIndicator_Small 0x0
int style Widget_Material3_CollapsingToolbar 0x0
int style Widget_Material3_CollapsingToolbar_Large 0x0
int style Widget_Material3_CollapsingToolbar_Medium 0x0
int style Widget_Material3_CompoundButton_CheckBox 0x0
int style Widget_Material3_CompoundButton_MaterialSwitch 0x0
int style Widget_Material3_CompoundButton_RadioButton 0x0
int style Widget_Material3_CompoundButton_Switch 0x0
int style Widget_Material3_DrawerLayout 0x0
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Primary 0x0
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Secondary 0x0
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Surface 0x0
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Tertiary 0x0
int style Widget_Material3_ExtendedFloatingActionButton_Primary 0x0
int style Widget_Material3_ExtendedFloatingActionButton_Secondary 0x0
int style Widget_Material3_ExtendedFloatingActionButton_Surface 0x0
int style Widget_Material3_ExtendedFloatingActionButton_Tertiary 0x0
int style Widget_Material3_FloatingActionButton_Large_Primary 0x0
int style Widget_Material3_FloatingActionButton_Large_Secondary 0x0
int style Widget_Material3_FloatingActionButton_Large_Surface 0x0
int style Widget_Material3_FloatingActionButton_Large_Tertiary 0x0
int style Widget_Material3_FloatingActionButton_Primary 0x0
int style Widget_Material3_FloatingActionButton_Secondary 0x0
int style Widget_Material3_FloatingActionButton_Small_Primary 0x0
int style Widget_Material3_FloatingActionButton_Small_Secondary 0x0
int style Widget_Material3_FloatingActionButton_Small_Surface 0x0
int style Widget_Material3_FloatingActionButton_Small_Tertiary 0x0
int style Widget_Material3_FloatingActionButton_Surface 0x0
int style Widget_Material3_FloatingActionButton_Tertiary 0x0
int style Widget_Material3_Light_ActionBar_Solid 0x0
int style Widget_Material3_LinearProgressIndicator 0x0
int style Widget_Material3_MaterialButtonToggleGroup 0x0
int style Widget_Material3_MaterialCalendar 0x0
int style Widget_Material3_MaterialCalendar_Day 0x0
int style Widget_Material3_MaterialCalendar_DayOfWeekLabel 0x0
int style Widget_Material3_MaterialCalendar_DayTextView 0x0
int style Widget_Material3_MaterialCalendar_Day_Invalid 0x0
int style Widget_Material3_MaterialCalendar_Day_Selected 0x0
int style Widget_Material3_MaterialCalendar_Day_Today 0x0
int style Widget_Material3_MaterialCalendar_Fullscreen 0x0
int style Widget_Material3_MaterialCalendar_HeaderCancelButton 0x0
int style Widget_Material3_MaterialCalendar_HeaderDivider 0x0
int style Widget_Material3_MaterialCalendar_HeaderLayout 0x0
int style Widget_Material3_MaterialCalendar_HeaderLayout_Fullscreen 0x0
int style Widget_Material3_MaterialCalendar_HeaderSelection 0x0
int style Widget_Material3_MaterialCalendar_HeaderSelection_Fullscreen 0x0
int style Widget_Material3_MaterialCalendar_HeaderTitle 0x0
int style Widget_Material3_MaterialCalendar_HeaderToggleButton 0x0
int style Widget_Material3_MaterialCalendar_Item 0x0
int style Widget_Material3_MaterialCalendar_MonthNavigationButton 0x0
int style Widget_Material3_MaterialCalendar_MonthTextView 0x0
int style Widget_Material3_MaterialCalendar_Year 0x0
int style Widget_Material3_MaterialCalendar_YearNavigationButton 0x0
int style Widget_Material3_MaterialCalendar_Year_Selected 0x0
int style Widget_Material3_MaterialCalendar_Year_Today 0x0
int style Widget_Material3_MaterialDivider 0x0
int style Widget_Material3_MaterialDivider_Heavy 0x0
int style Widget_Material3_MaterialTimePicker 0x0
int style Widget_Material3_MaterialTimePicker_Button 0x0
int style Widget_Material3_MaterialTimePicker_Clock 0x0
int style Widget_Material3_MaterialTimePicker_Display 0x0
int style Widget_Material3_MaterialTimePicker_Display_Divider 0x0
int style Widget_Material3_MaterialTimePicker_Display_HelperText 0x0
int style Widget_Material3_MaterialTimePicker_Display_TextInputEditText 0x0
int style Widget_Material3_MaterialTimePicker_Display_TextInputLayout 0x0
int style Widget_Material3_MaterialTimePicker_ImageButton 0x0
int style Widget_Material3_NavigationRailView 0x0
int style Widget_Material3_NavigationRailView_ActiveIndicator 0x0
int style Widget_Material3_NavigationRailView_Badge 0x0
int style Widget_Material3_NavigationView 0x0
int style Widget_Material3_PopupMenu 0x0
int style Widget_Material3_PopupMenu_ContextMenu 0x0
int style Widget_Material3_PopupMenu_ListPopupWindow 0x0
int style Widget_Material3_PopupMenu_Overflow 0x0
int style Widget_Material3_SearchBar 0x0
int style Widget_Material3_SearchBar_Outlined 0x0
int style Widget_Material3_SearchView 0x0
int style Widget_Material3_SearchView_Prefix 0x0
int style Widget_Material3_SearchView_Toolbar 0x0
int style Widget_Material3_Search_ActionButton_Overflow 0x0
int style Widget_Material3_Search_Toolbar_Button_Navigation 0x0
int style Widget_Material3_SideSheet 0x0
int style Widget_Material3_SideSheet_Detached 0x0
int style Widget_Material3_SideSheet_Modal 0x0
int style Widget_Material3_SideSheet_Modal_Detached 0x0
int style Widget_Material3_Slider 0x0
int style Widget_Material3_Slider_Label 0x0
int style Widget_Material3_Snackbar 0x0
int style Widget_Material3_Snackbar_FullWidth 0x0
int style Widget_Material3_Snackbar_TextView 0x0
int style Widget_Material3_TabLayout 0x0
int style Widget_Material3_TabLayout_OnSurface 0x0
int style Widget_Material3_TabLayout_Secondary 0x0
int style Widget_Material3_TextInputEditText_FilledBox 0x0
int style Widget_Material3_TextInputEditText_FilledBox_Dense 0x0
int style Widget_Material3_TextInputEditText_OutlinedBox 0x0
int style Widget_Material3_TextInputEditText_OutlinedBox_Dense 0x0
int style Widget_Material3_TextInputLayout_FilledBox 0x0
int style Widget_Material3_TextInputLayout_FilledBox_Dense 0x0
int style Widget_Material3_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x0
int style Widget_Material3_TextInputLayout_FilledBox_ExposedDropdownMenu 0x0
int style Widget_Material3_TextInputLayout_OutlinedBox 0x0
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense 0x0
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x0
int style Widget_Material3_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x0
int style Widget_Material3_Toolbar 0x0
int style Widget_Material3_Toolbar_OnSurface 0x0
int style Widget_Material3_Toolbar_Surface 0x0
int style Widget_Material3_Tooltip 0x0
int style Widget_MaterialComponents_ActionBar_Primary 0x0
int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x0
int style Widget_MaterialComponents_ActionBar_Solid 0x0
int style Widget_MaterialComponents_ActionBar_Surface 0x0
int style Widget_MaterialComponents_ActionMode 0x0
int style Widget_MaterialComponents_AppBarLayout_Primary 0x0
int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x0
int style Widget_MaterialComponents_AppBarLayout_Surface 0x0
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x0
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x0
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x0
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x0
int style Widget_MaterialComponents_Badge 0x0
int style Widget_MaterialComponents_BottomAppBar 0x0
int style Widget_MaterialComponents_BottomAppBar_Colored 0x0
int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x0
int style Widget_MaterialComponents_BottomNavigationView 0x0
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x0
int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x0
int style Widget_MaterialComponents_BottomSheet 0x0
int style Widget_MaterialComponents_BottomSheet_Modal 0x0
int style Widget_MaterialComponents_Button 0x0
int style Widget_MaterialComponents_Button_Icon 0x0
int style Widget_MaterialComponents_Button_OutlinedButton 0x0
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x0
int style Widget_MaterialComponents_Button_TextButton 0x0
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x0
int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x0
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x0
int style Widget_MaterialComponents_Button_TextButton_Icon 0x0
int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x0
int style Widget_MaterialComponents_Button_UnelevatedButton 0x0
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x0
int style Widget_MaterialComponents_CardView 0x0
int style Widget_MaterialComponents_CheckedTextView 0x0
int style Widget_MaterialComponents_ChipGroup 0x0
int style Widget_MaterialComponents_Chip_Action 0x0
int style Widget_MaterialComponents_Chip_Choice 0x0
int style Widget_MaterialComponents_Chip_Entry 0x0
int style Widget_MaterialComponents_Chip_Filter 0x0
int style Widget_MaterialComponents_CircularProgressIndicator 0x0
int style Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall 0x0
int style Widget_MaterialComponents_CircularProgressIndicator_Medium 0x0
int style Widget_MaterialComponents_CircularProgressIndicator_Small 0x0
int style Widget_MaterialComponents_CollapsingToolbar 0x0
int style Widget_MaterialComponents_CompoundButton_CheckBox 0x0
int style Widget_MaterialComponents_CompoundButton_RadioButton 0x0
int style Widget_MaterialComponents_CompoundButton_Switch 0x0
int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x0
int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x0
int style Widget_MaterialComponents_FloatingActionButton 0x0
int style Widget_MaterialComponents_Light_ActionBar_Solid 0x0
int style Widget_MaterialComponents_LinearProgressIndicator 0x0
int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x0
int style Widget_MaterialComponents_MaterialCalendar 0x0
int style Widget_MaterialComponents_MaterialCalendar_Day 0x0
int style Widget_MaterialComponents_MaterialCalendar_DayOfWeekLabel 0x0
int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x0
int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x0
int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x0
int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x0
int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x0
int style Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton 0x0
int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x0
int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x0
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x0
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout_Fullscreen 0x0
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x0
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x0
int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x0
int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x0
int style Widget_MaterialComponents_MaterialCalendar_Item 0x0
int style Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton 0x0
int style Widget_MaterialComponents_MaterialCalendar_MonthTextView 0x0
int style Widget_MaterialComponents_MaterialCalendar_Year 0x0
int style Widget_MaterialComponents_MaterialCalendar_YearNavigationButton 0x0
int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x0
int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x0
int style Widget_MaterialComponents_MaterialDivider 0x0
int style Widget_MaterialComponents_NavigationRailView 0x0
int style Widget_MaterialComponents_NavigationRailView_Colored 0x0
int style Widget_MaterialComponents_NavigationRailView_Colored_Compact 0x0
int style Widget_MaterialComponents_NavigationRailView_Compact 0x0
int style Widget_MaterialComponents_NavigationRailView_PrimarySurface 0x0
int style Widget_MaterialComponents_NavigationView 0x0
int style Widget_MaterialComponents_PopupMenu 0x0
int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x0
int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x0
int style Widget_MaterialComponents_PopupMenu_Overflow 0x0
int style Widget_MaterialComponents_ProgressIndicator 0x0
int style Widget_MaterialComponents_ShapeableImageView 0x0
int style Widget_MaterialComponents_Slider 0x0
int style Widget_MaterialComponents_Snackbar 0x0
int style Widget_MaterialComponents_Snackbar_FullWidth 0x0
int style Widget_MaterialComponents_Snackbar_TextView 0x0
int style Widget_MaterialComponents_TabLayout 0x0
int style Widget_MaterialComponents_TabLayout_Colored 0x0
int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x0
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x0
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x0
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x0
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x0
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x0
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x0
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x0
int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x0
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x0
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x0
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x0
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x0
int style Widget_MaterialComponents_TextView 0x0
int style Widget_MaterialComponents_TimePicker 0x0
int style Widget_MaterialComponents_TimePicker_Button 0x0
int style Widget_MaterialComponents_TimePicker_Clock 0x0
int style Widget_MaterialComponents_TimePicker_Display 0x0
int style Widget_MaterialComponents_TimePicker_Display_Divider 0x0
int style Widget_MaterialComponents_TimePicker_Display_HelperText 0x0
int style Widget_MaterialComponents_TimePicker_Display_TextInputEditText 0x0
int style Widget_MaterialComponents_TimePicker_Display_TextInputLayout 0x0
int style Widget_MaterialComponents_TimePicker_ImageButton 0x0
int style Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance 0x0
int style Widget_MaterialComponents_Toolbar 0x0
int style Widget_MaterialComponents_Toolbar_Primary 0x0
int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x0
int style Widget_MaterialComponents_Toolbar_Surface 0x0
int style Widget_MaterialComponents_Tooltip 0x0
int style Widget_Support_CoordinatorLayout 0x0
int[] styleable ActionBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x10100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView {  }
int[] styleable ActionMode { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x0, 0x0 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityFilter { 0x0, 0x0 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityRule { 0x0, 0x0 }
int styleable ActivityRule_alwaysExpand 0
int styleable ActivityRule_tag 1
int[] styleable AlertDialog { 0x10100f2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
int styleable AnimatedStateListDrawableCompat_android_constantSize 0
int styleable AnimatedStateListDrawableCompat_android_dither 1
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
int styleable AnimatedStateListDrawableCompat_android_visible 5
int[] styleable AnimatedStateListDrawableItem { 0x1010199, 0x10100d0 }
int styleable AnimatedStateListDrawableItem_android_drawable 0
int styleable AnimatedStateListDrawableItem_android_id 1
int[] styleable AnimatedStateListDrawableTransition { 0x1010199, 0x101044a, 0x101044b, 0x1010449 }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_fromId 1
int styleable AnimatedStateListDrawableTransition_android_reversible 2
int styleable AnimatedStateListDrawableTransition_android_toId 3
int[] styleable AppBarLayout { 0x10100d4, 0x1010540, 0x101048f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_keyboardNavigationCluster 1
int styleable AppBarLayout_android_touchscreenBlocksFocus 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollColor 6
int styleable AppBarLayout_liftOnScrollTargetViewId 7
int styleable AppBarLayout_statusBarForeground 8
int[] styleable AppBarLayoutStates { 0x0, 0x0, 0x0, 0x0 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x0, 0x0, 0x0 }
int styleable AppBarLayout_Layout_layout_scrollEffect 0
int styleable AppBarLayout_Layout_layout_scrollFlags 1
int styleable AppBarLayout_Layout_layout_scrollInterpolator 2
int[] styleable AppCompatEmojiHelper {  }
int[] styleable AppCompatImageView { 0x1010119, 0x0, 0x0, 0x0 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x1010142, 0x0, 0x0, 0x0 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x101016e, 0x1010393, 0x101016f, 0x1010170, 0x1010392, 0x101016d, 0x1010034 }
int styleable AppCompatTextHelper_android_drawableBottom 0
int styleable AppCompatTextHelper_android_drawableEnd 1
int styleable AppCompatTextHelper_android_drawableLeft 2
int styleable AppCompatTextHelper_android_drawableRight 3
int styleable AppCompatTextHelper_android_drawableStart 4
int styleable AppCompatTextHelper_android_drawableTop 5
int styleable AppCompatTextHelper_android_textAppearance 6
int[] styleable AppCompatTextView { 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10100ae, 0x1010057, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable AppCompatTheme_actionBarDivider 0
int styleable AppCompatTheme_actionBarItemBackground 1
int styleable AppCompatTheme_actionBarPopupTheme 2
int styleable AppCompatTheme_actionBarSize 3
int styleable AppCompatTheme_actionBarSplitStyle 4
int styleable AppCompatTheme_actionBarStyle 5
int styleable AppCompatTheme_actionBarTabBarStyle 6
int styleable AppCompatTheme_actionBarTabStyle 7
int styleable AppCompatTheme_actionBarTabTextStyle 8
int styleable AppCompatTheme_actionBarTheme 9
int styleable AppCompatTheme_actionBarWidgetTheme 10
int styleable AppCompatTheme_actionButtonStyle 11
int styleable AppCompatTheme_actionDropDownStyle 12
int styleable AppCompatTheme_actionMenuTextAppearance 13
int styleable AppCompatTheme_actionMenuTextColor 14
int styleable AppCompatTheme_actionModeBackground 15
int styleable AppCompatTheme_actionModeCloseButtonStyle 16
int styleable AppCompatTheme_actionModeCloseContentDescription 17
int styleable AppCompatTheme_actionModeCloseDrawable 18
int styleable AppCompatTheme_actionModeCopyDrawable 19
int styleable AppCompatTheme_actionModeCutDrawable 20
int styleable AppCompatTheme_actionModeFindDrawable 21
int styleable AppCompatTheme_actionModePasteDrawable 22
int styleable AppCompatTheme_actionModePopupWindowStyle 23
int styleable AppCompatTheme_actionModeSelectAllDrawable 24
int styleable AppCompatTheme_actionModeShareDrawable 25
int styleable AppCompatTheme_actionModeSplitBackground 26
int styleable AppCompatTheme_actionModeStyle 27
int styleable AppCompatTheme_actionModeTheme 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_android_windowAnimationStyle 37
int styleable AppCompatTheme_android_windowIsFloating 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable Badge { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Badge_autoAdjustToWithinGrandparentBounds 0
int styleable Badge_backgroundColor 1
int styleable Badge_badgeGravity 2
int styleable Badge_badgeHeight 3
int styleable Badge_badgeRadius 4
int styleable Badge_badgeShapeAppearance 5
int styleable Badge_badgeShapeAppearanceOverlay 6
int styleable Badge_badgeText 7
int styleable Badge_badgeTextAppearance 8
int styleable Badge_badgeTextColor 9
int styleable Badge_badgeVerticalPadding 10
int styleable Badge_badgeWidePadding 11
int styleable Badge_badgeWidth 12
int styleable Badge_badgeWithTextHeight 13
int styleable Badge_badgeWithTextRadius 14
int styleable Badge_badgeWithTextShapeAppearance 15
int styleable Badge_badgeWithTextShapeAppearanceOverlay 16
int styleable Badge_badgeWithTextWidth 17
int styleable Badge_horizontalOffset 18
int styleable Badge_horizontalOffsetWithText 19
int styleable Badge_largeFontVerticalOffsetAdjustment 20
int styleable Badge_maxCharacterCount 21
int styleable Badge_maxNumber 22
int styleable Badge_number 23
int styleable Badge_offsetAlignmentMode 24
int styleable Badge_verticalOffset 25
int styleable Badge_verticalOffsetWithText 26
int[] styleable BaseProgressIndicator { 0x1010139, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable BaseProgressIndicator_android_indeterminate 0
int styleable BaseProgressIndicator_hideAnimationBehavior 1
int styleable BaseProgressIndicator_indicatorColor 2
int styleable BaseProgressIndicator_minHideDelay 3
int styleable BaseProgressIndicator_showAnimationBehavior 4
int styleable BaseProgressIndicator_showDelay 5
int styleable BaseProgressIndicator_trackColor 6
int styleable BaseProgressIndicator_trackCornerRadius 7
int styleable BaseProgressIndicator_trackThickness 8
int[] styleable BottomAppBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable BottomAppBar_addElevationShadow 0
int styleable BottomAppBar_backgroundTint 1
int styleable BottomAppBar_elevation 2
int styleable BottomAppBar_fabAlignmentMode 3
int styleable BottomAppBar_fabAlignmentModeEndMargin 4
int styleable BottomAppBar_fabAnchorMode 5
int styleable BottomAppBar_fabAnimationMode 6
int styleable BottomAppBar_fabCradleMargin 7
int styleable BottomAppBar_fabCradleRoundedCornerRadius 8
int styleable BottomAppBar_fabCradleVerticalOffset 9
int styleable BottomAppBar_hideOnScroll 10
int styleable BottomAppBar_menuAlignmentMode 11
int styleable BottomAppBar_navigationIconTint 12
int styleable BottomAppBar_paddingBottomSystemWindowInsets 13
int styleable BottomAppBar_paddingLeftSystemWindowInsets 14
int styleable BottomAppBar_paddingRightSystemWindowInsets 15
int styleable BottomAppBar_removeEmbeddedFabElevation 16
int[] styleable BottomNavigationView { 0x1010140, 0x0, 0x0, 0x0, 0x0 }
int styleable BottomNavigationView_android_minHeight 0
int styleable BottomNavigationView_compatShadowEnabled 1
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 2
int styleable BottomNavigationView_shapeAppearance 3
int styleable BottomNavigationView_shapeAppearanceOverlay 4
int[] styleable BottomSheetBehavior_Layout { 0x1010440, 0x1010120, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable BottomSheetBehavior_Layout_android_elevation 0
int styleable BottomSheetBehavior_Layout_android_maxHeight 1
int styleable BottomSheetBehavior_Layout_android_maxWidth 2
int styleable BottomSheetBehavior_Layout_backgroundTint 3
int styleable BottomSheetBehavior_Layout_behavior_draggable 4
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 5
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 6
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 7
int styleable BottomSheetBehavior_Layout_behavior_hideable 8
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 9
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 10
int styleable BottomSheetBehavior_Layout_behavior_significantVelocityThreshold 11
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 12
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 13
int styleable BottomSheetBehavior_Layout_marginLeftSystemWindowInsets 14
int styleable BottomSheetBehavior_Layout_marginRightSystemWindowInsets 15
int styleable BottomSheetBehavior_Layout_marginTopSystemWindowInsets 16
int styleable BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets 17
int styleable BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets 18
int styleable BottomSheetBehavior_Layout_paddingRightSystemWindowInsets 19
int styleable BottomSheetBehavior_Layout_paddingTopSystemWindowInsets 20
int styleable BottomSheetBehavior_Layout_shapeAppearance 21
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 22
int styleable BottomSheetBehavior_Layout_shouldRemoveExpandedCorners 23
int[] styleable ButtonBarLayout { 0x0 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x0, 0x0 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x1010140, 0x101013f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CardView_android_minHeight 0
int styleable CardView_android_minWidth 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Carousel { 0x0 }
int styleable Carousel_carousel_alignment 0
int[] styleable CheckedTextView { 0x1010108, 0x0, 0x0, 0x0 }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable Chip { 0x10101e5, 0x10100ab, 0x101011f, 0x101014f, 0x1010034, 0x1010098, 0x1010095, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Chip_android_checkable 0
int styleable Chip_android_ellipsize 1
int styleable Chip_android_maxWidth 2
int styleable Chip_android_text 3
int styleable Chip_android_textAppearance 4
int styleable Chip_android_textColor 5
int styleable Chip_android_textSize 6
int styleable Chip_checkedIcon 7
int styleable Chip_checkedIconEnabled 8
int styleable Chip_checkedIconTint 9
int styleable Chip_checkedIconVisible 10
int styleable Chip_chipBackgroundColor 11
int styleable Chip_chipCornerRadius 12
int styleable Chip_chipEndPadding 13
int styleable Chip_chipIcon 14
int styleable Chip_chipIconEnabled 15
int styleable Chip_chipIconSize 16
int styleable Chip_chipIconTint 17
int styleable Chip_chipIconVisible 18
int styleable Chip_chipMinHeight 19
int styleable Chip_chipMinTouchTargetSize 20
int styleable Chip_chipStartPadding 21
int styleable Chip_chipStrokeColor 22
int styleable Chip_chipStrokeWidth 23
int styleable Chip_chipSurfaceColor 24
int styleable Chip_closeIcon 25
int styleable Chip_closeIconEnabled 26
int styleable Chip_closeIconEndPadding 27
int styleable Chip_closeIconSize 28
int styleable Chip_closeIconStartPadding 29
int styleable Chip_closeIconTint 30
int styleable Chip_closeIconVisible 31
int styleable Chip_ensureMinTouchTargetSize 32
int styleable Chip_hideMotionSpec 33
int styleable Chip_iconEndPadding 34
int styleable Chip_iconStartPadding 35
int styleable Chip_rippleColor 36
int styleable Chip_shapeAppearance 37
int styleable Chip_shapeAppearanceOverlay 38
int styleable Chip_showMotionSpec 39
int styleable Chip_textEndPadding 40
int styleable Chip_textStartPadding 41
int[] styleable ChipGroup { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int[] styleable CircularProgressIndicator { 0x0, 0x0, 0x0 }
int styleable CircularProgressIndicator_indicatorDirectionCircular 0
int styleable CircularProgressIndicator_indicatorInset 1
int styleable CircularProgressIndicator_indicatorSize 2
int[] styleable ClockFaceView { 0x0, 0x0 }
int styleable ClockFaceView_clockFaceBackgroundColor 0
int styleable ClockFaceView_clockNumberTextColor 1
int[] styleable ClockHandView { 0x0, 0x0, 0x0 }
int styleable ClockHandView_clockHandColor 0
int styleable ClockHandView_materialCircleRadius 1
int styleable ClockHandView_selectorSize 2
int[] styleable CollapsingToolbarLayout { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_collapsedTitleTextColor 2
int styleable CollapsingToolbarLayout_contentScrim 3
int styleable CollapsingToolbarLayout_expandedTitleGravity 4
int styleable CollapsingToolbarLayout_expandedTitleMargin 5
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 6
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 7
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 8
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 9
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 10
int styleable CollapsingToolbarLayout_expandedTitleTextColor 11
int styleable CollapsingToolbarLayout_extraMultilineHeightEnabled 12
int styleable CollapsingToolbarLayout_forceApplySystemWindowInsetTop 13
int styleable CollapsingToolbarLayout_maxLines 14
int styleable CollapsingToolbarLayout_scrimAnimationDuration 15
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 16
int styleable CollapsingToolbarLayout_statusBarScrim 17
int styleable CollapsingToolbarLayout_title 18
int styleable CollapsingToolbarLayout_titleCollapseMode 19
int styleable CollapsingToolbarLayout_titleEnabled 20
int styleable CollapsingToolbarLayout_titlePositionInterpolator 21
int styleable CollapsingToolbarLayout_titleTextEllipsize 22
int styleable CollapsingToolbarLayout_toolbarId 23
int[] styleable CollapsingToolbarLayout_Layout { 0x0, 0x0 }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5, 0x1010647, 0x0 }
int styleable ColorStateListItem_alpha 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_color 2
int styleable ColorStateListItem_android_lStar 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x1010107, 0x0, 0x0, 0x0 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x101031f, 0x1010440, 0x10100d0, 0x10100f5, 0x10100fa, 0x10103b6, 0x10100f7, 0x10100f9, 0x10103b5, 0x10100f8, 0x10100f4, 0x1010120, 0x101011f, 0x1010140, 0x101013f, 0x10100c4, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010320, 0x1010321, 0x1010322, 0x1010323, 0x10103fa, 0x10100dc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Constraint_android_alpha 0
int styleable Constraint_android_elevation 1
int styleable Constraint_android_id 2
int styleable Constraint_android_layout_height 3
int styleable Constraint_android_layout_marginBottom 4
int styleable Constraint_android_layout_marginEnd 5
int styleable Constraint_android_layout_marginLeft 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginStart 8
int styleable Constraint_android_layout_marginTop 9
int styleable Constraint_android_layout_width 10
int styleable Constraint_android_maxHeight 11
int styleable Constraint_android_maxWidth 12
int styleable Constraint_android_minHeight 13
int styleable Constraint_android_minWidth 14
int styleable Constraint_android_orientation 15
int styleable Constraint_android_rotation 16
int styleable Constraint_android_rotationX 17
int styleable Constraint_android_rotationY 18
int styleable Constraint_android_scaleX 19
int styleable Constraint_android_scaleY 20
int styleable Constraint_android_transformPivotX 21
int styleable Constraint_android_transformPivotY 22
int styleable Constraint_android_translationX 23
int styleable Constraint_android_translationY 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_visibility 26
int styleable Constraint_animate_relativeTo 27
int styleable Constraint_barrierAllowsGoneWidgets 28
int styleable Constraint_barrierDirection 29
int styleable Constraint_barrierMargin 30
int styleable Constraint_chainUseRtl 31
int styleable Constraint_constraint_referenced_ids 32
int styleable Constraint_drawPath 33
int styleable Constraint_flow_firstHorizontalBias 34
int styleable Constraint_flow_firstHorizontalStyle 35
int styleable Constraint_flow_firstVerticalBias 36
int styleable Constraint_flow_firstVerticalStyle 37
int styleable Constraint_flow_horizontalAlign 38
int styleable Constraint_flow_horizontalBias 39
int styleable Constraint_flow_horizontalGap 40
int styleable Constraint_flow_horizontalStyle 41
int styleable Constraint_flow_lastHorizontalBias 42
int styleable Constraint_flow_lastHorizontalStyle 43
int styleable Constraint_flow_lastVerticalBias 44
int styleable Constraint_flow_lastVerticalStyle 45
int styleable Constraint_flow_maxElementsWrap 46
int styleable Constraint_flow_verticalAlign 47
int styleable Constraint_flow_verticalBias 48
int styleable Constraint_flow_verticalGap 49
int styleable Constraint_flow_verticalStyle 50
int styleable Constraint_flow_wrapMode 51
int styleable Constraint_layout_constrainedHeight 52
int styleable Constraint_layout_constrainedWidth 53
int styleable Constraint_layout_constraintBaseline_creator 54
int styleable Constraint_layout_constraintBaseline_toBaselineOf 55
int styleable Constraint_layout_constraintBottom_creator 56
int styleable Constraint_layout_constraintBottom_toBottomOf 57
int styleable Constraint_layout_constraintBottom_toTopOf 58
int styleable Constraint_layout_constraintCircle 59
int styleable Constraint_layout_constraintCircleAngle 60
int styleable Constraint_layout_constraintCircleRadius 61
int styleable Constraint_layout_constraintDimensionRatio 62
int styleable Constraint_layout_constraintEnd_toEndOf 63
int styleable Constraint_layout_constraintEnd_toStartOf 64
int styleable Constraint_layout_constraintGuide_begin 65
int styleable Constraint_layout_constraintGuide_end 66
int styleable Constraint_layout_constraintGuide_percent 67
int styleable Constraint_layout_constraintHeight_default 68
int styleable Constraint_layout_constraintHeight_max 69
int styleable Constraint_layout_constraintHeight_min 70
int styleable Constraint_layout_constraintHeight_percent 71
int styleable Constraint_layout_constraintHorizontal_bias 72
int styleable Constraint_layout_constraintHorizontal_chainStyle 73
int styleable Constraint_layout_constraintHorizontal_weight 74
int styleable Constraint_layout_constraintLeft_creator 75
int styleable Constraint_layout_constraintLeft_toLeftOf 76
int styleable Constraint_layout_constraintLeft_toRightOf 77
int styleable Constraint_layout_constraintRight_creator 78
int styleable Constraint_layout_constraintRight_toLeftOf 79
int styleable Constraint_layout_constraintRight_toRightOf 80
int styleable Constraint_layout_constraintStart_toEndOf 81
int styleable Constraint_layout_constraintStart_toStartOf 82
int styleable Constraint_layout_constraintTag 83
int styleable Constraint_layout_constraintTop_creator 84
int styleable Constraint_layout_constraintTop_toBottomOf 85
int styleable Constraint_layout_constraintTop_toTopOf 86
int styleable Constraint_layout_constraintVertical_bias 87
int styleable Constraint_layout_constraintVertical_chainStyle 88
int styleable Constraint_layout_constraintVertical_weight 89
int styleable Constraint_layout_constraintWidth_default 90
int styleable Constraint_layout_constraintWidth_max 91
int styleable Constraint_layout_constraintWidth_min 92
int styleable Constraint_layout_constraintWidth_percent 93
int styleable Constraint_layout_editor_absoluteX 94
int styleable Constraint_layout_editor_absoluteY 95
int styleable Constraint_layout_goneMarginBottom 96
int styleable Constraint_layout_goneMarginEnd 97
int styleable Constraint_layout_goneMarginLeft 98
int styleable Constraint_layout_goneMarginRight 99
int styleable Constraint_layout_goneMarginStart 100
int styleable Constraint_layout_goneMarginTop 101
int styleable Constraint_motionProgress 102
int styleable Constraint_motionStagger 103
int styleable Constraint_pathMotionArc 104
int styleable Constraint_pivotAnchor 105
int styleable Constraint_transitionEasing 106
int styleable Constraint_transitionPathRotate 107
int styleable Constraint_visibilityMode 108
int[] styleable ConstraintLayout_Layout { 0x1010440, 0x1010120, 0x101011f, 0x1010140, 0x101013f, 0x10100c4, 0x10100d5, 0x10100d9, 0x10103b4, 0x10100d6, 0x10100d8, 0x10103b3, 0x10100d7, 0x10100dc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable ConstraintLayout_Layout_android_elevation 0
int styleable ConstraintLayout_Layout_android_maxHeight 1
int styleable ConstraintLayout_Layout_android_maxWidth 2
int styleable ConstraintLayout_Layout_android_minHeight 3
int styleable ConstraintLayout_Layout_android_minWidth 4
int styleable ConstraintLayout_Layout_android_orientation 5
int styleable ConstraintLayout_Layout_android_padding 6
int styleable ConstraintLayout_Layout_android_paddingBottom 7
int styleable ConstraintLayout_Layout_android_paddingEnd 8
int styleable ConstraintLayout_Layout_android_paddingLeft 9
int styleable ConstraintLayout_Layout_android_paddingRight 10
int styleable ConstraintLayout_Layout_android_paddingStart 11
int styleable ConstraintLayout_Layout_android_paddingTop 12
int styleable ConstraintLayout_Layout_android_visibility 13
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 14
int styleable ConstraintLayout_Layout_barrierDirection 15
int styleable ConstraintLayout_Layout_barrierMargin 16
int styleable ConstraintLayout_Layout_chainUseRtl 17
int styleable ConstraintLayout_Layout_constraintSet 18
int styleable ConstraintLayout_Layout_constraint_referenced_ids 19
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 20
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 21
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 22
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 23
int styleable ConstraintLayout_Layout_flow_horizontalAlign 24
int styleable ConstraintLayout_Layout_flow_horizontalBias 25
int styleable ConstraintLayout_Layout_flow_horizontalGap 26
int styleable ConstraintLayout_Layout_flow_horizontalStyle 27
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 28
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 29
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 30
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 31
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 32
int styleable ConstraintLayout_Layout_flow_verticalAlign 33
int styleable ConstraintLayout_Layout_flow_verticalBias 34
int styleable ConstraintLayout_Layout_flow_verticalGap 35
int styleable ConstraintLayout_Layout_flow_verticalStyle 36
int styleable ConstraintLayout_Layout_flow_wrapMode 37
int styleable ConstraintLayout_Layout_layoutDescription 38
int styleable ConstraintLayout_Layout_layout_constrainedHeight 39
int styleable ConstraintLayout_Layout_layout_constrainedWidth 40
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 41
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 42
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 43
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 44
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 45
int styleable ConstraintLayout_Layout_layout_constraintCircle 46
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 47
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 48
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 49
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 50
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 51
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 52
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 53
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 54
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 55
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 56
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 57
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 58
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 59
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 60
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 61
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 62
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 63
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 64
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 65
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 66
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 67
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 68
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 69
int styleable ConstraintLayout_Layout_layout_constraintTag 70
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 71
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 72
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 73
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 74
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 75
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 76
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 77
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 78
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 79
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 80
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 81
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 82
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 83
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 84
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 85
int styleable ConstraintLayout_Layout_layout_goneMarginRight 86
int styleable ConstraintLayout_Layout_layout_goneMarginStart 87
int styleable ConstraintLayout_Layout_layout_goneMarginTop 88
int styleable ConstraintLayout_Layout_layout_optimizationLevel 89
int[] styleable ConstraintLayout_placeholder { 0x0, 0x0 }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintSet { 0x101031f, 0x1010440, 0x10100d0, 0x10100f5, 0x10100fa, 0x10103b6, 0x10100f7, 0x10100f9, 0x10103b5, 0x10100f8, 0x10100f4, 0x1010120, 0x101011f, 0x1010140, 0x101013f, 0x10100c4, 0x10101b5, 0x10101b6, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010320, 0x1010321, 0x1010322, 0x1010323, 0x10103fa, 0x10100dc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable ConstraintSet_android_alpha 0
int styleable ConstraintSet_android_elevation 1
int styleable ConstraintSet_android_id 2
int styleable ConstraintSet_android_layout_height 3
int styleable ConstraintSet_android_layout_marginBottom 4
int styleable ConstraintSet_android_layout_marginEnd 5
int styleable ConstraintSet_android_layout_marginLeft 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginStart 8
int styleable ConstraintSet_android_layout_marginTop 9
int styleable ConstraintSet_android_layout_width 10
int styleable ConstraintSet_android_maxHeight 11
int styleable ConstraintSet_android_maxWidth 12
int styleable ConstraintSet_android_minHeight 13
int styleable ConstraintSet_android_minWidth 14
int styleable ConstraintSet_android_orientation 15
int styleable ConstraintSet_android_pivotX 16
int styleable ConstraintSet_android_pivotY 17
int styleable ConstraintSet_android_rotation 18
int styleable ConstraintSet_android_rotationX 19
int styleable ConstraintSet_android_rotationY 20
int styleable ConstraintSet_android_scaleX 21
int styleable ConstraintSet_android_scaleY 22
int styleable ConstraintSet_android_transformPivotX 23
int styleable ConstraintSet_android_transformPivotY 24
int styleable ConstraintSet_android_translationX 25
int styleable ConstraintSet_android_translationY 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_visibility 28
int styleable ConstraintSet_animate_relativeTo 29
int styleable ConstraintSet_barrierAllowsGoneWidgets 30
int styleable ConstraintSet_barrierDirection 31
int styleable ConstraintSet_barrierMargin 32
int styleable ConstraintSet_chainUseRtl 33
int styleable ConstraintSet_constraint_referenced_ids 34
int styleable ConstraintSet_deriveConstraintsFrom 35
int styleable ConstraintSet_drawPath 36
int styleable ConstraintSet_flow_firstHorizontalBias 37
int styleable ConstraintSet_flow_firstHorizontalStyle 38
int styleable ConstraintSet_flow_firstVerticalBias 39
int styleable ConstraintSet_flow_firstVerticalStyle 40
int styleable ConstraintSet_flow_horizontalAlign 41
int styleable ConstraintSet_flow_horizontalBias 42
int styleable ConstraintSet_flow_horizontalGap 43
int styleable ConstraintSet_flow_horizontalStyle 44
int styleable ConstraintSet_flow_lastHorizontalBias 45
int styleable ConstraintSet_flow_lastHorizontalStyle 46
int styleable ConstraintSet_flow_lastVerticalBias 47
int styleable ConstraintSet_flow_lastVerticalStyle 48
int styleable ConstraintSet_flow_maxElementsWrap 49
int styleable ConstraintSet_flow_verticalAlign 50
int styleable ConstraintSet_flow_verticalBias 51
int styleable ConstraintSet_flow_verticalGap 52
int styleable ConstraintSet_flow_verticalStyle 53
int styleable ConstraintSet_flow_wrapMode 54
int styleable ConstraintSet_layout_constrainedHeight 55
int styleable ConstraintSet_layout_constrainedWidth 56
int styleable ConstraintSet_layout_constraintBaseline_creator 57
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 58
int styleable ConstraintSet_layout_constraintBottom_creator 59
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 60
int styleable ConstraintSet_layout_constraintBottom_toTopOf 61
int styleable ConstraintSet_layout_constraintCircle 62
int styleable ConstraintSet_layout_constraintCircleAngle 63
int styleable ConstraintSet_layout_constraintCircleRadius 64
int styleable ConstraintSet_layout_constraintDimensionRatio 65
int styleable ConstraintSet_layout_constraintEnd_toEndOf 66
int styleable ConstraintSet_layout_constraintEnd_toStartOf 67
int styleable ConstraintSet_layout_constraintGuide_begin 68
int styleable ConstraintSet_layout_constraintGuide_end 69
int styleable ConstraintSet_layout_constraintGuide_percent 70
int styleable ConstraintSet_layout_constraintHeight_default 71
int styleable ConstraintSet_layout_constraintHeight_max 72
int styleable ConstraintSet_layout_constraintHeight_min 73
int styleable ConstraintSet_layout_constraintHeight_percent 74
int styleable ConstraintSet_layout_constraintHorizontal_bias 75
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 76
int styleable ConstraintSet_layout_constraintHorizontal_weight 77
int styleable ConstraintSet_layout_constraintLeft_creator 78
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 79
int styleable ConstraintSet_layout_constraintLeft_toRightOf 80
int styleable ConstraintSet_layout_constraintRight_creator 81
int styleable ConstraintSet_layout_constraintRight_toLeftOf 82
int styleable ConstraintSet_layout_constraintRight_toRightOf 83
int styleable ConstraintSet_layout_constraintStart_toEndOf 84
int styleable ConstraintSet_layout_constraintStart_toStartOf 85
int styleable ConstraintSet_layout_constraintTag 86
int styleable ConstraintSet_layout_constraintTop_creator 87
int styleable ConstraintSet_layout_constraintTop_toBottomOf 88
int styleable ConstraintSet_layout_constraintTop_toTopOf 89
int styleable ConstraintSet_layout_constraintVertical_bias 90
int styleable ConstraintSet_layout_constraintVertical_chainStyle 91
int styleable ConstraintSet_layout_constraintVertical_weight 92
int styleable ConstraintSet_layout_constraintWidth_default 93
int styleable ConstraintSet_layout_constraintWidth_max 94
int styleable ConstraintSet_layout_constraintWidth_min 95
int styleable ConstraintSet_layout_constraintWidth_percent 96
int styleable ConstraintSet_layout_editor_absoluteX 97
int styleable ConstraintSet_layout_editor_absoluteY 98
int styleable ConstraintSet_layout_goneMarginBottom 99
int styleable ConstraintSet_layout_goneMarginEnd 100
int styleable ConstraintSet_layout_goneMarginLeft 101
int styleable ConstraintSet_layout_goneMarginRight 102
int styleable ConstraintSet_layout_goneMarginStart 103
int styleable ConstraintSet_layout_goneMarginTop 104
int styleable ConstraintSet_motionProgress 105
int styleable ConstraintSet_motionStagger 106
int styleable ConstraintSet_pathMotionArc 107
int styleable ConstraintSet_pivotAnchor 108
int styleable ConstraintSet_transitionEasing 109
int styleable ConstraintSet_transitionPathRotate 110
int[] styleable CoordinatorLayout { 0x0, 0x0 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable CustomAttribute { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customStringValue 8
int[] styleable DrawerArrowToggle { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable DrawerLayout { 0x0 }
int styleable DrawerLayout_elevation 0
int[] styleable ExtendedFloatingActionButton { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable ExtendedFloatingActionButton_collapsedSize 0
int styleable ExtendedFloatingActionButton_elevation 1
int styleable ExtendedFloatingActionButton_extendMotionSpec 2
int styleable ExtendedFloatingActionButton_extendStrategy 3
int styleable ExtendedFloatingActionButton_hideMotionSpec 4
int styleable ExtendedFloatingActionButton_showMotionSpec 5
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 6
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x0, 0x0 }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int[] styleable FloatingActionButton { 0x101000e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FloatingActionButton_Behavior_Layout { 0x0 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x0, 0x0 }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontStyle 1
int styleable FontFamilyFont_android_fontVariationSettings 2
int styleable FontFamilyFont_android_fontWeight 3
int styleable FontFamilyFont_android_ttcIndex 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x1010109, 0x1010200, 0x0 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x10100d0, 0x1010003, 0x10100d1 }
int styleable Fragment_android_id 0
int styleable Fragment_android_name 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x1010003, 0x10100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
int styleable GradientColor_android_centerColor 0
int styleable GradientColor_android_centerX 1
int styleable GradientColor_android_centerY 2
int styleable GradientColor_android_endColor 3
int styleable GradientColor_android_endX 4
int styleable GradientColor_android_endY 5
int styleable GradientColor_android_gradientRadius 6
int styleable GradientColor_android_startColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_tileMode 10
int styleable GradientColor_android_type 11
int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_brightness 1
int styleable ImageFilterView_contrast 2
int styleable ImageFilterView_crossfade 3
int styleable ImageFilterView_overlay 4
int styleable ImageFilterView_round 5
int styleable ImageFilterView_roundPercent 6
int styleable ImageFilterView_saturation 7
int styleable ImageFilterView_warmth 8
int[] styleable Insets { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Insets_marginLeftSystemWindowInsets 0
int styleable Insets_marginRightSystemWindowInsets 1
int styleable Insets_marginTopSystemWindowInsets 2
int styleable Insets_paddingBottomSystemWindowInsets 3
int styleable Insets_paddingLeftSystemWindowInsets 4
int styleable Insets_paddingRightSystemWindowInsets 5
int styleable Insets_paddingStartSystemWindowInsets 6
int styleable Insets_paddingTopSystemWindowInsets 7
int[] styleable KeyAttribute { 0x101031f, 0x1010440, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010320, 0x1010321, 0x1010322, 0x1010323, 0x10103fa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_elevation 1
int styleable KeyAttribute_android_rotation 2
int styleable KeyAttribute_android_rotationX 3
int styleable KeyAttribute_android_rotationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_transformPivotX 7
int styleable KeyAttribute_android_transformPivotY 8
int styleable KeyAttribute_android_translationX 9
int styleable KeyAttribute_android_translationY 10
int styleable KeyAttribute_android_translationZ 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transitionEasing 16
int styleable KeyAttribute_transitionPathRotate 17
int[] styleable KeyCycle { 0x101031f, 0x1010440, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010322, 0x1010323, 0x10103fa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_elevation 1
int styleable KeyCycle_android_rotation 2
int styleable KeyCycle_android_rotationX 3
int styleable KeyCycle_android_rotationY 4
int styleable KeyCycle_android_scaleX 5
int styleable KeyCycle_android_scaleY 6
int styleable KeyCycle_android_translationX 7
int styleable KeyCycle_android_translationY 8
int styleable KeyCycle_android_translationZ 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_waveShape 18
int styleable KeyCycle_waveVariesBy 19
int[] styleable KeyPosition { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x101031f, 0x1010440, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010322, 0x1010323, 0x10103fa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_elevation 1
int styleable KeyTimeCycle_android_rotation 2
int styleable KeyTimeCycle_android_rotationX 3
int styleable KeyTimeCycle_android_rotationY 4
int styleable KeyTimeCycle_android_scaleX 5
int styleable KeyTimeCycle_android_scaleY 6
int styleable KeyTimeCycle_android_translationX 7
int styleable KeyTimeCycle_android_translationY 8
int styleable KeyTimeCycle_android_translationZ 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_waveShape 19
int[] styleable KeyTrigger { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int[] styleable Layout { 0x10100f5, 0x10100fa, 0x10103b6, 0x10100f7, 0x10100f9, 0x10103b5, 0x10100f8, 0x10100f4, 0x10100c4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Layout_android_layout_height 0
int styleable Layout_android_layout_marginBottom 1
int styleable Layout_android_layout_marginEnd 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginRight 4
int styleable Layout_android_layout_marginStart 5
int styleable Layout_android_layout_marginTop 6
int styleable Layout_android_layout_width 7
int styleable Layout_android_orientation 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_layout_constrainedHeight 14
int styleable Layout_layout_constrainedWidth 15
int styleable Layout_layout_constraintBaseline_creator 16
int styleable Layout_layout_constraintBaseline_toBaselineOf 17
int styleable Layout_layout_constraintBottom_creator 18
int styleable Layout_layout_constraintBottom_toBottomOf 19
int styleable Layout_layout_constraintBottom_toTopOf 20
int styleable Layout_layout_constraintCircle 21
int styleable Layout_layout_constraintCircleAngle 22
int styleable Layout_layout_constraintCircleRadius 23
int styleable Layout_layout_constraintDimensionRatio 24
int styleable Layout_layout_constraintEnd_toEndOf 25
int styleable Layout_layout_constraintEnd_toStartOf 26
int styleable Layout_layout_constraintGuide_begin 27
int styleable Layout_layout_constraintGuide_end 28
int styleable Layout_layout_constraintGuide_percent 29
int styleable Layout_layout_constraintHeight_default 30
int styleable Layout_layout_constraintHeight_max 31
int styleable Layout_layout_constraintHeight_min 32
int styleable Layout_layout_constraintHeight_percent 33
int styleable Layout_layout_constraintHorizontal_bias 34
int styleable Layout_layout_constraintHorizontal_chainStyle 35
int styleable Layout_layout_constraintHorizontal_weight 36
int styleable Layout_layout_constraintLeft_creator 37
int styleable Layout_layout_constraintLeft_toLeftOf 38
int styleable Layout_layout_constraintLeft_toRightOf 39
int styleable Layout_layout_constraintRight_creator 40
int styleable Layout_layout_constraintRight_toLeftOf 41
int styleable Layout_layout_constraintRight_toRightOf 42
int styleable Layout_layout_constraintStart_toEndOf 43
int styleable Layout_layout_constraintStart_toStartOf 44
int styleable Layout_layout_constraintTop_creator 45
int styleable Layout_layout_constraintTop_toBottomOf 46
int styleable Layout_layout_constraintTop_toTopOf 47
int styleable Layout_layout_constraintVertical_bias 48
int styleable Layout_layout_constraintVertical_chainStyle 49
int styleable Layout_layout_constraintVertical_weight 50
int styleable Layout_layout_constraintWidth_default 51
int styleable Layout_layout_constraintWidth_max 52
int styleable Layout_layout_constraintWidth_min 53
int styleable Layout_layout_constraintWidth_percent 54
int styleable Layout_layout_editor_absoluteX 55
int styleable Layout_layout_editor_absoluteY 56
int styleable Layout_layout_goneMarginBottom 57
int styleable Layout_layout_goneMarginEnd 58
int styleable Layout_layout_goneMarginLeft 59
int styleable Layout_layout_goneMarginRight 60
int styleable Layout_layout_goneMarginStart 61
int styleable Layout_layout_goneMarginTop 62
int styleable Layout_maxHeight 63
int styleable Layout_maxWidth 64
int styleable Layout_minHeight 65
int styleable Layout_minWidth 66
int[] styleable LinearLayoutCompat { 0x1010126, 0x1010127, 0x10100af, 0x10100c4, 0x1010128, 0x0, 0x0, 0x0, 0x0 }
int styleable LinearLayoutCompat_android_baselineAligned 0
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
int styleable LinearLayoutCompat_android_gravity 2
int styleable LinearLayoutCompat_android_orientation 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x10100b3, 0x10100f5, 0x1010181, 0x10100f4 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_height 1
int styleable LinearLayoutCompat_Layout_android_layout_weight 2
int styleable LinearLayoutCompat_Layout_android_layout_width 3
int[] styleable LinearProgressIndicator { 0x0, 0x0 }
int styleable LinearProgressIndicator_indeterminateAnimationType 0
int styleable LinearProgressIndicator_indicatorDirectionLinear 1
int[] styleable ListPopupWindow { 0x10102ac, 0x10102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MaterialAlertDialog { 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int styleable MaterialAlertDialog_backgroundTint 4
int[] styleable MaterialAlertDialogTheme { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogButtonSpacerVisibility 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 4
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 5
int[] styleable MaterialAutoCompleteTextView { 0x1010220, 0x101048c, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int styleable MaterialAutoCompleteTextView_android_popupElevation 1
int styleable MaterialAutoCompleteTextView_dropDownBackgroundTint 2
int styleable MaterialAutoCompleteTextView_simpleItemLayout 3
int styleable MaterialAutoCompleteTextView_simpleItemSelectedColor 4
int styleable MaterialAutoCompleteTextView_simpleItemSelectedRippleColor 5
int styleable MaterialAutoCompleteTextView_simpleItems 6
int[] styleable MaterialButton { 0x10100d4, 0x10101e5, 0x10101ba, 0x10101b7, 0x10101b8, 0x10101b9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_checkable 1
int styleable MaterialButton_android_insetBottom 2
int styleable MaterialButton_android_insetLeft 3
int styleable MaterialButton_android_insetRight 4
int styleable MaterialButton_android_insetTop 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int styleable MaterialButton_toggleCheckedStateOnClick 21
int[] styleable MaterialButtonToggleGroup { 0x101000e, 0x0, 0x0, 0x0 }
int styleable MaterialButtonToggleGroup_android_enabled 0
int styleable MaterialButtonToggleGroup_checkedButton 1
int styleable MaterialButtonToggleGroup_selectionRequired 2
int styleable MaterialButtonToggleGroup_singleSelection 3
int[] styleable MaterialCalendar { 0x101020d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_backgroundTint 1
int styleable MaterialCalendar_dayInvalidStyle 2
int styleable MaterialCalendar_daySelectedStyle 3
int styleable MaterialCalendar_dayStyle 4
int styleable MaterialCalendar_dayTodayStyle 5
int styleable MaterialCalendar_nestedScrollable 6
int styleable MaterialCalendar_rangeFillColor 7
int styleable MaterialCalendar_yearSelectedStyle 8
int styleable MaterialCalendar_yearStyle 9
int styleable MaterialCalendar_yearTodayStyle 10
int[] styleable MaterialCalendarItem { 0x10101ba, 0x10101b7, 0x10101b8, 0x10101b9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialCalendarItem_android_insetBottom 0
int styleable MaterialCalendarItem_android_insetLeft 1
int styleable MaterialCalendarItem_android_insetRight 2
int styleable MaterialCalendarItem_android_insetTop 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int[] styleable MaterialCardView { 0x10101e5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconGravity 3
int styleable MaterialCardView_checkedIconMargin 4
int styleable MaterialCardView_checkedIconSize 5
int styleable MaterialCardView_checkedIconTint 6
int styleable MaterialCardView_rippleColor 7
int styleable MaterialCardView_shapeAppearance 8
int styleable MaterialCardView_shapeAppearanceOverlay 9
int styleable MaterialCardView_state_dragged 10
int styleable MaterialCardView_strokeColor 11
int styleable MaterialCardView_strokeWidth 12
int[] styleable MaterialCheckBox { 0x1010107, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialCheckBox_android_button 0
int styleable MaterialCheckBox_buttonCompat 1
int styleable MaterialCheckBox_buttonIcon 2
int styleable MaterialCheckBox_buttonIconTint 3
int styleable MaterialCheckBox_buttonIconTintMode 4
int styleable MaterialCheckBox_buttonTint 5
int styleable MaterialCheckBox_centerIfNoTextEnabled 6
int styleable MaterialCheckBox_checkedState 7
int styleable MaterialCheckBox_errorAccessibilityLabel 8
int styleable MaterialCheckBox_errorShown 9
int styleable MaterialCheckBox_useMaterialThemeColors 10
int[] styleable MaterialCheckBoxStates { 0x0, 0x0 }
int styleable MaterialCheckBoxStates_state_error 0
int styleable MaterialCheckBoxStates_state_indeterminate 1
int[] styleable MaterialDivider { 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialDivider_dividerColor 0
int styleable MaterialDivider_dividerInsetEnd 1
int styleable MaterialDivider_dividerInsetStart 2
int styleable MaterialDivider_dividerThickness 3
int styleable MaterialDivider_lastItemDecorated 4
int[] styleable MaterialRadioButton { 0x0, 0x0 }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x0, 0x0 }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialSwitch { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialSwitch_thumbIcon 0
int styleable MaterialSwitch_thumbIconSize 1
int styleable MaterialSwitch_thumbIconTint 2
int styleable MaterialSwitch_thumbIconTintMode 3
int styleable MaterialSwitch_trackDecoration 4
int styleable MaterialSwitch_trackDecorationTint 5
int styleable MaterialSwitch_trackDecorationTintMode 6
int[] styleable MaterialTextAppearance { 0x10104b6, 0x101057f, 0x0 }
int styleable MaterialTextAppearance_android_letterSpacing 0
int styleable MaterialTextAppearance_android_lineHeight 1
int styleable MaterialTextAppearance_lineHeight 2
int[] styleable MaterialTextView { 0x101057f, 0x1010034, 0x0 }
int styleable MaterialTextView_android_lineHeight 0
int styleable MaterialTextView_android_textAppearance 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MaterialTimePicker { 0x0, 0x0, 0x0 }
int styleable MaterialTimePicker_backgroundTint 0
int styleable MaterialTimePicker_clockIcon 1
int styleable MaterialTimePicker_keyboardIcon 2
int[] styleable MaterialToolbar { 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MaterialToolbar_logoAdjustViewBounds 0
int styleable MaterialToolbar_logoScaleType 1
int styleable MaterialToolbar_navigationIconTint 2
int styleable MaterialToolbar_subtitleCentered 3
int styleable MaterialToolbar_titleCentered 4
int[] styleable MenuGroup { 0x10101e0, 0x101000e, 0x10100d0, 0x10101de, 0x10101df, 0x1010194 }
int styleable MenuGroup_android_checkableBehavior 0
int styleable MenuGroup_android_enabled 1
int styleable MenuGroup_android_id 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_visible 5
int[] styleable MenuItem { 0x0, 0x0, 0x0, 0x0, 0x10101e3, 0x10101e5, 0x1010106, 0x101000e, 0x1010002, 0x10100d0, 0x10101de, 0x10101e4, 0x101026f, 0x10101df, 0x10101e1, 0x10101e2, 0x1010194, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MenuItem_actionLayout 0
int styleable MenuItem_actionProviderClass 1
int styleable MenuItem_actionViewClass 2
int styleable MenuItem_alphabeticModifiers 3
int styleable MenuItem_android_alphabeticShortcut 4
int styleable MenuItem_android_checkable 5
int styleable MenuItem_android_checked 6
int styleable MenuItem_android_enabled 7
int styleable MenuItem_android_icon 8
int styleable MenuItem_android_id 9
int styleable MenuItem_android_menuCategory 10
int styleable MenuItem_android_numericShortcut 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_android_orderInCategory 13
int styleable MenuItem_android_title 14
int styleable MenuItem_android_titleCondensed 15
int styleable MenuItem_android_visible 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x101012f, 0x101012d, 0x1010130, 0x1010131, 0x101012c, 0x101012e, 0x10100ae, 0x0, 0x0 }
int styleable MenuView_android_headerBackground 0
int styleable MenuView_android_horizontalDivider 1
int styleable MenuView_android_itemBackground 2
int styleable MenuView_android_itemIconDisabledAlpha 3
int styleable MenuView_android_itemTextAppearance 4
int styleable MenuView_android_verticalDivider 5
int styleable MenuView_android_windowAnimationStyle 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Motion_animate_relativeTo 0
int styleable Motion_drawPath 1
int styleable Motion_motionPathRotate 2
int styleable Motion_motionStagger 3
int styleable Motion_pathMotionArc 4
int styleable Motion_transitionEasing 5
int[] styleable MotionHelper { 0x0, 0x0 }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLayout { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x0, 0x0 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x0, 0x0, 0x0 }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable NavigationBarActiveIndicator { 0x10101a5, 0x1010155, 0x1010159, 0x0, 0x0 }
int styleable NavigationBarActiveIndicator_android_color 0
int styleable NavigationBarActiveIndicator_android_height 1
int styleable NavigationBarActiveIndicator_android_width 2
int styleable NavigationBarActiveIndicator_marginHorizontal 3
int styleable NavigationBarActiveIndicator_shapeAppearance 4
int[] styleable NavigationBarView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable NavigationBarView_activeIndicatorLabelPadding 0
int styleable NavigationBarView_backgroundTint 1
int styleable NavigationBarView_elevation 2
int styleable NavigationBarView_itemActiveIndicatorStyle 3
int styleable NavigationBarView_itemBackground 4
int styleable NavigationBarView_itemIconSize 5
int styleable NavigationBarView_itemIconTint 6
int styleable NavigationBarView_itemPaddingBottom 7
int styleable NavigationBarView_itemPaddingTop 8
int styleable NavigationBarView_itemRippleColor 9
int styleable NavigationBarView_itemTextAppearanceActive 10
int styleable NavigationBarView_itemTextAppearanceActiveBoldEnabled 11
int styleable NavigationBarView_itemTextAppearanceInactive 12
int styleable NavigationBarView_itemTextColor 13
int styleable NavigationBarView_labelVisibilityMode 14
int styleable NavigationBarView_menu 15
int[] styleable NavigationRailView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable NavigationRailView_headerLayout 0
int styleable NavigationRailView_itemMinHeight 1
int styleable NavigationRailView_menuGravity 2
int styleable NavigationRailView_paddingBottomSystemWindowInsets 3
int styleable NavigationRailView_paddingStartSystemWindowInsets 4
int styleable NavigationRailView_paddingTopSystemWindowInsets 5
int styleable NavigationRailView_shapeAppearance 6
int styleable NavigationRailView_shapeAppearanceOverlay 7
int[] styleable NavigationView { 0x10100d4, 0x10100dd, 0x10100b3, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable NavigationView_android_background 0
int styleable NavigationView_android_fitsSystemWindows 1
int styleable NavigationView_android_layout_gravity 2
int styleable NavigationView_android_maxWidth 3
int styleable NavigationView_bottomInsetScrimEnabled 4
int styleable NavigationView_dividerInsetEnd 5
int styleable NavigationView_dividerInsetStart 6
int styleable NavigationView_drawerLayoutCornerSize 7
int styleable NavigationView_elevation 8
int styleable NavigationView_headerLayout 9
int styleable NavigationView_itemBackground 10
int styleable NavigationView_itemHorizontalPadding 11
int styleable NavigationView_itemIconPadding 12
int styleable NavigationView_itemIconSize 13
int styleable NavigationView_itemIconTint 14
int styleable NavigationView_itemMaxLines 15
int styleable NavigationView_itemRippleColor 16
int styleable NavigationView_itemShapeAppearance 17
int styleable NavigationView_itemShapeAppearanceOverlay 18
int styleable NavigationView_itemShapeFillColor 19
int styleable NavigationView_itemShapeInsetBottom 20
int styleable NavigationView_itemShapeInsetEnd 21
int styleable NavigationView_itemShapeInsetStart 22
int styleable NavigationView_itemShapeInsetTop 23
int styleable NavigationView_itemTextAppearance 24
int styleable NavigationView_itemTextAppearanceActiveBoldEnabled 25
int styleable NavigationView_itemTextColor 26
int styleable NavigationView_itemVerticalPadding 27
int styleable NavigationView_menu 28
int styleable NavigationView_shapeAppearance 29
int styleable NavigationView_shapeAppearanceOverlay 30
int styleable NavigationView_subheaderColor 31
int styleable NavigationView_subheaderInsetEnd 32
int styleable NavigationView_subheaderInsetStart 33
int styleable NavigationView_subheaderTextAppearance 34
int styleable NavigationView_topInsetScrimEnabled 35
int[] styleable OnClick { 0x0, 0x0 }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable OnSwipe_dragDirection 0
int styleable OnSwipe_dragScale 1
int styleable OnSwipe_dragThreshold 2
int styleable OnSwipe_limitBoundsTo 3
int styleable OnSwipe_maxAcceleration 4
int styleable OnSwipe_maxVelocity 5
int styleable OnSwipe_moveWhenScrollAtTop 6
int styleable OnSwipe_nestedScrollFlags 7
int styleable OnSwipe_onTouchUp 8
int styleable OnSwipe_touchAnchorId 9
int styleable OnSwipe_touchAnchorSide 10
int styleable OnSwipe_touchRegionId 11
int[] styleable PopupWindow { 0x10102c9, 0x1010176, 0x0 }
int styleable PopupWindow_android_popupAnimationStyle 0
int styleable PopupWindow_android_popupBackground 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x0 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PropertySet { 0x101031f, 0x10100dc, 0x0, 0x0, 0x0 }
int styleable PropertySet_android_alpha 0
int styleable PropertySet_android_visibility 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RadialViewGroup { 0x0 }
int styleable RadialViewGroup_materialCircleRadius 0
int[] styleable RangeSlider { 0x0, 0x0 }
int styleable RangeSlider_minSeparation 0
int styleable RangeSlider_values 1
int[] styleable RecycleListView { 0x0, 0x0 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x10100eb, 0x10100f1, 0x10100c4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable RecyclerView_android_clipToPadding 0
int styleable RecyclerView_android_descendantFocusability 1
int styleable RecyclerView_android_orientation 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x0 }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x0 }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchBar { 0x1010150, 0x101014f, 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SearchBar_android_hint 0
int styleable SearchBar_android_text 1
int styleable SearchBar_android_textAppearance 2
int styleable SearchBar_backgroundTint 3
int styleable SearchBar_defaultMarginsEnabled 4
int styleable SearchBar_defaultScrollFlagsEnabled 5
int styleable SearchBar_elevation 6
int styleable SearchBar_forceDefaultNavigationOnClickListener 7
int styleable SearchBar_hideNavigationIcon 8
int styleable SearchBar_navigationIconTint 9
int styleable SearchBar_strokeColor 10
int styleable SearchBar_strokeWidth 11
int styleable SearchBar_tintNavigationIcon 12
int[] styleable SearchView { 0x10100da, 0x1010150, 0x1010264, 0x1010220, 0x101011f, 0x101014f, 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_hint 1
int styleable SearchView_android_imeOptions 2
int styleable SearchView_android_inputType 3
int styleable SearchView_android_maxWidth 4
int styleable SearchView_android_text 5
int styleable SearchView_android_textAppearance 6
int styleable SearchView_animateMenuItems 7
int styleable SearchView_animateNavigationIcon 8
int styleable SearchView_autoShowKeyboard 9
int styleable SearchView_backHandlingEnabled 10
int styleable SearchView_backgroundTint 11
int styleable SearchView_closeIcon 12
int styleable SearchView_commitIcon 13
int styleable SearchView_defaultQueryHint 14
int styleable SearchView_goIcon 15
int styleable SearchView_headerLayout 16
int styleable SearchView_hideNavigationIcon 17
int styleable SearchView_iconifiedByDefault 18
int styleable SearchView_layout 19
int styleable SearchView_queryBackground 20
int styleable SearchView_queryHint 21
int styleable SearchView_searchHintIcon 22
int styleable SearchView_searchIcon 23
int styleable SearchView_searchPrefixText 24
int styleable SearchView_submitBackground 25
int styleable SearchView_suggestionRowLayout 26
int styleable SearchView_useDrawerArrowDrawable 27
int styleable SearchView_voiceIcon 28
int[] styleable ShapeAppearance { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable ShapeableImageView_contentPadding 0
int styleable ShapeableImageView_contentPaddingBottom 1
int styleable ShapeableImageView_contentPaddingEnd 2
int styleable ShapeableImageView_contentPaddingLeft 3
int styleable ShapeableImageView_contentPaddingRight 4
int styleable ShapeableImageView_contentPaddingStart 5
int styleable ShapeableImageView_contentPaddingTop 6
int styleable ShapeableImageView_shapeAppearance 7
int styleable ShapeableImageView_shapeAppearanceOverlay 8
int styleable ShapeableImageView_strokeColor 9
int styleable ShapeableImageView_strokeWidth 10
int[] styleable SideSheetBehavior_Layout { 0x1010440, 0x1010120, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SideSheetBehavior_Layout_android_elevation 0
int styleable SideSheetBehavior_Layout_android_maxHeight 1
int styleable SideSheetBehavior_Layout_android_maxWidth 2
int styleable SideSheetBehavior_Layout_backgroundTint 3
int styleable SideSheetBehavior_Layout_behavior_draggable 4
int styleable SideSheetBehavior_Layout_coplanarSiblingViewId 5
int styleable SideSheetBehavior_Layout_shapeAppearance 6
int styleable SideSheetBehavior_Layout_shapeAppearanceOverlay 7
int[] styleable Slider { 0x101000e, 0x1010146, 0x1010024, 0x10102de, 0x10102df, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Slider_android_enabled 0
int styleable Slider_android_stepSize 1
int styleable Slider_android_value 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_minTouchTargetSize 9
int styleable Slider_thumbColor 10
int styleable Slider_thumbElevation 11
int styleable Slider_thumbRadius 12
int styleable Slider_thumbStrokeColor 13
int styleable Slider_thumbStrokeWidth 14
int styleable Slider_tickColor 15
int styleable Slider_tickColorActive 16
int styleable Slider_tickColorInactive 17
int styleable Slider_tickRadiusActive 18
int styleable Slider_tickRadiusInactive 19
int styleable Slider_tickVisible 20
int styleable Slider_trackColor 21
int styleable Slider_trackColorActive 22
int styleable Slider_trackColorInactive 23
int styleable Slider_trackHeight 24
int[] styleable Snackbar { 0x0, 0x0, 0x0 }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable SnackbarLayout { 0x0, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SnackbarLayout_actionTextColorAlpha 0
int styleable SnackbarLayout_android_maxWidth 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int styleable SnackbarLayout_shapeAppearance 8
int styleable SnackbarLayout_shapeAppearanceOverlay 9
int[] styleable Spinner { 0x1010262, 0x10100b2, 0x1010176, 0x101017b, 0x0 }
int styleable Spinner_android_dropDownWidth 0
int styleable Spinner_android_entries 1
int styleable Spinner_android_popupBackground 2
int styleable Spinner_android_prompt 3
int styleable Spinner_popupTheme 4
int[] styleable SplitPairFilter { 0x0, 0x0, 0x0 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SplitPairRule_animationBackgroundColor 0
int styleable SplitPairRule_clearTop 1
int styleable SplitPairRule_finishPrimaryWithSecondary 2
int styleable SplitPairRule_finishSecondaryWithPrimary 3
int styleable SplitPairRule_splitLayoutDirection 4
int styleable SplitPairRule_splitMaxAspectRatioInLandscape 5
int styleable SplitPairRule_splitMaxAspectRatioInPortrait 6
int styleable SplitPairRule_splitMinHeightDp 7
int styleable SplitPairRule_splitMinSmallestWidthDp 8
int styleable SplitPairRule_splitMinWidthDp 9
int styleable SplitPairRule_splitRatio 10
int styleable SplitPairRule_tag 11
int[] styleable SplitPlaceholderRule { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SplitPlaceholderRule_animationBackgroundColor 0
int styleable SplitPlaceholderRule_finishPrimaryWithPlaceholder 1
int styleable SplitPlaceholderRule_placeholderActivityName 2
int styleable SplitPlaceholderRule_splitLayoutDirection 3
int styleable SplitPlaceholderRule_splitMaxAspectRatioInLandscape 4
int styleable SplitPlaceholderRule_splitMaxAspectRatioInPortrait 5
int styleable SplitPlaceholderRule_splitMinHeightDp 6
int styleable SplitPlaceholderRule_splitMinSmallestWidthDp 7
int styleable SplitPlaceholderRule_splitMinWidthDp 8
int styleable SplitPlaceholderRule_splitRatio 9
int styleable SplitPlaceholderRule_stickyPlaceholder 10
int styleable SplitPlaceholderRule_tag 11
int[] styleable State { 0x10100d0, 0x0 }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
int styleable StateListDrawable_android_constantSize 0
int styleable StateListDrawable_android_dither 1
int styleable StateListDrawable_android_enterFadeDuration 2
int styleable StateListDrawable_android_exitFadeDuration 3
int styleable StateListDrawable_android_variablePadding 4
int styleable StateListDrawable_android_visible 5
int[] styleable StateListDrawableItem { 0x1010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x0 }
int styleable StateSet_defaultState 0
int[] styleable SwitchCompat { 0x1010125, 0x1010124, 0x1010142, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SwitchCompat_android_textOff 0
int styleable SwitchCompat_android_textOn 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x0 }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable TabItem { 0x1010002, 0x10100f2, 0x101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorAnimationMode 7
int styleable TabLayout_tabIndicatorColor 8
int styleable TabLayout_tabIndicatorFullWidth 9
int styleable TabLayout_tabIndicatorGravity 10
int styleable TabLayout_tabIndicatorHeight 11
int styleable TabLayout_tabInlineLabel 12
int styleable TabLayout_tabMaxWidth 13
int styleable TabLayout_tabMinWidth 14
int styleable TabLayout_tabMode 15
int styleable TabLayout_tabPadding 16
int styleable TabLayout_tabPaddingBottom 17
int styleable TabLayout_tabPaddingEnd 18
int styleable TabLayout_tabPaddingStart 19
int styleable TabLayout_tabPaddingTop 20
int styleable TabLayout_tabRippleColor 21
int styleable TabLayout_tabSelectedTextAppearance 22
int styleable TabLayout_tabSelectedTextColor 23
int styleable TabLayout_tabTextAppearance 24
int styleable TabLayout_tabTextColor 25
int styleable TabLayout_tabUnboundedRipple 26
int[] styleable TextAppearance { 0x10103ac, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x1010098, 0x101009a, 0x101009b, 0x1010585, 0x1010095, 0x1010097, 0x1010096, 0x0, 0x0, 0x0, 0x0 }
int styleable TextAppearance_android_fontFamily 0
int styleable TextAppearance_android_shadowColor 1
int styleable TextAppearance_android_shadowDx 2
int styleable TextAppearance_android_shadowDy 3
int styleable TextAppearance_android_shadowRadius 4
int styleable TextAppearance_android_textColor 5
int styleable TextAppearance_android_textColorHint 6
int styleable TextAppearance_android_textColorLink 7
int styleable TextAppearance_android_textFontWeight 8
int styleable TextAppearance_android_textSize 9
int styleable TextAppearance_android_textStyle 10
int styleable TextAppearance_android_typeface 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextInputEditText { 0x0 }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x101000e, 0x1010150, 0x1010157, 0x101011f, 0x101015a, 0x101013f, 0x101009a, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_hint 1
int styleable TextInputLayout_android_maxEms 2
int styleable TextInputLayout_android_maxWidth 3
int styleable TextInputLayout_android_minEms 4
int styleable TextInputLayout_android_minWidth 5
int styleable TextInputLayout_android_textColorHint 6
int styleable TextInputLayout_boxBackgroundColor 7
int styleable TextInputLayout_boxBackgroundMode 8
int styleable TextInputLayout_boxCollapsedPaddingTop 9
int styleable TextInputLayout_boxCornerRadiusBottomEnd 10
int styleable TextInputLayout_boxCornerRadiusBottomStart 11
int styleable TextInputLayout_boxCornerRadiusTopEnd 12
int styleable TextInputLayout_boxCornerRadiusTopStart 13
int styleable TextInputLayout_boxStrokeColor 14
int styleable TextInputLayout_boxStrokeErrorColor 15
int styleable TextInputLayout_boxStrokeWidth 16
int styleable TextInputLayout_boxStrokeWidthFocused 17
int styleable TextInputLayout_counterEnabled 18
int styleable TextInputLayout_counterMaxLength 19
int styleable TextInputLayout_counterOverflowTextAppearance 20
int styleable TextInputLayout_counterOverflowTextColor 21
int styleable TextInputLayout_counterTextAppearance 22
int styleable TextInputLayout_counterTextColor 23
int styleable TextInputLayout_cursorColor 24
int styleable TextInputLayout_cursorErrorColor 25
int styleable TextInputLayout_endIconCheckable 26
int styleable TextInputLayout_endIconContentDescription 27
int styleable TextInputLayout_endIconDrawable 28
int styleable TextInputLayout_endIconMinSize 29
int styleable TextInputLayout_endIconMode 30
int styleable TextInputLayout_endIconScaleType 31
int styleable TextInputLayout_endIconTint 32
int styleable TextInputLayout_endIconTintMode 33
int styleable TextInputLayout_errorAccessibilityLiveRegion 34
int styleable TextInputLayout_errorContentDescription 35
int styleable TextInputLayout_errorEnabled 36
int styleable TextInputLayout_errorIconDrawable 37
int styleable TextInputLayout_errorIconTint 38
int styleable TextInputLayout_errorIconTintMode 39
int styleable TextInputLayout_errorTextAppearance 40
int styleable TextInputLayout_errorTextColor 41
int styleable TextInputLayout_expandedHintEnabled 42
int styleable TextInputLayout_helperText 43
int styleable TextInputLayout_helperTextEnabled 44
int styleable TextInputLayout_helperTextTextAppearance 45
int styleable TextInputLayout_helperTextTextColor 46
int styleable TextInputLayout_hintAnimationEnabled 47
int styleable TextInputLayout_hintEnabled 48
int styleable TextInputLayout_hintTextAppearance 49
int styleable TextInputLayout_hintTextColor 50
int styleable TextInputLayout_passwordToggleContentDescription 51
int styleable TextInputLayout_passwordToggleDrawable 52
int styleable TextInputLayout_passwordToggleEnabled 53
int styleable TextInputLayout_passwordToggleTint 54
int styleable TextInputLayout_passwordToggleTintMode 55
int styleable TextInputLayout_placeholderText 56
int styleable TextInputLayout_placeholderTextAppearance 57
int styleable TextInputLayout_placeholderTextColor 58
int styleable TextInputLayout_prefixText 59
int styleable TextInputLayout_prefixTextAppearance 60
int styleable TextInputLayout_prefixTextColor 61
int styleable TextInputLayout_shapeAppearance 62
int styleable TextInputLayout_shapeAppearanceOverlay 63
int styleable TextInputLayout_startIconCheckable 64
int styleable TextInputLayout_startIconContentDescription 65
int styleable TextInputLayout_startIconDrawable 66
int styleable TextInputLayout_startIconMinSize 67
int styleable TextInputLayout_startIconScaleType 68
int styleable TextInputLayout_startIconTint 69
int styleable TextInputLayout_startIconTintMode 70
int styleable TextInputLayout_suffixText 71
int styleable TextInputLayout_suffixTextAppearance 72
int styleable TextInputLayout_suffixTextColor 73
int[] styleable ThemeEnforcement { 0x1010034, 0x0, 0x0 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x10100af, 0x1010140, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x10100f6, 0x1010140, 0x101013f, 0x10100d5, 0x101014f, 0x1010034, 0x1010098, 0x0 }
int styleable Tooltip_android_layout_margin 0
int styleable Tooltip_android_minHeight 1
int styleable Tooltip_android_minWidth 2
int styleable Tooltip_android_padding 3
int styleable Tooltip_android_text 4
int styleable Tooltip_android_textAppearance 5
int styleable Tooltip_android_textColor 6
int styleable Tooltip_backgroundTint 7
int[] styleable Transform { 0x1010440, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010320, 0x1010321, 0x1010322, 0x1010323, 0x10103fa }
int styleable Transform_android_elevation 0
int styleable Transform_android_rotation 1
int styleable Transform_android_rotationX 2
int styleable Transform_android_rotationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_transformPivotX 6
int styleable Transform_android_transformPivotY 7
int styleable Transform_android_translationX 8
int styleable Transform_android_translationY 9
int styleable Transform_android_translationZ 10
int[] styleable Transition { 0x10100d0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x10100da, 0x1010000, 0x0, 0x0, 0x0 }
int styleable View_android_focusable 0
int styleable View_android_theme 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x10100d4, 0x0, 0x0 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x10100c4 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x10100d0, 0x10100f3, 0x10100f2 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_inflatedId 1
int styleable ViewStubCompat_android_layout 2
