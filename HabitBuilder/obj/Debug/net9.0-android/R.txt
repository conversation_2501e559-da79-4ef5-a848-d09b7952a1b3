int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim design_bottom_sheet_slide_in 0x7f010018
int anim design_bottom_sheet_slide_out 0x7f010019
int anim design_snackbar_in 0x7f01001a
int anim design_snackbar_out 0x7f01001b
int anim enterfromleft 0x7f01001c
int anim enterfromright 0x7f01001d
int anim exittoleft 0x7f01001e
int anim exittoright 0x7f01001f
int anim fragment_fast_out_extra_slow_in 0x7f010020
int anim linear_indeterminate_line1_head_interpolator 0x7f010021
int anim linear_indeterminate_line1_tail_interpolator 0x7f010022
int anim linear_indeterminate_line2_head_interpolator 0x7f010023
int anim linear_indeterminate_line2_tail_interpolator 0x7f010024
int anim m3_bottom_sheet_slide_in 0x7f010025
int anim m3_bottom_sheet_slide_out 0x7f010026
int anim m3_motion_fade_enter 0x7f010027
int anim m3_motion_fade_exit 0x7f010028
int anim m3_side_sheet_enter_from_left 0x7f010029
int anim m3_side_sheet_enter_from_right 0x7f01002a
int anim m3_side_sheet_exit_to_left 0x7f01002b
int anim m3_side_sheet_exit_to_right 0x7f01002c
int anim mtrl_bottom_sheet_slide_in 0x7f01002d
int anim mtrl_bottom_sheet_slide_out 0x7f01002e
int anim mtrl_card_lowers_interpolator 0x7f01002f
int anim nav_default_enter_anim 0x7f010030
int anim nav_default_exit_anim 0x7f010031
int anim nav_default_pop_enter_anim 0x7f010032
int anim nav_default_pop_exit_anim 0x7f010033
int anim nav_modal_default_enter_anim 0x7f010034
int anim nav_modal_default_exit_anim 0x7f010035
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator fragment_close_enter 0x7f020003
int animator fragment_close_exit 0x7f020004
int animator fragment_fade_enter 0x7f020005
int animator fragment_fade_exit 0x7f020006
int animator fragment_open_enter 0x7f020007
int animator fragment_open_exit 0x7f020008
int animator m3_appbar_state_list_animator 0x7f020009
int animator m3_btn_elevated_btn_state_list_anim 0x7f02000a
int animator m3_btn_state_list_anim 0x7f02000b
int animator m3_card_elevated_state_list_anim 0x7f02000c
int animator m3_card_state_list_anim 0x7f02000d
int animator m3_chip_state_list_anim 0x7f02000e
int animator m3_elevated_chip_state_list_anim 0x7f02000f
int animator m3_extended_fab_change_size_collapse_motion_spec 0x7f020010
int animator m3_extended_fab_change_size_expand_motion_spec 0x7f020011
int animator m3_extended_fab_hide_motion_spec 0x7f020012
int animator m3_extended_fab_show_motion_spec 0x7f020013
int animator m3_extended_fab_state_list_animator 0x7f020014
int animator mtrl_btn_state_list_anim 0x7f020015
int animator mtrl_btn_unelevated_state_list_anim 0x7f020016
int animator mtrl_card_state_list_anim 0x7f020017
int animator mtrl_chip_state_list_anim 0x7f020018
int animator mtrl_extended_fab_change_size_collapse_motion_spec 0x7f020019
int animator mtrl_extended_fab_change_size_expand_motion_spec 0x7f02001a
int animator mtrl_extended_fab_hide_motion_spec 0x7f02001b
int animator mtrl_extended_fab_show_motion_spec 0x7f02001c
int animator mtrl_extended_fab_state_list_animator 0x7f02001d
int animator mtrl_fab_hide_motion_spec 0x7f02001e
int animator mtrl_fab_show_motion_spec 0x7f02001f
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020020
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020021
int animator nav_default_enter_anim 0x7f020022
int animator nav_default_exit_anim 0x7f020023
int animator nav_default_pop_enter_anim 0x7f020024
int animator nav_default_pop_exit_anim 0x7f020025
int attr SharedValue 0x7f030000
int attr SharedValueId 0x7f030001
int attr action 0x7f030002
int attr actionBarDivider 0x7f030003
int attr actionBarItemBackground 0x7f030004
int attr actionBarPopupTheme 0x7f030005
int attr actionBarSize 0x7f030006
int attr actionBarSplitStyle 0x7f030007
int attr actionBarStyle 0x7f030008
int attr actionBarTabBarStyle 0x7f030009
int attr actionBarTabStyle 0x7f03000a
int attr actionBarTabTextStyle 0x7f03000b
int attr actionBarTheme 0x7f03000c
int attr actionBarWidgetTheme 0x7f03000d
int attr actionButtonStyle 0x7f03000e
int attr actionDropDownStyle 0x7f03000f
int attr actionLayout 0x7f030010
int attr actionMenuTextAppearance 0x7f030011
int attr actionMenuTextColor 0x7f030012
int attr actionModeBackground 0x7f030013
int attr actionModeCloseButtonStyle 0x7f030014
int attr actionModeCloseContentDescription 0x7f030015
int attr actionModeCloseDrawable 0x7f030016
int attr actionModeCopyDrawable 0x7f030017
int attr actionModeCutDrawable 0x7f030018
int attr actionModeFindDrawable 0x7f030019
int attr actionModePasteDrawable 0x7f03001a
int attr actionModePopupWindowStyle 0x7f03001b
int attr actionModeSelectAllDrawable 0x7f03001c
int attr actionModeShareDrawable 0x7f03001d
int attr actionModeSplitBackground 0x7f03001e
int attr actionModeStyle 0x7f03001f
int attr actionModeTheme 0x7f030020
int attr actionModeWebSearchDrawable 0x7f030021
int attr actionOverflowButtonStyle 0x7f030022
int attr actionOverflowMenuStyle 0x7f030023
int attr actionProviderClass 0x7f030024
int attr actionTextColorAlpha 0x7f030025
int attr actionViewClass 0x7f030026
int attr activeIndicatorLabelPadding 0x7f030027
int attr activityAction 0x7f030028
int attr activityChooserViewStyle 0x7f030029
int attr activityName 0x7f03002a
int attr addElevationShadow 0x7f03002b
int attr alertDialogButtonGroupStyle 0x7f03002c
int attr alertDialogCenterButtons 0x7f03002d
int attr alertDialogStyle 0x7f03002e
int attr alertDialogTheme 0x7f03002f
int attr allowStacking 0x7f030030
int attr alpha 0x7f030031
int attr alphabeticModifiers 0x7f030032
int attr altSrc 0x7f030033
int attr alwaysExpand 0x7f030034
int attr animateCircleAngleTo 0x7f030035
int attr animateRelativeTo 0x7f030036
int attr animationBackgroundColor 0x7f030037
int attr animationMode 0x7f030038
int attr appBarLayoutStyle 0x7f030039
int attr applyMotionScene 0x7f03003a
int attr arcMode 0x7f03003b
int attr argType 0x7f03003c
int attr arrowHeadLength 0x7f03003d
int attr arrowShaftLength 0x7f03003e
int attr attributeName 0x7f03003f
int attr autoAdjustToWithinGrandparentBounds 0x7f030040
int attr autoCompleteMode 0x7f030041
int attr autoCompleteTextViewStyle 0x7f030042
int attr autoSizeMaxTextSize 0x7f030043
int attr autoSizeMinTextSize 0x7f030044
int attr autoSizePresetSizes 0x7f030045
int attr autoSizeStepGranularity 0x7f030046
int attr autoSizeTextType 0x7f030047
int attr autoTransition 0x7f030048
int attr background 0x7f030049
int attr backgroundColor 0x7f03004a
int attr backgroundInsetBottom 0x7f03004b
int attr backgroundInsetEnd 0x7f03004c
int attr backgroundInsetStart 0x7f03004d
int attr backgroundInsetTop 0x7f03004e
int attr backgroundOverlayColorAlpha 0x7f03004f
int attr backgroundSplit 0x7f030050
int attr backgroundStacked 0x7f030051
int attr backgroundTint 0x7f030052
int attr backgroundTintMode 0x7f030053
int attr badgeGravity 0x7f030054
int attr badgeHeight 0x7f030055
int attr badgeRadius 0x7f030056
int attr badgeShapeAppearance 0x7f030057
int attr badgeShapeAppearanceOverlay 0x7f030058
int attr badgeStyle 0x7f030059
int attr badgeText 0x7f03005a
int attr badgeTextAppearance 0x7f03005b
int attr badgeTextColor 0x7f03005c
int attr badgeVerticalPadding 0x7f03005d
int attr badgeWidePadding 0x7f03005e
int attr badgeWidth 0x7f03005f
int attr badgeWithTextHeight 0x7f030060
int attr badgeWithTextRadius 0x7f030061
int attr badgeWithTextShapeAppearance 0x7f030062
int attr badgeWithTextShapeAppearanceOverlay 0x7f030063
int attr badgeWithTextWidth 0x7f030064
int attr barLength 0x7f030065
int attr barrierAllowsGoneWidgets 0x7f030066
int attr barrierDirection 0x7f030067
int attr barrierMargin 0x7f030068
int attr behavior_autoHide 0x7f030069
int attr behavior_autoShrink 0x7f03006a
int attr behavior_draggable 0x7f03006b
int attr behavior_expandedOffset 0x7f03006c
int attr behavior_fitToContents 0x7f03006d
int attr behavior_halfExpandedRatio 0x7f03006e
int attr behavior_hideable 0x7f03006f
int attr behavior_overlapTop 0x7f030070
int attr behavior_peekHeight 0x7f030071
int attr behavior_saveFlags 0x7f030072
int attr behavior_significantVelocityThreshold 0x7f030073
int attr behavior_skipCollapsed 0x7f030074
int attr blendSrc 0x7f030075
int attr borderRound 0x7f030076
int attr borderRoundPercent 0x7f030077
int attr borderWidth 0x7f030078
int attr borderlessButtonStyle 0x7f030079
int attr bottomAppBarStyle 0x7f03007a
int attr bottomInsetScrimEnabled 0x7f03007b
int attr bottomNavigationStyle 0x7f03007c
int attr bottomNavigationViewStyle 0x7f03007d
int attr bottomSheetDialogTheme 0x7f03007e
int attr bottomSheetDragHandleStyle 0x7f03007f
int attr bottomSheetStyle 0x7f030080
int attr boxBackgroundColor 0x7f030081
int attr boxBackgroundMode 0x7f030082
int attr boxCollapsedPaddingTop 0x7f030083
int attr boxCornerRadiusBottomEnd 0x7f030084
int attr boxCornerRadiusBottomStart 0x7f030085
int attr boxCornerRadiusTopEnd 0x7f030086
int attr boxCornerRadiusTopStart 0x7f030087
int attr boxStrokeColor 0x7f030088
int attr boxStrokeErrorColor 0x7f030089
int attr boxStrokeWidth 0x7f03008a
int attr boxStrokeWidthFocused 0x7f03008b
int attr brightness 0x7f03008c
int attr buttonBarButtonStyle 0x7f03008d
int attr buttonBarNegativeButtonStyle 0x7f03008e
int attr buttonBarNeutralButtonStyle 0x7f03008f
int attr buttonBarPositiveButtonStyle 0x7f030090
int attr buttonBarStyle 0x7f030091
int attr buttonCompat 0x7f030092
int attr buttonGravity 0x7f030093
int attr buttonIcon 0x7f030094
int attr buttonIconDimen 0x7f030095
int attr buttonIconTint 0x7f030096
int attr buttonIconTintMode 0x7f030097
int attr buttonPanelSideLayout 0x7f030098
int attr buttonStyle 0x7f030099
int attr buttonStyleSmall 0x7f03009a
int attr buttonTint 0x7f03009b
int attr buttonTintMode 0x7f03009c
int attr cardBackgroundColor 0x7f03009d
int attr cardCornerRadius 0x7f03009e
int attr cardElevation 0x7f03009f
int attr cardForegroundColor 0x7f0300a0
int attr cardMaxElevation 0x7f0300a1
int attr cardPreventCornerOverlap 0x7f0300a2
int attr cardUseCompatPadding 0x7f0300a3
int attr cardViewStyle 0x7f0300a4
int attr carousel_backwardTransition 0x7f0300a5
int attr carousel_emptyViewsBehavior 0x7f0300a6
int attr carousel_firstView 0x7f0300a7
int attr carousel_forwardTransition 0x7f0300a8
int attr carousel_infinite 0x7f0300a9
int attr carousel_nextState 0x7f0300aa
int attr carousel_previousState 0x7f0300ab
int attr carousel_touchUpMode 0x7f0300ac
int attr carousel_touchUp_dampeningFactor 0x7f0300ad
int attr carousel_touchUp_velocityThreshold 0x7f0300ae
int attr centerIfNoTextEnabled 0x7f0300af
int attr chainUseRtl 0x7f0300b0
int attr checkMarkCompat 0x7f0300b1
int attr checkMarkTint 0x7f0300b2
int attr checkMarkTintMode 0x7f0300b3
int attr checkboxStyle 0x7f0300b4
int attr checkedButton 0x7f0300b5
int attr checkedChip 0x7f0300b6
int attr checkedIcon 0x7f0300b7
int attr checkedIconEnabled 0x7f0300b8
int attr checkedIconGravity 0x7f0300b9
int attr checkedIconMargin 0x7f0300ba
int attr checkedIconSize 0x7f0300bb
int attr checkedIconTint 0x7f0300bc
int attr checkedIconVisible 0x7f0300bd
int attr checkedState 0x7f0300be
int attr checkedTextViewStyle 0x7f0300bf
int attr chipBackgroundColor 0x7f0300c0
int attr chipCornerRadius 0x7f0300c1
int attr chipEndPadding 0x7f0300c2
int attr chipGroupStyle 0x7f0300c3
int attr chipIcon 0x7f0300c4
int attr chipIconEnabled 0x7f0300c5
int attr chipIconSize 0x7f0300c6
int attr chipIconTint 0x7f0300c7
int attr chipIconVisible 0x7f0300c8
int attr chipMinHeight 0x7f0300c9
int attr chipMinTouchTargetSize 0x7f0300ca
int attr chipSpacing 0x7f0300cb
int attr chipSpacingHorizontal 0x7f0300cc
int attr chipSpacingVertical 0x7f0300cd
int attr chipStandaloneStyle 0x7f0300ce
int attr chipStartPadding 0x7f0300cf
int attr chipStrokeColor 0x7f0300d0
int attr chipStrokeWidth 0x7f0300d1
int attr chipStyle 0x7f0300d2
int attr chipSurfaceColor 0x7f0300d3
int attr circleRadius 0x7f0300d4
int attr circularProgressIndicatorStyle 0x7f0300d5
int attr circularflow_angles 0x7f0300d6
int attr circularflow_defaultAngle 0x7f0300d7
int attr circularflow_defaultRadius 0x7f0300d8
int attr circularflow_radiusInDP 0x7f0300d9
int attr circularflow_viewCenter 0x7f0300da
int attr clearTop 0x7f0300db
int attr clearsTag 0x7f0300dc
int attr clickAction 0x7f0300dd
int attr clockFaceBackgroundColor 0x7f0300de
int attr clockHandColor 0x7f0300df
int attr clockIcon 0x7f0300e0
int attr clockNumberTextColor 0x7f0300e1
int attr closeIcon 0x7f0300e2
int attr closeIconEnabled 0x7f0300e3
int attr closeIconEndPadding 0x7f0300e4
int attr closeIconSize 0x7f0300e5
int attr closeIconStartPadding 0x7f0300e6
int attr closeIconTint 0x7f0300e7
int attr closeIconVisible 0x7f0300e8
int attr closeItemLayout 0x7f0300e9
int attr collapseContentDescription 0x7f0300ea
int attr collapseIcon 0x7f0300eb
int attr collapsedSize 0x7f0300ec
int attr collapsedTitleGravity 0x7f0300ed
int attr collapsedTitleTextAppearance 0x7f0300ee
int attr collapsedTitleTextColor 0x7f0300ef
int attr collapsingToolbarLayoutLargeSize 0x7f0300f0
int attr collapsingToolbarLayoutLargeStyle 0x7f0300f1
int attr collapsingToolbarLayoutMediumSize 0x7f0300f2
int attr collapsingToolbarLayoutMediumStyle 0x7f0300f3
int attr collapsingToolbarLayoutStyle 0x7f0300f4
int attr collectionViewStyle 0x7f0300f5
int attr color 0x7f0300f6
int attr colorAccent 0x7f0300f7
int attr colorBackgroundFloating 0x7f0300f8
int attr colorButtonNormal 0x7f0300f9
int attr colorContainer 0x7f0300fa
int attr colorControlActivated 0x7f0300fb
int attr colorControlHighlight 0x7f0300fc
int attr colorControlNormal 0x7f0300fd
int attr colorError 0x7f0300fe
int attr colorErrorContainer 0x7f0300ff
int attr colorOnBackground 0x7f030100
int attr colorOnContainer 0x7f030101
int attr colorOnContainerUnchecked 0x7f030102
int attr colorOnError 0x7f030103
int attr colorOnErrorContainer 0x7f030104
int attr colorOnPrimary 0x7f030105
int attr colorOnPrimaryContainer 0x7f030106
int attr colorOnPrimaryFixed 0x7f030107
int attr colorOnPrimaryFixedVariant 0x7f030108
int attr colorOnPrimarySurface 0x7f030109
int attr colorOnSecondary 0x7f03010a
int attr colorOnSecondaryContainer 0x7f03010b
int attr colorOnSecondaryFixed 0x7f03010c
int attr colorOnSecondaryFixedVariant 0x7f03010d
int attr colorOnSurface 0x7f03010e
int attr colorOnSurfaceInverse 0x7f03010f
int attr colorOnSurfaceVariant 0x7f030110
int attr colorOnTertiary 0x7f030111
int attr colorOnTertiaryContainer 0x7f030112
int attr colorOnTertiaryFixed 0x7f030113
int attr colorOnTertiaryFixedVariant 0x7f030114
int attr colorOutline 0x7f030115
int attr colorOutlineVariant 0x7f030116
int attr colorPrimary 0x7f030117
int attr colorPrimaryContainer 0x7f030118
int attr colorPrimaryDark 0x7f030119
int attr colorPrimaryFixed 0x7f03011a
int attr colorPrimaryFixedDim 0x7f03011b
int attr colorPrimaryInverse 0x7f03011c
int attr colorPrimarySurface 0x7f03011d
int attr colorPrimaryVariant 0x7f03011e
int attr colorSecondary 0x7f03011f
int attr colorSecondaryContainer 0x7f030120
int attr colorSecondaryFixed 0x7f030121
int attr colorSecondaryFixedDim 0x7f030122
int attr colorSecondaryVariant 0x7f030123
int attr colorSurface 0x7f030124
int attr colorSurfaceBright 0x7f030125
int attr colorSurfaceContainer 0x7f030126
int attr colorSurfaceContainerHigh 0x7f030127
int attr colorSurfaceContainerHighest 0x7f030128
int attr colorSurfaceContainerLow 0x7f030129
int attr colorSurfaceContainerLowest 0x7f03012a
int attr colorSurfaceDim 0x7f03012b
int attr colorSurfaceInverse 0x7f03012c
int attr colorSurfaceVariant 0x7f03012d
int attr colorSwitchThumbNormal 0x7f03012e
int attr colorTertiary 0x7f03012f
int attr colorTertiaryContainer 0x7f030130
int attr colorTertiaryFixed 0x7f030131
int attr colorTertiaryFixedDim 0x7f030132
int attr commitIcon 0x7f030133
int attr compatShadowEnabled 0x7f030134
int attr constraintRotate 0x7f030135
int attr constraintSet 0x7f030136
int attr constraintSetEnd 0x7f030137
int attr constraintSetStart 0x7f030138
int attr constraint_referenced_ids 0x7f030139
int attr constraint_referenced_tags 0x7f03013a
int attr constraints 0x7f03013b
int attr content 0x7f03013c
int attr contentDescription 0x7f03013d
int attr contentInsetEnd 0x7f03013e
int attr contentInsetEndWithActions 0x7f03013f
int attr contentInsetLeft 0x7f030140
int attr contentInsetRight 0x7f030141
int attr contentInsetStart 0x7f030142
int attr contentInsetStartWithNavigation 0x7f030143
int attr contentPadding 0x7f030144
int attr contentPaddingBottom 0x7f030145
int attr contentPaddingEnd 0x7f030146
int attr contentPaddingLeft 0x7f030147
int attr contentPaddingRight 0x7f030148
int attr contentPaddingStart 0x7f030149
int attr contentPaddingTop 0x7f03014a
int attr contentScrim 0x7f03014b
int attr contrast 0x7f03014c
int attr controlBackground 0x7f03014d
int attr coordinatorLayoutStyle 0x7f03014e
int attr coplanarSiblingViewId 0x7f03014f
int attr cornerFamily 0x7f030150
int attr cornerFamilyBottomLeft 0x7f030151
int attr cornerFamilyBottomRight 0x7f030152
int attr cornerFamilyTopLeft 0x7f030153
int attr cornerFamilyTopRight 0x7f030154
int attr cornerRadius 0x7f030155
int attr cornerSize 0x7f030156
int attr cornerSizeBottomLeft 0x7f030157
int attr cornerSizeBottomRight 0x7f030158
int attr cornerSizeTopLeft 0x7f030159
int attr cornerSizeTopRight 0x7f03015a
int attr counterEnabled 0x7f03015b
int attr counterMaxLength 0x7f03015c
int attr counterOverflowTextAppearance 0x7f03015d
int attr counterOverflowTextColor 0x7f03015e
int attr counterTextAppearance 0x7f03015f
int attr counterTextColor 0x7f030160
int attr crossfade 0x7f030161
int attr currentState 0x7f030162
int attr cursorColor 0x7f030163
int attr cursorErrorColor 0x7f030164
int attr curveFit 0x7f030165
int attr customBoolean 0x7f030166
int attr customColorDrawableValue 0x7f030167
int attr customColorValue 0x7f030168
int attr customDimension 0x7f030169
int attr customFloatValue 0x7f03016a
int attr customIntegerValue 0x7f03016b
int attr customNavigationLayout 0x7f03016c
int attr customPixelDimension 0x7f03016d
int attr customReference 0x7f03016e
int attr customStringValue 0x7f03016f
int attr data 0x7f030170
int attr dataPattern 0x7f030171
int attr dayInvalidStyle 0x7f030172
int attr daySelectedStyle 0x7f030173
int attr dayStyle 0x7f030174
int attr dayTodayStyle 0x7f030175
int attr defaultDuration 0x7f030176
int attr defaultMarginsEnabled 0x7f030177
int attr defaultNavHost 0x7f030178
int attr defaultQueryHint 0x7f030179
int attr defaultScrollFlagsEnabled 0x7f03017a
int attr defaultState 0x7f03017b
int attr deltaPolarAngle 0x7f03017c
int attr deltaPolarRadius 0x7f03017d
int attr deriveConstraintsFrom 0x7f03017e
int attr destination 0x7f03017f
int attr dialogCornerRadius 0x7f030180
int attr dialogPreferredPadding 0x7f030181
int attr dialogTheme 0x7f030182
int attr displayOptions 0x7f030183
int attr divider 0x7f030184
int attr dividerColor 0x7f030185
int attr dividerHorizontal 0x7f030186
int attr dividerInsetEnd 0x7f030187
int attr dividerInsetStart 0x7f030188
int attr dividerPadding 0x7f030189
int attr dividerThickness 0x7f03018a
int attr dividerVertical 0x7f03018b
int attr dragDirection 0x7f03018c
int attr dragScale 0x7f03018d
int attr dragThreshold 0x7f03018e
int attr drawPath 0x7f03018f
int attr drawableBottomCompat 0x7f030190
int attr drawableEndCompat 0x7f030191
int attr drawableLeftCompat 0x7f030192
int attr drawableRightCompat 0x7f030193
int attr drawableSize 0x7f030194
int attr drawableStartCompat 0x7f030195
int attr drawableTint 0x7f030196
int attr drawableTintMode 0x7f030197
int attr drawableTopCompat 0x7f030198
int attr drawerArrowStyle 0x7f030199
int attr drawerLayoutCornerSize 0x7f03019a
int attr drawerLayoutStyle 0x7f03019b
int attr dropDownBackgroundTint 0x7f03019c
int attr dropDownListViewStyle 0x7f03019d
int attr dropdownListPreferredItemHeight 0x7f03019e
int attr duration 0x7f03019f
int attr dynamicColorThemeOverlay 0x7f0301a0
int attr editTextBackground 0x7f0301a1
int attr editTextColor 0x7f0301a2
int attr editTextStyle 0x7f0301a3
int attr elevation 0x7f0301a4
int attr elevationOverlayAccentColor 0x7f0301a5
int attr elevationOverlayColor 0x7f0301a6
int attr elevationOverlayEnabled 0x7f0301a7
int attr emojiCompatEnabled 0x7f0301a8
int attr enableEdgeToEdge 0x7f0301a9
int attr endIconCheckable 0x7f0301aa
int attr endIconContentDescription 0x7f0301ab
int attr endIconDrawable 0x7f0301ac
int attr endIconMinSize 0x7f0301ad
int attr endIconMode 0x7f0301ae
int attr endIconScaleType 0x7f0301af
int attr endIconTint 0x7f0301b0
int attr endIconTintMode 0x7f0301b1
int attr enforceMaterialTheme 0x7f0301b2
int attr enforceTextAppearance 0x7f0301b3
int attr ensureMinTouchTargetSize 0x7f0301b4
int attr enterAnim 0x7f0301b5
int attr errorAccessibilityLabel 0x7f0301b6
int attr errorAccessibilityLiveRegion 0x7f0301b7
int attr errorContentDescription 0x7f0301b8
int attr errorEnabled 0x7f0301b9
int attr errorIconDrawable 0x7f0301ba
int attr errorIconTint 0x7f0301bb
int attr errorIconTintMode 0x7f0301bc
int attr errorShown 0x7f0301bd
int attr errorTextAppearance 0x7f0301be
int attr errorTextColor 0x7f0301bf
int attr exitAnim 0x7f0301c0
int attr expandActivityOverflowButtonDrawable 0x7f0301c1
int attr expanded 0x7f0301c2
int attr expandedHintEnabled 0x7f0301c3
int attr expandedTitleGravity 0x7f0301c4
int attr expandedTitleMargin 0x7f0301c5
int attr expandedTitleMarginBottom 0x7f0301c6
int attr expandedTitleMarginEnd 0x7f0301c7
int attr expandedTitleMarginStart 0x7f0301c8
int attr expandedTitleMarginTop 0x7f0301c9
int attr expandedTitleTextAppearance 0x7f0301ca
int attr expandedTitleTextColor 0x7f0301cb
int attr extendMotionSpec 0x7f0301cc
int attr extendStrategy 0x7f0301cd
int attr extendedFloatingActionButtonPrimaryStyle 0x7f0301ce
int attr extendedFloatingActionButtonSecondaryStyle 0x7f0301cf
int attr extendedFloatingActionButtonStyle 0x7f0301d0
int attr extendedFloatingActionButtonSurfaceStyle 0x7f0301d1
int attr extendedFloatingActionButtonTertiaryStyle 0x7f0301d2
int attr extraMultilineHeightEnabled 0x7f0301d3
int attr fabAlignmentMode 0x7f0301d4
int attr fabAlignmentModeEndMargin 0x7f0301d5
int attr fabAnchorMode 0x7f0301d6
int attr fabAnimationMode 0x7f0301d7
int attr fabCradleMargin 0x7f0301d8
int attr fabCradleRoundedCornerRadius 0x7f0301d9
int attr fabCradleVerticalOffset 0x7f0301da
int attr fabCustomSize 0x7f0301db
int attr fabSize 0x7f0301dc
int attr fastScrollEnabled 0x7f0301dd
int attr fastScrollHorizontalThumbDrawable 0x7f0301de
int attr fastScrollHorizontalTrackDrawable 0x7f0301df
int attr fastScrollVerticalThumbDrawable 0x7f0301e0
int attr fastScrollVerticalTrackDrawable 0x7f0301e1
int attr finishPrimaryWithPlaceholder 0x7f0301e2
int attr finishPrimaryWithSecondary 0x7f0301e3
int attr finishSecondaryWithPrimary 0x7f0301e4
int attr firstBaselineToTopHeight 0x7f0301e5
int attr floatingActionButtonLargePrimaryStyle 0x7f0301e6
int attr floatingActionButtonLargeSecondaryStyle 0x7f0301e7
int attr floatingActionButtonLargeStyle 0x7f0301e8
int attr floatingActionButtonLargeSurfaceStyle 0x7f0301e9
int attr floatingActionButtonLargeTertiaryStyle 0x7f0301ea
int attr floatingActionButtonPrimaryStyle 0x7f0301eb
int attr floatingActionButtonSecondaryStyle 0x7f0301ec
int attr floatingActionButtonSmallPrimaryStyle 0x7f0301ed
int attr floatingActionButtonSmallSecondaryStyle 0x7f0301ee
int attr floatingActionButtonSmallStyle 0x7f0301ef
int attr floatingActionButtonSmallSurfaceStyle 0x7f0301f0
int attr floatingActionButtonSmallTertiaryStyle 0x7f0301f1
int attr floatingActionButtonStyle 0x7f0301f2
int attr floatingActionButtonSurfaceStyle 0x7f0301f3
int attr floatingActionButtonTertiaryStyle 0x7f0301f4
int attr flow_firstHorizontalBias 0x7f0301f5
int attr flow_firstHorizontalStyle 0x7f0301f6
int attr flow_firstVerticalBias 0x7f0301f7
int attr flow_firstVerticalStyle 0x7f0301f8
int attr flow_horizontalAlign 0x7f0301f9
int attr flow_horizontalBias 0x7f0301fa
int attr flow_horizontalGap 0x7f0301fb
int attr flow_horizontalStyle 0x7f0301fc
int attr flow_lastHorizontalBias 0x7f0301fd
int attr flow_lastHorizontalStyle 0x7f0301fe
int attr flow_lastVerticalBias 0x7f0301ff
int attr flow_lastVerticalStyle 0x7f030200
int attr flow_maxElementsWrap 0x7f030201
int attr flow_padding 0x7f030202
int attr flow_verticalAlign 0x7f030203
int attr flow_verticalBias 0x7f030204
int attr flow_verticalGap 0x7f030205
int attr flow_verticalStyle 0x7f030206
int attr flow_wrapMode 0x7f030207
int attr font 0x7f030208
int attr fontFamily 0x7f030209
int attr fontProviderAuthority 0x7f03020a
int attr fontProviderCerts 0x7f03020b
int attr fontProviderFallbackQuery 0x7f03020c
int attr fontProviderFetchStrategy 0x7f03020d
int attr fontProviderFetchTimeout 0x7f03020e
int attr fontProviderPackage 0x7f03020f
int attr fontProviderQuery 0x7f030210
int attr fontProviderSystemFontFamily 0x7f030211
int attr fontStyle 0x7f030212
int attr fontVariationSettings 0x7f030213
int attr fontWeight 0x7f030214
int attr forceApplySystemWindowInsetTop 0x7f030215
int attr forceDefaultNavigationOnClickListener 0x7f030216
int attr foregroundInsidePadding 0x7f030217
int attr framePosition 0x7f030218
int attr gapBetweenBars 0x7f030219
int attr gestureInsetBottomIgnored 0x7f03021a
int attr goIcon 0x7f03021b
int attr graph 0x7f03021c
int attr grid_columnWeights 0x7f03021d
int attr grid_columns 0x7f03021e
int attr grid_horizontalGaps 0x7f03021f
int attr grid_orientation 0x7f030220
int attr grid_rowWeights 0x7f030221
int attr grid_rows 0x7f030222
int attr grid_skips 0x7f030223
int attr grid_spans 0x7f030224
int attr grid_useRtl 0x7f030225
int attr grid_validateInputs 0x7f030226
int attr grid_verticalGaps 0x7f030227
int attr guidelineUseRtl 0x7f030228
int attr haloColor 0x7f030229
int attr haloRadius 0x7f03022a
int attr headerLayout 0x7f03022b
int attr height 0x7f03022c
int attr helperText 0x7f03022d
int attr helperTextEnabled 0x7f03022e
int attr helperTextTextAppearance 0x7f03022f
int attr helperTextTextColor 0x7f030230
int attr hideAnimationBehavior 0x7f030231
int attr hideMotionSpec 0x7f030232
int attr hideNavigationIcon 0x7f030233
int attr hideOnContentScroll 0x7f030234
int attr hideOnScroll 0x7f030235
int attr hintAnimationEnabled 0x7f030236
int attr hintEnabled 0x7f030237
int attr hintTextAppearance 0x7f030238
int attr hintTextColor 0x7f030239
int attr homeAsUpIndicator 0x7f03023a
int attr homeLayout 0x7f03023b
int attr horizontalOffset 0x7f03023c
int attr horizontalOffsetWithText 0x7f03023d
int attr hoveredFocusedTranslationZ 0x7f03023e
int attr icon 0x7f03023f
int attr iconEndPadding 0x7f030240
int attr iconGravity 0x7f030241
int attr iconPadding 0x7f030242
int attr iconSize 0x7f030243
int attr iconStartPadding 0x7f030244
int attr iconTint 0x7f030245
int attr iconTintMode 0x7f030246
int attr iconifiedByDefault 0x7f030247
int attr ifTagNotSet 0x7f030248
int attr ifTagSet 0x7f030249
int attr imageButtonStyle 0x7f03024a
int attr imagePanX 0x7f03024b
int attr imagePanY 0x7f03024c
int attr imageRotate 0x7f03024d
int attr imageZoom 0x7f03024e
int attr indeterminateAnimationType 0x7f03024f
int attr indeterminateProgressStyle 0x7f030250
int attr indicatorColor 0x7f030251
int attr indicatorDirectionCircular 0x7f030252
int attr indicatorDirectionLinear 0x7f030253
int attr indicatorInset 0x7f030254
int attr indicatorSize 0x7f030255
int attr indicatorTrackGapSize 0x7f030256
int attr initialActivityCount 0x7f030257
int attr insetForeground 0x7f030258
int attr isLightTheme 0x7f030259
int attr isMaterial3DynamicColorApplied 0x7f03025a
int attr isMaterial3Theme 0x7f03025b
int attr isMaterialTheme 0x7f03025c
int attr itemActiveIndicatorStyle 0x7f03025d
int attr itemBackground 0x7f03025e
int attr itemFillColor 0x7f03025f
int attr itemHorizontalPadding 0x7f030260
int attr itemHorizontalTranslationEnabled 0x7f030261
int attr itemIconPadding 0x7f030262
int attr itemIconSize 0x7f030263
int attr itemIconTint 0x7f030264
int attr itemMaxLines 0x7f030265
int attr itemMinHeight 0x7f030266
int attr itemPadding 0x7f030267
int attr itemPaddingBottom 0x7f030268
int attr itemPaddingTop 0x7f030269
int attr itemRippleColor 0x7f03026a
int attr itemShapeAppearance 0x7f03026b
int attr itemShapeAppearanceOverlay 0x7f03026c
int attr itemShapeFillColor 0x7f03026d
int attr itemShapeInsetBottom 0x7f03026e
int attr itemShapeInsetEnd 0x7f03026f
int attr itemShapeInsetStart 0x7f030270
int attr itemShapeInsetTop 0x7f030271
int attr itemSpacing 0x7f030272
int attr itemStrokeColor 0x7f030273
int attr itemStrokeWidth 0x7f030274
int attr itemTextAppearance 0x7f030275
int attr itemTextAppearanceActive 0x7f030276
int attr itemTextAppearanceActiveBoldEnabled 0x7f030277
int attr itemTextAppearanceInactive 0x7f030278
int attr itemTextColor 0x7f030279
int attr itemVerticalPadding 0x7f03027a
int attr keyPositionType 0x7f03027b
int attr keyboardIcon 0x7f03027c
int attr keylines 0x7f03027d
int attr lStar 0x7f03027e
int attr labelBehavior 0x7f03027f
int attr labelStyle 0x7f030280
int attr labelVisibilityMode 0x7f030281
int attr largeFontVerticalOffsetAdjustment 0x7f030282
int attr lastBaselineToBottomHeight 0x7f030283
int attr lastItemDecorated 0x7f030284
int attr launchSingleTop 0x7f030285
int attr layout 0x7f030286
int attr layoutDescription 0x7f030287
int attr layoutDuringTransition 0x7f030288
int attr layoutManager 0x7f030289
int attr layout_anchor 0x7f03028a
int attr layout_anchorGravity 0x7f03028b
int attr layout_behavior 0x7f03028c
int attr layout_collapseMode 0x7f03028d
int attr layout_collapseParallaxMultiplier 0x7f03028e
int attr layout_constrainedHeight 0x7f03028f
int attr layout_constrainedWidth 0x7f030290
int attr layout_constraintBaseline_creator 0x7f030291
int attr layout_constraintBaseline_toBaselineOf 0x7f030292
int attr layout_constraintBaseline_toBottomOf 0x7f030293
int attr layout_constraintBaseline_toTopOf 0x7f030294
int attr layout_constraintBottom_creator 0x7f030295
int attr layout_constraintBottom_toBottomOf 0x7f030296
int attr layout_constraintBottom_toTopOf 0x7f030297
int attr layout_constraintCircle 0x7f030298
int attr layout_constraintCircleAngle 0x7f030299
int attr layout_constraintCircleRadius 0x7f03029a
int attr layout_constraintDimensionRatio 0x7f03029b
int attr layout_constraintEnd_toEndOf 0x7f03029c
int attr layout_constraintEnd_toStartOf 0x7f03029d
int attr layout_constraintGuide_begin 0x7f03029e
int attr layout_constraintGuide_end 0x7f03029f
int attr layout_constraintGuide_percent 0x7f0302a0
int attr layout_constraintHeight 0x7f0302a1
int attr layout_constraintHeight_default 0x7f0302a2
int attr layout_constraintHeight_max 0x7f0302a3
int attr layout_constraintHeight_min 0x7f0302a4
int attr layout_constraintHeight_percent 0x7f0302a5
int attr layout_constraintHorizontal_bias 0x7f0302a6
int attr layout_constraintHorizontal_chainStyle 0x7f0302a7
int attr layout_constraintHorizontal_weight 0x7f0302a8
int attr layout_constraintLeft_creator 0x7f0302a9
int attr layout_constraintLeft_toLeftOf 0x7f0302aa
int attr layout_constraintLeft_toRightOf 0x7f0302ab
int attr layout_constraintRight_creator 0x7f0302ac
int attr layout_constraintRight_toLeftOf 0x7f0302ad
int attr layout_constraintRight_toRightOf 0x7f0302ae
int attr layout_constraintStart_toEndOf 0x7f0302af
int attr layout_constraintStart_toStartOf 0x7f0302b0
int attr layout_constraintTag 0x7f0302b1
int attr layout_constraintTop_creator 0x7f0302b2
int attr layout_constraintTop_toBottomOf 0x7f0302b3
int attr layout_constraintTop_toTopOf 0x7f0302b4
int attr layout_constraintVertical_bias 0x7f0302b5
int attr layout_constraintVertical_chainStyle 0x7f0302b6
int attr layout_constraintVertical_weight 0x7f0302b7
int attr layout_constraintWidth 0x7f0302b8
int attr layout_constraintWidth_default 0x7f0302b9
int attr layout_constraintWidth_max 0x7f0302ba
int attr layout_constraintWidth_min 0x7f0302bb
int attr layout_constraintWidth_percent 0x7f0302bc
int attr layout_dodgeInsetEdges 0x7f0302bd
int attr layout_editor_absoluteX 0x7f0302be
int attr layout_editor_absoluteY 0x7f0302bf
int attr layout_goneMarginBaseline 0x7f0302c0
int attr layout_goneMarginBottom 0x7f0302c1
int attr layout_goneMarginEnd 0x7f0302c2
int attr layout_goneMarginLeft 0x7f0302c3
int attr layout_goneMarginRight 0x7f0302c4
int attr layout_goneMarginStart 0x7f0302c5
int attr layout_goneMarginTop 0x7f0302c6
int attr layout_insetEdge 0x7f0302c7
int attr layout_keyline 0x7f0302c8
int attr layout_marginBaseline 0x7f0302c9
int attr layout_optimizationLevel 0x7f0302ca
int attr layout_scrollEffect 0x7f0302cb
int attr layout_scrollFlags 0x7f0302cc
int attr layout_scrollInterpolator 0x7f0302cd
int attr layout_wrapBehaviorInParent 0x7f0302ce
int attr liftOnScroll 0x7f0302cf
int attr liftOnScrollColor 0x7f0302d0
int attr liftOnScrollTargetViewId 0x7f0302d1
int attr limitBoundsTo 0x7f0302d2
int attr lineHeight 0x7f0302d3
int attr lineSpacing 0x7f0302d4
int attr linearProgressIndicatorStyle 0x7f0302d5
int attr listChoiceBackgroundIndicator 0x7f0302d6
int attr listChoiceIndicatorMultipleAnimated 0x7f0302d7
int attr listChoiceIndicatorSingleAnimated 0x7f0302d8
int attr listDividerAlertDialog 0x7f0302d9
int attr listItemLayout 0x7f0302da
int attr listLayout 0x7f0302db
int attr listMenuViewStyle 0x7f0302dc
int attr listPopupWindowStyle 0x7f0302dd
int attr listPreferredItemHeight 0x7f0302de
int attr listPreferredItemHeightLarge 0x7f0302df
int attr listPreferredItemHeightSmall 0x7f0302e0
int attr listPreferredItemPaddingEnd 0x7f0302e1
int attr listPreferredItemPaddingLeft 0x7f0302e2
int attr listPreferredItemPaddingRight 0x7f0302e3
int attr listPreferredItemPaddingStart 0x7f0302e4
int attr logo 0x7f0302e5
int attr logoAdjustViewBounds 0x7f0302e6
int attr logoDescription 0x7f0302e7
int attr logoScaleType 0x7f0302e8
int attr marginHorizontal 0x7f0302e9
int attr marginLeftSystemWindowInsets 0x7f0302ea
int attr marginRightSystemWindowInsets 0x7f0302eb
int attr marginTopSystemWindowInsets 0x7f0302ec
int attr materialAlertDialogBodyTextStyle 0x7f0302ed
int attr materialAlertDialogButtonSpacerVisibility 0x7f0302ee
int attr materialAlertDialogTheme 0x7f0302ef
int attr materialAlertDialogTitleIconStyle 0x7f0302f0
int attr materialAlertDialogTitlePanelStyle 0x7f0302f1
int attr materialAlertDialogTitleTextStyle 0x7f0302f2
int attr materialButtonOutlinedStyle 0x7f0302f3
int attr materialButtonStyle 0x7f0302f4
int attr materialButtonToggleGroupStyle 0x7f0302f5
int attr materialCalendarDay 0x7f0302f6
int attr materialCalendarDayOfWeekLabel 0x7f0302f7
int attr materialCalendarFullscreenTheme 0x7f0302f8
int attr materialCalendarHeaderCancelButton 0x7f0302f9
int attr materialCalendarHeaderConfirmButton 0x7f0302fa
int attr materialCalendarHeaderDivider 0x7f0302fb
int attr materialCalendarHeaderLayout 0x7f0302fc
int attr materialCalendarHeaderSelection 0x7f0302fd
int attr materialCalendarHeaderTitle 0x7f0302fe
int attr materialCalendarHeaderToggleButton 0x7f0302ff
int attr materialCalendarMonth 0x7f030300
int attr materialCalendarMonthNavigationButton 0x7f030301
int attr materialCalendarStyle 0x7f030302
int attr materialCalendarTheme 0x7f030303
int attr materialCalendarYearNavigationButton 0x7f030304
int attr materialCardViewElevatedStyle 0x7f030305
int attr materialCardViewFilledStyle 0x7f030306
int attr materialCardViewOutlinedStyle 0x7f030307
int attr materialCardViewStyle 0x7f030308
int attr materialCircleRadius 0x7f030309
int attr materialClockStyle 0x7f03030a
int attr materialDisplayDividerStyle 0x7f03030b
int attr materialIconButtonFilledStyle 0x7f03030c
int attr materialIconButtonFilledTonalStyle 0x7f03030d
int attr materialIconButtonOutlinedStyle 0x7f03030e
int attr materialIconButtonStyle 0x7f03030f
int attr materialSearchBarStyle 0x7f030310
int attr materialSearchViewPrefixStyle 0x7f030311
int attr materialSearchViewStyle 0x7f030312
int attr materialSearchViewToolbarHeight 0x7f030313
int attr materialSearchViewToolbarStyle 0x7f030314
int attr materialThemeOverlay 0x7f030315
int attr materialTimePickerStyle 0x7f030316
int attr materialTimePickerTheme 0x7f030317
int attr materialTimePickerTitleStyle 0x7f030318
int attr maui_edgetoedge_optout 0x7f030319
int attr maui_splash 0x7f03031a
int attr maxAcceleration 0x7f03031b
int attr maxActionInlineWidth 0x7f03031c
int attr maxButtonHeight 0x7f03031d
int attr maxCharacterCount 0x7f03031e
int attr maxHeight 0x7f03031f
int attr maxImageSize 0x7f030320
int attr maxLines 0x7f030321
int attr maxNumber 0x7f030322
int attr maxVelocity 0x7f030323
int attr maxWidth 0x7f030324
int attr measureWithLargestChild 0x7f030325
int attr menu 0x7f030326
int attr menuAlignmentMode 0x7f030327
int attr menuGravity 0x7f030328
int attr methodName 0x7f030329
int attr mimeType 0x7f03032a
int attr minHeight 0x7f03032b
int attr minHideDelay 0x7f03032c
int attr minSeparation 0x7f03032d
int attr minTouchTargetSize 0x7f03032e
int attr minWidth 0x7f03032f
int attr mock_diagonalsColor 0x7f030330
int attr mock_label 0x7f030331
int attr mock_labelBackgroundColor 0x7f030332
int attr mock_labelColor 0x7f030333
int attr mock_showDiagonals 0x7f030334
int attr mock_showLabel 0x7f030335
int attr motionDebug 0x7f030336
int attr motionDurationExtraLong1 0x7f030337
int attr motionDurationExtraLong2 0x7f030338
int attr motionDurationExtraLong3 0x7f030339
int attr motionDurationExtraLong4 0x7f03033a
int attr motionDurationLong1 0x7f03033b
int attr motionDurationLong2 0x7f03033c
int attr motionDurationLong3 0x7f03033d
int attr motionDurationLong4 0x7f03033e
int attr motionDurationMedium1 0x7f03033f
int attr motionDurationMedium2 0x7f030340
int attr motionDurationMedium3 0x7f030341
int attr motionDurationMedium4 0x7f030342
int attr motionDurationShort1 0x7f030343
int attr motionDurationShort2 0x7f030344
int attr motionDurationShort3 0x7f030345
int attr motionDurationShort4 0x7f030346
int attr motionEasingAccelerated 0x7f030347
int attr motionEasingDecelerated 0x7f030348
int attr motionEasingEmphasized 0x7f030349
int attr motionEasingEmphasizedAccelerateInterpolator 0x7f03034a
int attr motionEasingEmphasizedDecelerateInterpolator 0x7f03034b
int attr motionEasingEmphasizedInterpolator 0x7f03034c
int attr motionEasingLinear 0x7f03034d
int attr motionEasingLinearInterpolator 0x7f03034e
int attr motionEasingStandard 0x7f03034f
int attr motionEasingStandardAccelerateInterpolator 0x7f030350
int attr motionEasingStandardDecelerateInterpolator 0x7f030351
int attr motionEasingStandardInterpolator 0x7f030352
int attr motionEffect_alpha 0x7f030353
int attr motionEffect_end 0x7f030354
int attr motionEffect_move 0x7f030355
int attr motionEffect_start 0x7f030356
int attr motionEffect_strict 0x7f030357
int attr motionEffect_translationX 0x7f030358
int attr motionEffect_translationY 0x7f030359
int attr motionEffect_viewTransition 0x7f03035a
int attr motionInterpolator 0x7f03035b
int attr motionPath 0x7f03035c
int attr motionPathRotate 0x7f03035d
int attr motionProgress 0x7f03035e
int attr motionStagger 0x7f03035f
int attr motionTarget 0x7f030360
int attr motion_postLayoutCollision 0x7f030361
int attr motion_triggerOnCollision 0x7f030362
int attr moveWhenScrollAtTop 0x7f030363
int attr multiChoiceItemLayout 0x7f030364
int attr navGraph 0x7f030365
int attr navigationContentDescription 0x7f030366
int attr navigationIcon 0x7f030367
int attr navigationIconTint 0x7f030368
int attr navigationMode 0x7f030369
int attr navigationRailStyle 0x7f03036a
int attr navigationViewStyle 0x7f03036b
int attr nestedScrollFlags 0x7f03036c
int attr nestedScrollViewStyle 0x7f03036d
int attr nestedScrollable 0x7f03036e
int attr nullable 0x7f03036f
int attr number 0x7f030370
int attr numericModifiers 0x7f030371
int attr offsetAlignmentMode 0x7f030372
int attr onCross 0x7f030373
int attr onHide 0x7f030374
int attr onNegativeCross 0x7f030375
int attr onPositiveCross 0x7f030376
int attr onShow 0x7f030377
int attr onStateTransition 0x7f030378
int attr onTouchUp 0x7f030379
int attr overlapAnchor 0x7f03037a
int attr overlay 0x7f03037b
int attr paddingBottomNoButtons 0x7f03037c
int attr paddingBottomSystemWindowInsets 0x7f03037d
int attr paddingEnd 0x7f03037e
int attr paddingLeftSystemWindowInsets 0x7f03037f
int attr paddingRightSystemWindowInsets 0x7f030380
int attr paddingStart 0x7f030381
int attr paddingStartSystemWindowInsets 0x7f030382
int attr paddingTopNoTitle 0x7f030383
int attr paddingTopSystemWindowInsets 0x7f030384
int attr panelBackground 0x7f030385
int attr panelMenuListTheme 0x7f030386
int attr panelMenuListWidth 0x7f030387
int attr passwordToggleContentDescription 0x7f030388
int attr passwordToggleDrawable 0x7f030389
int attr passwordToggleEnabled 0x7f03038a
int attr passwordToggleTint 0x7f03038b
int attr passwordToggleTintMode 0x7f03038c
int attr pathMotionArc 0x7f03038d
int attr path_percent 0x7f03038e
int attr percentHeight 0x7f03038f
int attr percentWidth 0x7f030390
int attr percentX 0x7f030391
int attr percentY 0x7f030392
int attr perpendicularPath_percent 0x7f030393
int attr pivotAnchor 0x7f030394
int attr placeholderActivityName 0x7f030395
int attr placeholderText 0x7f030396
int attr placeholderTextAppearance 0x7f030397
int attr placeholderTextColor 0x7f030398
int attr placeholder_emptyVisibility 0x7f030399
int attr polarRelativeTo 0x7f03039a
int attr popEnterAnim 0x7f03039b
int attr popExitAnim 0x7f03039c
int attr popUpTo 0x7f03039d
int attr popUpToInclusive 0x7f03039e
int attr popUpToSaveState 0x7f03039f
int attr popupMenuBackground 0x7f0303a0
int attr popupMenuStyle 0x7f0303a1
int attr popupTheme 0x7f0303a2
int attr popupWindowStyle 0x7f0303a3
int attr prefixText 0x7f0303a4
int attr prefixTextAppearance 0x7f0303a5
int attr prefixTextColor 0x7f0303a6
int attr preserveIconSpacing 0x7f0303a7
int attr pressedTranslationZ 0x7f0303a8
int attr primaryActivityName 0x7f0303a9
int attr progressBarPadding 0x7f0303aa
int attr progressBarStyle 0x7f0303ab
int attr quantizeMotionInterpolator 0x7f0303ac
int attr quantizeMotionPhase 0x7f0303ad
int attr quantizeMotionSteps 0x7f0303ae
int attr queryBackground 0x7f0303af
int attr queryHint 0x7f0303b0
int attr queryPatterns 0x7f0303b1
int attr radioButtonStyle 0x7f0303b2
int attr rangeFillColor 0x7f0303b3
int attr ratingBarStyle 0x7f0303b4
int attr ratingBarStyleIndicator 0x7f0303b5
int attr ratingBarStyleSmall 0x7f0303b6
int attr reactiveGuide_animateChange 0x7f0303b7
int attr reactiveGuide_applyToAllConstraintSets 0x7f0303b8
int attr reactiveGuide_applyToConstraintSet 0x7f0303b9
int attr reactiveGuide_valueId 0x7f0303ba
int attr recyclerViewStyle 0x7f0303bb
int attr region_heightLessThan 0x7f0303bc
int attr region_heightMoreThan 0x7f0303bd
int attr region_widthLessThan 0x7f0303be
int attr region_widthMoreThan 0x7f0303bf
int attr removeEmbeddedFabElevation 0x7f0303c0
int attr restoreState 0x7f0303c1
int attr reverseLayout 0x7f0303c2
int attr rippleColor 0x7f0303c3
int attr rotationCenterId 0x7f0303c4
int attr round 0x7f0303c5
int attr roundPercent 0x7f0303c6
int attr route 0x7f0303c7
int attr saturation 0x7f0303c8
int attr scaleFromTextSize 0x7f0303c9
int attr scrimAnimationDuration 0x7f0303ca
int attr scrimBackground 0x7f0303cb
int attr scrimVisibleHeightTrigger 0x7f0303cc
int attr scrollViewStyle 0x7f0303cd
int attr searchHintIcon 0x7f0303ce
int attr searchIcon 0x7f0303cf
int attr searchViewStyle 0x7f0303d0
int attr secondaryActivityAction 0x7f0303d1
int attr secondaryActivityName 0x7f0303d2
int attr seekBarStyle 0x7f0303d3
int attr selectableItemBackground 0x7f0303d4
int attr selectableItemBackgroundBorderless 0x7f0303d5
int attr selectionRequired 0x7f0303d6
int attr selectorSize 0x7f0303d7
int attr setsTag 0x7f0303d8
int attr shapeAppearance 0x7f0303d9
int attr shapeAppearanceCornerExtraLarge 0x7f0303da
int attr shapeAppearanceCornerExtraSmall 0x7f0303db
int attr shapeAppearanceCornerLarge 0x7f0303dc
int attr shapeAppearanceCornerMedium 0x7f0303dd
int attr shapeAppearanceCornerSmall 0x7f0303de
int attr shapeAppearanceLargeComponent 0x7f0303df
int attr shapeAppearanceMediumComponent 0x7f0303e0
int attr shapeAppearanceOverlay 0x7f0303e1
int attr shapeAppearanceSmallComponent 0x7f0303e2
int attr shapeCornerFamily 0x7f0303e3
int attr shortcutMatchRequired 0x7f0303e4
int attr shouldRemoveExpandedCorners 0x7f0303e5
int attr showAnimationBehavior 0x7f0303e6
int attr showAsAction 0x7f0303e7
int attr showDelay 0x7f0303e8
int attr showDividers 0x7f0303e9
int attr showMarker 0x7f0303ea
int attr showMotionSpec 0x7f0303eb
int attr showPaths 0x7f0303ec
int attr showText 0x7f0303ed
int attr showTitle 0x7f0303ee
int attr shrinkMotionSpec 0x7f0303ef
int attr sideSheetDialogTheme 0x7f0303f0
int attr sideSheetModalStyle 0x7f0303f1
int attr simpleItemLayout 0x7f0303f2
int attr simpleItemSelectedColor 0x7f0303f3
int attr simpleItemSelectedRippleColor 0x7f0303f4
int attr simpleItems 0x7f0303f5
int attr singleChoiceItemLayout 0x7f0303f6
int attr singleLine 0x7f0303f7
int attr singleSelection 0x7f0303f8
int attr sizePercent 0x7f0303f9
int attr sliderStyle 0x7f0303fa
int attr snackbarButtonStyle 0x7f0303fb
int attr snackbarStyle 0x7f0303fc
int attr snackbarTextViewStyle 0x7f0303fd
int attr spanCount 0x7f0303fe
int attr spinBars 0x7f0303ff
int attr spinnerDropDownItemStyle 0x7f030400
int attr spinnerStyle 0x7f030401
int attr splitLayoutDirection 0x7f030402
int attr splitMaxAspectRatioInLandscape 0x7f030403
int attr splitMaxAspectRatioInPortrait 0x7f030404
int attr splitMinHeightDp 0x7f030405
int attr splitMinSmallestWidthDp 0x7f030406
int attr splitMinWidthDp 0x7f030407
int attr splitRatio 0x7f030408
int attr splitTrack 0x7f030409
int attr springBoundary 0x7f03040a
int attr springDamping 0x7f03040b
int attr springMass 0x7f03040c
int attr springStiffness 0x7f03040d
int attr springStopThreshold 0x7f03040e
int attr srcCompat 0x7f03040f
int attr stackFromEnd 0x7f030410
int attr staggered 0x7f030411
int attr startDestination 0x7f030412
int attr startIconCheckable 0x7f030413
int attr startIconContentDescription 0x7f030414
int attr startIconDrawable 0x7f030415
int attr startIconMinSize 0x7f030416
int attr startIconScaleType 0x7f030417
int attr startIconTint 0x7f030418
int attr startIconTintMode 0x7f030419
int attr stateLabels 0x7f03041a
int attr state_above_anchor 0x7f03041b
int attr state_collapsed 0x7f03041c
int attr state_collapsible 0x7f03041d
int attr state_dragged 0x7f03041e
int attr state_error 0x7f03041f
int attr state_indeterminate 0x7f030420
int attr state_liftable 0x7f030421
int attr state_lifted 0x7f030422
int attr state_with_icon 0x7f030423
int attr statusBarBackground 0x7f030424
int attr statusBarForeground 0x7f030425
int attr statusBarScrim 0x7f030426
int attr stickyPlaceholder 0x7f030427
int attr strokeColor 0x7f030428
int attr strokeWidth 0x7f030429
int attr subMenuArrow 0x7f03042a
int attr subheaderColor 0x7f03042b
int attr subheaderInsetEnd 0x7f03042c
int attr subheaderInsetStart 0x7f03042d
int attr subheaderTextAppearance 0x7f03042e
int attr submitBackground 0x7f03042f
int attr subtitle 0x7f030430
int attr subtitleCentered 0x7f030431
int attr subtitleTextAppearance 0x7f030432
int attr subtitleTextColor 0x7f030433
int attr subtitleTextStyle 0x7f030434
int attr suffixText 0x7f030435
int attr suffixTextAppearance 0x7f030436
int attr suffixTextColor 0x7f030437
int attr suggestionRowLayout 0x7f030438
int attr swipeRefreshLayoutProgressSpinnerBackgroundColor 0x7f030439
int attr switchMinWidth 0x7f03043a
int attr switchPadding 0x7f03043b
int attr switchStyle 0x7f03043c
int attr switchTextAppearance 0x7f03043d
int attr tabBackground 0x7f03043e
int attr tabContentStart 0x7f03043f
int attr tabGravity 0x7f030440
int attr tabIconTint 0x7f030441
int attr tabIconTintMode 0x7f030442
int attr tabIndicator 0x7f030443
int attr tabIndicatorAnimationDuration 0x7f030444
int attr tabIndicatorAnimationMode 0x7f030445
int attr tabIndicatorColor 0x7f030446
int attr tabIndicatorFullWidth 0x7f030447
int attr tabIndicatorGravity 0x7f030448
int attr tabIndicatorHeight 0x7f030449
int attr tabInlineLabel 0x7f03044a
int attr tabMaxWidth 0x7f03044b
int attr tabMinWidth 0x7f03044c
int attr tabMode 0x7f03044d
int attr tabPadding 0x7f03044e
int attr tabPaddingBottom 0x7f03044f
int attr tabPaddingEnd 0x7f030450
int attr tabPaddingStart 0x7f030451
int attr tabPaddingTop 0x7f030452
int attr tabRippleColor 0x7f030453
int attr tabSecondaryStyle 0x7f030454
int attr tabSelectedTextAppearance 0x7f030455
int attr tabSelectedTextColor 0x7f030456
int attr tabStyle 0x7f030457
int attr tabTextAppearance 0x7f030458
int attr tabTextColor 0x7f030459
int attr tabUnboundedRipple 0x7f03045a
int attr tag 0x7f03045b
int attr targetId 0x7f03045c
int attr targetPackage 0x7f03045d
int attr telltales_tailColor 0x7f03045e
int attr telltales_tailScale 0x7f03045f
int attr telltales_velocityMode 0x7f030460
int attr textAllCaps 0x7f030461
int attr textAppearanceBody1 0x7f030462
int attr textAppearanceBody2 0x7f030463
int attr textAppearanceBodyLarge 0x7f030464
int attr textAppearanceBodyMedium 0x7f030465
int attr textAppearanceBodySmall 0x7f030466
int attr textAppearanceButton 0x7f030467
int attr textAppearanceCaption 0x7f030468
int attr textAppearanceDisplayLarge 0x7f030469
int attr textAppearanceDisplayMedium 0x7f03046a
int attr textAppearanceDisplaySmall 0x7f03046b
int attr textAppearanceHeadline1 0x7f03046c
int attr textAppearanceHeadline2 0x7f03046d
int attr textAppearanceHeadline3 0x7f03046e
int attr textAppearanceHeadline4 0x7f03046f
int attr textAppearanceHeadline5 0x7f030470
int attr textAppearanceHeadline6 0x7f030471
int attr textAppearanceHeadlineLarge 0x7f030472
int attr textAppearanceHeadlineMedium 0x7f030473
int attr textAppearanceHeadlineSmall 0x7f030474
int attr textAppearanceLabelLarge 0x7f030475
int attr textAppearanceLabelMedium 0x7f030476
int attr textAppearanceLabelSmall 0x7f030477
int attr textAppearanceLargePopupMenu 0x7f030478
int attr textAppearanceLineHeightEnabled 0x7f030479
int attr textAppearanceListItem 0x7f03047a
int attr textAppearanceListItemSecondary 0x7f03047b
int attr textAppearanceListItemSmall 0x7f03047c
int attr textAppearanceOverline 0x7f03047d
int attr textAppearancePopupMenuHeader 0x7f03047e
int attr textAppearanceSearchResultSubtitle 0x7f03047f
int attr textAppearanceSearchResultTitle 0x7f030480
int attr textAppearanceSmallPopupMenu 0x7f030481
int attr textAppearanceSubtitle1 0x7f030482
int attr textAppearanceSubtitle2 0x7f030483
int attr textAppearanceTitleLarge 0x7f030484
int attr textAppearanceTitleMedium 0x7f030485
int attr textAppearanceTitleSmall 0x7f030486
int attr textBackground 0x7f030487
int attr textBackgroundPanX 0x7f030488
int attr textBackgroundPanY 0x7f030489
int attr textBackgroundRotate 0x7f03048a
int attr textBackgroundZoom 0x7f03048b
int attr textColorAlertDialogListItem 0x7f03048c
int attr textColorSearchUrl 0x7f03048d
int attr textEndPadding 0x7f03048e
int attr textFillColor 0x7f03048f
int attr textInputFilledDenseStyle 0x7f030490
int attr textInputFilledExposedDropdownMenuStyle 0x7f030491
int attr textInputFilledStyle 0x7f030492
int attr textInputLayoutFocusedRectEnabled 0x7f030493
int attr textInputOutlinedDenseStyle 0x7f030494
int attr textInputOutlinedExposedDropdownMenuStyle 0x7f030495
int attr textInputOutlinedStyle 0x7f030496
int attr textInputStyle 0x7f030497
int attr textLocale 0x7f030498
int attr textOutlineColor 0x7f030499
int attr textOutlineThickness 0x7f03049a
int attr textPanX 0x7f03049b
int attr textPanY 0x7f03049c
int attr textStartPadding 0x7f03049d
int attr textureBlurFactor 0x7f03049e
int attr textureEffect 0x7f03049f
int attr textureHeight 0x7f0304a0
int attr textureWidth 0x7f0304a1
int attr theme 0x7f0304a2
int attr thickness 0x7f0304a3
int attr thumbColor 0x7f0304a4
int attr thumbElevation 0x7f0304a5
int attr thumbHeight 0x7f0304a6
int attr thumbIcon 0x7f0304a7
int attr thumbIconSize 0x7f0304a8
int attr thumbIconTint 0x7f0304a9
int attr thumbIconTintMode 0x7f0304aa
int attr thumbRadius 0x7f0304ab
int attr thumbStrokeColor 0x7f0304ac
int attr thumbStrokeWidth 0x7f0304ad
int attr thumbTextPadding 0x7f0304ae
int attr thumbTint 0x7f0304af
int attr thumbTintMode 0x7f0304b0
int attr thumbTrackGapSize 0x7f0304b1
int attr thumbWidth 0x7f0304b2
int attr tickColor 0x7f0304b3
int attr tickColorActive 0x7f0304b4
int attr tickColorInactive 0x7f0304b5
int attr tickMark 0x7f0304b6
int attr tickMarkTint 0x7f0304b7
int attr tickMarkTintMode 0x7f0304b8
int attr tickRadiusActive 0x7f0304b9
int attr tickRadiusInactive 0x7f0304ba
int attr tickVisible 0x7f0304bb
int attr tint 0x7f0304bc
int attr tintMode 0x7f0304bd
int attr tintNavigationIcon 0x7f0304be
int attr title 0x7f0304bf
int attr titleCentered 0x7f0304c0
int attr titleCollapseMode 0x7f0304c1
int attr titleEnabled 0x7f0304c2
int attr titleMargin 0x7f0304c3
int attr titleMarginBottom 0x7f0304c4
int attr titleMarginEnd 0x7f0304c5
int attr titleMarginStart 0x7f0304c6
int attr titleMarginTop 0x7f0304c7
int attr titleMargins 0x7f0304c8
int attr titlePositionInterpolator 0x7f0304c9
int attr titleTextAppearance 0x7f0304ca
int attr titleTextColor 0x7f0304cb
int attr titleTextEllipsize 0x7f0304cc
int attr titleTextStyle 0x7f0304cd
int attr toggleCheckedStateOnClick 0x7f0304ce
int attr toolbarId 0x7f0304cf
int attr toolbarNavigationButtonStyle 0x7f0304d0
int attr toolbarStyle 0x7f0304d1
int attr toolbarSurfaceStyle 0x7f0304d2
int attr tooltipForegroundColor 0x7f0304d3
int attr tooltipFrameBackground 0x7f0304d4
int attr tooltipStyle 0x7f0304d5
int attr tooltipText 0x7f0304d6
int attr topInsetScrimEnabled 0x7f0304d7
int attr touchAnchorId 0x7f0304d8
int attr touchAnchorSide 0x7f0304d9
int attr touchRegionId 0x7f0304da
int attr track 0x7f0304db
int attr trackColor 0x7f0304dc
int attr trackColorActive 0x7f0304dd
int attr trackColorInactive 0x7f0304de
int attr trackCornerRadius 0x7f0304df
int attr trackDecoration 0x7f0304e0
int attr trackDecorationTint 0x7f0304e1
int attr trackDecorationTintMode 0x7f0304e2
int attr trackHeight 0x7f0304e3
int attr trackInsideCornerSize 0x7f0304e4
int attr trackStopIndicatorSize 0x7f0304e5
int attr trackThickness 0x7f0304e6
int attr trackTint 0x7f0304e7
int attr trackTintMode 0x7f0304e8
int attr transformPivotTarget 0x7f0304e9
int attr transitionDisable 0x7f0304ea
int attr transitionEasing 0x7f0304eb
int attr transitionFlags 0x7f0304ec
int attr transitionPathRotate 0x7f0304ed
int attr transitionShapeAppearance 0x7f0304ee
int attr triggerId 0x7f0304ef
int attr triggerReceiver 0x7f0304f0
int attr triggerSlack 0x7f0304f1
int attr ttcIndex 0x7f0304f2
int attr upDuration 0x7f0304f3
int attr uri 0x7f0304f4
int attr useCompatPadding 0x7f0304f5
int attr useMaterialThemeColors 0x7f0304f6
int attr values 0x7f0304f7
int attr verticalOffset 0x7f0304f8
int attr verticalOffsetWithText 0x7f0304f9
int attr viewInflaterClass 0x7f0304fa
int attr viewTransitionMode 0x7f0304fb
int attr viewTransitionOnCross 0x7f0304fc
int attr viewTransitionOnNegativeCross 0x7f0304fd
int attr viewTransitionOnPositiveCross 0x7f0304fe
int attr visibilityMode 0x7f0304ff
int attr voiceIcon 0x7f030500
int attr warmth 0x7f030501
int attr waveDecay 0x7f030502
int attr waveOffset 0x7f030503
int attr wavePeriod 0x7f030504
int attr wavePhase 0x7f030505
int attr waveShape 0x7f030506
int attr waveVariesBy 0x7f030507
int attr windowActionBar 0x7f030508
int attr windowActionBarOverlay 0x7f030509
int attr windowActionModeOverlay 0x7f03050a
int attr windowFixedHeightMajor 0x7f03050b
int attr windowFixedHeightMinor 0x7f03050c
int attr windowFixedWidthMajor 0x7f03050d
int attr windowFixedWidthMinor 0x7f03050e
int attr windowMinWidthMajor 0x7f03050f
int attr windowMinWidthMinor 0x7f030510
int attr windowNoTitle 0x7f030511
int attr yearSelectedStyle 0x7f030512
int attr yearStyle 0x7f030513
int attr yearTodayStyle 0x7f030514
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_config_actionMenuItemAllCaps 0x7f040001
int bool mtrl_btn_textappearance_all_caps 0x7f040002
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_decor_view_status_guard 0x7f050005
int color abc_decor_view_status_guard_light 0x7f050006
int color abc_hint_foreground_material_dark 0x7f050007
int color abc_hint_foreground_material_light 0x7f050008
int color abc_primary_text_disable_only_material_dark 0x7f050009
int color abc_primary_text_disable_only_material_light 0x7f05000a
int color abc_primary_text_material_dark 0x7f05000b
int color abc_primary_text_material_light 0x7f05000c
int color abc_search_url_text 0x7f05000d
int color abc_search_url_text_normal 0x7f05000e
int color abc_search_url_text_pressed 0x7f05000f
int color abc_search_url_text_selected 0x7f050010
int color abc_secondary_text_material_dark 0x7f050011
int color abc_secondary_text_material_light 0x7f050012
int color abc_tint_btn_checkable 0x7f050013
int color abc_tint_default 0x7f050014
int color abc_tint_edittext 0x7f050015
int color abc_tint_seek_thumb 0x7f050016
int color abc_tint_spinner 0x7f050017
int color abc_tint_switch_track 0x7f050018
int color accent_material_dark 0x7f050019
int color accent_material_light 0x7f05001a
int color androidx_core_ripple_material_light 0x7f05001b
int color androidx_core_secondary_text_default_material_light 0x7f05001c
int color background_floating_material_dark 0x7f05001d
int color background_floating_material_light 0x7f05001e
int color background_material_dark 0x7f05001f
int color background_material_light 0x7f050020
int color bright_foreground_disabled_material_dark 0x7f050021
int color bright_foreground_disabled_material_light 0x7f050022
int color bright_foreground_inverse_material_dark 0x7f050023
int color bright_foreground_inverse_material_light 0x7f050024
int color bright_foreground_material_dark 0x7f050025
int color bright_foreground_material_light 0x7f050026
int color browser_actions_bg_grey 0x7f050027
int color browser_actions_divider_color 0x7f050028
int color browser_actions_text_color 0x7f050029
int color browser_actions_title_color 0x7f05002a
int color button_material_dark 0x7f05002b
int color button_material_light 0x7f05002c
int color call_notification_answer_color 0x7f05002d
int color call_notification_decline_color 0x7f05002e
int color cardview_dark_background 0x7f05002f
int color cardview_light_background 0x7f050030
int color cardview_shadow_end_color 0x7f050031
int color cardview_shadow_start_color 0x7f050032
int color colorAccent 0x7f050033
int color colorActionMenuTextColor 0x7f050034
int color colorPrimary 0x7f050035
int color colorPrimaryDark 0x7f050036
int color design_bottom_navigation_shadow_color 0x7f050037
int color design_box_stroke_color 0x7f050038
int color design_dark_default_color_background 0x7f050039
int color design_dark_default_color_error 0x7f05003a
int color design_dark_default_color_on_background 0x7f05003b
int color design_dark_default_color_on_error 0x7f05003c
int color design_dark_default_color_on_primary 0x7f05003d
int color design_dark_default_color_on_secondary 0x7f05003e
int color design_dark_default_color_on_surface 0x7f05003f
int color design_dark_default_color_primary 0x7f050040
int color design_dark_default_color_primary_dark 0x7f050041
int color design_dark_default_color_primary_variant 0x7f050042
int color design_dark_default_color_secondary 0x7f050043
int color design_dark_default_color_secondary_variant 0x7f050044
int color design_dark_default_color_surface 0x7f050045
int color design_default_color_background 0x7f050046
int color design_default_color_error 0x7f050047
int color design_default_color_on_background 0x7f050048
int color design_default_color_on_error 0x7f050049
int color design_default_color_on_primary 0x7f05004a
int color design_default_color_on_secondary 0x7f05004b
int color design_default_color_on_surface 0x7f05004c
int color design_default_color_primary 0x7f05004d
int color design_default_color_primary_dark 0x7f05004e
int color design_default_color_primary_variant 0x7f05004f
int color design_default_color_secondary 0x7f050050
int color design_default_color_secondary_variant 0x7f050051
int color design_default_color_surface 0x7f050052
int color design_error 0x7f050053
int color design_fab_shadow_end_color 0x7f050054
int color design_fab_shadow_mid_color 0x7f050055
int color design_fab_shadow_start_color 0x7f050056
int color design_fab_stroke_end_inner_color 0x7f050057
int color design_fab_stroke_end_outer_color 0x7f050058
int color design_fab_stroke_top_inner_color 0x7f050059
int color design_fab_stroke_top_outer_color 0x7f05005a
int color design_icon_tint 0x7f05005b
int color design_snackbar_background_color 0x7f05005c
int color dim_foreground_disabled_material_dark 0x7f05005d
int color dim_foreground_disabled_material_light 0x7f05005e
int color dim_foreground_material_dark 0x7f05005f
int color dim_foreground_material_light 0x7f050060
int color error_color_material_dark 0x7f050061
int color error_color_material_light 0x7f050062
int color foreground_material_dark 0x7f050063
int color foreground_material_light 0x7f050064
int color highlighted_text_material_dark 0x7f050065
int color highlighted_text_material_light 0x7f050066
int color m3_appbar_overlay_color 0x7f050067
int color m3_assist_chip_icon_tint_color 0x7f050068
int color m3_assist_chip_stroke_color 0x7f050069
int color m3_bottom_sheet_drag_handle_color 0x7f05006a
int color m3_button_background_color_selector 0x7f05006b
int color m3_button_foreground_color_selector 0x7f05006c
int color m3_button_outline_color_selector 0x7f05006d
int color m3_button_ripple_color 0x7f05006e
int color m3_button_ripple_color_selector 0x7f05006f
int color m3_calendar_item_disabled_text 0x7f050070
int color m3_calendar_item_stroke_color 0x7f050071
int color m3_card_foreground_color 0x7f050072
int color m3_card_ripple_color 0x7f050073
int color m3_card_stroke_color 0x7f050074
int color m3_checkbox_button_icon_tint 0x7f050075
int color m3_checkbox_button_tint 0x7f050076
int color m3_chip_assist_text_color 0x7f050077
int color m3_chip_background_color 0x7f050078
int color m3_chip_ripple_color 0x7f050079
int color m3_chip_stroke_color 0x7f05007a
int color m3_chip_text_color 0x7f05007b
int color m3_dark_default_color_primary_text 0x7f05007c
int color m3_dark_default_color_secondary_text 0x7f05007d
int color m3_dark_highlighted_text 0x7f05007e
int color m3_dark_hint_foreground 0x7f05007f
int color m3_dark_primary_text_disable_only 0x7f050080
int color m3_default_color_primary_text 0x7f050081
int color m3_default_color_secondary_text 0x7f050082
int color m3_dynamic_dark_default_color_primary_text 0x7f050083
int color m3_dynamic_dark_default_color_secondary_text 0x7f050084
int color m3_dynamic_dark_highlighted_text 0x7f050085
int color m3_dynamic_dark_hint_foreground 0x7f050086
int color m3_dynamic_dark_primary_text_disable_only 0x7f050087
int color m3_dynamic_default_color_primary_text 0x7f050088
int color m3_dynamic_default_color_secondary_text 0x7f050089
int color m3_dynamic_highlighted_text 0x7f05008a
int color m3_dynamic_hint_foreground 0x7f05008b
int color m3_dynamic_primary_text_disable_only 0x7f05008c
int color m3_efab_ripple_color_selector 0x7f05008d
int color m3_elevated_chip_background_color 0x7f05008e
int color m3_fab_efab_background_color_selector 0x7f05008f
int color m3_fab_efab_foreground_color_selector 0x7f050090
int color m3_fab_ripple_color_selector 0x7f050091
int color m3_filled_icon_button_container_color_selector 0x7f050092
int color m3_highlighted_text 0x7f050093
int color m3_hint_foreground 0x7f050094
int color m3_icon_button_icon_color_selector 0x7f050095
int color m3_navigation_bar_item_with_indicator_icon_tint 0x7f050096
int color m3_navigation_bar_item_with_indicator_label_tint 0x7f050097
int color m3_navigation_bar_ripple_color_selector 0x7f050098
int color m3_navigation_item_background_color 0x7f050099
int color m3_navigation_item_icon_tint 0x7f05009a
int color m3_navigation_item_ripple_color 0x7f05009b
int color m3_navigation_item_text_color 0x7f05009c
int color m3_navigation_rail_item_with_indicator_icon_tint 0x7f05009d
int color m3_navigation_rail_item_with_indicator_label_tint 0x7f05009e
int color m3_navigation_rail_ripple_color_selector 0x7f05009f
int color m3_popupmenu_overlay_color 0x7f0500a0
int color m3_primary_text_disable_only 0x7f0500a1
int color m3_radiobutton_button_tint 0x7f0500a2
int color m3_radiobutton_ripple_tint 0x7f0500a3
int color m3_ref_palette_black 0x7f0500a4
int color m3_ref_palette_dynamic_neutral0 0x7f0500a5
int color m3_ref_palette_dynamic_neutral10 0x7f0500a6
int color m3_ref_palette_dynamic_neutral100 0x7f0500a7
int color m3_ref_palette_dynamic_neutral12 0x7f0500a8
int color m3_ref_palette_dynamic_neutral17 0x7f0500a9
int color m3_ref_palette_dynamic_neutral20 0x7f0500aa
int color m3_ref_palette_dynamic_neutral22 0x7f0500ab
int color m3_ref_palette_dynamic_neutral24 0x7f0500ac
int color m3_ref_palette_dynamic_neutral30 0x7f0500ad
int color m3_ref_palette_dynamic_neutral4 0x7f0500ae
int color m3_ref_palette_dynamic_neutral40 0x7f0500af
int color m3_ref_palette_dynamic_neutral50 0x7f0500b0
int color m3_ref_palette_dynamic_neutral6 0x7f0500b1
int color m3_ref_palette_dynamic_neutral60 0x7f0500b2
int color m3_ref_palette_dynamic_neutral70 0x7f0500b3
int color m3_ref_palette_dynamic_neutral80 0x7f0500b4
int color m3_ref_palette_dynamic_neutral87 0x7f0500b5
int color m3_ref_palette_dynamic_neutral90 0x7f0500b6
int color m3_ref_palette_dynamic_neutral92 0x7f0500b7
int color m3_ref_palette_dynamic_neutral94 0x7f0500b8
int color m3_ref_palette_dynamic_neutral95 0x7f0500b9
int color m3_ref_palette_dynamic_neutral96 0x7f0500ba
int color m3_ref_palette_dynamic_neutral98 0x7f0500bb
int color m3_ref_palette_dynamic_neutral99 0x7f0500bc
int color m3_ref_palette_dynamic_neutral_variant0 0x7f0500bd
int color m3_ref_palette_dynamic_neutral_variant10 0x7f0500be
int color m3_ref_palette_dynamic_neutral_variant100 0x7f0500bf
int color m3_ref_palette_dynamic_neutral_variant12 0x7f0500c0
int color m3_ref_palette_dynamic_neutral_variant17 0x7f0500c1
int color m3_ref_palette_dynamic_neutral_variant20 0x7f0500c2
int color m3_ref_palette_dynamic_neutral_variant22 0x7f0500c3
int color m3_ref_palette_dynamic_neutral_variant24 0x7f0500c4
int color m3_ref_palette_dynamic_neutral_variant30 0x7f0500c5
int color m3_ref_palette_dynamic_neutral_variant4 0x7f0500c6
int color m3_ref_palette_dynamic_neutral_variant40 0x7f0500c7
int color m3_ref_palette_dynamic_neutral_variant50 0x7f0500c8
int color m3_ref_palette_dynamic_neutral_variant6 0x7f0500c9
int color m3_ref_palette_dynamic_neutral_variant60 0x7f0500ca
int color m3_ref_palette_dynamic_neutral_variant70 0x7f0500cb
int color m3_ref_palette_dynamic_neutral_variant80 0x7f0500cc
int color m3_ref_palette_dynamic_neutral_variant87 0x7f0500cd
int color m3_ref_palette_dynamic_neutral_variant90 0x7f0500ce
int color m3_ref_palette_dynamic_neutral_variant92 0x7f0500cf
int color m3_ref_palette_dynamic_neutral_variant94 0x7f0500d0
int color m3_ref_palette_dynamic_neutral_variant95 0x7f0500d1
int color m3_ref_palette_dynamic_neutral_variant96 0x7f0500d2
int color m3_ref_palette_dynamic_neutral_variant98 0x7f0500d3
int color m3_ref_palette_dynamic_neutral_variant99 0x7f0500d4
int color m3_ref_palette_dynamic_primary0 0x7f0500d5
int color m3_ref_palette_dynamic_primary10 0x7f0500d6
int color m3_ref_palette_dynamic_primary100 0x7f0500d7
int color m3_ref_palette_dynamic_primary20 0x7f0500d8
int color m3_ref_palette_dynamic_primary30 0x7f0500d9
int color m3_ref_palette_dynamic_primary40 0x7f0500da
int color m3_ref_palette_dynamic_primary50 0x7f0500db
int color m3_ref_palette_dynamic_primary60 0x7f0500dc
int color m3_ref_palette_dynamic_primary70 0x7f0500dd
int color m3_ref_palette_dynamic_primary80 0x7f0500de
int color m3_ref_palette_dynamic_primary90 0x7f0500df
int color m3_ref_palette_dynamic_primary95 0x7f0500e0
int color m3_ref_palette_dynamic_primary99 0x7f0500e1
int color m3_ref_palette_dynamic_secondary0 0x7f0500e2
int color m3_ref_palette_dynamic_secondary10 0x7f0500e3
int color m3_ref_palette_dynamic_secondary100 0x7f0500e4
int color m3_ref_palette_dynamic_secondary20 0x7f0500e5
int color m3_ref_palette_dynamic_secondary30 0x7f0500e6
int color m3_ref_palette_dynamic_secondary40 0x7f0500e7
int color m3_ref_palette_dynamic_secondary50 0x7f0500e8
int color m3_ref_palette_dynamic_secondary60 0x7f0500e9
int color m3_ref_palette_dynamic_secondary70 0x7f0500ea
int color m3_ref_palette_dynamic_secondary80 0x7f0500eb
int color m3_ref_palette_dynamic_secondary90 0x7f0500ec
int color m3_ref_palette_dynamic_secondary95 0x7f0500ed
int color m3_ref_palette_dynamic_secondary99 0x7f0500ee
int color m3_ref_palette_dynamic_tertiary0 0x7f0500ef
int color m3_ref_palette_dynamic_tertiary10 0x7f0500f0
int color m3_ref_palette_dynamic_tertiary100 0x7f0500f1
int color m3_ref_palette_dynamic_tertiary20 0x7f0500f2
int color m3_ref_palette_dynamic_tertiary30 0x7f0500f3
int color m3_ref_palette_dynamic_tertiary40 0x7f0500f4
int color m3_ref_palette_dynamic_tertiary50 0x7f0500f5
int color m3_ref_palette_dynamic_tertiary60 0x7f0500f6
int color m3_ref_palette_dynamic_tertiary70 0x7f0500f7
int color m3_ref_palette_dynamic_tertiary80 0x7f0500f8
int color m3_ref_palette_dynamic_tertiary90 0x7f0500f9
int color m3_ref_palette_dynamic_tertiary95 0x7f0500fa
int color m3_ref_palette_dynamic_tertiary99 0x7f0500fb
int color m3_ref_palette_error0 0x7f0500fc
int color m3_ref_palette_error10 0x7f0500fd
int color m3_ref_palette_error100 0x7f0500fe
int color m3_ref_palette_error20 0x7f0500ff
int color m3_ref_palette_error30 0x7f050100
int color m3_ref_palette_error40 0x7f050101
int color m3_ref_palette_error50 0x7f050102
int color m3_ref_palette_error60 0x7f050103
int color m3_ref_palette_error70 0x7f050104
int color m3_ref_palette_error80 0x7f050105
int color m3_ref_palette_error90 0x7f050106
int color m3_ref_palette_error95 0x7f050107
int color m3_ref_palette_error99 0x7f050108
int color m3_ref_palette_neutral0 0x7f050109
int color m3_ref_palette_neutral10 0x7f05010a
int color m3_ref_palette_neutral100 0x7f05010b
int color m3_ref_palette_neutral12 0x7f05010c
int color m3_ref_palette_neutral17 0x7f05010d
int color m3_ref_palette_neutral20 0x7f05010e
int color m3_ref_palette_neutral22 0x7f05010f
int color m3_ref_palette_neutral24 0x7f050110
int color m3_ref_palette_neutral30 0x7f050111
int color m3_ref_palette_neutral4 0x7f050112
int color m3_ref_palette_neutral40 0x7f050113
int color m3_ref_palette_neutral50 0x7f050114
int color m3_ref_palette_neutral6 0x7f050115
int color m3_ref_palette_neutral60 0x7f050116
int color m3_ref_palette_neutral70 0x7f050117
int color m3_ref_palette_neutral80 0x7f050118
int color m3_ref_palette_neutral87 0x7f050119
int color m3_ref_palette_neutral90 0x7f05011a
int color m3_ref_palette_neutral92 0x7f05011b
int color m3_ref_palette_neutral94 0x7f05011c
int color m3_ref_palette_neutral95 0x7f05011d
int color m3_ref_palette_neutral96 0x7f05011e
int color m3_ref_palette_neutral98 0x7f05011f
int color m3_ref_palette_neutral99 0x7f050120
int color m3_ref_palette_neutral_variant0 0x7f050121
int color m3_ref_palette_neutral_variant10 0x7f050122
int color m3_ref_palette_neutral_variant100 0x7f050123
int color m3_ref_palette_neutral_variant20 0x7f050124
int color m3_ref_palette_neutral_variant30 0x7f050125
int color m3_ref_palette_neutral_variant40 0x7f050126
int color m3_ref_palette_neutral_variant50 0x7f050127
int color m3_ref_palette_neutral_variant60 0x7f050128
int color m3_ref_palette_neutral_variant70 0x7f050129
int color m3_ref_palette_neutral_variant80 0x7f05012a
int color m3_ref_palette_neutral_variant90 0x7f05012b
int color m3_ref_palette_neutral_variant95 0x7f05012c
int color m3_ref_palette_neutral_variant99 0x7f05012d
int color m3_ref_palette_primary0 0x7f05012e
int color m3_ref_palette_primary10 0x7f05012f
int color m3_ref_palette_primary100 0x7f050130
int color m3_ref_palette_primary20 0x7f050131
int color m3_ref_palette_primary30 0x7f050132
int color m3_ref_palette_primary40 0x7f050133
int color m3_ref_palette_primary50 0x7f050134
int color m3_ref_palette_primary60 0x7f050135
int color m3_ref_palette_primary70 0x7f050136
int color m3_ref_palette_primary80 0x7f050137
int color m3_ref_palette_primary90 0x7f050138
int color m3_ref_palette_primary95 0x7f050139
int color m3_ref_palette_primary99 0x7f05013a
int color m3_ref_palette_secondary0 0x7f05013b
int color m3_ref_palette_secondary10 0x7f05013c
int color m3_ref_palette_secondary100 0x7f05013d
int color m3_ref_palette_secondary20 0x7f05013e
int color m3_ref_palette_secondary30 0x7f05013f
int color m3_ref_palette_secondary40 0x7f050140
int color m3_ref_palette_secondary50 0x7f050141
int color m3_ref_palette_secondary60 0x7f050142
int color m3_ref_palette_secondary70 0x7f050143
int color m3_ref_palette_secondary80 0x7f050144
int color m3_ref_palette_secondary90 0x7f050145
int color m3_ref_palette_secondary95 0x7f050146
int color m3_ref_palette_secondary99 0x7f050147
int color m3_ref_palette_tertiary0 0x7f050148
int color m3_ref_palette_tertiary10 0x7f050149
int color m3_ref_palette_tertiary100 0x7f05014a
int color m3_ref_palette_tertiary20 0x7f05014b
int color m3_ref_palette_tertiary30 0x7f05014c
int color m3_ref_palette_tertiary40 0x7f05014d
int color m3_ref_palette_tertiary50 0x7f05014e
int color m3_ref_palette_tertiary60 0x7f05014f
int color m3_ref_palette_tertiary70 0x7f050150
int color m3_ref_palette_tertiary80 0x7f050151
int color m3_ref_palette_tertiary90 0x7f050152
int color m3_ref_palette_tertiary95 0x7f050153
int color m3_ref_palette_tertiary99 0x7f050154
int color m3_ref_palette_white 0x7f050155
int color m3_selection_control_ripple_color_selector 0x7f050156
int color m3_simple_item_ripple_color 0x7f050157
int color m3_slider_active_track_color 0x7f050158
int color m3_slider_active_track_color_legacy 0x7f050159
int color m3_slider_halo_color_legacy 0x7f05015a
int color m3_slider_inactive_track_color 0x7f05015b
int color m3_slider_inactive_track_color_legacy 0x7f05015c
int color m3_slider_thumb_color 0x7f05015d
int color m3_slider_thumb_color_legacy 0x7f05015e
int color m3_switch_thumb_tint 0x7f05015f
int color m3_switch_track_tint 0x7f050160
int color m3_sys_color_dark_background 0x7f050161
int color m3_sys_color_dark_error 0x7f050162
int color m3_sys_color_dark_error_container 0x7f050163
int color m3_sys_color_dark_inverse_on_surface 0x7f050164
int color m3_sys_color_dark_inverse_primary 0x7f050165
int color m3_sys_color_dark_inverse_surface 0x7f050166
int color m3_sys_color_dark_on_background 0x7f050167
int color m3_sys_color_dark_on_error 0x7f050168
int color m3_sys_color_dark_on_error_container 0x7f050169
int color m3_sys_color_dark_on_primary 0x7f05016a
int color m3_sys_color_dark_on_primary_container 0x7f05016b
int color m3_sys_color_dark_on_secondary 0x7f05016c
int color m3_sys_color_dark_on_secondary_container 0x7f05016d
int color m3_sys_color_dark_on_surface 0x7f05016e
int color m3_sys_color_dark_on_surface_variant 0x7f05016f
int color m3_sys_color_dark_on_tertiary 0x7f050170
int color m3_sys_color_dark_on_tertiary_container 0x7f050171
int color m3_sys_color_dark_outline 0x7f050172
int color m3_sys_color_dark_outline_variant 0x7f050173
int color m3_sys_color_dark_primary 0x7f050174
int color m3_sys_color_dark_primary_container 0x7f050175
int color m3_sys_color_dark_secondary 0x7f050176
int color m3_sys_color_dark_secondary_container 0x7f050177
int color m3_sys_color_dark_surface 0x7f050178
int color m3_sys_color_dark_surface_bright 0x7f050179
int color m3_sys_color_dark_surface_container 0x7f05017a
int color m3_sys_color_dark_surface_container_high 0x7f05017b
int color m3_sys_color_dark_surface_container_highest 0x7f05017c
int color m3_sys_color_dark_surface_container_low 0x7f05017d
int color m3_sys_color_dark_surface_container_lowest 0x7f05017e
int color m3_sys_color_dark_surface_dim 0x7f05017f
int color m3_sys_color_dark_surface_variant 0x7f050180
int color m3_sys_color_dark_tertiary 0x7f050181
int color m3_sys_color_dark_tertiary_container 0x7f050182
int color m3_sys_color_dynamic_dark_background 0x7f050183
int color m3_sys_color_dynamic_dark_error 0x7f050184
int color m3_sys_color_dynamic_dark_error_container 0x7f050185
int color m3_sys_color_dynamic_dark_inverse_on_surface 0x7f050186
int color m3_sys_color_dynamic_dark_inverse_primary 0x7f050187
int color m3_sys_color_dynamic_dark_inverse_surface 0x7f050188
int color m3_sys_color_dynamic_dark_on_background 0x7f050189
int color m3_sys_color_dynamic_dark_on_error 0x7f05018a
int color m3_sys_color_dynamic_dark_on_error_container 0x7f05018b
int color m3_sys_color_dynamic_dark_on_primary 0x7f05018c
int color m3_sys_color_dynamic_dark_on_primary_container 0x7f05018d
int color m3_sys_color_dynamic_dark_on_secondary 0x7f05018e
int color m3_sys_color_dynamic_dark_on_secondary_container 0x7f05018f
int color m3_sys_color_dynamic_dark_on_surface 0x7f050190
int color m3_sys_color_dynamic_dark_on_surface_variant 0x7f050191
int color m3_sys_color_dynamic_dark_on_tertiary 0x7f050192
int color m3_sys_color_dynamic_dark_on_tertiary_container 0x7f050193
int color m3_sys_color_dynamic_dark_outline 0x7f050194
int color m3_sys_color_dynamic_dark_outline_variant 0x7f050195
int color m3_sys_color_dynamic_dark_primary 0x7f050196
int color m3_sys_color_dynamic_dark_primary_container 0x7f050197
int color m3_sys_color_dynamic_dark_secondary 0x7f050198
int color m3_sys_color_dynamic_dark_secondary_container 0x7f050199
int color m3_sys_color_dynamic_dark_surface 0x7f05019a
int color m3_sys_color_dynamic_dark_surface_bright 0x7f05019b
int color m3_sys_color_dynamic_dark_surface_container 0x7f05019c
int color m3_sys_color_dynamic_dark_surface_container_high 0x7f05019d
int color m3_sys_color_dynamic_dark_surface_container_highest 0x7f05019e
int color m3_sys_color_dynamic_dark_surface_container_low 0x7f05019f
int color m3_sys_color_dynamic_dark_surface_container_lowest 0x7f0501a0
int color m3_sys_color_dynamic_dark_surface_dim 0x7f0501a1
int color m3_sys_color_dynamic_dark_surface_variant 0x7f0501a2
int color m3_sys_color_dynamic_dark_tertiary 0x7f0501a3
int color m3_sys_color_dynamic_dark_tertiary_container 0x7f0501a4
int color m3_sys_color_dynamic_light_background 0x7f0501a5
int color m3_sys_color_dynamic_light_error 0x7f0501a6
int color m3_sys_color_dynamic_light_error_container 0x7f0501a7
int color m3_sys_color_dynamic_light_inverse_on_surface 0x7f0501a8
int color m3_sys_color_dynamic_light_inverse_primary 0x7f0501a9
int color m3_sys_color_dynamic_light_inverse_surface 0x7f0501aa
int color m3_sys_color_dynamic_light_on_background 0x7f0501ab
int color m3_sys_color_dynamic_light_on_error 0x7f0501ac
int color m3_sys_color_dynamic_light_on_error_container 0x7f0501ad
int color m3_sys_color_dynamic_light_on_primary 0x7f0501ae
int color m3_sys_color_dynamic_light_on_primary_container 0x7f0501af
int color m3_sys_color_dynamic_light_on_secondary 0x7f0501b0
int color m3_sys_color_dynamic_light_on_secondary_container 0x7f0501b1
int color m3_sys_color_dynamic_light_on_surface 0x7f0501b2
int color m3_sys_color_dynamic_light_on_surface_variant 0x7f0501b3
int color m3_sys_color_dynamic_light_on_tertiary 0x7f0501b4
int color m3_sys_color_dynamic_light_on_tertiary_container 0x7f0501b5
int color m3_sys_color_dynamic_light_outline 0x7f0501b6
int color m3_sys_color_dynamic_light_outline_variant 0x7f0501b7
int color m3_sys_color_dynamic_light_primary 0x7f0501b8
int color m3_sys_color_dynamic_light_primary_container 0x7f0501b9
int color m3_sys_color_dynamic_light_secondary 0x7f0501ba
int color m3_sys_color_dynamic_light_secondary_container 0x7f0501bb
int color m3_sys_color_dynamic_light_surface 0x7f0501bc
int color m3_sys_color_dynamic_light_surface_bright 0x7f0501bd
int color m3_sys_color_dynamic_light_surface_container 0x7f0501be
int color m3_sys_color_dynamic_light_surface_container_high 0x7f0501bf
int color m3_sys_color_dynamic_light_surface_container_highest 0x7f0501c0
int color m3_sys_color_dynamic_light_surface_container_low 0x7f0501c1
int color m3_sys_color_dynamic_light_surface_container_lowest 0x7f0501c2
int color m3_sys_color_dynamic_light_surface_dim 0x7f0501c3
int color m3_sys_color_dynamic_light_surface_variant 0x7f0501c4
int color m3_sys_color_dynamic_light_tertiary 0x7f0501c5
int color m3_sys_color_dynamic_light_tertiary_container 0x7f0501c6
int color m3_sys_color_dynamic_on_primary_fixed 0x7f0501c7
int color m3_sys_color_dynamic_on_primary_fixed_variant 0x7f0501c8
int color m3_sys_color_dynamic_on_secondary_fixed 0x7f0501c9
int color m3_sys_color_dynamic_on_secondary_fixed_variant 0x7f0501ca
int color m3_sys_color_dynamic_on_tertiary_fixed 0x7f0501cb
int color m3_sys_color_dynamic_on_tertiary_fixed_variant 0x7f0501cc
int color m3_sys_color_dynamic_primary_fixed 0x7f0501cd
int color m3_sys_color_dynamic_primary_fixed_dim 0x7f0501ce
int color m3_sys_color_dynamic_secondary_fixed 0x7f0501cf
int color m3_sys_color_dynamic_secondary_fixed_dim 0x7f0501d0
int color m3_sys_color_dynamic_tertiary_fixed 0x7f0501d1
int color m3_sys_color_dynamic_tertiary_fixed_dim 0x7f0501d2
int color m3_sys_color_light_background 0x7f0501d3
int color m3_sys_color_light_error 0x7f0501d4
int color m3_sys_color_light_error_container 0x7f0501d5
int color m3_sys_color_light_inverse_on_surface 0x7f0501d6
int color m3_sys_color_light_inverse_primary 0x7f0501d7
int color m3_sys_color_light_inverse_surface 0x7f0501d8
int color m3_sys_color_light_on_background 0x7f0501d9
int color m3_sys_color_light_on_error 0x7f0501da
int color m3_sys_color_light_on_error_container 0x7f0501db
int color m3_sys_color_light_on_primary 0x7f0501dc
int color m3_sys_color_light_on_primary_container 0x7f0501dd
int color m3_sys_color_light_on_secondary 0x7f0501de
int color m3_sys_color_light_on_secondary_container 0x7f0501df
int color m3_sys_color_light_on_surface 0x7f0501e0
int color m3_sys_color_light_on_surface_variant 0x7f0501e1
int color m3_sys_color_light_on_tertiary 0x7f0501e2
int color m3_sys_color_light_on_tertiary_container 0x7f0501e3
int color m3_sys_color_light_outline 0x7f0501e4
int color m3_sys_color_light_outline_variant 0x7f0501e5
int color m3_sys_color_light_primary 0x7f0501e6
int color m3_sys_color_light_primary_container 0x7f0501e7
int color m3_sys_color_light_secondary 0x7f0501e8
int color m3_sys_color_light_secondary_container 0x7f0501e9
int color m3_sys_color_light_surface 0x7f0501ea
int color m3_sys_color_light_surface_bright 0x7f0501eb
int color m3_sys_color_light_surface_container 0x7f0501ec
int color m3_sys_color_light_surface_container_high 0x7f0501ed
int color m3_sys_color_light_surface_container_highest 0x7f0501ee
int color m3_sys_color_light_surface_container_low 0x7f0501ef
int color m3_sys_color_light_surface_container_lowest 0x7f0501f0
int color m3_sys_color_light_surface_dim 0x7f0501f1
int color m3_sys_color_light_surface_variant 0x7f0501f2
int color m3_sys_color_light_tertiary 0x7f0501f3
int color m3_sys_color_light_tertiary_container 0x7f0501f4
int color m3_sys_color_on_primary_fixed 0x7f0501f5
int color m3_sys_color_on_primary_fixed_variant 0x7f0501f6
int color m3_sys_color_on_secondary_fixed 0x7f0501f7
int color m3_sys_color_on_secondary_fixed_variant 0x7f0501f8
int color m3_sys_color_on_tertiary_fixed 0x7f0501f9
int color m3_sys_color_on_tertiary_fixed_variant 0x7f0501fa
int color m3_sys_color_primary_fixed 0x7f0501fb
int color m3_sys_color_primary_fixed_dim 0x7f0501fc
int color m3_sys_color_secondary_fixed 0x7f0501fd
int color m3_sys_color_secondary_fixed_dim 0x7f0501fe
int color m3_sys_color_tertiary_fixed 0x7f0501ff
int color m3_sys_color_tertiary_fixed_dim 0x7f050200
int color m3_tabs_icon_color 0x7f050201
int color m3_tabs_icon_color_secondary 0x7f050202
int color m3_tabs_ripple_color 0x7f050203
int color m3_tabs_ripple_color_secondary 0x7f050204
int color m3_tabs_text_color 0x7f050205
int color m3_tabs_text_color_secondary 0x7f050206
int color m3_text_button_background_color_selector 0x7f050207
int color m3_text_button_foreground_color_selector 0x7f050208
int color m3_text_button_ripple_color_selector 0x7f050209
int color m3_textfield_filled_background_color 0x7f05020a
int color m3_textfield_indicator_text_color 0x7f05020b
int color m3_textfield_input_text_color 0x7f05020c
int color m3_textfield_label_color 0x7f05020d
int color m3_textfield_stroke_color 0x7f05020e
int color m3_timepicker_button_background_color 0x7f05020f
int color m3_timepicker_button_ripple_color 0x7f050210
int color m3_timepicker_button_text_color 0x7f050211
int color m3_timepicker_clock_text_color 0x7f050212
int color m3_timepicker_display_background_color 0x7f050213
int color m3_timepicker_display_ripple_color 0x7f050214
int color m3_timepicker_display_text_color 0x7f050215
int color m3_timepicker_secondary_text_button_ripple_color 0x7f050216
int color m3_timepicker_secondary_text_button_text_color 0x7f050217
int color m3_timepicker_time_input_stroke_color 0x7f050218
int color m3_tonal_button_ripple_color_selector 0x7f050219
int color material_blue_grey_800 0x7f05021a
int color material_blue_grey_900 0x7f05021b
int color material_blue_grey_950 0x7f05021c
int color material_cursor_color 0x7f05021d
int color material_deep_teal_200 0x7f05021e
int color material_deep_teal_500 0x7f05021f
int color material_divider_color 0x7f050220
int color material_dynamic_color_dark_error 0x7f050221
int color material_dynamic_color_dark_error_container 0x7f050222
int color material_dynamic_color_dark_on_error 0x7f050223
int color material_dynamic_color_dark_on_error_container 0x7f050224
int color material_dynamic_color_light_error 0x7f050225
int color material_dynamic_color_light_error_container 0x7f050226
int color material_dynamic_color_light_on_error 0x7f050227
int color material_dynamic_color_light_on_error_container 0x7f050228
int color material_dynamic_neutral0 0x7f050229
int color material_dynamic_neutral10 0x7f05022a
int color material_dynamic_neutral100 0x7f05022b
int color material_dynamic_neutral20 0x7f05022c
int color material_dynamic_neutral30 0x7f05022d
int color material_dynamic_neutral40 0x7f05022e
int color material_dynamic_neutral50 0x7f05022f
int color material_dynamic_neutral60 0x7f050230
int color material_dynamic_neutral70 0x7f050231
int color material_dynamic_neutral80 0x7f050232
int color material_dynamic_neutral90 0x7f050233
int color material_dynamic_neutral95 0x7f050234
int color material_dynamic_neutral99 0x7f050235
int color material_dynamic_neutral_variant0 0x7f050236
int color material_dynamic_neutral_variant10 0x7f050237
int color material_dynamic_neutral_variant100 0x7f050238
int color material_dynamic_neutral_variant20 0x7f050239
int color material_dynamic_neutral_variant30 0x7f05023a
int color material_dynamic_neutral_variant40 0x7f05023b
int color material_dynamic_neutral_variant50 0x7f05023c
int color material_dynamic_neutral_variant60 0x7f05023d
int color material_dynamic_neutral_variant70 0x7f05023e
int color material_dynamic_neutral_variant80 0x7f05023f
int color material_dynamic_neutral_variant90 0x7f050240
int color material_dynamic_neutral_variant95 0x7f050241
int color material_dynamic_neutral_variant99 0x7f050242
int color material_dynamic_primary0 0x7f050243
int color material_dynamic_primary10 0x7f050244
int color material_dynamic_primary100 0x7f050245
int color material_dynamic_primary20 0x7f050246
int color material_dynamic_primary30 0x7f050247
int color material_dynamic_primary40 0x7f050248
int color material_dynamic_primary50 0x7f050249
int color material_dynamic_primary60 0x7f05024a
int color material_dynamic_primary70 0x7f05024b
int color material_dynamic_primary80 0x7f05024c
int color material_dynamic_primary90 0x7f05024d
int color material_dynamic_primary95 0x7f05024e
int color material_dynamic_primary99 0x7f05024f
int color material_dynamic_secondary0 0x7f050250
int color material_dynamic_secondary10 0x7f050251
int color material_dynamic_secondary100 0x7f050252
int color material_dynamic_secondary20 0x7f050253
int color material_dynamic_secondary30 0x7f050254
int color material_dynamic_secondary40 0x7f050255
int color material_dynamic_secondary50 0x7f050256
int color material_dynamic_secondary60 0x7f050257
int color material_dynamic_secondary70 0x7f050258
int color material_dynamic_secondary80 0x7f050259
int color material_dynamic_secondary90 0x7f05025a
int color material_dynamic_secondary95 0x7f05025b
int color material_dynamic_secondary99 0x7f05025c
int color material_dynamic_tertiary0 0x7f05025d
int color material_dynamic_tertiary10 0x7f05025e
int color material_dynamic_tertiary100 0x7f05025f
int color material_dynamic_tertiary20 0x7f050260
int color material_dynamic_tertiary30 0x7f050261
int color material_dynamic_tertiary40 0x7f050262
int color material_dynamic_tertiary50 0x7f050263
int color material_dynamic_tertiary60 0x7f050264
int color material_dynamic_tertiary70 0x7f050265
int color material_dynamic_tertiary80 0x7f050266
int color material_dynamic_tertiary90 0x7f050267
int color material_dynamic_tertiary95 0x7f050268
int color material_dynamic_tertiary99 0x7f050269
int color material_grey_100 0x7f05026a
int color material_grey_300 0x7f05026b
int color material_grey_50 0x7f05026c
int color material_grey_600 0x7f05026d
int color material_grey_800 0x7f05026e
int color material_grey_850 0x7f05026f
int color material_grey_900 0x7f050270
int color material_harmonized_color_error 0x7f050271
int color material_harmonized_color_error_container 0x7f050272
int color material_harmonized_color_on_error 0x7f050273
int color material_harmonized_color_on_error_container 0x7f050274
int color material_on_background_disabled 0x7f050275
int color material_on_background_emphasis_high_type 0x7f050276
int color material_on_background_emphasis_medium 0x7f050277
int color material_on_primary_disabled 0x7f050278
int color material_on_primary_emphasis_high_type 0x7f050279
int color material_on_primary_emphasis_medium 0x7f05027a
int color material_on_surface_disabled 0x7f05027b
int color material_on_surface_emphasis_high_type 0x7f05027c
int color material_on_surface_emphasis_medium 0x7f05027d
int color material_on_surface_stroke 0x7f05027e
int color material_personalized__highlighted_text 0x7f05027f
int color material_personalized__highlighted_text_inverse 0x7f050280
int color material_personalized_color_background 0x7f050281
int color material_personalized_color_control_activated 0x7f050282
int color material_personalized_color_control_highlight 0x7f050283
int color material_personalized_color_control_normal 0x7f050284
int color material_personalized_color_error 0x7f050285
int color material_personalized_color_error_container 0x7f050286
int color material_personalized_color_on_background 0x7f050287
int color material_personalized_color_on_error 0x7f050288
int color material_personalized_color_on_error_container 0x7f050289
int color material_personalized_color_on_primary 0x7f05028a
int color material_personalized_color_on_primary_container 0x7f05028b
int color material_personalized_color_on_secondary 0x7f05028c
int color material_personalized_color_on_secondary_container 0x7f05028d
int color material_personalized_color_on_surface 0x7f05028e
int color material_personalized_color_on_surface_inverse 0x7f05028f
int color material_personalized_color_on_surface_variant 0x7f050290
int color material_personalized_color_on_tertiary 0x7f050291
int color material_personalized_color_on_tertiary_container 0x7f050292
int color material_personalized_color_outline 0x7f050293
int color material_personalized_color_outline_variant 0x7f050294
int color material_personalized_color_primary 0x7f050295
int color material_personalized_color_primary_container 0x7f050296
int color material_personalized_color_primary_inverse 0x7f050297
int color material_personalized_color_primary_text 0x7f050298
int color material_personalized_color_primary_text_inverse 0x7f050299
int color material_personalized_color_secondary 0x7f05029a
int color material_personalized_color_secondary_container 0x7f05029b
int color material_personalized_color_secondary_text 0x7f05029c
int color material_personalized_color_secondary_text_inverse 0x7f05029d
int color material_personalized_color_surface 0x7f05029e
int color material_personalized_color_surface_bright 0x7f05029f
int color material_personalized_color_surface_container 0x7f0502a0
int color material_personalized_color_surface_container_high 0x7f0502a1
int color material_personalized_color_surface_container_highest 0x7f0502a2
int color material_personalized_color_surface_container_low 0x7f0502a3
int color material_personalized_color_surface_container_lowest 0x7f0502a4
int color material_personalized_color_surface_dim 0x7f0502a5
int color material_personalized_color_surface_inverse 0x7f0502a6
int color material_personalized_color_surface_variant 0x7f0502a7
int color material_personalized_color_tertiary 0x7f0502a8
int color material_personalized_color_tertiary_container 0x7f0502a9
int color material_personalized_color_text_hint_foreground_inverse 0x7f0502aa
int color material_personalized_color_text_primary_inverse 0x7f0502ab
int color material_personalized_color_text_primary_inverse_disable_only 0x7f0502ac
int color material_personalized_color_text_secondary_and_tertiary_inverse 0x7f0502ad
int color material_personalized_color_text_secondary_and_tertiary_inverse_disabled 0x7f0502ae
int color material_personalized_hint_foreground 0x7f0502af
int color material_personalized_hint_foreground_inverse 0x7f0502b0
int color material_personalized_primary_inverse_text_disable_only 0x7f0502b1
int color material_personalized_primary_text_disable_only 0x7f0502b2
int color material_slider_active_tick_marks_color 0x7f0502b3
int color material_slider_active_track_color 0x7f0502b4
int color material_slider_halo_color 0x7f0502b5
int color material_slider_inactive_tick_marks_color 0x7f0502b6
int color material_slider_inactive_track_color 0x7f0502b7
int color material_slider_thumb_color 0x7f0502b8
int color material_timepicker_button_background 0x7f0502b9
int color material_timepicker_button_stroke 0x7f0502ba
int color material_timepicker_clock_text_color 0x7f0502bb
int color material_timepicker_clockface 0x7f0502bc
int color material_timepicker_modebutton_tint 0x7f0502bd
int color maui_splash_color 0x7f0502be
int color mtrl_btn_bg_color_selector 0x7f0502bf
int color mtrl_btn_ripple_color 0x7f0502c0
int color mtrl_btn_stroke_color_selector 0x7f0502c1
int color mtrl_btn_text_btn_bg_color_selector 0x7f0502c2
int color mtrl_btn_text_btn_ripple_color 0x7f0502c3
int color mtrl_btn_text_color_disabled 0x7f0502c4
int color mtrl_btn_text_color_selector 0x7f0502c5
int color mtrl_btn_transparent_bg_color 0x7f0502c6
int color mtrl_calendar_item_stroke_color 0x7f0502c7
int color mtrl_calendar_selected_range 0x7f0502c8
int color mtrl_card_view_foreground 0x7f0502c9
int color mtrl_card_view_ripple 0x7f0502ca
int color mtrl_chip_background_color 0x7f0502cb
int color mtrl_chip_close_icon_tint 0x7f0502cc
int color mtrl_chip_surface_color 0x7f0502cd
int color mtrl_chip_text_color 0x7f0502ce
int color mtrl_choice_chip_background_color 0x7f0502cf
int color mtrl_choice_chip_ripple_color 0x7f0502d0
int color mtrl_choice_chip_text_color 0x7f0502d1
int color mtrl_error 0x7f0502d2
int color mtrl_fab_bg_color_selector 0x7f0502d3
int color mtrl_fab_icon_text_color_selector 0x7f0502d4
int color mtrl_fab_ripple_color 0x7f0502d5
int color mtrl_filled_background_color 0x7f0502d6
int color mtrl_filled_icon_tint 0x7f0502d7
int color mtrl_filled_stroke_color 0x7f0502d8
int color mtrl_indicator_text_color 0x7f0502d9
int color mtrl_navigation_bar_colored_item_tint 0x7f0502da
int color mtrl_navigation_bar_colored_ripple_color 0x7f0502db
int color mtrl_navigation_bar_item_tint 0x7f0502dc
int color mtrl_navigation_bar_ripple_color 0x7f0502dd
int color mtrl_navigation_item_background_color 0x7f0502de
int color mtrl_navigation_item_icon_tint 0x7f0502df
int color mtrl_navigation_item_text_color 0x7f0502e0
int color mtrl_on_primary_text_btn_text_color_selector 0x7f0502e1
int color mtrl_on_surface_ripple_color 0x7f0502e2
int color mtrl_outlined_icon_tint 0x7f0502e3
int color mtrl_outlined_stroke_color 0x7f0502e4
int color mtrl_popupmenu_overlay_color 0x7f0502e5
int color mtrl_scrim_color 0x7f0502e6
int color mtrl_switch_thumb_icon_tint 0x7f0502e7
int color mtrl_switch_thumb_tint 0x7f0502e8
int color mtrl_switch_track_decoration_tint 0x7f0502e9
int color mtrl_switch_track_tint 0x7f0502ea
int color mtrl_tabs_colored_ripple_color 0x7f0502eb
int color mtrl_tabs_icon_color_selector 0x7f0502ec
int color mtrl_tabs_icon_color_selector_colored 0x7f0502ed
int color mtrl_tabs_legacy_text_color_selector 0x7f0502ee
int color mtrl_tabs_ripple_color 0x7f0502ef
int color mtrl_text_btn_text_color_selector 0x7f0502f0
int color mtrl_textinput_default_box_stroke_color 0x7f0502f1
int color mtrl_textinput_disabled_color 0x7f0502f2
int color mtrl_textinput_filled_box_default_background_color 0x7f0502f3
int color mtrl_textinput_focused_box_stroke_color 0x7f0502f4
int color mtrl_textinput_hovered_box_stroke_color 0x7f0502f5
int color notification_action_color_filter 0x7f0502f6
int color notification_icon_bg_color 0x7f0502f7
int color primary_dark_material_dark 0x7f0502f8
int color primary_dark_material_light 0x7f0502f9
int color primary_material_dark 0x7f0502fa
int color primary_material_light 0x7f0502fb
int color primary_text_default_material_dark 0x7f0502fc
int color primary_text_default_material_light 0x7f0502fd
int color primary_text_disabled_material_dark 0x7f0502fe
int color primary_text_disabled_material_light 0x7f0502ff
int color ripple_material_dark 0x7f050300
int color ripple_material_light 0x7f050301
int color secondary_text_default_material_dark 0x7f050302
int color secondary_text_default_material_light 0x7f050303
int color secondary_text_disabled_material_dark 0x7f050304
int color secondary_text_disabled_material_light 0x7f050305
int color switch_thumb_disabled_material_dark 0x7f050306
int color switch_thumb_disabled_material_light 0x7f050307
int color switch_thumb_material_dark 0x7f050308
int color switch_thumb_material_light 0x7f050309
int color switch_thumb_normal_material_dark 0x7f05030a
int color switch_thumb_normal_material_light 0x7f05030b
int color tooltip_background_dark 0x7f05030c
int color tooltip_background_light 0x7f05030d
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_height_large_material 0x7f060030
int dimen abc_list_item_height_material 0x7f060031
int dimen abc_list_item_height_small_material 0x7f060032
int dimen abc_list_item_padding_horizontal_material 0x7f060033
int dimen abc_panel_menu_list_width 0x7f060034
int dimen abc_progress_bar_height_material 0x7f060035
int dimen abc_search_view_preferred_height 0x7f060036
int dimen abc_search_view_preferred_width 0x7f060037
int dimen abc_seekbar_track_background_height_material 0x7f060038
int dimen abc_seekbar_track_progress_height_material 0x7f060039
int dimen abc_select_dialog_padding_start_material 0x7f06003a
int dimen abc_star_big 0x7f06003b
int dimen abc_star_medium 0x7f06003c
int dimen abc_star_small 0x7f06003d
int dimen abc_switch_padding 0x7f06003e
int dimen abc_text_size_body_1_material 0x7f06003f
int dimen abc_text_size_body_2_material 0x7f060040
int dimen abc_text_size_button_material 0x7f060041
int dimen abc_text_size_caption_material 0x7f060042
int dimen abc_text_size_display_1_material 0x7f060043
int dimen abc_text_size_display_2_material 0x7f060044
int dimen abc_text_size_display_3_material 0x7f060045
int dimen abc_text_size_display_4_material 0x7f060046
int dimen abc_text_size_headline_material 0x7f060047
int dimen abc_text_size_large_material 0x7f060048
int dimen abc_text_size_medium_material 0x7f060049
int dimen abc_text_size_menu_header_material 0x7f06004a
int dimen abc_text_size_menu_material 0x7f06004b
int dimen abc_text_size_small_material 0x7f06004c
int dimen abc_text_size_subhead_material 0x7f06004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f06004e
int dimen abc_text_size_title_material 0x7f06004f
int dimen abc_text_size_title_material_toolbar 0x7f060050
int dimen appcompat_dialog_background_inset 0x7f060051
int dimen browser_actions_context_menu_max_width 0x7f060052
int dimen browser_actions_context_menu_min_padding 0x7f060053
int dimen cardview_compat_inset_shadow 0x7f060054
int dimen cardview_default_elevation 0x7f060055
int dimen cardview_default_radius 0x7f060056
int dimen clock_face_margin_start 0x7f060057
int dimen compat_button_inset_horizontal_material 0x7f060058
int dimen compat_button_inset_vertical_material 0x7f060059
int dimen compat_button_padding_horizontal_material 0x7f06005a
int dimen compat_button_padding_vertical_material 0x7f06005b
int dimen compat_control_corner_material 0x7f06005c
int dimen compat_notification_large_icon_max_height 0x7f06005d
int dimen compat_notification_large_icon_max_width 0x7f06005e
int dimen def_drawer_elevation 0x7f06005f
int dimen design_appbar_elevation 0x7f060060
int dimen design_bottom_navigation_active_item_max_width 0x7f060061
int dimen design_bottom_navigation_active_item_min_width 0x7f060062
int dimen design_bottom_navigation_active_text_size 0x7f060063
int dimen design_bottom_navigation_elevation 0x7f060064
int dimen design_bottom_navigation_height 0x7f060065
int dimen design_bottom_navigation_icon_size 0x7f060066
int dimen design_bottom_navigation_item_max_width 0x7f060067
int dimen design_bottom_navigation_item_min_width 0x7f060068
int dimen design_bottom_navigation_label_padding 0x7f060069
int dimen design_bottom_navigation_margin 0x7f06006a
int dimen design_bottom_navigation_shadow_height 0x7f06006b
int dimen design_bottom_navigation_text_size 0x7f06006c
int dimen design_bottom_sheet_elevation 0x7f06006d
int dimen design_bottom_sheet_modal_elevation 0x7f06006e
int dimen design_bottom_sheet_peek_height_min 0x7f06006f
int dimen design_fab_border_width 0x7f060070
int dimen design_fab_elevation 0x7f060071
int dimen design_fab_image_size 0x7f060072
int dimen design_fab_size_mini 0x7f060073
int dimen design_fab_size_normal 0x7f060074
int dimen design_fab_translation_z_hovered_focused 0x7f060075
int dimen design_fab_translation_z_pressed 0x7f060076
int dimen design_navigation_elevation 0x7f060077
int dimen design_navigation_icon_padding 0x7f060078
int dimen design_navigation_icon_size 0x7f060079
int dimen design_navigation_item_horizontal_padding 0x7f06007a
int dimen design_navigation_item_icon_padding 0x7f06007b
int dimen design_navigation_item_vertical_padding 0x7f06007c
int dimen design_navigation_max_width 0x7f06007d
int dimen design_navigation_padding_bottom 0x7f06007e
int dimen design_navigation_separator_vertical_padding 0x7f06007f
int dimen design_snackbar_action_inline_max_width 0x7f060080
int dimen design_snackbar_action_text_color_alpha 0x7f060081
int dimen design_snackbar_background_corner_radius 0x7f060082
int dimen design_snackbar_elevation 0x7f060083
int dimen design_snackbar_extra_spacing_horizontal 0x7f060084
int dimen design_snackbar_max_width 0x7f060085
int dimen design_snackbar_min_width 0x7f060086
int dimen design_snackbar_padding_horizontal 0x7f060087
int dimen design_snackbar_padding_vertical 0x7f060088
int dimen design_snackbar_padding_vertical_2lines 0x7f060089
int dimen design_snackbar_text_size 0x7f06008a
int dimen design_tab_max_width 0x7f06008b
int dimen design_tab_scrollable_min_width 0x7f06008c
int dimen design_tab_text_size 0x7f06008d
int dimen design_tab_text_size_2line 0x7f06008e
int dimen design_textinput_caption_translate_y 0x7f06008f
int dimen disabled_alpha_material_dark 0x7f060090
int dimen disabled_alpha_material_light 0x7f060091
int dimen fastscroll_default_thickness 0x7f060092
int dimen fastscroll_margin 0x7f060093
int dimen fastscroll_minimum_range 0x7f060094
int dimen highlight_alpha_material_colored 0x7f060095
int dimen highlight_alpha_material_dark 0x7f060096
int dimen highlight_alpha_material_light 0x7f060097
int dimen hint_alpha_material_dark 0x7f060098
int dimen hint_alpha_material_light 0x7f060099
int dimen hint_pressed_alpha_material_dark 0x7f06009a
int dimen hint_pressed_alpha_material_light 0x7f06009b
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f06009c
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f06009d
int dimen item_touch_helper_swipe_escape_velocity 0x7f06009e
int dimen m3_alert_dialog_action_bottom_padding 0x7f06009f
int dimen m3_alert_dialog_action_top_padding 0x7f0600a0
int dimen m3_alert_dialog_corner_size 0x7f0600a1
int dimen m3_alert_dialog_elevation 0x7f0600a2
int dimen m3_alert_dialog_icon_margin 0x7f0600a3
int dimen m3_alert_dialog_icon_size 0x7f0600a4
int dimen m3_alert_dialog_title_bottom_margin 0x7f0600a5
int dimen m3_appbar_expanded_title_margin_bottom 0x7f0600a6
int dimen m3_appbar_expanded_title_margin_horizontal 0x7f0600a7
int dimen m3_appbar_scrim_height_trigger 0x7f0600a8
int dimen m3_appbar_scrim_height_trigger_large 0x7f0600a9
int dimen m3_appbar_scrim_height_trigger_medium 0x7f0600aa
int dimen m3_appbar_size_compact 0x7f0600ab
int dimen m3_appbar_size_large 0x7f0600ac
int dimen m3_appbar_size_medium 0x7f0600ad
int dimen m3_back_progress_bottom_container_max_scale_x_distance 0x7f0600ae
int dimen m3_back_progress_bottom_container_max_scale_y_distance 0x7f0600af
int dimen m3_back_progress_main_container_max_translation_y 0x7f0600b0
int dimen m3_back_progress_main_container_min_edge_gap 0x7f0600b1
int dimen m3_back_progress_side_container_max_scale_x_distance_grow 0x7f0600b2
int dimen m3_back_progress_side_container_max_scale_x_distance_shrink 0x7f0600b3
int dimen m3_back_progress_side_container_max_scale_y_distance 0x7f0600b4
int dimen m3_badge_horizontal_offset 0x7f0600b5
int dimen m3_badge_offset 0x7f0600b6
int dimen m3_badge_size 0x7f0600b7
int dimen m3_badge_vertical_offset 0x7f0600b8
int dimen m3_badge_with_text_horizontal_offset 0x7f0600b9
int dimen m3_badge_with_text_offset 0x7f0600ba
int dimen m3_badge_with_text_size 0x7f0600bb
int dimen m3_badge_with_text_vertical_offset 0x7f0600bc
int dimen m3_badge_with_text_vertical_padding 0x7f0600bd
int dimen m3_bottom_nav_item_active_indicator_height 0x7f0600be
int dimen m3_bottom_nav_item_active_indicator_margin_horizontal 0x7f0600bf
int dimen m3_bottom_nav_item_active_indicator_width 0x7f0600c0
int dimen m3_bottom_nav_item_padding_bottom 0x7f0600c1
int dimen m3_bottom_nav_item_padding_top 0x7f0600c2
int dimen m3_bottom_nav_min_height 0x7f0600c3
int dimen m3_bottom_sheet_drag_handle_bottom_padding 0x7f0600c4
int dimen m3_bottom_sheet_elevation 0x7f0600c5
int dimen m3_bottom_sheet_modal_elevation 0x7f0600c6
int dimen m3_bottomappbar_fab_cradle_margin 0x7f0600c7
int dimen m3_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0600c8
int dimen m3_bottomappbar_fab_cradle_vertical_offset 0x7f0600c9
int dimen m3_bottomappbar_fab_end_margin 0x7f0600ca
int dimen m3_bottomappbar_height 0x7f0600cb
int dimen m3_bottomappbar_horizontal_padding 0x7f0600cc
int dimen m3_btn_dialog_btn_min_width 0x7f0600cd
int dimen m3_btn_dialog_btn_spacing 0x7f0600ce
int dimen m3_btn_disabled_elevation 0x7f0600cf
int dimen m3_btn_disabled_translation_z 0x7f0600d0
int dimen m3_btn_elevated_btn_elevation 0x7f0600d1
int dimen m3_btn_elevation 0x7f0600d2
int dimen m3_btn_icon_btn_padding_left 0x7f0600d3
int dimen m3_btn_icon_btn_padding_right 0x7f0600d4
int dimen m3_btn_icon_only_default_padding 0x7f0600d5
int dimen m3_btn_icon_only_default_size 0x7f0600d6
int dimen m3_btn_icon_only_icon_padding 0x7f0600d7
int dimen m3_btn_icon_only_min_width 0x7f0600d8
int dimen m3_btn_inset 0x7f0600d9
int dimen m3_btn_max_width 0x7f0600da
int dimen m3_btn_padding_bottom 0x7f0600db
int dimen m3_btn_padding_left 0x7f0600dc
int dimen m3_btn_padding_right 0x7f0600dd
int dimen m3_btn_padding_top 0x7f0600de
int dimen m3_btn_stroke_size 0x7f0600df
int dimen m3_btn_text_btn_icon_padding_left 0x7f0600e0
int dimen m3_btn_text_btn_icon_padding_right 0x7f0600e1
int dimen m3_btn_text_btn_padding_left 0x7f0600e2
int dimen m3_btn_text_btn_padding_right 0x7f0600e3
int dimen m3_btn_translation_z_base 0x7f0600e4
int dimen m3_btn_translation_z_hovered 0x7f0600e5
int dimen m3_card_disabled_z 0x7f0600e6
int dimen m3_card_dragged_z 0x7f0600e7
int dimen m3_card_elevated_disabled_z 0x7f0600e8
int dimen m3_card_elevated_dragged_z 0x7f0600e9
int dimen m3_card_elevated_elevation 0x7f0600ea
int dimen m3_card_elevated_hovered_z 0x7f0600eb
int dimen m3_card_elevation 0x7f0600ec
int dimen m3_card_hovered_z 0x7f0600ed
int dimen m3_card_stroke_width 0x7f0600ee
int dimen m3_carousel_debug_keyline_width 0x7f0600ef
int dimen m3_carousel_extra_small_item_size 0x7f0600f0
int dimen m3_carousel_gone_size 0x7f0600f1
int dimen m3_carousel_small_item_default_corner_size 0x7f0600f2
int dimen m3_carousel_small_item_size_max 0x7f0600f3
int dimen m3_carousel_small_item_size_min 0x7f0600f4
int dimen m3_chip_checked_hovered_translation_z 0x7f0600f5
int dimen m3_chip_corner_size 0x7f0600f6
int dimen m3_chip_disabled_translation_z 0x7f0600f7
int dimen m3_chip_dragged_translation_z 0x7f0600f8
int dimen m3_chip_elevated_elevation 0x7f0600f9
int dimen m3_chip_hovered_translation_z 0x7f0600fa
int dimen m3_chip_icon_size 0x7f0600fb
int dimen m3_comp_assist_chip_container_height 0x7f0600fc
int dimen m3_comp_assist_chip_elevated_container_elevation 0x7f0600fd
int dimen m3_comp_assist_chip_flat_container_elevation 0x7f0600fe
int dimen m3_comp_assist_chip_flat_outline_width 0x7f0600ff
int dimen m3_comp_assist_chip_with_icon_icon_size 0x7f060100
int dimen m3_comp_badge_large_size 0x7f060101
int dimen m3_comp_badge_size 0x7f060102
int dimen m3_comp_bottom_app_bar_container_elevation 0x7f060103
int dimen m3_comp_bottom_app_bar_container_height 0x7f060104
int dimen m3_comp_checkbox_selected_disabled_container_opacity 0x7f060105
int dimen m3_comp_date_picker_modal_date_today_container_outline_width 0x7f060106
int dimen m3_comp_date_picker_modal_header_container_height 0x7f060107
int dimen m3_comp_date_picker_modal_range_selection_header_container_height 0x7f060108
int dimen m3_comp_divider_thickness 0x7f060109
int dimen m3_comp_elevated_button_container_elevation 0x7f06010a
int dimen m3_comp_elevated_button_disabled_container_elevation 0x7f06010b
int dimen m3_comp_elevated_card_container_elevation 0x7f06010c
int dimen m3_comp_elevated_card_icon_size 0x7f06010d
int dimen m3_comp_extended_fab_primary_container_elevation 0x7f06010e
int dimen m3_comp_extended_fab_primary_container_height 0x7f06010f
int dimen m3_comp_extended_fab_primary_focus_container_elevation 0x7f060110
int dimen m3_comp_extended_fab_primary_focus_state_layer_opacity 0x7f060111
int dimen m3_comp_extended_fab_primary_hover_container_elevation 0x7f060112
int dimen m3_comp_extended_fab_primary_hover_state_layer_opacity 0x7f060113
int dimen m3_comp_extended_fab_primary_icon_size 0x7f060114
int dimen m3_comp_extended_fab_primary_pressed_container_elevation 0x7f060115
int dimen m3_comp_extended_fab_primary_pressed_state_layer_opacity 0x7f060116
int dimen m3_comp_fab_primary_container_elevation 0x7f060117
int dimen m3_comp_fab_primary_container_height 0x7f060118
int dimen m3_comp_fab_primary_focus_state_layer_opacity 0x7f060119
int dimen m3_comp_fab_primary_hover_container_elevation 0x7f06011a
int dimen m3_comp_fab_primary_hover_state_layer_opacity 0x7f06011b
int dimen m3_comp_fab_primary_icon_size 0x7f06011c
int dimen m3_comp_fab_primary_large_container_height 0x7f06011d
int dimen m3_comp_fab_primary_large_icon_size 0x7f06011e
int dimen m3_comp_fab_primary_pressed_container_elevation 0x7f06011f
int dimen m3_comp_fab_primary_pressed_state_layer_opacity 0x7f060120
int dimen m3_comp_fab_primary_small_container_height 0x7f060121
int dimen m3_comp_fab_primary_small_icon_size 0x7f060122
int dimen m3_comp_filled_autocomplete_menu_container_elevation 0x7f060123
int dimen m3_comp_filled_button_container_elevation 0x7f060124
int dimen m3_comp_filled_button_with_icon_icon_size 0x7f060125
int dimen m3_comp_filled_card_container_elevation 0x7f060126
int dimen m3_comp_filled_card_dragged_state_layer_opacity 0x7f060127
int dimen m3_comp_filled_card_focus_state_layer_opacity 0x7f060128
int dimen m3_comp_filled_card_hover_state_layer_opacity 0x7f060129
int dimen m3_comp_filled_card_icon_size 0x7f06012a
int dimen m3_comp_filled_card_pressed_state_layer_opacity 0x7f06012b
int dimen m3_comp_filled_text_field_disabled_active_indicator_opacity 0x7f06012c
int dimen m3_comp_filter_chip_container_height 0x7f06012d
int dimen m3_comp_filter_chip_elevated_container_elevation 0x7f06012e
int dimen m3_comp_filter_chip_flat_container_elevation 0x7f06012f
int dimen m3_comp_filter_chip_flat_unselected_outline_width 0x7f060130
int dimen m3_comp_filter_chip_with_icon_icon_size 0x7f060131
int dimen m3_comp_input_chip_container_elevation 0x7f060132
int dimen m3_comp_input_chip_container_height 0x7f060133
int dimen m3_comp_input_chip_unselected_outline_width 0x7f060134
int dimen m3_comp_input_chip_with_avatar_avatar_size 0x7f060135
int dimen m3_comp_input_chip_with_leading_icon_leading_icon_size 0x7f060136
int dimen m3_comp_menu_container_elevation 0x7f060137
int dimen m3_comp_navigation_bar_active_indicator_height 0x7f060138
int dimen m3_comp_navigation_bar_active_indicator_width 0x7f060139
int dimen m3_comp_navigation_bar_container_elevation 0x7f06013a
int dimen m3_comp_navigation_bar_container_height 0x7f06013b
int dimen m3_comp_navigation_bar_focus_state_layer_opacity 0x7f06013c
int dimen m3_comp_navigation_bar_hover_state_layer_opacity 0x7f06013d
int dimen m3_comp_navigation_bar_icon_size 0x7f06013e
int dimen m3_comp_navigation_bar_pressed_state_layer_opacity 0x7f06013f
int dimen m3_comp_navigation_drawer_container_width 0x7f060140
int dimen m3_comp_navigation_drawer_focus_state_layer_opacity 0x7f060141
int dimen m3_comp_navigation_drawer_hover_state_layer_opacity 0x7f060142
int dimen m3_comp_navigation_drawer_icon_size 0x7f060143
int dimen m3_comp_navigation_drawer_modal_container_elevation 0x7f060144
int dimen m3_comp_navigation_drawer_pressed_state_layer_opacity 0x7f060145
int dimen m3_comp_navigation_drawer_standard_container_elevation 0x7f060146
int dimen m3_comp_navigation_rail_active_indicator_height 0x7f060147
int dimen m3_comp_navigation_rail_active_indicator_width 0x7f060148
int dimen m3_comp_navigation_rail_container_elevation 0x7f060149
int dimen m3_comp_navigation_rail_container_width 0x7f06014a
int dimen m3_comp_navigation_rail_focus_state_layer_opacity 0x7f06014b
int dimen m3_comp_navigation_rail_hover_state_layer_opacity 0x7f06014c
int dimen m3_comp_navigation_rail_icon_size 0x7f06014d
int dimen m3_comp_navigation_rail_pressed_state_layer_opacity 0x7f06014e
int dimen m3_comp_outlined_autocomplete_menu_container_elevation 0x7f06014f
int dimen m3_comp_outlined_button_disabled_outline_opacity 0x7f060150
int dimen m3_comp_outlined_button_outline_width 0x7f060151
int dimen m3_comp_outlined_card_container_elevation 0x7f060152
int dimen m3_comp_outlined_card_disabled_outline_opacity 0x7f060153
int dimen m3_comp_outlined_card_icon_size 0x7f060154
int dimen m3_comp_outlined_card_outline_width 0x7f060155
int dimen m3_comp_outlined_icon_button_unselected_outline_width 0x7f060156
int dimen m3_comp_outlined_text_field_disabled_input_text_opacity 0x7f060157
int dimen m3_comp_outlined_text_field_disabled_label_text_opacity 0x7f060158
int dimen m3_comp_outlined_text_field_disabled_supporting_text_opacity 0x7f060159
int dimen m3_comp_outlined_text_field_focus_outline_width 0x7f06015a
int dimen m3_comp_outlined_text_field_outline_width 0x7f06015b
int dimen m3_comp_primary_navigation_tab_active_focus_state_layer_opacity 0x7f06015c
int dimen m3_comp_primary_navigation_tab_active_hover_state_layer_opacity 0x7f06015d
int dimen m3_comp_primary_navigation_tab_active_indicator_height 0x7f06015e
int dimen m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity 0x7f06015f
int dimen m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity 0x7f060160
int dimen m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity 0x7f060161
int dimen m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity 0x7f060162
int dimen m3_comp_primary_navigation_tab_with_icon_icon_size 0x7f060163
int dimen m3_comp_progress_indicator_active_indicator_track_space 0x7f060164
int dimen m3_comp_progress_indicator_stop_indicator_size 0x7f060165
int dimen m3_comp_progress_indicator_track_thickness 0x7f060166
int dimen m3_comp_radio_button_disabled_selected_icon_opacity 0x7f060167
int dimen m3_comp_radio_button_disabled_unselected_icon_opacity 0x7f060168
int dimen m3_comp_radio_button_selected_focus_state_layer_opacity 0x7f060169
int dimen m3_comp_radio_button_selected_hover_state_layer_opacity 0x7f06016a
int dimen m3_comp_radio_button_selected_pressed_state_layer_opacity 0x7f06016b
int dimen m3_comp_radio_button_unselected_focus_state_layer_opacity 0x7f06016c
int dimen m3_comp_radio_button_unselected_hover_state_layer_opacity 0x7f06016d
int dimen m3_comp_radio_button_unselected_pressed_state_layer_opacity 0x7f06016e
int dimen m3_comp_scrim_container_opacity 0x7f06016f
int dimen m3_comp_search_bar_avatar_size 0x7f060170
int dimen m3_comp_search_bar_container_elevation 0x7f060171
int dimen m3_comp_search_bar_container_height 0x7f060172
int dimen m3_comp_search_bar_hover_state_layer_opacity 0x7f060173
int dimen m3_comp_search_bar_pressed_state_layer_opacity 0x7f060174
int dimen m3_comp_search_view_container_elevation 0x7f060175
int dimen m3_comp_search_view_docked_header_container_height 0x7f060176
int dimen m3_comp_search_view_full_screen_header_container_height 0x7f060177
int dimen m3_comp_secondary_navigation_tab_active_indicator_height 0x7f060178
int dimen m3_comp_secondary_navigation_tab_focus_state_layer_opacity 0x7f060179
int dimen m3_comp_secondary_navigation_tab_hover_state_layer_opacity 0x7f06017a
int dimen m3_comp_secondary_navigation_tab_pressed_state_layer_opacity 0x7f06017b
int dimen m3_comp_sheet_bottom_docked_drag_handle_height 0x7f06017c
int dimen m3_comp_sheet_bottom_docked_drag_handle_width 0x7f06017d
int dimen m3_comp_sheet_bottom_docked_modal_container_elevation 0x7f06017e
int dimen m3_comp_sheet_bottom_docked_standard_container_elevation 0x7f06017f
int dimen m3_comp_sheet_side_docked_container_width 0x7f060180
int dimen m3_comp_sheet_side_docked_modal_container_elevation 0x7f060181
int dimen m3_comp_sheet_side_docked_standard_container_elevation 0x7f060182
int dimen m3_comp_slider_active_handle_height 0x7f060183
int dimen m3_comp_slider_active_handle_leading_space 0x7f060184
int dimen m3_comp_slider_active_handle_width 0x7f060185
int dimen m3_comp_slider_disabled_active_track_opacity 0x7f060186
int dimen m3_comp_slider_disabled_handle_opacity 0x7f060187
int dimen m3_comp_slider_disabled_inactive_track_opacity 0x7f060188
int dimen m3_comp_slider_inactive_track_height 0x7f060189
int dimen m3_comp_slider_stop_indicator_size 0x7f06018a
int dimen m3_comp_snackbar_container_elevation 0x7f06018b
int dimen m3_comp_suggestion_chip_container_height 0x7f06018c
int dimen m3_comp_suggestion_chip_elevated_container_elevation 0x7f06018d
int dimen m3_comp_suggestion_chip_flat_container_elevation 0x7f06018e
int dimen m3_comp_suggestion_chip_flat_outline_width 0x7f06018f
int dimen m3_comp_suggestion_chip_with_leading_icon_leading_icon_size 0x7f060190
int dimen m3_comp_switch_disabled_selected_handle_opacity 0x7f060191
int dimen m3_comp_switch_disabled_selected_icon_opacity 0x7f060192
int dimen m3_comp_switch_disabled_track_opacity 0x7f060193
int dimen m3_comp_switch_disabled_unselected_handle_opacity 0x7f060194
int dimen m3_comp_switch_disabled_unselected_icon_opacity 0x7f060195
int dimen m3_comp_switch_selected_focus_state_layer_opacity 0x7f060196
int dimen m3_comp_switch_selected_hover_state_layer_opacity 0x7f060197
int dimen m3_comp_switch_selected_pressed_state_layer_opacity 0x7f060198
int dimen m3_comp_switch_track_height 0x7f060199
int dimen m3_comp_switch_track_width 0x7f06019a
int dimen m3_comp_switch_unselected_focus_state_layer_opacity 0x7f06019b
int dimen m3_comp_switch_unselected_hover_state_layer_opacity 0x7f06019c
int dimen m3_comp_switch_unselected_pressed_state_layer_opacity 0x7f06019d
int dimen m3_comp_text_button_focus_state_layer_opacity 0x7f06019e
int dimen m3_comp_text_button_hover_state_layer_opacity 0x7f06019f
int dimen m3_comp_text_button_pressed_state_layer_opacity 0x7f0601a0
int dimen m3_comp_time_input_time_input_field_focus_outline_width 0x7f0601a1
int dimen m3_comp_time_picker_container_elevation 0x7f0601a2
int dimen m3_comp_time_picker_period_selector_focus_state_layer_opacity 0x7f0601a3
int dimen m3_comp_time_picker_period_selector_hover_state_layer_opacity 0x7f0601a4
int dimen m3_comp_time_picker_period_selector_outline_width 0x7f0601a5
int dimen m3_comp_time_picker_period_selector_pressed_state_layer_opacity 0x7f0601a6
int dimen m3_comp_time_picker_time_selector_focus_state_layer_opacity 0x7f0601a7
int dimen m3_comp_time_picker_time_selector_hover_state_layer_opacity 0x7f0601a8
int dimen m3_comp_time_picker_time_selector_pressed_state_layer_opacity 0x7f0601a9
int dimen m3_comp_top_app_bar_large_container_height 0x7f0601aa
int dimen m3_comp_top_app_bar_medium_container_height 0x7f0601ab
int dimen m3_comp_top_app_bar_small_container_elevation 0x7f0601ac
int dimen m3_comp_top_app_bar_small_container_height 0x7f0601ad
int dimen m3_comp_top_app_bar_small_on_scroll_container_elevation 0x7f0601ae
int dimen m3_datepicker_elevation 0x7f0601af
int dimen m3_divider_heavy_thickness 0x7f0601b0
int dimen m3_extended_fab_bottom_padding 0x7f0601b1
int dimen m3_extended_fab_end_padding 0x7f0601b2
int dimen m3_extended_fab_icon_padding 0x7f0601b3
int dimen m3_extended_fab_min_height 0x7f0601b4
int dimen m3_extended_fab_start_padding 0x7f0601b5
int dimen m3_extended_fab_top_padding 0x7f0601b6
int dimen m3_fab_border_width 0x7f0601b7
int dimen m3_fab_corner_size 0x7f0601b8
int dimen m3_fab_translation_z_hovered_focused 0x7f0601b9
int dimen m3_fab_translation_z_pressed 0x7f0601ba
int dimen m3_large_fab_max_image_size 0x7f0601bb
int dimen m3_large_fab_size 0x7f0601bc
int dimen m3_large_text_vertical_offset_adjustment 0x7f0601bd
int dimen m3_menu_elevation 0x7f0601be
int dimen m3_nav_badge_with_text_vertical_offset 0x7f0601bf
int dimen m3_navigation_drawer_layout_corner_size 0x7f0601c0
int dimen m3_navigation_item_active_indicator_label_padding 0x7f0601c1
int dimen m3_navigation_item_horizontal_padding 0x7f0601c2
int dimen m3_navigation_item_icon_padding 0x7f0601c3
int dimen m3_navigation_item_shape_inset_bottom 0x7f0601c4
int dimen m3_navigation_item_shape_inset_end 0x7f0601c5
int dimen m3_navigation_item_shape_inset_start 0x7f0601c6
int dimen m3_navigation_item_shape_inset_top 0x7f0601c7
int dimen m3_navigation_item_vertical_padding 0x7f0601c8
int dimen m3_navigation_menu_divider_horizontal_padding 0x7f0601c9
int dimen m3_navigation_menu_headline_horizontal_padding 0x7f0601ca
int dimen m3_navigation_rail_default_width 0x7f0601cb
int dimen m3_navigation_rail_elevation 0x7f0601cc
int dimen m3_navigation_rail_icon_size 0x7f0601cd
int dimen m3_navigation_rail_item_active_indicator_height 0x7f0601ce
int dimen m3_navigation_rail_item_active_indicator_margin_horizontal 0x7f0601cf
int dimen m3_navigation_rail_item_active_indicator_width 0x7f0601d0
int dimen m3_navigation_rail_item_min_height 0x7f0601d1
int dimen m3_navigation_rail_item_padding_bottom 0x7f0601d2
int dimen m3_navigation_rail_item_padding_bottom_with_large_font 0x7f0601d3
int dimen m3_navigation_rail_item_padding_top 0x7f0601d4
int dimen m3_navigation_rail_item_padding_top_with_large_font 0x7f0601d5
int dimen m3_navigation_rail_label_padding_horizontal 0x7f0601d6
int dimen m3_ripple_default_alpha 0x7f0601d7
int dimen m3_ripple_focused_alpha 0x7f0601d8
int dimen m3_ripple_hovered_alpha 0x7f0601d9
int dimen m3_ripple_pressed_alpha 0x7f0601da
int dimen m3_ripple_selectable_pressed_alpha 0x7f0601db
int dimen m3_searchbar_elevation 0x7f0601dc
int dimen m3_searchbar_height 0x7f0601dd
int dimen m3_searchbar_margin_horizontal 0x7f0601de
int dimen m3_searchbar_margin_vertical 0x7f0601df
int dimen m3_searchbar_outlined_stroke_width 0x7f0601e0
int dimen m3_searchbar_padding_start 0x7f0601e1
int dimen m3_searchbar_text_margin_start_no_navigation_icon 0x7f0601e2
int dimen m3_searchbar_text_size 0x7f0601e3
int dimen m3_searchview_divider_size 0x7f0601e4
int dimen m3_searchview_elevation 0x7f0601e5
int dimen m3_searchview_height 0x7f0601e6
int dimen m3_side_sheet_margin_detached 0x7f0601e7
int dimen m3_side_sheet_modal_elevation 0x7f0601e8
int dimen m3_side_sheet_standard_elevation 0x7f0601e9
int dimen m3_side_sheet_width 0x7f0601ea
int dimen m3_simple_item_color_hovered_alpha 0x7f0601eb
int dimen m3_simple_item_color_selected_alpha 0x7f0601ec
int dimen m3_slider_thumb_elevation 0x7f0601ed
int dimen m3_small_fab_max_image_size 0x7f0601ee
int dimen m3_small_fab_size 0x7f0601ef
int dimen m3_snackbar_action_text_color_alpha 0x7f0601f0
int dimen m3_snackbar_margin 0x7f0601f1
int dimen m3_sys_elevation_level0 0x7f0601f2
int dimen m3_sys_elevation_level1 0x7f0601f3
int dimen m3_sys_elevation_level2 0x7f0601f4
int dimen m3_sys_elevation_level3 0x7f0601f5
int dimen m3_sys_elevation_level4 0x7f0601f6
int dimen m3_sys_elevation_level5 0x7f0601f7
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x1 0x7f0601f8
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x2 0x7f0601f9
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y1 0x7f0601fa
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y2 0x7f0601fb
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x1 0x7f0601fc
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x2 0x7f0601fd
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y1 0x7f0601fe
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y2 0x7f0601ff
int dimen m3_sys_motion_easing_legacy_accelerate_control_x1 0x7f060200
int dimen m3_sys_motion_easing_legacy_accelerate_control_x2 0x7f060201
int dimen m3_sys_motion_easing_legacy_accelerate_control_y1 0x7f060202
int dimen m3_sys_motion_easing_legacy_accelerate_control_y2 0x7f060203
int dimen m3_sys_motion_easing_legacy_control_x1 0x7f060204
int dimen m3_sys_motion_easing_legacy_control_x2 0x7f060205
int dimen m3_sys_motion_easing_legacy_control_y1 0x7f060206
int dimen m3_sys_motion_easing_legacy_control_y2 0x7f060207
int dimen m3_sys_motion_easing_legacy_decelerate_control_x1 0x7f060208
int dimen m3_sys_motion_easing_legacy_decelerate_control_x2 0x7f060209
int dimen m3_sys_motion_easing_legacy_decelerate_control_y1 0x7f06020a
int dimen m3_sys_motion_easing_legacy_decelerate_control_y2 0x7f06020b
int dimen m3_sys_motion_easing_linear_control_x1 0x7f06020c
int dimen m3_sys_motion_easing_linear_control_x2 0x7f06020d
int dimen m3_sys_motion_easing_linear_control_y1 0x7f06020e
int dimen m3_sys_motion_easing_linear_control_y2 0x7f06020f
int dimen m3_sys_motion_easing_standard_accelerate_control_x1 0x7f060210
int dimen m3_sys_motion_easing_standard_accelerate_control_x2 0x7f060211
int dimen m3_sys_motion_easing_standard_accelerate_control_y1 0x7f060212
int dimen m3_sys_motion_easing_standard_accelerate_control_y2 0x7f060213
int dimen m3_sys_motion_easing_standard_control_x1 0x7f060214
int dimen m3_sys_motion_easing_standard_control_x2 0x7f060215
int dimen m3_sys_motion_easing_standard_control_y1 0x7f060216
int dimen m3_sys_motion_easing_standard_control_y2 0x7f060217
int dimen m3_sys_motion_easing_standard_decelerate_control_x1 0x7f060218
int dimen m3_sys_motion_easing_standard_decelerate_control_x2 0x7f060219
int dimen m3_sys_motion_easing_standard_decelerate_control_y1 0x7f06021a
int dimen m3_sys_motion_easing_standard_decelerate_control_y2 0x7f06021b
int dimen m3_sys_state_dragged_state_layer_opacity 0x7f06021c
int dimen m3_sys_state_focus_state_layer_opacity 0x7f06021d
int dimen m3_sys_state_hover_state_layer_opacity 0x7f06021e
int dimen m3_sys_state_pressed_state_layer_opacity 0x7f06021f
int dimen m3_timepicker_display_stroke_width 0x7f060220
int dimen m3_timepicker_window_elevation 0x7f060221
int dimen m3_toolbar_text_size_title 0x7f060222
int dimen material_bottom_sheet_max_width 0x7f060223
int dimen material_clock_display_height 0x7f060224
int dimen material_clock_display_padding 0x7f060225
int dimen material_clock_display_width 0x7f060226
int dimen material_clock_face_margin_bottom 0x7f060227
int dimen material_clock_face_margin_top 0x7f060228
int dimen material_clock_hand_center_dot_radius 0x7f060229
int dimen material_clock_hand_padding 0x7f06022a
int dimen material_clock_hand_stroke_width 0x7f06022b
int dimen material_clock_number_text_size 0x7f06022c
int dimen material_clock_period_toggle_height 0x7f06022d
int dimen material_clock_period_toggle_horizontal_gap 0x7f06022e
int dimen material_clock_period_toggle_vertical_gap 0x7f06022f
int dimen material_clock_period_toggle_width 0x7f060230
int dimen material_clock_size 0x7f060231
int dimen material_cursor_inset 0x7f060232
int dimen material_cursor_width 0x7f060233
int dimen material_divider_thickness 0x7f060234
int dimen material_emphasis_disabled 0x7f060235
int dimen material_emphasis_disabled_background 0x7f060236
int dimen material_emphasis_high_type 0x7f060237
int dimen material_emphasis_medium 0x7f060238
int dimen material_filled_edittext_font_1_3_padding_bottom 0x7f060239
int dimen material_filled_edittext_font_1_3_padding_top 0x7f06023a
int dimen material_filled_edittext_font_2_0_padding_bottom 0x7f06023b
int dimen material_filled_edittext_font_2_0_padding_top 0x7f06023c
int dimen material_font_1_3_box_collapsed_padding_top 0x7f06023d
int dimen material_font_2_0_box_collapsed_padding_top 0x7f06023e
int dimen material_helper_text_default_padding_top 0x7f06023f
int dimen material_helper_text_font_1_3_padding_horizontal 0x7f060240
int dimen material_helper_text_font_1_3_padding_top 0x7f060241
int dimen material_input_text_to_prefix_suffix_padding 0x7f060242
int dimen material_textinput_default_width 0x7f060243
int dimen material_textinput_max_width 0x7f060244
int dimen material_textinput_min_width 0x7f060245
int dimen material_time_picker_minimum_screen_height 0x7f060246
int dimen material_time_picker_minimum_screen_width 0x7f060247
int dimen mtrl_alert_dialog_background_inset_bottom 0x7f060248
int dimen mtrl_alert_dialog_background_inset_end 0x7f060249
int dimen mtrl_alert_dialog_background_inset_start 0x7f06024a
int dimen mtrl_alert_dialog_background_inset_top 0x7f06024b
int dimen mtrl_alert_dialog_picker_background_inset 0x7f06024c
int dimen mtrl_badge_horizontal_edge_offset 0x7f06024d
int dimen mtrl_badge_long_text_horizontal_padding 0x7f06024e
int dimen mtrl_badge_size 0x7f06024f
int dimen mtrl_badge_text_horizontal_edge_offset 0x7f060250
int dimen mtrl_badge_text_size 0x7f060251
int dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset 0x7f060252
int dimen mtrl_badge_toolbar_action_menu_item_vertical_offset 0x7f060253
int dimen mtrl_badge_with_text_size 0x7f060254
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f060255
int dimen mtrl_bottomappbar_fab_bottom_margin 0x7f060256
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f060257
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f060258
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f060259
int dimen mtrl_bottomappbar_height 0x7f06025a
int dimen mtrl_btn_corner_radius 0x7f06025b
int dimen mtrl_btn_dialog_btn_min_width 0x7f06025c
int dimen mtrl_btn_disabled_elevation 0x7f06025d
int dimen mtrl_btn_disabled_z 0x7f06025e
int dimen mtrl_btn_elevation 0x7f06025f
int dimen mtrl_btn_focused_z 0x7f060260
int dimen mtrl_btn_hovered_z 0x7f060261
int dimen mtrl_btn_icon_btn_padding_left 0x7f060262
int dimen mtrl_btn_icon_padding 0x7f060263
int dimen mtrl_btn_inset 0x7f060264
int dimen mtrl_btn_letter_spacing 0x7f060265
int dimen mtrl_btn_max_width 0x7f060266
int dimen mtrl_btn_padding_bottom 0x7f060267
int dimen mtrl_btn_padding_left 0x7f060268
int dimen mtrl_btn_padding_right 0x7f060269
int dimen mtrl_btn_padding_top 0x7f06026a
int dimen mtrl_btn_pressed_z 0x7f06026b
int dimen mtrl_btn_snackbar_margin_horizontal 0x7f06026c
int dimen mtrl_btn_stroke_size 0x7f06026d
int dimen mtrl_btn_text_btn_icon_padding 0x7f06026e
int dimen mtrl_btn_text_btn_padding_left 0x7f06026f
int dimen mtrl_btn_text_btn_padding_right 0x7f060270
int dimen mtrl_btn_text_size 0x7f060271
int dimen mtrl_btn_z 0x7f060272
int dimen mtrl_calendar_action_confirm_button_min_width 0x7f060273
int dimen mtrl_calendar_action_height 0x7f060274
int dimen mtrl_calendar_action_padding 0x7f060275
int dimen mtrl_calendar_bottom_padding 0x7f060276
int dimen mtrl_calendar_content_padding 0x7f060277
int dimen mtrl_calendar_day_corner 0x7f060278
int dimen mtrl_calendar_day_height 0x7f060279
int dimen mtrl_calendar_day_horizontal_padding 0x7f06027a
int dimen mtrl_calendar_day_today_stroke 0x7f06027b
int dimen mtrl_calendar_day_vertical_padding 0x7f06027c
int dimen mtrl_calendar_day_width 0x7f06027d
int dimen mtrl_calendar_days_of_week_height 0x7f06027e
int dimen mtrl_calendar_dialog_background_inset 0x7f06027f
int dimen mtrl_calendar_header_content_padding 0x7f060280
int dimen mtrl_calendar_header_content_padding_fullscreen 0x7f060281
int dimen mtrl_calendar_header_divider_thickness 0x7f060282
int dimen mtrl_calendar_header_height 0x7f060283
int dimen mtrl_calendar_header_height_fullscreen 0x7f060284
int dimen mtrl_calendar_header_selection_line_height 0x7f060285
int dimen mtrl_calendar_header_text_padding 0x7f060286
int dimen mtrl_calendar_header_toggle_margin_bottom 0x7f060287
int dimen mtrl_calendar_header_toggle_margin_top 0x7f060288
int dimen mtrl_calendar_landscape_header_width 0x7f060289
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x7f06028a
int dimen mtrl_calendar_month_horizontal_padding 0x7f06028b
int dimen mtrl_calendar_month_vertical_padding 0x7f06028c
int dimen mtrl_calendar_navigation_bottom_padding 0x7f06028d
int dimen mtrl_calendar_navigation_height 0x7f06028e
int dimen mtrl_calendar_navigation_top_padding 0x7f06028f
int dimen mtrl_calendar_pre_l_text_clip_padding 0x7f060290
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x7f060291
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x7f060292
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x7f060293
int dimen mtrl_calendar_selection_text_baseline_to_top 0x7f060294
int dimen mtrl_calendar_text_input_padding_top 0x7f060295
int dimen mtrl_calendar_title_baseline_to_top 0x7f060296
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x7f060297
int dimen mtrl_calendar_year_corner 0x7f060298
int dimen mtrl_calendar_year_height 0x7f060299
int dimen mtrl_calendar_year_horizontal_padding 0x7f06029a
int dimen mtrl_calendar_year_vertical_padding 0x7f06029b
int dimen mtrl_calendar_year_width 0x7f06029c
int dimen mtrl_card_checked_icon_margin 0x7f06029d
int dimen mtrl_card_checked_icon_size 0x7f06029e
int dimen mtrl_card_corner_radius 0x7f06029f
int dimen mtrl_card_dragged_z 0x7f0602a0
int dimen mtrl_card_elevation 0x7f0602a1
int dimen mtrl_card_spacing 0x7f0602a2
int dimen mtrl_chip_pressed_translation_z 0x7f0602a3
int dimen mtrl_chip_text_size 0x7f0602a4
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x7f0602a5
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x7f0602a6
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x7f0602a7
int dimen mtrl_extended_fab_bottom_padding 0x7f0602a8
int dimen mtrl_extended_fab_disabled_elevation 0x7f0602a9
int dimen mtrl_extended_fab_disabled_translation_z 0x7f0602aa
int dimen mtrl_extended_fab_elevation 0x7f0602ab
int dimen mtrl_extended_fab_end_padding 0x7f0602ac
int dimen mtrl_extended_fab_end_padding_icon 0x7f0602ad
int dimen mtrl_extended_fab_icon_size 0x7f0602ae
int dimen mtrl_extended_fab_icon_text_spacing 0x7f0602af
int dimen mtrl_extended_fab_min_height 0x7f0602b0
int dimen mtrl_extended_fab_min_width 0x7f0602b1
int dimen mtrl_extended_fab_start_padding 0x7f0602b2
int dimen mtrl_extended_fab_start_padding_icon 0x7f0602b3
int dimen mtrl_extended_fab_top_padding 0x7f0602b4
int dimen mtrl_extended_fab_translation_z_base 0x7f0602b5
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x7f0602b6
int dimen mtrl_extended_fab_translation_z_pressed 0x7f0602b7
int dimen mtrl_fab_elevation 0x7f0602b8
int dimen mtrl_fab_min_touch_target 0x7f0602b9
int dimen mtrl_fab_translation_z_hovered_focused 0x7f0602ba
int dimen mtrl_fab_translation_z_pressed 0x7f0602bb
int dimen mtrl_high_ripple_default_alpha 0x7f0602bc
int dimen mtrl_high_ripple_focused_alpha 0x7f0602bd
int dimen mtrl_high_ripple_hovered_alpha 0x7f0602be
int dimen mtrl_high_ripple_pressed_alpha 0x7f0602bf
int dimen mtrl_low_ripple_default_alpha 0x7f0602c0
int dimen mtrl_low_ripple_focused_alpha 0x7f0602c1
int dimen mtrl_low_ripple_hovered_alpha 0x7f0602c2
int dimen mtrl_low_ripple_pressed_alpha 0x7f0602c3
int dimen mtrl_min_touch_target_size 0x7f0602c4
int dimen mtrl_navigation_bar_item_default_icon_size 0x7f0602c5
int dimen mtrl_navigation_bar_item_default_margin 0x7f0602c6
int dimen mtrl_navigation_elevation 0x7f0602c7
int dimen mtrl_navigation_item_horizontal_padding 0x7f0602c8
int dimen mtrl_navigation_item_icon_padding 0x7f0602c9
int dimen mtrl_navigation_item_icon_size 0x7f0602ca
int dimen mtrl_navigation_item_shape_horizontal_margin 0x7f0602cb
int dimen mtrl_navigation_item_shape_vertical_margin 0x7f0602cc
int dimen mtrl_navigation_rail_active_text_size 0x7f0602cd
int dimen mtrl_navigation_rail_compact_width 0x7f0602ce
int dimen mtrl_navigation_rail_default_width 0x7f0602cf
int dimen mtrl_navigation_rail_elevation 0x7f0602d0
int dimen mtrl_navigation_rail_icon_margin 0x7f0602d1
int dimen mtrl_navigation_rail_icon_size 0x7f0602d2
int dimen mtrl_navigation_rail_margin 0x7f0602d3
int dimen mtrl_navigation_rail_text_bottom_margin 0x7f0602d4
int dimen mtrl_navigation_rail_text_size 0x7f0602d5
int dimen mtrl_progress_circular_inset 0x7f0602d6
int dimen mtrl_progress_circular_inset_extra_small 0x7f0602d7
int dimen mtrl_progress_circular_inset_medium 0x7f0602d8
int dimen mtrl_progress_circular_inset_small 0x7f0602d9
int dimen mtrl_progress_circular_radius 0x7f0602da
int dimen mtrl_progress_circular_size 0x7f0602db
int dimen mtrl_progress_circular_size_extra_small 0x7f0602dc
int dimen mtrl_progress_circular_size_medium 0x7f0602dd
int dimen mtrl_progress_circular_size_small 0x7f0602de
int dimen mtrl_progress_circular_track_thickness_extra_small 0x7f0602df
int dimen mtrl_progress_circular_track_thickness_medium 0x7f0602e0
int dimen mtrl_progress_circular_track_thickness_small 0x7f0602e1
int dimen mtrl_progress_indicator_full_rounded_corner_radius 0x7f0602e2
int dimen mtrl_progress_track_thickness 0x7f0602e3
int dimen mtrl_shape_corner_size_large_component 0x7f0602e4
int dimen mtrl_shape_corner_size_medium_component 0x7f0602e5
int dimen mtrl_shape_corner_size_small_component 0x7f0602e6
int dimen mtrl_slider_halo_radius 0x7f0602e7
int dimen mtrl_slider_label_padding 0x7f0602e8
int dimen mtrl_slider_label_radius 0x7f0602e9
int dimen mtrl_slider_label_square_side 0x7f0602ea
int dimen mtrl_slider_thumb_elevation 0x7f0602eb
int dimen mtrl_slider_thumb_radius 0x7f0602ec
int dimen mtrl_slider_tick_min_spacing 0x7f0602ed
int dimen mtrl_slider_tick_radius 0x7f0602ee
int dimen mtrl_slider_track_height 0x7f0602ef
int dimen mtrl_slider_track_side_padding 0x7f0602f0
int dimen mtrl_slider_widget_height 0x7f0602f1
int dimen mtrl_snackbar_action_text_color_alpha 0x7f0602f2
int dimen mtrl_snackbar_background_corner_radius 0x7f0602f3
int dimen mtrl_snackbar_background_overlay_color_alpha 0x7f0602f4
int dimen mtrl_snackbar_margin 0x7f0602f5
int dimen mtrl_snackbar_message_margin_horizontal 0x7f0602f6
int dimen mtrl_snackbar_padding_horizontal 0x7f0602f7
int dimen mtrl_switch_text_padding 0x7f0602f8
int dimen mtrl_switch_thumb_elevation 0x7f0602f9
int dimen mtrl_switch_thumb_icon_size 0x7f0602fa
int dimen mtrl_switch_thumb_size 0x7f0602fb
int dimen mtrl_switch_track_height 0x7f0602fc
int dimen mtrl_switch_track_width 0x7f0602fd
int dimen mtrl_textinput_box_corner_radius_medium 0x7f0602fe
int dimen mtrl_textinput_box_corner_radius_small 0x7f0602ff
int dimen mtrl_textinput_box_label_cutout_padding 0x7f060300
int dimen mtrl_textinput_box_stroke_width_default 0x7f060301
int dimen mtrl_textinput_box_stroke_width_focused 0x7f060302
int dimen mtrl_textinput_counter_margin_start 0x7f060303
int dimen mtrl_textinput_end_icon_margin_start 0x7f060304
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f060305
int dimen mtrl_textinput_start_icon_margin_end 0x7f060306
int dimen mtrl_toolbar_default_height 0x7f060307
int dimen mtrl_tooltip_arrowSize 0x7f060308
int dimen mtrl_tooltip_cornerSize 0x7f060309
int dimen mtrl_tooltip_minHeight 0x7f06030a
int dimen mtrl_tooltip_minWidth 0x7f06030b
int dimen mtrl_tooltip_padding 0x7f06030c
int dimen mtrl_transition_shared_axis_slide_distance 0x7f06030d
int dimen notification_action_icon_size 0x7f06030e
int dimen notification_action_text_size 0x7f06030f
int dimen notification_big_circle_margin 0x7f060310
int dimen notification_content_margin_start 0x7f060311
int dimen notification_large_icon_height 0x7f060312
int dimen notification_large_icon_width 0x7f060313
int dimen notification_main_column_padding_top 0x7f060314
int dimen notification_media_narrow_margin 0x7f060315
int dimen notification_right_icon_size 0x7f060316
int dimen notification_right_side_padding_top 0x7f060317
int dimen notification_small_icon_background_padding 0x7f060318
int dimen notification_small_icon_size_as_large 0x7f060319
int dimen notification_subtext_size 0x7f06031a
int dimen notification_top_pad 0x7f06031b
int dimen notification_top_pad_large_text 0x7f06031c
int dimen sliding_pane_detail_pane_width 0x7f06031d
int dimen tooltip_corner_radius 0x7f06031e
int dimen tooltip_horizontal_padding 0x7f06031f
int dimen tooltip_margin 0x7f060320
int dimen tooltip_precise_anchor_extra_offset 0x7f060321
int dimen tooltip_precise_anchor_threshold 0x7f060322
int dimen tooltip_vertical_padding 0x7f060323
int dimen tooltip_y_offset_non_touch 0x7f060324
int dimen tooltip_y_offset_touch 0x7f060325
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070000
int drawable abc_action_bar_item_background_material 0x7f070001
int drawable abc_btn_borderless_material 0x7f070002
int drawable abc_btn_check_material 0x7f070003
int drawable abc_btn_check_material_anim 0x7f070004
int drawable abc_btn_check_to_on_mtrl_000 0x7f070005
int drawable abc_btn_check_to_on_mtrl_015 0x7f070006
int drawable abc_btn_colored_material 0x7f070007
int drawable abc_btn_default_mtrl_shape 0x7f070008
int drawable abc_btn_radio_material 0x7f070009
int drawable abc_btn_radio_material_anim 0x7f07000a
int drawable abc_btn_radio_to_on_mtrl_000 0x7f07000b
int drawable abc_btn_radio_to_on_mtrl_015 0x7f07000c
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f07000d
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f07000e
int drawable abc_cab_background_internal_bg 0x7f07000f
int drawable abc_cab_background_top_material 0x7f070010
int drawable abc_cab_background_top_mtrl_alpha 0x7f070011
int drawable abc_control_background_material 0x7f070012
int drawable abc_dialog_material_background 0x7f070013
int drawable abc_edit_text_material 0x7f070014
int drawable abc_ic_ab_back_material 0x7f070015
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f070016
int drawable abc_ic_clear_material 0x7f070017
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f070018
int drawable abc_ic_go_search_api_material 0x7f070019
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f07001a
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f07001b
int drawable abc_ic_menu_overflow_material 0x7f07001c
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f07001d
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f07001e
int drawable abc_ic_menu_share_mtrl_alpha 0x7f07001f
int drawable abc_ic_search_api_material 0x7f070020
int drawable abc_ic_voice_search_api_material 0x7f070021
int drawable abc_item_background_holo_dark 0x7f070022
int drawable abc_item_background_holo_light 0x7f070023
int drawable abc_list_divider_material 0x7f070024
int drawable abc_list_divider_mtrl_alpha 0x7f070025
int drawable abc_list_focused_holo 0x7f070026
int drawable abc_list_longpressed_holo 0x7f070027
int drawable abc_list_pressed_holo_dark 0x7f070028
int drawable abc_list_pressed_holo_light 0x7f070029
int drawable abc_list_selector_background_transition_holo_dark 0x7f07002a
int drawable abc_list_selector_background_transition_holo_light 0x7f07002b
int drawable abc_list_selector_disabled_holo_dark 0x7f07002c
int drawable abc_list_selector_disabled_holo_light 0x7f07002d
int drawable abc_list_selector_holo_dark 0x7f07002e
int drawable abc_list_selector_holo_light 0x7f07002f
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f070030
int drawable abc_popup_background_mtrl_mult 0x7f070031
int drawable abc_ratingbar_indicator_material 0x7f070032
int drawable abc_ratingbar_material 0x7f070033
int drawable abc_ratingbar_small_material 0x7f070034
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f070035
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f070036
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f070037
int drawable abc_scrubber_primary_mtrl_alpha 0x7f070038
int drawable abc_scrubber_track_mtrl_alpha 0x7f070039
int drawable abc_seekbar_thumb_material 0x7f07003a
int drawable abc_seekbar_tick_mark_material 0x7f07003b
int drawable abc_seekbar_track_material 0x7f07003c
int drawable abc_spinner_mtrl_am_alpha 0x7f07003d
int drawable abc_spinner_textfield_background_material 0x7f07003e
int drawable abc_star_black_48dp 0x7f07003f
int drawable abc_star_half_black_48dp 0x7f070040
int drawable abc_switch_thumb_material 0x7f070041
int drawable abc_switch_track_mtrl_alpha 0x7f070042
int drawable abc_tab_indicator_material 0x7f070043
int drawable abc_tab_indicator_mtrl_alpha 0x7f070044
int drawable abc_text_cursor_material 0x7f070045
int drawable abc_text_select_handle_left_mtrl 0x7f070046
int drawable abc_text_select_handle_middle_mtrl 0x7f070047
int drawable abc_text_select_handle_right_mtrl 0x7f070048
int drawable abc_textfield_activated_mtrl_alpha 0x7f070049
int drawable abc_textfield_default_mtrl_alpha 0x7f07004a
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f07004b
int drawable abc_textfield_search_default_mtrl_alpha 0x7f07004c
int drawable abc_textfield_search_material 0x7f07004d
int drawable abc_vector_test 0x7f07004e
int drawable avd_hide_password 0x7f07004f
int drawable avd_show_password 0x7f070050
int drawable btn_checkbox_checked_mtrl 0x7f070051
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f070052
int drawable btn_checkbox_unchecked_mtrl 0x7f070053
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f070054
int drawable btn_radio_off_mtrl 0x7f070055
int drawable btn_radio_off_to_on_mtrl_animation 0x7f070056
int drawable btn_radio_on_mtrl 0x7f070057
int drawable btn_radio_on_to_off_mtrl_animation 0x7f070058
int drawable design_fab_background 0x7f070059
int drawable design_ic_visibility 0x7f07005a
int drawable design_ic_visibility_off 0x7f07005b
int drawable design_password_eye 0x7f07005c
int drawable design_snackbar_background 0x7f07005d
int drawable dotnet_bot 0x7f07005e
int drawable ic_arrow_back_black_24 0x7f07005f
int drawable ic_call_answer 0x7f070060
int drawable ic_call_answer_low 0x7f070061
int drawable ic_call_answer_video 0x7f070062
int drawable ic_call_answer_video_low 0x7f070063
int drawable ic_call_decline 0x7f070064
int drawable ic_call_decline_low 0x7f070065
int drawable ic_clear_black_24 0x7f070066
int drawable ic_clock_black_24dp 0x7f070067
int drawable ic_keyboard_black_24dp 0x7f070068
int drawable ic_m3_chip_check 0x7f070069
int drawable ic_m3_chip_checked_circle 0x7f07006a
int drawable ic_m3_chip_close 0x7f07006b
int drawable ic_mtrl_checked_circle 0x7f07006c
int drawable ic_mtrl_chip_checked_black 0x7f07006d
int drawable ic_mtrl_chip_checked_circle 0x7f07006e
int drawable ic_mtrl_chip_close_circle 0x7f07006f
int drawable ic_search_black_24 0x7f070070
int drawable indeterminate_static 0x7f070071
int drawable m3_avd_hide_password 0x7f070072
int drawable m3_avd_show_password 0x7f070073
int drawable m3_bottom_sheet_drag_handle 0x7f070074
int drawable m3_password_eye 0x7f070075
int drawable m3_popupmenu_background_overlay 0x7f070076
int drawable m3_radiobutton_ripple 0x7f070077
int drawable m3_selection_control_ripple 0x7f070078
int drawable m3_tabs_background 0x7f070079
int drawable m3_tabs_line_indicator 0x7f07007a
int drawable m3_tabs_rounded_line_indicator 0x7f07007b
int drawable m3_tabs_transparent_background 0x7f07007c
int drawable material_cursor_drawable 0x7f07007d
int drawable material_ic_calendar_black_24dp 0x7f07007e
int drawable material_ic_clear_black_24dp 0x7f07007f
int drawable material_ic_edit_black_24dp 0x7f070080
int drawable material_ic_keyboard_arrow_left_black_24dp 0x7f070081
int drawable material_ic_keyboard_arrow_next_black_24dp 0x7f070082
int drawable material_ic_keyboard_arrow_previous_black_24dp 0x7f070083
int drawable material_ic_keyboard_arrow_right_black_24dp 0x7f070084
int drawable material_ic_menu_arrow_down_black_24dp 0x7f070085
int drawable material_ic_menu_arrow_up_black_24dp 0x7f070086
int drawable maui_splash 0x7f070087
int drawable maui_splash_image 0x7f070088
int drawable mtrl_bottomsheet_drag_handle 0x7f070089
int drawable mtrl_checkbox_button 0x7f07008a
int drawable mtrl_checkbox_button_checked_unchecked 0x7f07008b
int drawable mtrl_checkbox_button_icon 0x7f07008c
int drawable mtrl_checkbox_button_icon_checked_indeterminate 0x7f07008d
int drawable mtrl_checkbox_button_icon_checked_unchecked 0x7f07008e
int drawable mtrl_checkbox_button_icon_indeterminate_checked 0x7f07008f
int drawable mtrl_checkbox_button_icon_indeterminate_unchecked 0x7f070090
int drawable mtrl_checkbox_button_icon_unchecked_checked 0x7f070091
int drawable mtrl_checkbox_button_icon_unchecked_indeterminate 0x7f070092
int drawable mtrl_checkbox_button_unchecked_checked 0x7f070093
int drawable mtrl_dialog_background 0x7f070094
int drawable mtrl_dropdown_arrow 0x7f070095
int drawable mtrl_ic_arrow_drop_down 0x7f070096
int drawable mtrl_ic_arrow_drop_up 0x7f070097
int drawable mtrl_ic_cancel 0x7f070098
int drawable mtrl_ic_check_mark 0x7f070099
int drawable mtrl_ic_checkbox_checked 0x7f07009a
int drawable mtrl_ic_checkbox_unchecked 0x7f07009b
int drawable mtrl_ic_error 0x7f07009c
int drawable mtrl_ic_indeterminate 0x7f07009d
int drawable mtrl_navigation_bar_item_background 0x7f07009e
int drawable mtrl_popupmenu_background 0x7f07009f
int drawable mtrl_popupmenu_background_overlay 0x7f0700a0
int drawable mtrl_switch_thumb 0x7f0700a1
int drawable mtrl_switch_thumb_checked 0x7f0700a2
int drawable mtrl_switch_thumb_checked_pressed 0x7f0700a3
int drawable mtrl_switch_thumb_checked_unchecked 0x7f0700a4
int drawable mtrl_switch_thumb_pressed 0x7f0700a5
int drawable mtrl_switch_thumb_pressed_checked 0x7f0700a6
int drawable mtrl_switch_thumb_pressed_unchecked 0x7f0700a7
int drawable mtrl_switch_thumb_unchecked 0x7f0700a8
int drawable mtrl_switch_thumb_unchecked_checked 0x7f0700a9
int drawable mtrl_switch_thumb_unchecked_pressed 0x7f0700aa
int drawable mtrl_switch_track 0x7f0700ab
int drawable mtrl_switch_track_decoration 0x7f0700ac
int drawable mtrl_tabs_default_indicator 0x7f0700ad
int drawable navigation_empty_icon 0x7f0700ae
int drawable notification_action_background 0x7f0700af
int drawable notification_bg 0x7f0700b0
int drawable notification_bg_low 0x7f0700b1
int drawable notification_bg_low_normal 0x7f0700b2
int drawable notification_bg_low_pressed 0x7f0700b3
int drawable notification_bg_normal 0x7f0700b4
int drawable notification_bg_normal_pressed 0x7f0700b5
int drawable notification_icon_background 0x7f0700b6
int drawable notification_oversize_large_icon_bg 0x7f0700b7
int drawable notification_template_icon_bg 0x7f0700b8
int drawable notification_template_icon_low_bg 0x7f0700b9
int drawable notification_tile_bg 0x7f0700ba
int drawable notify_panel_notification_icon_bg 0x7f0700bb
int drawable splash 0x7f0700bc
int drawable test_level_drawable 0x7f0700bd
int drawable tooltip_frame_dark 0x7f0700be
int drawable tooltip_frame_light 0x7f0700bf
int id above 0x7f080000
int id accelerate 0x7f080001
int id accessibility_action_clickable_span 0x7f080002
int id accessibility_custom_action_0 0x7f080003
int id accessibility_custom_action_1 0x7f080004
int id accessibility_custom_action_10 0x7f080005
int id accessibility_custom_action_11 0x7f080006
int id accessibility_custom_action_12 0x7f080007
int id accessibility_custom_action_13 0x7f080008
int id accessibility_custom_action_14 0x7f080009
int id accessibility_custom_action_15 0x7f08000a
int id accessibility_custom_action_16 0x7f08000b
int id accessibility_custom_action_17 0x7f08000c
int id accessibility_custom_action_18 0x7f08000d
int id accessibility_custom_action_19 0x7f08000e
int id accessibility_custom_action_2 0x7f08000f
int id accessibility_custom_action_20 0x7f080010
int id accessibility_custom_action_21 0x7f080011
int id accessibility_custom_action_22 0x7f080012
int id accessibility_custom_action_23 0x7f080013
int id accessibility_custom_action_24 0x7f080014
int id accessibility_custom_action_25 0x7f080015
int id accessibility_custom_action_26 0x7f080016
int id accessibility_custom_action_27 0x7f080017
int id accessibility_custom_action_28 0x7f080018
int id accessibility_custom_action_29 0x7f080019
int id accessibility_custom_action_3 0x7f08001a
int id accessibility_custom_action_30 0x7f08001b
int id accessibility_custom_action_31 0x7f08001c
int id accessibility_custom_action_4 0x7f08001d
int id accessibility_custom_action_5 0x7f08001e
int id accessibility_custom_action_6 0x7f08001f
int id accessibility_custom_action_7 0x7f080020
int id accessibility_custom_action_8 0x7f080021
int id accessibility_custom_action_9 0x7f080022
int id actionDown 0x7f080023
int id actionDownUp 0x7f080024
int id actionUp 0x7f080025
int id action_bar 0x7f080026
int id action_bar_activity_content 0x7f080027
int id action_bar_container 0x7f080028
int id action_bar_root 0x7f080029
int id action_bar_spinner 0x7f08002a
int id action_bar_subtitle 0x7f08002b
int id action_bar_title 0x7f08002c
int id action_container 0x7f08002d
int id action_context_bar 0x7f08002e
int id action_divider 0x7f08002f
int id action_image 0x7f080030
int id action_menu_divider 0x7f080031
int id action_menu_presenter 0x7f080032
int id action_mode_bar 0x7f080033
int id action_mode_bar_stub 0x7f080034
int id action_mode_close_button 0x7f080035
int id action_text 0x7f080036
int id actions 0x7f080037
int id activity_chooser_view_content 0x7f080038
int id adjacent 0x7f080039
int id alertTitle 0x7f08003a
int id aligned 0x7f08003b
int id allStates 0x7f08003c
int id always 0x7f08003d
int id alwaysAllow 0x7f08003e
int id alwaysDisallow 0x7f08003f
int id androidx_window_activity_scope 0x7f080040
int id antiClockwise 0x7f080041
int id arc 0x7f080042
int id asConfigured 0x7f080043
int id background 0x7f080044
int id barrier 0x7f080045
int id baseline 0x7f080046
int id below 0x7f080047
int id bestChoice 0x7f080048
int id bottom 0x7f080049
int id bottomToTop 0x7f08004a
int id bounce 0x7f08004b
int id browser_actions_header_text 0x7f08004c
int id browser_actions_menu_item_icon 0x7f08004d
int id browser_actions_menu_item_text 0x7f08004e
int id browser_actions_menu_items 0x7f08004f
int id browser_actions_menu_view 0x7f080050
int id button1 0x7f080051
int id button2 0x7f080052
int id button3 0x7f080053
int id buttonPanel 0x7f080054
int id cache_measures 0x7f080055
int id callMeasure 0x7f080056
int id cancel_button 0x7f080057
int id center 0x7f080058
int id chain 0x7f080059
int id chain2 0x7f08005a
int id chains 0x7f08005b
int id checkbox 0x7f08005c
int id checked 0x7f08005d
int id chronometer 0x7f08005e
int id circle_center 0x7f08005f
int id clockwise 0x7f080060
int id closest 0x7f080061
int id confirm_button 0x7f080062
int id constraint 0x7f080063
int id container 0x7f080064
int id content 0x7f080065
int id contentPanel 0x7f080066
int id coordinator 0x7f080067
int id cos 0x7f080068
int id currentState 0x7f080069
int id custom 0x7f08006a
int id customPanel 0x7f08006b
int id cut 0x7f08006c
int id date_picker_actions 0x7f08006d
int id decelerate 0x7f08006e
int id decor_content_parent 0x7f08006f
int id default_activity_button 0x7f080070
int id deltaRelative 0x7f080071
int id dependency_ordering 0x7f080072
int id design_bottom_sheet 0x7f080073
int id design_menu_item_action_area 0x7f080074
int id design_menu_item_action_area_stub 0x7f080075
int id design_menu_item_text 0x7f080076
int id design_navigation_view 0x7f080077
int id dialog_button 0x7f080078
int id dimensions 0x7f080079
int id direct 0x7f08007a
int id dragAnticlockwise 0x7f08007b
int id dragClockwise 0x7f08007c
int id dragDown 0x7f08007d
int id dragEnd 0x7f08007e
int id dragLeft 0x7f08007f
int id dragRight 0x7f080080
int id dragStart 0x7f080081
int id dragUp 0x7f080082
int id easeIn 0x7f080083
int id easeInOut 0x7f080084
int id easeOut 0x7f080085
int id edit_query 0x7f080086
int id edit_text_id 0x7f080087
int id end 0x7f080088
int id expand_activities_button 0x7f080089
int id expanded_menu 0x7f08008a
int id flip 0x7f08008b
int id flyoutcontent_appbar 0x7f08008c
int id fragment_container_view_tag 0x7f08008d
int id fullscreen_header 0x7f08008e
int id ghost_view 0x7f08008f
int id ghost_view_holder 0x7f080090
int id glide_custom_view_target_tag 0x7f080091
int id gone 0x7f080092
int id graph 0x7f080093
int id graph_wrap 0x7f080094
int id group_divider 0x7f080095
int id grouping 0x7f080096
int id groups 0x7f080097
int id header_title 0x7f080098
int id hide_ime_id 0x7f080099
int id home 0x7f08009a
int id honorRequest 0x7f08009b
int id horizontal_only 0x7f08009c
int id icon 0x7f08009d
int id icon1 0x7f08009e
int id icon_group 0x7f08009f
int id ignore 0x7f0800a0
int id ignoreRequest 0x7f0800a1
int id image 0x7f0800a2
int id included 0x7f0800a3
int id indeterminate 0x7f0800a4
int id info 0x7f0800a5
int id invisible 0x7f0800a6
int id is_pooling_container_tag 0x7f0800a7
int id item_touch_helper_previous_elevation 0x7f0800a8
int id left 0x7f0800a9
int id legacy 0x7f0800aa
int id line1 0x7f0800ab
int id line3 0x7f0800ac
int id linear 0x7f0800ad
int id list_item 0x7f0800ae
int id locale 0x7f0800af
int id ltr 0x7f0800b0
int id m3_side_sheet 0x7f0800b1
int id mask 0x7f0800b2
int id masked 0x7f0800b3
int id match_constraint 0x7f0800b4
int id match_parent 0x7f0800b5
int id material_clock_display 0x7f0800b6
int id material_clock_display_and_toggle 0x7f0800b7
int id material_clock_face 0x7f0800b8
int id material_clock_hand 0x7f0800b9
int id material_clock_level 0x7f0800ba
int id material_clock_period_am_button 0x7f0800bb
int id material_clock_period_pm_button 0x7f0800bc
int id material_clock_period_toggle 0x7f0800bd
int id material_hour_text_input 0x7f0800be
int id material_hour_tv 0x7f0800bf
int id material_label 0x7f0800c0
int id material_minute_text_input 0x7f0800c1
int id material_minute_tv 0x7f0800c2
int id material_textinput_timepicker 0x7f0800c3
int id material_timepicker_cancel_button 0x7f0800c4
int id material_timepicker_container 0x7f0800c5
int id material_timepicker_mode_button 0x7f0800c6
int id material_timepicker_ok_button 0x7f0800c7
int id material_timepicker_view 0x7f0800c8
int id material_value_index 0x7f0800c9
int id maui_custom_view_target_running_callbacks_tag 0x7f0800ca
int id message 0x7f0800cb
int id middle 0x7f0800cc
int id month_grid 0x7f0800cd
int id month_navigation_bar 0x7f0800ce
int id month_navigation_fragment_toggle 0x7f0800cf
int id month_navigation_next 0x7f0800d0
int id month_navigation_previous 0x7f0800d1
int id month_title 0x7f0800d2
int id motion_base 0x7f0800d3
int id mtrl_anchor_parent 0x7f0800d4
int id mtrl_calendar_day_selector_frame 0x7f0800d5
int id mtrl_calendar_days_of_week 0x7f0800d6
int id mtrl_calendar_frame 0x7f0800d7
int id mtrl_calendar_main_pane 0x7f0800d8
int id mtrl_calendar_months 0x7f0800d9
int id mtrl_calendar_selection_frame 0x7f0800da
int id mtrl_calendar_text_input_frame 0x7f0800db
int id mtrl_calendar_year_selector_frame 0x7f0800dc
int id mtrl_card_checked_layer_id 0x7f0800dd
int id mtrl_child_content_container 0x7f0800de
int id mtrl_internal_children_alpha_tag 0x7f0800df
int id mtrl_motion_snapshot_view 0x7f0800e0
int id mtrl_picker_fullscreen 0x7f0800e1
int id mtrl_picker_header 0x7f0800e2
int id mtrl_picker_header_selection_text 0x7f0800e3
int id mtrl_picker_header_title_and_selection 0x7f0800e4
int id mtrl_picker_header_toggle 0x7f0800e5
int id mtrl_picker_text_input_date 0x7f0800e6
int id mtrl_picker_text_input_range_end 0x7f0800e7
int id mtrl_picker_text_input_range_start 0x7f0800e8
int id mtrl_picker_title_text 0x7f0800e9
int id mtrl_view_tag_bottom_padding 0x7f0800ea
int id nav_controller_view_tag 0x7f0800eb
int id nav_host 0x7f0800ec
int id nav_host_fragment_container 0x7f0800ed
int id navigation_bar_item_active_indicator_view 0x7f0800ee
int id navigation_bar_item_icon_container 0x7f0800ef
int id navigation_bar_item_icon_view 0x7f0800f0
int id navigation_bar_item_labels_group 0x7f0800f1
int id navigation_bar_item_large_label_view 0x7f0800f2
int id navigation_bar_item_small_label_view 0x7f0800f3
int id navigation_header_container 0x7f0800f4
int id navigation_layout 0x7f0800f5
int id navigationlayout_appbar 0x7f0800f6
int id navigationlayout_bottomtabs 0x7f0800f7
int id navigationlayout_content 0x7f0800f8
int id navigationlayout_toptabs 0x7f0800f9
int id never 0x7f0800fa
int id noState 0x7f0800fb
int id none 0x7f0800fc
int id normal 0x7f0800fd
int id notification_background 0x7f0800fe
int id notification_main_column 0x7f0800ff
int id notification_main_column_container 0x7f080100
int id off 0x7f080101
int id on 0x7f080102
int id open_search_bar_text_view 0x7f080103
int id open_search_view_background 0x7f080104
int id open_search_view_clear_button 0x7f080105
int id open_search_view_content_container 0x7f080106
int id open_search_view_divider 0x7f080107
int id open_search_view_dummy_toolbar 0x7f080108
int id open_search_view_edit_text 0x7f080109
int id open_search_view_header_container 0x7f08010a
int id open_search_view_root 0x7f08010b
int id open_search_view_scrim 0x7f08010c
int id open_search_view_search_prefix 0x7f08010d
int id open_search_view_status_bar_spacer 0x7f08010e
int id open_search_view_toolbar 0x7f08010f
int id open_search_view_toolbar_container 0x7f080110
int id overshoot 0x7f080111
int id packed 0x7f080112
int id parent 0x7f080113
int id parentPanel 0x7f080114
int id parent_matrix 0x7f080115
int id path 0x7f080116
int id pathRelative 0x7f080117
int id percent 0x7f080118
int id pooling_container_listener_holder_tag 0x7f080119
int id position 0x7f08011a
int id pressed 0x7f08011b
int id progress 0x7f08011c
int id progress_circular 0x7f08011d
int id progress_horizontal 0x7f08011e
int id radio 0x7f08011f
int id ratio 0x7f080120
int id rectangles 0x7f080121
int id report_drawn 0x7f080122
int id reverseSawtooth 0x7f080123
int id right 0x7f080124
int id right_icon 0x7f080125
int id right_side 0x7f080126
int id rounded 0x7f080127
int id row_index_key 0x7f080128
int id rtl 0x7f080129
int id save_non_transition_alpha 0x7f08012a
int id save_overlay_view 0x7f08012b
int id sawtooth 0x7f08012c
int id scrollIndicatorDown 0x7f08012d
int id scrollIndicatorUp 0x7f08012e
int id scrollView 0x7f08012f
int id search_badge 0x7f080130
int id search_bar 0x7f080131
int id search_button 0x7f080132
int id search_close_btn 0x7f080133
int id search_edit_frame 0x7f080134
int id search_go_btn 0x7f080135
int id search_mag_icon 0x7f080136
int id search_plate 0x7f080137
int id search_src_text 0x7f080138
int id search_voice_btn 0x7f080139
int id secondaryProgress 0x7f08013a
int id select_dialog_listview 0x7f08013b
int id selection_type 0x7f08013c
int id sharedValueSet 0x7f08013d
int id sharedValueUnset 0x7f08013e
int id shellcontent_appbar 0x7f08013f
int id shortcut 0x7f080140
int id sin 0x7f080141
int id skipped 0x7f080142
int id sliding_pane_detail_container 0x7f080143
int id sliding_pane_layout 0x7f080144
int id snackbar_action 0x7f080145
int id snackbar_text 0x7f080146
int id spacer 0x7f080147
int id special_effects_controller_view_tag 0x7f080148
int id spline 0x7f080149
int id split_action_bar 0x7f08014a
int id spread 0x7f08014b
int id spread_inside 0x7f08014c
int id square 0x7f08014d
int id standard 0x7f08014e
int id start 0x7f08014f
int id startHorizontal 0x7f080150
int id startVertical 0x7f080151
int id submenuarrow 0x7f080152
int id submit_area 0x7f080153
int id tag_accessibility_actions 0x7f080154
int id tag_accessibility_clickable_spans 0x7f080155
int id tag_accessibility_heading 0x7f080156
int id tag_accessibility_pane_title 0x7f080157
int id tag_on_apply_window_listener 0x7f080158
int id tag_on_receive_content_listener 0x7f080159
int id tag_on_receive_content_mime_types 0x7f08015a
int id tag_screen_reader_focusable 0x7f08015b
int id tag_state_description 0x7f08015c
int id tag_transition_group 0x7f08015d
int id tag_unhandled_key_event_manager 0x7f08015e
int id tag_unhandled_key_listeners 0x7f08015f
int id tag_window_insets_animation_callback 0x7f080160
int id text 0x7f080161
int id text1 0x7f080162
int id text2 0x7f080163
int id textSpacerNoButtons 0x7f080164
int id textSpacerNoTitle 0x7f080165
int id text_input_end_icon 0x7f080166
int id text_input_error_icon 0x7f080167
int id text_input_start_icon 0x7f080168
int id textinput_counter 0x7f080169
int id textinput_error 0x7f08016a
int id textinput_helper_text 0x7f08016b
int id textinput_placeholder 0x7f08016c
int id textinput_prefix_text 0x7f08016d
int id textinput_suffix_text 0x7f08016e
int id time 0x7f08016f
int id title 0x7f080170
int id titleDividerNoCustom 0x7f080171
int id title_template 0x7f080172
int id top 0x7f080173
int id topPanel 0x7f080174
int id topToBottom 0x7f080175
int id touch_outside 0x7f080176
int id transition_clip 0x7f080177
int id transition_current_scene 0x7f080178
int id transition_image_transform 0x7f080179
int id transition_layout_save 0x7f08017a
int id transition_pause_alpha 0x7f08017b
int id transition_position 0x7f08017c
int id transition_scene_layoutid_cache 0x7f08017d
int id transition_transform 0x7f08017e
int id triangle 0x7f08017f
int id unchecked 0x7f080180
int id up 0x7f080181
int id vertical_only 0x7f080182
int id view_offset_helper 0x7f080183
int id view_transition 0x7f080184
int id view_tree_lifecycle_owner 0x7f080185
int id view_tree_on_back_pressed_dispatcher_owner 0x7f080186
int id view_tree_saved_state_registry_owner 0x7f080187
int id view_tree_view_model_store_owner 0x7f080188
int id visible 0x7f080189
int id visible_removing_fragment_view_tag 0x7f08018a
int id with_icon 0x7f08018b
int id wrap 0x7f08018c
int id wrap_content 0x7f08018d
int id wrap_content_constrained 0x7f08018e
int integer abc_config_activityDefaultDur 0x7f090000
int integer abc_config_activityShortDur 0x7f090001
int integer app_bar_elevation_anim_duration 0x7f090002
int integer bottom_sheet_slide_duration 0x7f090003
int integer cancel_button_image_alpha 0x7f090004
int integer config_navAnimTime 0x7f090005
int integer config_tooltipAnimTime 0x7f090006
int integer design_snackbar_text_max_lines 0x7f090007
int integer design_tab_indicator_anim_duration_ms 0x7f090008
int integer hide_password_duration 0x7f090009
int integer m3_badge_max_number 0x7f09000a
int integer m3_btn_anim_delay_ms 0x7f09000b
int integer m3_btn_anim_duration_ms 0x7f09000c
int integer m3_card_anim_delay_ms 0x7f09000d
int integer m3_card_anim_duration_ms 0x7f09000e
int integer m3_chip_anim_duration 0x7f09000f
int integer m3_sys_motion_duration_extra_long1 0x7f090010
int integer m3_sys_motion_duration_extra_long2 0x7f090011
int integer m3_sys_motion_duration_extra_long3 0x7f090012
int integer m3_sys_motion_duration_extra_long4 0x7f090013
int integer m3_sys_motion_duration_long1 0x7f090014
int integer m3_sys_motion_duration_long2 0x7f090015
int integer m3_sys_motion_duration_long3 0x7f090016
int integer m3_sys_motion_duration_long4 0x7f090017
int integer m3_sys_motion_duration_medium1 0x7f090018
int integer m3_sys_motion_duration_medium2 0x7f090019
int integer m3_sys_motion_duration_medium3 0x7f09001a
int integer m3_sys_motion_duration_medium4 0x7f09001b
int integer m3_sys_motion_duration_short1 0x7f09001c
int integer m3_sys_motion_duration_short2 0x7f09001d
int integer m3_sys_motion_duration_short3 0x7f09001e
int integer m3_sys_motion_duration_short4 0x7f09001f
int integer m3_sys_motion_path 0x7f090020
int integer m3_sys_shape_corner_extra_large_corner_family 0x7f090021
int integer m3_sys_shape_corner_extra_small_corner_family 0x7f090022
int integer m3_sys_shape_corner_full_corner_family 0x7f090023
int integer m3_sys_shape_corner_large_corner_family 0x7f090024
int integer m3_sys_shape_corner_medium_corner_family 0x7f090025
int integer m3_sys_shape_corner_small_corner_family 0x7f090026
int integer material_motion_duration_long_1 0x7f090027
int integer material_motion_duration_long_2 0x7f090028
int integer material_motion_duration_medium_1 0x7f090029
int integer material_motion_duration_medium_2 0x7f09002a
int integer material_motion_duration_short_1 0x7f09002b
int integer material_motion_duration_short_2 0x7f09002c
int integer material_motion_path 0x7f09002d
int integer mtrl_badge_max_character_count 0x7f09002e
int integer mtrl_btn_anim_delay_ms 0x7f09002f
int integer mtrl_btn_anim_duration_ms 0x7f090030
int integer mtrl_calendar_header_orientation 0x7f090031
int integer mtrl_calendar_selection_text_lines 0x7f090032
int integer mtrl_calendar_year_selector_span 0x7f090033
int integer mtrl_card_anim_delay_ms 0x7f090034
int integer mtrl_card_anim_duration_ms 0x7f090035
int integer mtrl_chip_anim_duration 0x7f090036
int integer mtrl_switch_thumb_motion_duration 0x7f090037
int integer mtrl_switch_thumb_post_morphing_duration 0x7f090038
int integer mtrl_switch_thumb_pre_morphing_duration 0x7f090039
int integer mtrl_switch_thumb_pressed_duration 0x7f09003a
int integer mtrl_switch_thumb_viewport_center_coordinate 0x7f09003b
int integer mtrl_switch_thumb_viewport_size 0x7f09003c
int integer mtrl_switch_track_viewport_height 0x7f09003d
int integer mtrl_switch_track_viewport_width 0x7f09003e
int integer mtrl_tab_indicator_anim_duration_ms 0x7f09003f
int integer mtrl_view_gone 0x7f090040
int integer mtrl_view_invisible 0x7f090041
int integer mtrl_view_visible 0x7f090042
int integer show_password_duration 0x7f090043
int integer status_bar_notification_info_maxnum 0x7f090044
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0a0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0a0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0a0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0a0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0a0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0a0005
int interpolator fast_out_slow_in 0x7f0a0006
int interpolator m3_sys_motion_easing_emphasized 0x7f0a0007
int interpolator m3_sys_motion_easing_emphasized_accelerate 0x7f0a0008
int interpolator m3_sys_motion_easing_emphasized_decelerate 0x7f0a0009
int interpolator m3_sys_motion_easing_linear 0x7f0a000a
int interpolator m3_sys_motion_easing_standard 0x7f0a000b
int interpolator m3_sys_motion_easing_standard_accelerate 0x7f0a000c
int interpolator m3_sys_motion_easing_standard_decelerate 0x7f0a000d
int interpolator mtrl_fast_out_linear_in 0x7f0a000e
int interpolator mtrl_fast_out_slow_in 0x7f0a000f
int interpolator mtrl_linear 0x7f0a0010
int interpolator mtrl_linear_out_slow_in 0x7f0a0011
int layout abc_action_bar_title_item 0x7f0b0000
int layout abc_action_bar_up_container 0x7f0b0001
int layout abc_action_menu_item_layout 0x7f0b0002
int layout abc_action_menu_layout 0x7f0b0003
int layout abc_action_mode_bar 0x7f0b0004
int layout abc_action_mode_close_item_material 0x7f0b0005
int layout abc_activity_chooser_view 0x7f0b0006
int layout abc_activity_chooser_view_list_item 0x7f0b0007
int layout abc_alert_dialog_button_bar_material 0x7f0b0008
int layout abc_alert_dialog_material 0x7f0b0009
int layout abc_alert_dialog_title_material 0x7f0b000a
int layout abc_cascading_menu_item_layout 0x7f0b000b
int layout abc_dialog_title_material 0x7f0b000c
int layout abc_expanded_menu_layout 0x7f0b000d
int layout abc_list_menu_item_checkbox 0x7f0b000e
int layout abc_list_menu_item_icon 0x7f0b000f
int layout abc_list_menu_item_layout 0x7f0b0010
int layout abc_list_menu_item_radio 0x7f0b0011
int layout abc_popup_menu_header_item_layout 0x7f0b0012
int layout abc_popup_menu_item_layout 0x7f0b0013
int layout abc_screen_content_include 0x7f0b0014
int layout abc_screen_simple 0x7f0b0015
int layout abc_screen_simple_overlay_action_mode 0x7f0b0016
int layout abc_screen_toolbar 0x7f0b0017
int layout abc_search_dropdown_item_icons_2line 0x7f0b0018
int layout abc_search_view 0x7f0b0019
int layout abc_select_dialog_material 0x7f0b001a
int layout abc_tooltip 0x7f0b001b
int layout browser_actions_context_menu_page 0x7f0b001c
int layout browser_actions_context_menu_row 0x7f0b001d
int layout custom_dialog 0x7f0b001e
int layout design_bottom_navigation_item 0x7f0b001f
int layout design_bottom_sheet_dialog 0x7f0b0020
int layout design_layout_snackbar 0x7f0b0021
int layout design_layout_snackbar_include 0x7f0b0022
int layout design_layout_tab_icon 0x7f0b0023
int layout design_layout_tab_text 0x7f0b0024
int layout design_menu_item_action_area 0x7f0b0025
int layout design_navigation_item 0x7f0b0026
int layout design_navigation_item_header 0x7f0b0027
int layout design_navigation_item_separator 0x7f0b0028
int layout design_navigation_item_subheader 0x7f0b0029
int layout design_navigation_menu 0x7f0b002a
int layout design_navigation_menu_item 0x7f0b002b
int layout design_text_input_end_icon 0x7f0b002c
int layout design_text_input_start_icon 0x7f0b002d
int layout drawer_layout 0x7f0b002e
int layout flyoutcontent 0x7f0b002f
int layout fragment_backstack 0x7f0b0030
int layout ime_base_split_test_activity 0x7f0b0031
int layout ime_secondary_split_test_activity 0x7f0b0032
int layout m3_alert_dialog 0x7f0b0033
int layout m3_alert_dialog_actions 0x7f0b0034
int layout m3_alert_dialog_title 0x7f0b0035
int layout m3_auto_complete_simple_item 0x7f0b0036
int layout m3_side_sheet_dialog 0x7f0b0037
int layout material_chip_input_combo 0x7f0b0038
int layout material_clock_display 0x7f0b0039
int layout material_clock_display_divider 0x7f0b003a
int layout material_clock_period_toggle 0x7f0b003b
int layout material_clock_period_toggle_land 0x7f0b003c
int layout material_clockface_textview 0x7f0b003d
int layout material_clockface_view 0x7f0b003e
int layout material_radial_view_group 0x7f0b003f
int layout material_textinput_timepicker 0x7f0b0040
int layout material_time_chip 0x7f0b0041
int layout material_time_input 0x7f0b0042
int layout material_timepicker 0x7f0b0043
int layout material_timepicker_dialog 0x7f0b0044
int layout material_timepicker_textinput_display 0x7f0b0045
int layout mtrl_alert_dialog 0x7f0b0046
int layout mtrl_alert_dialog_actions 0x7f0b0047
int layout mtrl_alert_dialog_title 0x7f0b0048
int layout mtrl_alert_select_dialog_item 0x7f0b0049
int layout mtrl_alert_select_dialog_multichoice 0x7f0b004a
int layout mtrl_alert_select_dialog_singlechoice 0x7f0b004b
int layout mtrl_auto_complete_simple_item 0x7f0b004c
int layout mtrl_calendar_day 0x7f0b004d
int layout mtrl_calendar_day_of_week 0x7f0b004e
int layout mtrl_calendar_days_of_week 0x7f0b004f
int layout mtrl_calendar_horizontal 0x7f0b0050
int layout mtrl_calendar_month 0x7f0b0051
int layout mtrl_calendar_month_labeled 0x7f0b0052
int layout mtrl_calendar_month_navigation 0x7f0b0053
int layout mtrl_calendar_months 0x7f0b0054
int layout mtrl_calendar_vertical 0x7f0b0055
int layout mtrl_calendar_year 0x7f0b0056
int layout mtrl_layout_snackbar 0x7f0b0057
int layout mtrl_layout_snackbar_include 0x7f0b0058
int layout mtrl_navigation_rail_item 0x7f0b0059
int layout mtrl_picker_actions 0x7f0b005a
int layout mtrl_picker_dialog 0x7f0b005b
int layout mtrl_picker_fullscreen 0x7f0b005c
int layout mtrl_picker_header_dialog 0x7f0b005d
int layout mtrl_picker_header_fullscreen 0x7f0b005e
int layout mtrl_picker_header_selection_text 0x7f0b005f
int layout mtrl_picker_header_title_text 0x7f0b0060
int layout mtrl_picker_header_toggle 0x7f0b0061
int layout mtrl_picker_text_input_date 0x7f0b0062
int layout mtrl_picker_text_input_date_range 0x7f0b0063
int layout mtrl_search_bar 0x7f0b0064
int layout mtrl_search_view 0x7f0b0065
int layout navigationlayout 0x7f0b0066
int layout notification_action 0x7f0b0067
int layout notification_action_tombstone 0x7f0b0068
int layout notification_template_custom_big 0x7f0b0069
int layout notification_template_icon_group 0x7f0b006a
int layout notification_template_part_chronometer 0x7f0b006b
int layout notification_template_part_time 0x7f0b006c
int layout select_dialog_item_material 0x7f0b006d
int layout select_dialog_multichoice_material 0x7f0b006e
int layout select_dialog_singlechoice_material 0x7f0b006f
int layout shellcontent 0x7f0b0070
int layout support_simple_spinner_dropdown_item 0x7f0b0071
int mipmap appicon 0x7f0c0000
int mipmap appicon_background 0x7f0c0001
int mipmap appicon_foreground 0x7f0c0002
int mipmap appicon_round 0x7f0c0003
int plurals mtrl_badge_content_description 0x7f0d0000
int string abc_action_bar_home_description 0x7f0e0000
int string abc_action_bar_up_description 0x7f0e0001
int string abc_action_menu_overflow_description 0x7f0e0002
int string abc_action_mode_done 0x7f0e0003
int string abc_activity_chooser_view_see_all 0x7f0e0004
int string abc_activitychooserview_choose_application 0x7f0e0005
int string abc_capital_off 0x7f0e0006
int string abc_capital_on 0x7f0e0007
int string abc_menu_alt_shortcut_label 0x7f0e0008
int string abc_menu_ctrl_shortcut_label 0x7f0e0009
int string abc_menu_delete_shortcut_label 0x7f0e000a
int string abc_menu_enter_shortcut_label 0x7f0e000b
int string abc_menu_function_shortcut_label 0x7f0e000c
int string abc_menu_meta_shortcut_label 0x7f0e000d
int string abc_menu_shift_shortcut_label 0x7f0e000e
int string abc_menu_space_shortcut_label 0x7f0e000f
int string abc_menu_sym_shortcut_label 0x7f0e0010
int string abc_prepend_shortcut_label 0x7f0e0011
int string abc_search_hint 0x7f0e0012
int string abc_searchview_description_clear 0x7f0e0013
int string abc_searchview_description_query 0x7f0e0014
int string abc_searchview_description_search 0x7f0e0015
int string abc_searchview_description_submit 0x7f0e0016
int string abc_searchview_description_voice 0x7f0e0017
int string abc_shareactionprovider_share_with 0x7f0e0018
int string abc_shareactionprovider_share_with_application 0x7f0e0019
int string abc_toolbar_collapse_description 0x7f0e001a
int string androidx_startup 0x7f0e001b
int string appbar_scrolling_view_behavior 0x7f0e001c
int string bottom_sheet_behavior 0x7f0e001d
int string bottomsheet_action_collapse 0x7f0e001e
int string bottomsheet_action_expand 0x7f0e001f
int string bottomsheet_action_expand_halfway 0x7f0e0020
int string bottomsheet_drag_handle_clicked 0x7f0e0021
int string bottomsheet_drag_handle_content_description 0x7f0e0022
int string call_notification_answer_action 0x7f0e0023
int string call_notification_answer_video_action 0x7f0e0024
int string call_notification_decline_action 0x7f0e0025
int string call_notification_hang_up_action 0x7f0e0026
int string call_notification_incoming_text 0x7f0e0027
int string call_notification_ongoing_text 0x7f0e0028
int string call_notification_screening_text 0x7f0e0029
int string character_counter_content_description 0x7f0e002a
int string character_counter_overflowed_content_description 0x7f0e002b
int string character_counter_pattern 0x7f0e002c
int string clear_text_end_icon_content_description 0x7f0e002d
int string copy_toast_msg 0x7f0e002e
int string dest_title 0x7f0e002f
int string error_a11y_label 0x7f0e0030
int string error_icon_content_description 0x7f0e0031
int string exposed_dropdown_menu_content_description 0x7f0e0032
int string fab_transformation_scrim_behavior 0x7f0e0033
int string fab_transformation_sheet_behavior 0x7f0e0034
int string fallback_menu_item_copy_link 0x7f0e0035
int string fallback_menu_item_open_in_browser 0x7f0e0036
int string fallback_menu_item_share_link 0x7f0e0037
int string hide_bottom_view_on_scroll_behavior 0x7f0e0038
int string icon_content_description 0x7f0e0039
int string item_view_role_description 0x7f0e003a
int string m3_exceed_max_badge_text_suffix 0x7f0e003b
int string m3_ref_typeface_brand_medium 0x7f0e003c
int string m3_ref_typeface_brand_regular 0x7f0e003d
int string m3_ref_typeface_plain_medium 0x7f0e003e
int string m3_ref_typeface_plain_regular 0x7f0e003f
int string m3_sys_motion_easing_emphasized 0x7f0e0040
int string m3_sys_motion_easing_emphasized_accelerate 0x7f0e0041
int string m3_sys_motion_easing_emphasized_decelerate 0x7f0e0042
int string m3_sys_motion_easing_emphasized_path_data 0x7f0e0043
int string m3_sys_motion_easing_legacy 0x7f0e0044
int string m3_sys_motion_easing_legacy_accelerate 0x7f0e0045
int string m3_sys_motion_easing_legacy_decelerate 0x7f0e0046
int string m3_sys_motion_easing_linear 0x7f0e0047
int string m3_sys_motion_easing_standard 0x7f0e0048
int string m3_sys_motion_easing_standard_accelerate 0x7f0e0049
int string m3_sys_motion_easing_standard_decelerate 0x7f0e004a
int string material_clock_display_divider 0x7f0e004b
int string material_clock_toggle_content_description 0x7f0e004c
int string material_hour_24h_suffix 0x7f0e004d
int string material_hour_selection 0x7f0e004e
int string material_hour_suffix 0x7f0e004f
int string material_minute_selection 0x7f0e0050
int string material_minute_suffix 0x7f0e0051
int string material_motion_easing_accelerated 0x7f0e0052
int string material_motion_easing_decelerated 0x7f0e0053
int string material_motion_easing_emphasized 0x7f0e0054
int string material_motion_easing_linear 0x7f0e0055
int string material_motion_easing_standard 0x7f0e0056
int string material_slider_range_end 0x7f0e0057
int string material_slider_range_start 0x7f0e0058
int string material_slider_value 0x7f0e0059
int string material_timepicker_am 0x7f0e005a
int string material_timepicker_clock_mode_description 0x7f0e005b
int string material_timepicker_hour 0x7f0e005c
int string material_timepicker_minute 0x7f0e005d
int string material_timepicker_pm 0x7f0e005e
int string material_timepicker_select_time 0x7f0e005f
int string material_timepicker_text_input_mode_description 0x7f0e0060
int string maui_empty_unused 0x7f0e0061
int string mtrl_badge_numberless_content_description 0x7f0e0062
int string mtrl_checkbox_button_icon_path_checked 0x7f0e0063
int string mtrl_checkbox_button_icon_path_group_name 0x7f0e0064
int string mtrl_checkbox_button_icon_path_indeterminate 0x7f0e0065
int string mtrl_checkbox_button_icon_path_name 0x7f0e0066
int string mtrl_checkbox_button_path_checked 0x7f0e0067
int string mtrl_checkbox_button_path_group_name 0x7f0e0068
int string mtrl_checkbox_button_path_name 0x7f0e0069
int string mtrl_checkbox_button_path_unchecked 0x7f0e006a
int string mtrl_checkbox_state_description_checked 0x7f0e006b
int string mtrl_checkbox_state_description_indeterminate 0x7f0e006c
int string mtrl_checkbox_state_description_unchecked 0x7f0e006d
int string mtrl_chip_close_icon_content_description 0x7f0e006e
int string mtrl_exceed_max_badge_number_content_description 0x7f0e006f
int string mtrl_exceed_max_badge_number_suffix 0x7f0e0070
int string mtrl_picker_a11y_next_month 0x7f0e0071
int string mtrl_picker_a11y_prev_month 0x7f0e0072
int string mtrl_picker_announce_current_range_selection 0x7f0e0073
int string mtrl_picker_announce_current_selection 0x7f0e0074
int string mtrl_picker_announce_current_selection_none 0x7f0e0075
int string mtrl_picker_cancel 0x7f0e0076
int string mtrl_picker_confirm 0x7f0e0077
int string mtrl_picker_date_header_selected 0x7f0e0078
int string mtrl_picker_date_header_title 0x7f0e0079
int string mtrl_picker_date_header_unselected 0x7f0e007a
int string mtrl_picker_day_of_week_column_header 0x7f0e007b
int string mtrl_picker_end_date_description 0x7f0e007c
int string mtrl_picker_invalid_format 0x7f0e007d
int string mtrl_picker_invalid_format_example 0x7f0e007e
int string mtrl_picker_invalid_format_use 0x7f0e007f
int string mtrl_picker_invalid_range 0x7f0e0080
int string mtrl_picker_navigate_to_current_year_description 0x7f0e0081
int string mtrl_picker_navigate_to_year_description 0x7f0e0082
int string mtrl_picker_out_of_range 0x7f0e0083
int string mtrl_picker_range_header_only_end_selected 0x7f0e0084
int string mtrl_picker_range_header_only_start_selected 0x7f0e0085
int string mtrl_picker_range_header_selected 0x7f0e0086
int string mtrl_picker_range_header_title 0x7f0e0087
int string mtrl_picker_range_header_unselected 0x7f0e0088
int string mtrl_picker_save 0x7f0e0089
int string mtrl_picker_start_date_description 0x7f0e008a
int string mtrl_picker_text_input_date_hint 0x7f0e008b
int string mtrl_picker_text_input_date_range_end_hint 0x7f0e008c
int string mtrl_picker_text_input_date_range_start_hint 0x7f0e008d
int string mtrl_picker_text_input_day_abbr 0x7f0e008e
int string mtrl_picker_text_input_month_abbr 0x7f0e008f
int string mtrl_picker_text_input_year_abbr 0x7f0e0090
int string mtrl_picker_today_description 0x7f0e0091
int string mtrl_picker_toggle_to_calendar_input_mode 0x7f0e0092
int string mtrl_picker_toggle_to_day_selection 0x7f0e0093
int string mtrl_picker_toggle_to_text_input_mode 0x7f0e0094
int string mtrl_picker_toggle_to_year_selection 0x7f0e0095
int string mtrl_switch_thumb_group_name 0x7f0e0096
int string mtrl_switch_thumb_path_checked 0x7f0e0097
int string mtrl_switch_thumb_path_morphing 0x7f0e0098
int string mtrl_switch_thumb_path_name 0x7f0e0099
int string mtrl_switch_thumb_path_pressed 0x7f0e009a
int string mtrl_switch_thumb_path_unchecked 0x7f0e009b
int string mtrl_switch_track_decoration_path 0x7f0e009c
int string mtrl_switch_track_path 0x7f0e009d
int string mtrl_timepicker_cancel 0x7f0e009e
int string mtrl_timepicker_confirm 0x7f0e009f
int string nav_app_bar_navigate_up_description 0x7f0e00a0
int string nav_app_bar_open_drawer_description 0x7f0e00a1
int string overflow_tab_title 0x7f0e00a2
int string password_toggle_content_description 0x7f0e00a3
int string path_password_eye 0x7f0e00a4
int string path_password_eye_mask_strike_through 0x7f0e00a5
int string path_password_eye_mask_visible 0x7f0e00a6
int string path_password_strike_through 0x7f0e00a7
int string search_menu_title 0x7f0e00a8
int string searchbar_scrolling_view_behavior 0x7f0e00a9
int string searchview_clear_text_content_description 0x7f0e00aa
int string searchview_navigation_content_description 0x7f0e00ab
int string side_sheet_accessibility_pane_title 0x7f0e00ac
int string side_sheet_behavior 0x7f0e00ad
int string status_bar_notification_info_overflow 0x7f0e00ae
int style ActionMode 0x7f0f0000
int style AlertDialog.AppCompat 0x7f0f0001
int style AlertDialog.AppCompat.Light 0x7f0f0002
int style Animation.AppCompat.Dialog 0x7f0f0003
int style Animation.AppCompat.DropDownUp 0x7f0f0004
int style Animation.AppCompat.Tooltip 0x7f0f0005
int style Animation.Design.BottomSheetDialog 0x7f0f0006
int style Animation.Material3.BottomSheetDialog 0x7f0f0007
int style Animation.Material3.SideSheetDialog 0x7f0f0008
int style Animation.Material3.SideSheetDialog.Left 0x7f0f0009
int style Animation.Material3.SideSheetDialog.Right 0x7f0f000a
int style Animation.MaterialComponents.BottomSheetDialog 0x7f0f000b
int style AppTheme 0x7f0f000c
int style AppTheme.NoActionBar 0x7f0f000d
int style Base.AlertDialog.AppCompat 0x7f0f000e
int style Base.AlertDialog.AppCompat.Light 0x7f0f000f
int style Base.Animation.AppCompat.Dialog 0x7f0f0010
int style Base.Animation.AppCompat.DropDownUp 0x7f0f0011
int style Base.Animation.AppCompat.Tooltip 0x7f0f0012
int style Base.CardView 0x7f0f0013
int style Base.DialogWindowTitle.AppCompat 0x7f0f0014
int style Base.DialogWindowTitleBackground.AppCompat 0x7f0f0015
int style Base.MaterialAlertDialog.MaterialComponents.Title.Icon 0x7f0f0016
int style Base.MaterialAlertDialog.MaterialComponents.Title.Panel 0x7f0f0017
int style Base.MaterialAlertDialog.MaterialComponents.Title.Text 0x7f0f0018
int style Base.TextAppearance.AppCompat 0x7f0f0019
int style Base.TextAppearance.AppCompat.Body1 0x7f0f001a
int style Base.TextAppearance.AppCompat.Body2 0x7f0f001b
int style Base.TextAppearance.AppCompat.Button 0x7f0f001c
int style Base.TextAppearance.AppCompat.Caption 0x7f0f001d
int style Base.TextAppearance.AppCompat.Display1 0x7f0f001e
int style Base.TextAppearance.AppCompat.Display2 0x7f0f001f
int style Base.TextAppearance.AppCompat.Display3 0x7f0f0020
int style Base.TextAppearance.AppCompat.Display4 0x7f0f0021
int style Base.TextAppearance.AppCompat.Headline 0x7f0f0022
int style Base.TextAppearance.AppCompat.Inverse 0x7f0f0023
int style Base.TextAppearance.AppCompat.Large 0x7f0f0024
int style Base.TextAppearance.AppCompat.Large.Inverse 0x7f0f0025
int style Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large 0x7f0f0026
int style Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small 0x7f0f0027
int style Base.TextAppearance.AppCompat.Medium 0x7f0f0028
int style Base.TextAppearance.AppCompat.Medium.Inverse 0x7f0f0029
int style Base.TextAppearance.AppCompat.Menu 0x7f0f002a
int style Base.TextAppearance.AppCompat.SearchResult 0x7f0f002b
int style Base.TextAppearance.AppCompat.SearchResult.Subtitle 0x7f0f002c
int style Base.TextAppearance.AppCompat.SearchResult.Title 0x7f0f002d
int style Base.TextAppearance.AppCompat.Small 0x7f0f002e
int style Base.TextAppearance.AppCompat.Small.Inverse 0x7f0f002f
int style Base.TextAppearance.AppCompat.Subhead 0x7f0f0030
int style Base.TextAppearance.AppCompat.Subhead.Inverse 0x7f0f0031
int style Base.TextAppearance.AppCompat.Title 0x7f0f0032
int style Base.TextAppearance.AppCompat.Title.Inverse 0x7f0f0033
int style Base.TextAppearance.AppCompat.Tooltip 0x7f0f0034
int style Base.TextAppearance.AppCompat.Widget.ActionBar.Menu 0x7f0f0035
int style Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle 0x7f0f0036
int style Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse 0x7f0f0037
int style Base.TextAppearance.AppCompat.Widget.ActionBar.Title 0x7f0f0038
int style Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse 0x7f0f0039
int style Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle 0x7f0f003a
int style Base.TextAppearance.AppCompat.Widget.ActionMode.Title 0x7f0f003b
int style Base.TextAppearance.AppCompat.Widget.Button 0x7f0f003c
int style Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored 0x7f0f003d
int style Base.TextAppearance.AppCompat.Widget.Button.Colored 0x7f0f003e
int style Base.TextAppearance.AppCompat.Widget.Button.Inverse 0x7f0f003f
int style Base.TextAppearance.AppCompat.Widget.DropDownItem 0x7f0f0040
int style Base.TextAppearance.AppCompat.Widget.PopupMenu.Header 0x7f0f0041
int style Base.TextAppearance.AppCompat.Widget.PopupMenu.Large 0x7f0f0042
int style Base.TextAppearance.AppCompat.Widget.PopupMenu.Small 0x7f0f0043
int style Base.TextAppearance.AppCompat.Widget.Switch 0x7f0f0044
int style Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem 0x7f0f0045
int style Base.TextAppearance.Material3.Search 0x7f0f0046
int style Base.TextAppearance.MaterialComponents.Badge 0x7f0f0047
int style Base.TextAppearance.MaterialComponents.Button 0x7f0f0048
int style Base.TextAppearance.MaterialComponents.Headline6 0x7f0f0049
int style Base.TextAppearance.MaterialComponents.Subtitle2 0x7f0f004a
int style Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item 0x7f0f004b
int style Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle 0x7f0f004c
int style Base.TextAppearance.Widget.AppCompat.Toolbar.Title 0x7f0f004d
int style Base.Theme.AppCompat 0x7f0f004e
int style Base.Theme.AppCompat.CompactMenu 0x7f0f004f
int style Base.Theme.AppCompat.Dialog 0x7f0f0050
int style Base.Theme.AppCompat.Dialog.Alert 0x7f0f0051
int style Base.Theme.AppCompat.Dialog.FixedSize 0x7f0f0052
int style Base.Theme.AppCompat.Dialog.MinWidth 0x7f0f0053
int style Base.Theme.AppCompat.DialogWhenLarge 0x7f0f0054
int style Base.Theme.AppCompat.Light 0x7f0f0055
int style Base.Theme.AppCompat.Light.DarkActionBar 0x7f0f0056
int style Base.Theme.AppCompat.Light.Dialog 0x7f0f0057
int style Base.Theme.AppCompat.Light.Dialog.Alert 0x7f0f0058
int style Base.Theme.AppCompat.Light.Dialog.FixedSize 0x7f0f0059
int style Base.Theme.AppCompat.Light.Dialog.MinWidth 0x7f0f005a
int style Base.Theme.AppCompat.Light.DialogWhenLarge 0x7f0f005b
int style Base.Theme.Material3.Dark 0x7f0f005c
int style Base.Theme.Material3.Dark.BottomSheetDialog 0x7f0f005d
int style Base.Theme.Material3.Dark.Dialog 0x7f0f005e
int style Base.Theme.Material3.Dark.Dialog.FixedSize 0x7f0f005f
int style Base.Theme.Material3.Dark.DialogWhenLarge 0x7f0f0060
int style Base.Theme.Material3.Dark.SideSheetDialog 0x7f0f0061
int style Base.Theme.Material3.Light 0x7f0f0062
int style Base.Theme.Material3.Light.BottomSheetDialog 0x7f0f0063
int style Base.Theme.Material3.Light.Dialog 0x7f0f0064
int style Base.Theme.Material3.Light.Dialog.FixedSize 0x7f0f0065
int style Base.Theme.Material3.Light.DialogWhenLarge 0x7f0f0066
int style Base.Theme.Material3.Light.SideSheetDialog 0x7f0f0067
int style Base.Theme.MaterialComponents 0x7f0f0068
int style Base.Theme.MaterialComponents.Bridge 0x7f0f0069
int style Base.Theme.MaterialComponents.CompactMenu 0x7f0f006a
int style Base.Theme.MaterialComponents.Dialog 0x7f0f006b
int style Base.Theme.MaterialComponents.Dialog.Alert 0x7f0f006c
int style Base.Theme.MaterialComponents.Dialog.Bridge 0x7f0f006d
int style Base.Theme.MaterialComponents.Dialog.FixedSize 0x7f0f006e
int style Base.Theme.MaterialComponents.Dialog.MinWidth 0x7f0f006f
int style Base.Theme.MaterialComponents.DialogWhenLarge 0x7f0f0070
int style Base.Theme.MaterialComponents.Light 0x7f0f0071
int style Base.Theme.MaterialComponents.Light.Bridge 0x7f0f0072
int style Base.Theme.MaterialComponents.Light.DarkActionBar 0x7f0f0073
int style Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge 0x7f0f0074
int style Base.Theme.MaterialComponents.Light.Dialog 0x7f0f0075
int style Base.Theme.MaterialComponents.Light.Dialog.Alert 0x7f0f0076
int style Base.Theme.MaterialComponents.Light.Dialog.Bridge 0x7f0f0077
int style Base.Theme.MaterialComponents.Light.Dialog.FixedSize 0x7f0f0078
int style Base.Theme.MaterialComponents.Light.Dialog.MinWidth 0x7f0f0079
int style Base.Theme.MaterialComponents.Light.DialogWhenLarge 0x7f0f007a
int style Base.ThemeOverlay.AppCompat 0x7f0f007b
int style Base.ThemeOverlay.AppCompat.ActionBar 0x7f0f007c
int style Base.ThemeOverlay.AppCompat.Dark 0x7f0f007d
int style Base.ThemeOverlay.AppCompat.Dark.ActionBar 0x7f0f007e
int style Base.ThemeOverlay.AppCompat.Dialog 0x7f0f007f
int style Base.ThemeOverlay.AppCompat.Dialog.Alert 0x7f0f0080
int style Base.ThemeOverlay.AppCompat.Light 0x7f0f0081
int style Base.ThemeOverlay.Material3.AutoCompleteTextView 0x7f0f0082
int style Base.ThemeOverlay.Material3.BottomSheetDialog 0x7f0f0083
int style Base.ThemeOverlay.Material3.Dialog 0x7f0f0084
int style Base.ThemeOverlay.Material3.SideSheetDialog 0x7f0f0085
int style Base.ThemeOverlay.Material3.TextInputEditText 0x7f0f0086
int style Base.ThemeOverlay.MaterialComponents.Dialog 0x7f0f0087
int style Base.ThemeOverlay.MaterialComponents.Dialog.Alert 0x7f0f0088
int style Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework 0x7f0f0089
int style Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework 0x7f0f008a
int style Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog 0x7f0f008b
int style Base.V14.Theme.Material3.Dark 0x7f0f008c
int style Base.V14.Theme.Material3.Dark.BottomSheetDialog 0x7f0f008d
int style Base.V14.Theme.Material3.Dark.Dialog 0x7f0f008e
int style Base.V14.Theme.Material3.Dark.SideSheetDialog 0x7f0f008f
int style Base.V14.Theme.Material3.Light 0x7f0f0090
int style Base.V14.Theme.Material3.Light.BottomSheetDialog 0x7f0f0091
int style Base.V14.Theme.Material3.Light.Dialog 0x7f0f0092
int style Base.V14.Theme.Material3.Light.SideSheetDialog 0x7f0f0093
int style Base.V14.Theme.MaterialComponents 0x7f0f0094
int style Base.V14.Theme.MaterialComponents.Bridge 0x7f0f0095
int style Base.V14.Theme.MaterialComponents.Dialog 0x7f0f0096
int style Base.V14.Theme.MaterialComponents.Dialog.Bridge 0x7f0f0097
int style Base.V14.Theme.MaterialComponents.Light 0x7f0f0098
int style Base.V14.Theme.MaterialComponents.Light.Bridge 0x7f0f0099
int style Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge 0x7f0f009a
int style Base.V14.Theme.MaterialComponents.Light.Dialog 0x7f0f009b
int style Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge 0x7f0f009c
int style Base.V14.ThemeOverlay.Material3.BottomSheetDialog 0x7f0f009d
int style Base.V14.ThemeOverlay.Material3.SideSheetDialog 0x7f0f009e
int style Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog 0x7f0f009f
int style Base.V14.ThemeOverlay.MaterialComponents.Dialog 0x7f0f00a0
int style Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert 0x7f0f00a1
int style Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog 0x7f0f00a2
int style Base.V14.Widget.MaterialComponents.AutoCompleteTextView 0x7f0f00a3
int style Base.V21.Theme.AppCompat 0x7f0f00a4
int style Base.V21.Theme.AppCompat.Dialog 0x7f0f00a5
int style Base.V21.Theme.AppCompat.Light 0x7f0f00a6
int style Base.V21.Theme.AppCompat.Light.Dialog 0x7f0f00a7
int style Base.V21.Theme.MaterialComponents 0x7f0f00a8
int style Base.V21.Theme.MaterialComponents.Dialog 0x7f0f00a9
int style Base.V21.Theme.MaterialComponents.Light 0x7f0f00aa
int style Base.V21.Theme.MaterialComponents.Light.Dialog 0x7f0f00ab
int style Base.V21.ThemeOverlay.AppCompat.Dialog 0x7f0f00ac
int style Base.V21.ThemeOverlay.Material3.BottomSheetDialog 0x7f0f00ad
int style Base.V21.ThemeOverlay.Material3.SideSheetDialog 0x7f0f00ae
int style Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog 0x7f0f00af
int style Base.V22.Theme.AppCompat 0x7f0f00b0
int style Base.V22.Theme.AppCompat.Light 0x7f0f00b1
int style Base.V23.Theme.AppCompat 0x7f0f00b2
int style Base.V23.Theme.AppCompat.Light 0x7f0f00b3
int style Base.V24.Theme.Material3.Dark 0x7f0f00b4
int style Base.V24.Theme.Material3.Dark.Dialog 0x7f0f00b5
int style Base.V24.Theme.Material3.Light 0x7f0f00b6
int style Base.V24.Theme.Material3.Light.Dialog 0x7f0f00b7
int style Base.V26.Theme.AppCompat 0x7f0f00b8
int style Base.V26.Theme.AppCompat.Light 0x7f0f00b9
int style Base.V26.Widget.AppCompat.Toolbar 0x7f0f00ba
int style Base.V28.Theme.AppCompat 0x7f0f00bb
int style Base.V28.Theme.AppCompat.Light 0x7f0f00bc
int style Base.V7.Theme.AppCompat 0x7f0f00bd
int style Base.V7.Theme.AppCompat.Dialog 0x7f0f00be
int style Base.V7.Theme.AppCompat.Light 0x7f0f00bf
int style Base.V7.Theme.AppCompat.Light.Dialog 0x7f0f00c0
int style Base.V7.ThemeOverlay.AppCompat.Dialog 0x7f0f00c1
int style Base.V7.Widget.AppCompat.AutoCompleteTextView 0x7f0f00c2
int style Base.V7.Widget.AppCompat.EditText 0x7f0f00c3
int style Base.V7.Widget.AppCompat.Toolbar 0x7f0f00c4
int style Base.Widget.AppCompat.ActionBar 0x7f0f00c5
int style Base.Widget.AppCompat.ActionBar.Solid 0x7f0f00c6
int style Base.Widget.AppCompat.ActionBar.TabBar 0x7f0f00c7
int style Base.Widget.AppCompat.ActionBar.TabText 0x7f0f00c8
int style Base.Widget.AppCompat.ActionBar.TabView 0x7f0f00c9
int style Base.Widget.AppCompat.ActionButton 0x7f0f00ca
int style Base.Widget.AppCompat.ActionButton.CloseMode 0x7f0f00cb
int style Base.Widget.AppCompat.ActionButton.Overflow 0x7f0f00cc
int style Base.Widget.AppCompat.ActionMode 0x7f0f00cd
int style Base.Widget.AppCompat.ActivityChooserView 0x7f0f00ce
int style Base.Widget.AppCompat.AutoCompleteTextView 0x7f0f00cf
int style Base.Widget.AppCompat.Button 0x7f0f00d0
int style Base.Widget.AppCompat.Button.Borderless 0x7f0f00d1
int style Base.Widget.AppCompat.Button.Borderless.Colored 0x7f0f00d2
int style Base.Widget.AppCompat.Button.ButtonBar.AlertDialog 0x7f0f00d3
int style Base.Widget.AppCompat.Button.Colored 0x7f0f00d4
int style Base.Widget.AppCompat.Button.Small 0x7f0f00d5
int style Base.Widget.AppCompat.ButtonBar 0x7f0f00d6
int style Base.Widget.AppCompat.ButtonBar.AlertDialog 0x7f0f00d7
int style Base.Widget.AppCompat.CompoundButton.CheckBox 0x7f0f00d8
int style Base.Widget.AppCompat.CompoundButton.RadioButton 0x7f0f00d9
int style Base.Widget.AppCompat.CompoundButton.Switch 0x7f0f00da
int style Base.Widget.AppCompat.DrawerArrowToggle 0x7f0f00db
int style Base.Widget.AppCompat.DrawerArrowToggle.Common 0x7f0f00dc
int style Base.Widget.AppCompat.DropDownItem.Spinner 0x7f0f00dd
int style Base.Widget.AppCompat.EditText 0x7f0f00de
int style Base.Widget.AppCompat.ImageButton 0x7f0f00df
int style Base.Widget.AppCompat.Light.ActionBar 0x7f0f00e0
int style Base.Widget.AppCompat.Light.ActionBar.Solid 0x7f0f00e1
int style Base.Widget.AppCompat.Light.ActionBar.TabBar 0x7f0f00e2
int style Base.Widget.AppCompat.Light.ActionBar.TabText 0x7f0f00e3
int style Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse 0x7f0f00e4
int style Base.Widget.AppCompat.Light.ActionBar.TabView 0x7f0f00e5
int style Base.Widget.AppCompat.Light.PopupMenu 0x7f0f00e6
int style Base.Widget.AppCompat.Light.PopupMenu.Overflow 0x7f0f00e7
int style Base.Widget.AppCompat.ListMenuView 0x7f0f00e8
int style Base.Widget.AppCompat.ListPopupWindow 0x7f0f00e9
int style Base.Widget.AppCompat.ListView 0x7f0f00ea
int style Base.Widget.AppCompat.ListView.DropDown 0x7f0f00eb
int style Base.Widget.AppCompat.ListView.Menu 0x7f0f00ec
int style Base.Widget.AppCompat.PopupMenu 0x7f0f00ed
int style Base.Widget.AppCompat.PopupMenu.Overflow 0x7f0f00ee
int style Base.Widget.AppCompat.PopupWindow 0x7f0f00ef
int style Base.Widget.AppCompat.ProgressBar 0x7f0f00f0
int style Base.Widget.AppCompat.ProgressBar.Horizontal 0x7f0f00f1
int style Base.Widget.AppCompat.RatingBar 0x7f0f00f2
int style Base.Widget.AppCompat.RatingBar.Indicator 0x7f0f00f3
int style Base.Widget.AppCompat.RatingBar.Small 0x7f0f00f4
int style Base.Widget.AppCompat.SearchView 0x7f0f00f5
int style Base.Widget.AppCompat.SearchView.ActionBar 0x7f0f00f6
int style Base.Widget.AppCompat.SeekBar 0x7f0f00f7
int style Base.Widget.AppCompat.SeekBar.Discrete 0x7f0f00f8
int style Base.Widget.AppCompat.Spinner 0x7f0f00f9
int style Base.Widget.AppCompat.Spinner.Underlined 0x7f0f00fa
int style Base.Widget.AppCompat.TextView 0x7f0f00fb
int style Base.Widget.AppCompat.TextView.SpinnerItem 0x7f0f00fc
int style Base.Widget.AppCompat.Toolbar 0x7f0f00fd
int style Base.Widget.AppCompat.Toolbar.Button.Navigation 0x7f0f00fe
int style Base.Widget.Design.TabLayout 0x7f0f00ff
int style Base.Widget.Material3.ActionBar.Solid 0x7f0f0100
int style Base.Widget.Material3.ActionMode 0x7f0f0101
int style Base.Widget.Material3.BottomNavigationView 0x7f0f0102
int style Base.Widget.Material3.CardView 0x7f0f0103
int style Base.Widget.Material3.Chip 0x7f0f0104
int style Base.Widget.Material3.CollapsingToolbar 0x7f0f0105
int style Base.Widget.Material3.CompoundButton.CheckBox 0x7f0f0106
int style Base.Widget.Material3.CompoundButton.RadioButton 0x7f0f0107
int style Base.Widget.Material3.CompoundButton.Switch 0x7f0f0108
int style Base.Widget.Material3.ExtendedFloatingActionButton 0x7f0f0109
int style Base.Widget.Material3.ExtendedFloatingActionButton.Icon 0x7f0f010a
int style Base.Widget.Material3.FloatingActionButton 0x7f0f010b
int style Base.Widget.Material3.FloatingActionButton.Large 0x7f0f010c
int style Base.Widget.Material3.FloatingActionButton.Small 0x7f0f010d
int style Base.Widget.Material3.Light.ActionBar.Solid 0x7f0f010e
int style Base.Widget.Material3.MaterialCalendar.NavigationButton 0x7f0f010f
int style Base.Widget.Material3.Snackbar 0x7f0f0110
int style Base.Widget.Material3.TabLayout 0x7f0f0111
int style Base.Widget.Material3.TabLayout.OnSurface 0x7f0f0112
int style Base.Widget.Material3.TabLayout.Secondary 0x7f0f0113
int style Base.Widget.MaterialComponents.AutoCompleteTextView 0x7f0f0114
int style Base.Widget.MaterialComponents.CheckedTextView 0x7f0f0115
int style Base.Widget.MaterialComponents.Chip 0x7f0f0116
int style Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton 0x7f0f0117
int style Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton 0x7f0f0118
int style Base.Widget.MaterialComponents.PopupMenu 0x7f0f0119
int style Base.Widget.MaterialComponents.PopupMenu.ContextMenu 0x7f0f011a
int style Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow 0x7f0f011b
int style Base.Widget.MaterialComponents.PopupMenu.Overflow 0x7f0f011c
int style Base.Widget.MaterialComponents.Slider 0x7f0f011d
int style Base.Widget.MaterialComponents.Snackbar 0x7f0f011e
int style Base.Widget.MaterialComponents.TextInputEditText 0x7f0f011f
int style Base.Widget.MaterialComponents.TextInputLayout 0x7f0f0120
int style Base.Widget.MaterialComponents.TextView 0x7f0f0121
int style CardView 0x7f0f0122
int style CardView.Dark 0x7f0f0123
int style CardView.Light 0x7f0f0124
int style MainTheme 0x7f0f0125
int style MainTheme.Base 0x7f0f0126
int style MainTheme.NoActionBar 0x7f0f0127
int style MaterialAlertDialog.Material3 0x7f0f0128
int style MaterialAlertDialog.Material3.Animation 0x7f0f0129
int style MaterialAlertDialog.Material3.Body.Text 0x7f0f012a
int style MaterialAlertDialog.Material3.Body.Text.CenterStacked 0x7f0f012b
int style MaterialAlertDialog.Material3.Title.Icon 0x7f0f012c
int style MaterialAlertDialog.Material3.Title.Icon.CenterStacked 0x7f0f012d
int style MaterialAlertDialog.Material3.Title.Panel 0x7f0f012e
int style MaterialAlertDialog.Material3.Title.Panel.CenterStacked 0x7f0f012f
int style MaterialAlertDialog.Material3.Title.Text 0x7f0f0130
int style MaterialAlertDialog.Material3.Title.Text.CenterStacked 0x7f0f0131
int style MaterialAlertDialog.MaterialComponents 0x7f0f0132
int style MaterialAlertDialog.MaterialComponents.Body.Text 0x7f0f0133
int style MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar 0x7f0f0134
int style MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner 0x7f0f0135
int style MaterialAlertDialog.MaterialComponents.Title.Icon 0x7f0f0136
int style MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked 0x7f0f0137
int style MaterialAlertDialog.MaterialComponents.Title.Panel 0x7f0f0138
int style MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked 0x7f0f0139
int style MaterialAlertDialog.MaterialComponents.Title.Text 0x7f0f013a
int style MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked 0x7f0f013b
int style Maui.MainTheme 0x7f0f013c
int style Maui.MainTheme.Base 0x7f0f013d
int style Maui.MainTheme.NoActionBar 0x7f0f013e
int style Maui.SplashTheme 0x7f0f013f
int style MauiAlertDialogTheme 0x7f0f0140
int style MauiCheckBox 0x7f0f0141
int style MauiMaterialButton 0x7f0f0142
int style Platform.AppCompat 0x7f0f0143
int style Platform.AppCompat.Light 0x7f0f0144
int style Platform.MaterialComponents 0x7f0f0145
int style Platform.MaterialComponents.Dialog 0x7f0f0146
int style Platform.MaterialComponents.Light 0x7f0f0147
int style Platform.MaterialComponents.Light.Dialog 0x7f0f0148
int style Platform.ThemeOverlay.AppCompat 0x7f0f0149
int style Platform.ThemeOverlay.AppCompat.Dark 0x7f0f014a
int style Platform.ThemeOverlay.AppCompat.Light 0x7f0f014b
int style Platform.V21.AppCompat 0x7f0f014c
int style Platform.V21.AppCompat.Light 0x7f0f014d
int style Platform.V25.AppCompat 0x7f0f014e
int style Platform.V25.AppCompat.Light 0x7f0f014f
int style Platform.Widget.AppCompat.Spinner 0x7f0f0150
int style RtlOverlay.DialogWindowTitle.AppCompat 0x7f0f0151
int style RtlOverlay.Widget.AppCompat.ActionBar.TitleItem 0x7f0f0152
int style RtlOverlay.Widget.AppCompat.DialogTitle.Icon 0x7f0f0153
int style RtlOverlay.Widget.AppCompat.PopupMenuItem 0x7f0f0154
int style RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup 0x7f0f0155
int style RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut 0x7f0f0156
int style RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow 0x7f0f0157
int style RtlOverlay.Widget.AppCompat.PopupMenuItem.Text 0x7f0f0158
int style RtlOverlay.Widget.AppCompat.PopupMenuItem.Title 0x7f0f0159
int style RtlOverlay.Widget.AppCompat.Search.DropDown 0x7f0f015a
int style RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 0x7f0f015b
int style RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 0x7f0f015c
int style RtlOverlay.Widget.AppCompat.Search.DropDown.Query 0x7f0f015d
int style RtlOverlay.Widget.AppCompat.Search.DropDown.Text 0x7f0f015e
int style RtlOverlay.Widget.AppCompat.SearchView.MagIcon 0x7f0f015f
int style RtlUnderlay.Widget.AppCompat.ActionButton 0x7f0f0160
int style RtlUnderlay.Widget.AppCompat.ActionButton.Overflow 0x7f0f0161
int style ShapeAppearance.M3.Comp.Badge.Large.Shape 0x7f0f0162
int style ShapeAppearance.M3.Comp.Badge.Shape 0x7f0f0163
int style ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape 0x7f0f0164
int style ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape 0x7f0f0165
int style ShapeAppearance.M3.Comp.FilledButton.Container.Shape 0x7f0f0166
int style ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape 0x7f0f0167
int style ShapeAppearance.M3.Comp.NavigationBar.Container.Shape 0x7f0f0168
int style ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape 0x7f0f0169
int style ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape 0x7f0f016a
int style ShapeAppearance.M3.Comp.NavigationRail.Container.Shape 0x7f0f016b
int style ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape 0x7f0f016c
int style ShapeAppearance.M3.Comp.SearchBar.Container.Shape 0x7f0f016d
int style ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape 0x7f0f016e
int style ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape 0x7f0f016f
int style ShapeAppearance.M3.Comp.Switch.Handle.Shape 0x7f0f0170
int style ShapeAppearance.M3.Comp.Switch.StateLayer.Shape 0x7f0f0171
int style ShapeAppearance.M3.Comp.Switch.Track.Shape 0x7f0f0172
int style ShapeAppearance.M3.Comp.TextButton.Container.Shape 0x7f0f0173
int style ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge 0x7f0f0174
int style ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall 0x7f0f0175
int style ShapeAppearance.M3.Sys.Shape.Corner.Full 0x7f0f0176
int style ShapeAppearance.M3.Sys.Shape.Corner.Large 0x7f0f0177
int style ShapeAppearance.M3.Sys.Shape.Corner.Medium 0x7f0f0178
int style ShapeAppearance.M3.Sys.Shape.Corner.None 0x7f0f0179
int style ShapeAppearance.M3.Sys.Shape.Corner.Small 0x7f0f017a
int style ShapeAppearance.Material3.Corner.ExtraLarge 0x7f0f017b
int style ShapeAppearance.Material3.Corner.ExtraSmall 0x7f0f017c
int style ShapeAppearance.Material3.Corner.Full 0x7f0f017d
int style ShapeAppearance.Material3.Corner.Large 0x7f0f017e
int style ShapeAppearance.Material3.Corner.Medium 0x7f0f017f
int style ShapeAppearance.Material3.Corner.None 0x7f0f0180
int style ShapeAppearance.Material3.Corner.Small 0x7f0f0181
int style ShapeAppearance.Material3.LargeComponent 0x7f0f0182
int style ShapeAppearance.Material3.MediumComponent 0x7f0f0183
int style ShapeAppearance.Material3.NavigationBarView.ActiveIndicator 0x7f0f0184
int style ShapeAppearance.Material3.SmallComponent 0x7f0f0185
int style ShapeAppearance.Material3.Tooltip 0x7f0f0186
int style ShapeAppearance.MaterialComponents 0x7f0f0187
int style ShapeAppearance.MaterialComponents.Badge 0x7f0f0188
int style ShapeAppearance.MaterialComponents.LargeComponent 0x7f0f0189
int style ShapeAppearance.MaterialComponents.MediumComponent 0x7f0f018a
int style ShapeAppearance.MaterialComponents.SmallComponent 0x7f0f018b
int style ShapeAppearance.MaterialComponents.Tooltip 0x7f0f018c
int style ShapeAppearanceOverlay.Material3.Button 0x7f0f018d
int style ShapeAppearanceOverlay.Material3.Chip 0x7f0f018e
int style ShapeAppearanceOverlay.Material3.Corner.Bottom 0x7f0f018f
int style ShapeAppearanceOverlay.Material3.Corner.Left 0x7f0f0190
int style ShapeAppearanceOverlay.Material3.Corner.Right 0x7f0f0191
int style ShapeAppearanceOverlay.Material3.Corner.Top 0x7f0f0192
int style ShapeAppearanceOverlay.Material3.FloatingActionButton 0x7f0f0193
int style ShapeAppearanceOverlay.Material3.NavigationView.Item 0x7f0f0194
int style ShapeAppearanceOverlay.Material3.SearchBar 0x7f0f0195
int style ShapeAppearanceOverlay.Material3.SearchView 0x7f0f0196
int style ShapeAppearanceOverlay.MaterialAlertDialog.Material3 0x7f0f0197
int style ShapeAppearanceOverlay.MaterialComponents.BottomSheet 0x7f0f0198
int style ShapeAppearanceOverlay.MaterialComponents.Chip 0x7f0f0199
int style ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton 0x7f0f019a
int style ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton 0x7f0f019b
int style ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day 0x7f0f019c
int style ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen 0x7f0f019d
int style ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year 0x7f0f019e
int style ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox 0x7f0f019f
int style TextAppearance.AppCompat 0x7f0f01a0
int style TextAppearance.AppCompat.Body1 0x7f0f01a1
int style TextAppearance.AppCompat.Body2 0x7f0f01a2
int style TextAppearance.AppCompat.Button 0x7f0f01a3
int style TextAppearance.AppCompat.Caption 0x7f0f01a4
int style TextAppearance.AppCompat.Display1 0x7f0f01a5
int style TextAppearance.AppCompat.Display2 0x7f0f01a6
int style TextAppearance.AppCompat.Display3 0x7f0f01a7
int style TextAppearance.AppCompat.Display4 0x7f0f01a8
int style TextAppearance.AppCompat.Headline 0x7f0f01a9
int style TextAppearance.AppCompat.Inverse 0x7f0f01aa
int style TextAppearance.AppCompat.Large 0x7f0f01ab
int style TextAppearance.AppCompat.Large.Inverse 0x7f0f01ac
int style TextAppearance.AppCompat.Light.SearchResult.Subtitle 0x7f0f01ad
int style TextAppearance.AppCompat.Light.SearchResult.Title 0x7f0f01ae
int style TextAppearance.AppCompat.Light.Widget.PopupMenu.Large 0x7f0f01af
int style TextAppearance.AppCompat.Light.Widget.PopupMenu.Small 0x7f0f01b0
int style TextAppearance.AppCompat.Medium 0x7f0f01b1
int style TextAppearance.AppCompat.Medium.Inverse 0x7f0f01b2
int style TextAppearance.AppCompat.Menu 0x7f0f01b3
int style TextAppearance.AppCompat.SearchResult.Subtitle 0x7f0f01b4
int style TextAppearance.AppCompat.SearchResult.Title 0x7f0f01b5
int style TextAppearance.AppCompat.Small 0x7f0f01b6
int style TextAppearance.AppCompat.Small.Inverse 0x7f0f01b7
int style TextAppearance.AppCompat.Subhead 0x7f0f01b8
int style TextAppearance.AppCompat.Subhead.Inverse 0x7f0f01b9
int style TextAppearance.AppCompat.Title 0x7f0f01ba
int style TextAppearance.AppCompat.Title.Inverse 0x7f0f01bb
int style TextAppearance.AppCompat.Tooltip 0x7f0f01bc
int style TextAppearance.AppCompat.Widget.ActionBar.Menu 0x7f0f01bd
int style TextAppearance.AppCompat.Widget.ActionBar.Subtitle 0x7f0f01be
int style TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse 0x7f0f01bf
int style TextAppearance.AppCompat.Widget.ActionBar.Title 0x7f0f01c0
int style TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse 0x7f0f01c1
int style TextAppearance.AppCompat.Widget.ActionMode.Subtitle 0x7f0f01c2
int style TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse 0x7f0f01c3
int style TextAppearance.AppCompat.Widget.ActionMode.Title 0x7f0f01c4
int style TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse 0x7f0f01c5
int style TextAppearance.AppCompat.Widget.Button 0x7f0f01c6
int style TextAppearance.AppCompat.Widget.Button.Borderless.Colored 0x7f0f01c7
int style TextAppearance.AppCompat.Widget.Button.Colored 0x7f0f01c8
int style TextAppearance.AppCompat.Widget.Button.Inverse 0x7f0f01c9
int style TextAppearance.AppCompat.Widget.DropDownItem 0x7f0f01ca
int style TextAppearance.AppCompat.Widget.PopupMenu.Header 0x7f0f01cb
int style TextAppearance.AppCompat.Widget.PopupMenu.Large 0x7f0f01cc
int style TextAppearance.AppCompat.Widget.PopupMenu.Small 0x7f0f01cd
int style TextAppearance.AppCompat.Widget.Switch 0x7f0f01ce
int style TextAppearance.AppCompat.Widget.TextView.SpinnerItem 0x7f0f01cf
int style TextAppearance.Compat.Notification 0x7f0f01d0
int style TextAppearance.Compat.Notification.Info 0x7f0f01d1
int style TextAppearance.Compat.Notification.Line2 0x7f0f01d2
int style TextAppearance.Compat.Notification.Time 0x7f0f01d3
int style TextAppearance.Compat.Notification.Title 0x7f0f01d4
int style TextAppearance.Design.CollapsingToolbar.Expanded 0x7f0f01d5
int style TextAppearance.Design.Counter 0x7f0f01d6
int style TextAppearance.Design.Counter.Overflow 0x7f0f01d7
int style TextAppearance.Design.Error 0x7f0f01d8
int style TextAppearance.Design.HelperText 0x7f0f01d9
int style TextAppearance.Design.Hint 0x7f0f01da
int style TextAppearance.Design.Placeholder 0x7f0f01db
int style TextAppearance.Design.Prefix 0x7f0f01dc
int style TextAppearance.Design.Snackbar.Message 0x7f0f01dd
int style TextAppearance.Design.Suffix 0x7f0f01de
int style TextAppearance.Design.Tab 0x7f0f01df
int style TextAppearance.M3.Sys.Typescale.BodyLarge 0x7f0f01e0
int style TextAppearance.M3.Sys.Typescale.BodyMedium 0x7f0f01e1
int style TextAppearance.M3.Sys.Typescale.BodySmall 0x7f0f01e2
int style TextAppearance.M3.Sys.Typescale.DisplayLarge 0x7f0f01e3
int style TextAppearance.M3.Sys.Typescale.DisplayMedium 0x7f0f01e4
int style TextAppearance.M3.Sys.Typescale.DisplaySmall 0x7f0f01e5
int style TextAppearance.M3.Sys.Typescale.HeadlineLarge 0x7f0f01e6
int style TextAppearance.M3.Sys.Typescale.HeadlineMedium 0x7f0f01e7
int style TextAppearance.M3.Sys.Typescale.HeadlineSmall 0x7f0f01e8
int style TextAppearance.M3.Sys.Typescale.LabelLarge 0x7f0f01e9
int style TextAppearance.M3.Sys.Typescale.LabelMedium 0x7f0f01ea
int style TextAppearance.M3.Sys.Typescale.LabelSmall 0x7f0f01eb
int style TextAppearance.M3.Sys.Typescale.TitleLarge 0x7f0f01ec
int style TextAppearance.M3.Sys.Typescale.TitleMedium 0x7f0f01ed
int style TextAppearance.M3.Sys.Typescale.TitleSmall 0x7f0f01ee
int style TextAppearance.Material3.ActionBar.Subtitle 0x7f0f01ef
int style TextAppearance.Material3.ActionBar.Title 0x7f0f01f0
int style TextAppearance.Material3.BodyLarge 0x7f0f01f1
int style TextAppearance.Material3.BodyMedium 0x7f0f01f2
int style TextAppearance.Material3.BodySmall 0x7f0f01f3
int style TextAppearance.Material3.DisplayLarge 0x7f0f01f4
int style TextAppearance.Material3.DisplayMedium 0x7f0f01f5
int style TextAppearance.Material3.DisplaySmall 0x7f0f01f6
int style TextAppearance.Material3.HeadlineLarge 0x7f0f01f7
int style TextAppearance.Material3.HeadlineMedium 0x7f0f01f8
int style TextAppearance.Material3.HeadlineSmall 0x7f0f01f9
int style TextAppearance.Material3.LabelLarge 0x7f0f01fa
int style TextAppearance.Material3.LabelMedium 0x7f0f01fb
int style TextAppearance.Material3.LabelSmall 0x7f0f01fc
int style TextAppearance.Material3.MaterialTimePicker.Title 0x7f0f01fd
int style TextAppearance.Material3.SearchBar 0x7f0f01fe
int style TextAppearance.Material3.SearchView 0x7f0f01ff
int style TextAppearance.Material3.SearchView.Prefix 0x7f0f0200
int style TextAppearance.Material3.TitleLarge 0x7f0f0201
int style TextAppearance.Material3.TitleMedium 0x7f0f0202
int style TextAppearance.Material3.TitleSmall 0x7f0f0203
int style TextAppearance.MaterialComponents.Badge 0x7f0f0204
int style TextAppearance.MaterialComponents.Body1 0x7f0f0205
int style TextAppearance.MaterialComponents.Body2 0x7f0f0206
int style TextAppearance.MaterialComponents.Button 0x7f0f0207
int style TextAppearance.MaterialComponents.Caption 0x7f0f0208
int style TextAppearance.MaterialComponents.Chip 0x7f0f0209
int style TextAppearance.MaterialComponents.Headline1 0x7f0f020a
int style TextAppearance.MaterialComponents.Headline2 0x7f0f020b
int style TextAppearance.MaterialComponents.Headline3 0x7f0f020c
int style TextAppearance.MaterialComponents.Headline4 0x7f0f020d
int style TextAppearance.MaterialComponents.Headline5 0x7f0f020e
int style TextAppearance.MaterialComponents.Headline6 0x7f0f020f
int style TextAppearance.MaterialComponents.Overline 0x7f0f0210
int style TextAppearance.MaterialComponents.Subtitle1 0x7f0f0211
int style TextAppearance.MaterialComponents.Subtitle2 0x7f0f0212
int style TextAppearance.MaterialComponents.TimePicker.Title 0x7f0f0213
int style TextAppearance.MaterialComponents.Tooltip 0x7f0f0214
int style TextAppearance.Widget.AppCompat.ExpandedMenu.Item 0x7f0f0215
int style TextAppearance.Widget.AppCompat.Toolbar.Subtitle 0x7f0f0216
int style TextAppearance.Widget.AppCompat.Toolbar.Title 0x7f0f0217
int style Theme.AppCompat 0x7f0f0218
int style Theme.AppCompat.CompactMenu 0x7f0f0219
int style Theme.AppCompat.DayNight 0x7f0f021a
int style Theme.AppCompat.DayNight.DarkActionBar 0x7f0f021b
int style Theme.AppCompat.DayNight.Dialog 0x7f0f021c
int style Theme.AppCompat.DayNight.Dialog.Alert 0x7f0f021d
int style Theme.AppCompat.DayNight.Dialog.MinWidth 0x7f0f021e
int style Theme.AppCompat.DayNight.DialogWhenLarge 0x7f0f021f
int style Theme.AppCompat.DayNight.NoActionBar 0x7f0f0220
int style Theme.AppCompat.Dialog 0x7f0f0221
int style Theme.AppCompat.Dialog.Alert 0x7f0f0222
int style Theme.AppCompat.Dialog.MinWidth 0x7f0f0223
int style Theme.AppCompat.DialogWhenLarge 0x7f0f0224
int style Theme.AppCompat.Empty 0x7f0f0225
int style Theme.AppCompat.Light 0x7f0f0226
int style Theme.AppCompat.Light.DarkActionBar 0x7f0f0227
int style Theme.AppCompat.Light.Dialog 0x7f0f0228
int style Theme.AppCompat.Light.Dialog.Alert 0x7f0f0229
int style Theme.AppCompat.Light.Dialog.MinWidth 0x7f0f022a
int style Theme.AppCompat.Light.DialogWhenLarge 0x7f0f022b
int style Theme.AppCompat.Light.NoActionBar 0x7f0f022c
int style Theme.AppCompat.NoActionBar 0x7f0f022d
int style Theme.Design 0x7f0f022e
int style Theme.Design.BottomSheetDialog 0x7f0f022f
int style Theme.Design.Light 0x7f0f0230
int style Theme.Design.Light.BottomSheetDialog 0x7f0f0231
int style Theme.Design.Light.NoActionBar 0x7f0f0232
int style Theme.Design.NoActionBar 0x7f0f0233
int style Theme.Material3.Dark 0x7f0f0234
int style Theme.Material3.Dark.BottomSheetDialog 0x7f0f0235
int style Theme.Material3.Dark.Dialog 0x7f0f0236
int style Theme.Material3.Dark.Dialog.Alert 0x7f0f0237
int style Theme.Material3.Dark.Dialog.MinWidth 0x7f0f0238
int style Theme.Material3.Dark.DialogWhenLarge 0x7f0f0239
int style Theme.Material3.Dark.NoActionBar 0x7f0f023a
int style Theme.Material3.Dark.SideSheetDialog 0x7f0f023b
int style Theme.Material3.DayNight 0x7f0f023c
int style Theme.Material3.DayNight.BottomSheetDialog 0x7f0f023d
int style Theme.Material3.DayNight.Dialog 0x7f0f023e
int style Theme.Material3.DayNight.Dialog.Alert 0x7f0f023f
int style Theme.Material3.DayNight.Dialog.MinWidth 0x7f0f0240
int style Theme.Material3.DayNight.DialogWhenLarge 0x7f0f0241
int style Theme.Material3.DayNight.NoActionBar 0x7f0f0242
int style Theme.Material3.DayNight.SideSheetDialog 0x7f0f0243
int style Theme.Material3.DynamicColors.Dark 0x7f0f0244
int style Theme.Material3.DynamicColors.Dark.NoActionBar 0x7f0f0245
int style Theme.Material3.DynamicColors.DayNight 0x7f0f0246
int style Theme.Material3.DynamicColors.DayNight.NoActionBar 0x7f0f0247
int style Theme.Material3.DynamicColors.Light 0x7f0f0248
int style Theme.Material3.DynamicColors.Light.NoActionBar 0x7f0f0249
int style Theme.Material3.Light 0x7f0f024a
int style Theme.Material3.Light.BottomSheetDialog 0x7f0f024b
int style Theme.Material3.Light.Dialog 0x7f0f024c
int style Theme.Material3.Light.Dialog.Alert 0x7f0f024d
int style Theme.Material3.Light.Dialog.MinWidth 0x7f0f024e
int style Theme.Material3.Light.DialogWhenLarge 0x7f0f024f
int style Theme.Material3.Light.NoActionBar 0x7f0f0250
int style Theme.Material3.Light.SideSheetDialog 0x7f0f0251
int style Theme.MaterialComponents 0x7f0f0252
int style Theme.MaterialComponents.BottomSheetDialog 0x7f0f0253
int style Theme.MaterialComponents.Bridge 0x7f0f0254
int style Theme.MaterialComponents.CompactMenu 0x7f0f0255
int style Theme.MaterialComponents.DayNight 0x7f0f0256
int style Theme.MaterialComponents.DayNight.BottomSheetDialog 0x7f0f0257
int style Theme.MaterialComponents.DayNight.Bridge 0x7f0f0258
int style Theme.MaterialComponents.DayNight.DarkActionBar 0x7f0f0259
int style Theme.MaterialComponents.DayNight.DarkActionBar.Bridge 0x7f0f025a
int style Theme.MaterialComponents.DayNight.Dialog 0x7f0f025b
int style Theme.MaterialComponents.DayNight.Dialog.Alert 0x7f0f025c
int style Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge 0x7f0f025d
int style Theme.MaterialComponents.DayNight.Dialog.Bridge 0x7f0f025e
int style Theme.MaterialComponents.DayNight.Dialog.FixedSize 0x7f0f025f
int style Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge 0x7f0f0260
int style Theme.MaterialComponents.DayNight.Dialog.MinWidth 0x7f0f0261
int style Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge 0x7f0f0262
int style Theme.MaterialComponents.DayNight.DialogWhenLarge 0x7f0f0263
int style Theme.MaterialComponents.DayNight.NoActionBar 0x7f0f0264
int style Theme.MaterialComponents.DayNight.NoActionBar.Bridge 0x7f0f0265
int style Theme.MaterialComponents.Dialog 0x7f0f0266
int style Theme.MaterialComponents.Dialog.Alert 0x7f0f0267
int style Theme.MaterialComponents.Dialog.Alert.Bridge 0x7f0f0268
int style Theme.MaterialComponents.Dialog.Bridge 0x7f0f0269
int style Theme.MaterialComponents.Dialog.FixedSize 0x7f0f026a
int style Theme.MaterialComponents.Dialog.FixedSize.Bridge 0x7f0f026b
int style Theme.MaterialComponents.Dialog.MinWidth 0x7f0f026c
int style Theme.MaterialComponents.Dialog.MinWidth.Bridge 0x7f0f026d
int style Theme.MaterialComponents.DialogWhenLarge 0x7f0f026e
int style Theme.MaterialComponents.Light 0x7f0f026f
int style Theme.MaterialComponents.Light.BottomSheetDialog 0x7f0f0270
int style Theme.MaterialComponents.Light.Bridge 0x7f0f0271
int style Theme.MaterialComponents.Light.DarkActionBar 0x7f0f0272
int style Theme.MaterialComponents.Light.DarkActionBar.Bridge 0x7f0f0273
int style Theme.MaterialComponents.Light.Dialog 0x7f0f0274
int style Theme.MaterialComponents.Light.Dialog.Alert 0x7f0f0275
int style Theme.MaterialComponents.Light.Dialog.Alert.Bridge 0x7f0f0276
int style Theme.MaterialComponents.Light.Dialog.Bridge 0x7f0f0277
int style Theme.MaterialComponents.Light.Dialog.FixedSize 0x7f0f0278
int style Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge 0x7f0f0279
int style Theme.MaterialComponents.Light.Dialog.MinWidth 0x7f0f027a
int style Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge 0x7f0f027b
int style Theme.MaterialComponents.Light.DialogWhenLarge 0x7f0f027c
int style Theme.MaterialComponents.Light.NoActionBar 0x7f0f027d
int style Theme.MaterialComponents.Light.NoActionBar.Bridge 0x7f0f027e
int style Theme.MaterialComponents.NoActionBar 0x7f0f027f
int style Theme.MaterialComponents.NoActionBar.Bridge 0x7f0f0280
int style ThemeOverlay.AppCompat 0x7f0f0281
int style ThemeOverlay.AppCompat.ActionBar 0x7f0f0282
int style ThemeOverlay.AppCompat.Dark 0x7f0f0283
int style ThemeOverlay.AppCompat.Dark.ActionBar 0x7f0f0284
int style ThemeOverlay.AppCompat.DayNight 0x7f0f0285
int style ThemeOverlay.AppCompat.DayNight.ActionBar 0x7f0f0286
int style ThemeOverlay.AppCompat.Dialog 0x7f0f0287
int style ThemeOverlay.AppCompat.Dialog.Alert 0x7f0f0288
int style ThemeOverlay.AppCompat.Light 0x7f0f0289
int style ThemeOverlay.Design.TextInputEditText 0x7f0f028a
int style ThemeOverlay.Material3 0x7f0f028b
int style ThemeOverlay.Material3.ActionBar 0x7f0f028c
int style ThemeOverlay.Material3.AutoCompleteTextView 0x7f0f028d
int style ThemeOverlay.Material3.AutoCompleteTextView.FilledBox 0x7f0f028e
int style ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense 0x7f0f028f
int style ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox 0x7f0f0290
int style ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense 0x7f0f0291
int style ThemeOverlay.Material3.BottomAppBar 0x7f0f0292
int style ThemeOverlay.Material3.BottomAppBar.Legacy 0x7f0f0293
int style ThemeOverlay.Material3.BottomNavigationView 0x7f0f0294
int style ThemeOverlay.Material3.BottomSheetDialog 0x7f0f0295
int style ThemeOverlay.Material3.Button 0x7f0f0296
int style ThemeOverlay.Material3.Button.ElevatedButton 0x7f0f0297
int style ThemeOverlay.Material3.Button.IconButton 0x7f0f0298
int style ThemeOverlay.Material3.Button.IconButton.Filled 0x7f0f0299
int style ThemeOverlay.Material3.Button.IconButton.Filled.Tonal 0x7f0f029a
int style ThemeOverlay.Material3.Button.TextButton 0x7f0f029b
int style ThemeOverlay.Material3.Button.TextButton.Snackbar 0x7f0f029c
int style ThemeOverlay.Material3.Button.TonalButton 0x7f0f029d
int style ThemeOverlay.Material3.Chip 0x7f0f029e
int style ThemeOverlay.Material3.Chip.Assist 0x7f0f029f
int style ThemeOverlay.Material3.Dark 0x7f0f02a0
int style ThemeOverlay.Material3.Dark.ActionBar 0x7f0f02a1
int style ThemeOverlay.Material3.DayNight.BottomSheetDialog 0x7f0f02a2
int style ThemeOverlay.Material3.DayNight.SideSheetDialog 0x7f0f02a3
int style ThemeOverlay.Material3.Dialog 0x7f0f02a4
int style ThemeOverlay.Material3.Dialog.Alert 0x7f0f02a5
int style ThemeOverlay.Material3.Dialog.Alert.Framework 0x7f0f02a6
int style ThemeOverlay.Material3.DynamicColors.Dark 0x7f0f02a7
int style ThemeOverlay.Material3.DynamicColors.DayNight 0x7f0f02a8
int style ThemeOverlay.Material3.DynamicColors.Light 0x7f0f02a9
int style ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary 0x7f0f02aa
int style ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary 0x7f0f02ab
int style ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface 0x7f0f02ac
int style ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary 0x7f0f02ad
int style ThemeOverlay.Material3.FloatingActionButton.Primary 0x7f0f02ae
int style ThemeOverlay.Material3.FloatingActionButton.Secondary 0x7f0f02af
int style ThemeOverlay.Material3.FloatingActionButton.Surface 0x7f0f02b0
int style ThemeOverlay.Material3.FloatingActionButton.Tertiary 0x7f0f02b1
int style ThemeOverlay.Material3.HarmonizedColors 0x7f0f02b2
int style ThemeOverlay.Material3.HarmonizedColors.Empty 0x7f0f02b3
int style ThemeOverlay.Material3.Light 0x7f0f02b4
int style ThemeOverlay.Material3.Light.Dialog.Alert.Framework 0x7f0f02b5
int style ThemeOverlay.Material3.MaterialAlertDialog 0x7f0f02b6
int style ThemeOverlay.Material3.MaterialAlertDialog.Centered 0x7f0f02b7
int style ThemeOverlay.Material3.MaterialCalendar 0x7f0f02b8
int style ThemeOverlay.Material3.MaterialCalendar.Fullscreen 0x7f0f02b9
int style ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton 0x7f0f02ba
int style ThemeOverlay.Material3.MaterialTimePicker 0x7f0f02bb
int style ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText 0x7f0f02bc
int style ThemeOverlay.Material3.NavigationRailView 0x7f0f02bd
int style ThemeOverlay.Material3.NavigationView 0x7f0f02be
int style ThemeOverlay.Material3.PersonalizedColors 0x7f0f02bf
int style ThemeOverlay.Material3.Search 0x7f0f02c0
int style ThemeOverlay.Material3.SideSheetDialog 0x7f0f02c1
int style ThemeOverlay.Material3.Snackbar 0x7f0f02c2
int style ThemeOverlay.Material3.TabLayout 0x7f0f02c3
int style ThemeOverlay.Material3.TextInputEditText 0x7f0f02c4
int style ThemeOverlay.Material3.TextInputEditText.FilledBox 0x7f0f02c5
int style ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense 0x7f0f02c6
int style ThemeOverlay.Material3.TextInputEditText.OutlinedBox 0x7f0f02c7
int style ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense 0x7f0f02c8
int style ThemeOverlay.Material3.Toolbar.Surface 0x7f0f02c9
int style ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon 0x7f0f02ca
int style ThemeOverlay.MaterialComponents 0x7f0f02cb
int style ThemeOverlay.MaterialComponents.ActionBar 0x7f0f02cc
int style ThemeOverlay.MaterialComponents.ActionBar.Primary 0x7f0f02cd
int style ThemeOverlay.MaterialComponents.ActionBar.Surface 0x7f0f02ce
int style ThemeOverlay.MaterialComponents.AutoCompleteTextView 0x7f0f02cf
int style ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox 0x7f0f02d0
int style ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense 0x7f0f02d1
int style ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox 0x7f0f02d2
int style ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense 0x7f0f02d3
int style ThemeOverlay.MaterialComponents.BottomAppBar.Primary 0x7f0f02d4
int style ThemeOverlay.MaterialComponents.BottomAppBar.Surface 0x7f0f02d5
int style ThemeOverlay.MaterialComponents.BottomSheetDialog 0x7f0f02d6
int style ThemeOverlay.MaterialComponents.Dark 0x7f0f02d7
int style ThemeOverlay.MaterialComponents.Dark.ActionBar 0x7f0f02d8
int style ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog 0x7f0f02d9
int style ThemeOverlay.MaterialComponents.Dialog 0x7f0f02da
int style ThemeOverlay.MaterialComponents.Dialog.Alert 0x7f0f02db
int style ThemeOverlay.MaterialComponents.Dialog.Alert.Framework 0x7f0f02dc
int style ThemeOverlay.MaterialComponents.Light 0x7f0f02dd
int style ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework 0x7f0f02de
int style ThemeOverlay.MaterialComponents.MaterialAlertDialog 0x7f0f02df
int style ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered 0x7f0f02e0
int style ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date 0x7f0f02e1
int style ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar 0x7f0f02e2
int style ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text 0x7f0f02e3
int style ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day 0x7f0f02e4
int style ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner 0x7f0f02e5
int style ThemeOverlay.MaterialComponents.MaterialCalendar 0x7f0f02e6
int style ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen 0x7f0f02e7
int style ThemeOverlay.MaterialComponents.TextInputEditText 0x7f0f02e8
int style ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox 0x7f0f02e9
int style ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense 0x7f0f02ea
int style ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox 0x7f0f02eb
int style ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense 0x7f0f02ec
int style ThemeOverlay.MaterialComponents.TimePicker 0x7f0f02ed
int style ThemeOverlay.MaterialComponents.TimePicker.Display 0x7f0f02ee
int style ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText 0x7f0f02ef
int style ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary 0x7f0f02f0
int style ThemeOverlay.MaterialComponents.Toolbar.Primary 0x7f0f02f1
int style ThemeOverlay.MaterialComponents.Toolbar.Surface 0x7f0f02f2
int style Widget.AppCompat.ActionBar 0x7f0f02f3
int style Widget.AppCompat.ActionBar.Solid 0x7f0f02f4
int style Widget.AppCompat.ActionBar.TabBar 0x7f0f02f5
int style Widget.AppCompat.ActionBar.TabText 0x7f0f02f6
int style Widget.AppCompat.ActionBar.TabView 0x7f0f02f7
int style Widget.AppCompat.ActionButton 0x7f0f02f8
int style Widget.AppCompat.ActionButton.CloseMode 0x7f0f02f9
int style Widget.AppCompat.ActionButton.Overflow 0x7f0f02fa
int style Widget.AppCompat.ActionMode 0x7f0f02fb
int style Widget.AppCompat.ActivityChooserView 0x7f0f02fc
int style Widget.AppCompat.AutoCompleteTextView 0x7f0f02fd
int style Widget.AppCompat.Button 0x7f0f02fe
int style Widget.AppCompat.Button.Borderless 0x7f0f02ff
int style Widget.AppCompat.Button.Borderless.Colored 0x7f0f0300
int style Widget.AppCompat.Button.ButtonBar.AlertDialog 0x7f0f0301
int style Widget.AppCompat.Button.Colored 0x7f0f0302
int style Widget.AppCompat.Button.Small 0x7f0f0303
int style Widget.AppCompat.ButtonBar 0x7f0f0304
int style Widget.AppCompat.ButtonBar.AlertDialog 0x7f0f0305
int style Widget.AppCompat.CompoundButton.CheckBox 0x7f0f0306
int style Widget.AppCompat.CompoundButton.RadioButton 0x7f0f0307
int style Widget.AppCompat.CompoundButton.Switch 0x7f0f0308
int style Widget.AppCompat.DrawerArrowToggle 0x7f0f0309
int style Widget.AppCompat.DropDownItem.Spinner 0x7f0f030a
int style Widget.AppCompat.EditText 0x7f0f030b
int style Widget.AppCompat.ImageButton 0x7f0f030c
int style Widget.AppCompat.Light.ActionBar 0x7f0f030d
int style Widget.AppCompat.Light.ActionBar.Solid 0x7f0f030e
int style Widget.AppCompat.Light.ActionBar.Solid.Inverse 0x7f0f030f
int style Widget.AppCompat.Light.ActionBar.TabBar 0x7f0f0310
int style Widget.AppCompat.Light.ActionBar.TabBar.Inverse 0x7f0f0311
int style Widget.AppCompat.Light.ActionBar.TabText 0x7f0f0312
int style Widget.AppCompat.Light.ActionBar.TabText.Inverse 0x7f0f0313
int style Widget.AppCompat.Light.ActionBar.TabView 0x7f0f0314
int style Widget.AppCompat.Light.ActionBar.TabView.Inverse 0x7f0f0315
int style Widget.AppCompat.Light.ActionButton 0x7f0f0316
int style Widget.AppCompat.Light.ActionButton.CloseMode 0x7f0f0317
int style Widget.AppCompat.Light.ActionButton.Overflow 0x7f0f0318
int style Widget.AppCompat.Light.ActionMode.Inverse 0x7f0f0319
int style Widget.AppCompat.Light.ActivityChooserView 0x7f0f031a
int style Widget.AppCompat.Light.AutoCompleteTextView 0x7f0f031b
int style Widget.AppCompat.Light.DropDownItem.Spinner 0x7f0f031c
int style Widget.AppCompat.Light.ListPopupWindow 0x7f0f031d
int style Widget.AppCompat.Light.ListView.DropDown 0x7f0f031e
int style Widget.AppCompat.Light.PopupMenu 0x7f0f031f
int style Widget.AppCompat.Light.PopupMenu.Overflow 0x7f0f0320
int style Widget.AppCompat.Light.SearchView 0x7f0f0321
int style Widget.AppCompat.Light.Spinner.DropDown.ActionBar 0x7f0f0322
int style Widget.AppCompat.ListMenuView 0x7f0f0323
int style Widget.AppCompat.ListPopupWindow 0x7f0f0324
int style Widget.AppCompat.ListView 0x7f0f0325
int style Widget.AppCompat.ListView.DropDown 0x7f0f0326
int style Widget.AppCompat.ListView.Menu 0x7f0f0327
int style Widget.AppCompat.PopupMenu 0x7f0f0328
int style Widget.AppCompat.PopupMenu.Overflow 0x7f0f0329
int style Widget.AppCompat.PopupWindow 0x7f0f032a
int style Widget.AppCompat.ProgressBar 0x7f0f032b
int style Widget.AppCompat.ProgressBar.Horizontal 0x7f0f032c
int style Widget.AppCompat.RatingBar 0x7f0f032d
int style Widget.AppCompat.RatingBar.Indicator 0x7f0f032e
int style Widget.AppCompat.RatingBar.Small 0x7f0f032f
int style Widget.AppCompat.SearchView 0x7f0f0330
int style Widget.AppCompat.SearchView.ActionBar 0x7f0f0331
int style Widget.AppCompat.SeekBar 0x7f0f0332
int style Widget.AppCompat.SeekBar.Discrete 0x7f0f0333
int style Widget.AppCompat.Spinner 0x7f0f0334
int style Widget.AppCompat.Spinner.DropDown 0x7f0f0335
int style Widget.AppCompat.Spinner.DropDown.ActionBar 0x7f0f0336
int style Widget.AppCompat.Spinner.Underlined 0x7f0f0337
int style Widget.AppCompat.TextView 0x7f0f0338
int style Widget.AppCompat.TextView.SpinnerItem 0x7f0f0339
int style Widget.AppCompat.Toolbar 0x7f0f033a
int style Widget.AppCompat.Toolbar.Button.Navigation 0x7f0f033b
int style Widget.Compat.NotificationActionContainer 0x7f0f033c
int style Widget.Compat.NotificationActionText 0x7f0f033d
int style Widget.Design.AppBarLayout 0x7f0f033e
int style Widget.Design.BottomNavigationView 0x7f0f033f
int style Widget.Design.BottomSheet.Modal 0x7f0f0340
int style Widget.Design.CollapsingToolbar 0x7f0f0341
int style Widget.Design.FloatingActionButton 0x7f0f0342
int style Widget.Design.NavigationView 0x7f0f0343
int style Widget.Design.ScrimInsetsFrameLayout 0x7f0f0344
int style Widget.Design.Snackbar 0x7f0f0345
int style Widget.Design.TabLayout 0x7f0f0346
int style Widget.Design.TextInputEditText 0x7f0f0347
int style Widget.Design.TextInputLayout 0x7f0f0348
int style Widget.Material3.ActionBar.Solid 0x7f0f0349
int style Widget.Material3.ActionMode 0x7f0f034a
int style Widget.Material3.AppBarLayout 0x7f0f034b
int style Widget.Material3.AutoCompleteTextView.FilledBox 0x7f0f034c
int style Widget.Material3.AutoCompleteTextView.FilledBox.Dense 0x7f0f034d
int style Widget.Material3.AutoCompleteTextView.OutlinedBox 0x7f0f034e
int style Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense 0x7f0f034f
int style Widget.Material3.Badge 0x7f0f0350
int style Widget.Material3.Badge.AdjustToBounds 0x7f0f0351
int style Widget.Material3.BottomAppBar 0x7f0f0352
int style Widget.Material3.BottomAppBar.Button.Navigation 0x7f0f0353
int style Widget.Material3.BottomAppBar.Legacy 0x7f0f0354
int style Widget.Material3.BottomNavigation.Badge 0x7f0f0355
int style Widget.Material3.BottomNavigationView 0x7f0f0356
int style Widget.Material3.BottomNavigationView.ActiveIndicator 0x7f0f0357
int style Widget.Material3.BottomSheet 0x7f0f0358
int style Widget.Material3.BottomSheet.DragHandle 0x7f0f0359
int style Widget.Material3.BottomSheet.Modal 0x7f0f035a
int style Widget.Material3.Button 0x7f0f035b
int style Widget.Material3.Button.ElevatedButton 0x7f0f035c
int style Widget.Material3.Button.ElevatedButton.Icon 0x7f0f035d
int style Widget.Material3.Button.Icon 0x7f0f035e
int style Widget.Material3.Button.IconButton 0x7f0f035f
int style Widget.Material3.Button.IconButton.Filled 0x7f0f0360
int style Widget.Material3.Button.IconButton.Filled.Tonal 0x7f0f0361
int style Widget.Material3.Button.IconButton.Outlined 0x7f0f0362
int style Widget.Material3.Button.OutlinedButton 0x7f0f0363
int style Widget.Material3.Button.OutlinedButton.Icon 0x7f0f0364
int style Widget.Material3.Button.TextButton 0x7f0f0365
int style Widget.Material3.Button.TextButton.Dialog 0x7f0f0366
int style Widget.Material3.Button.TextButton.Dialog.Flush 0x7f0f0367
int style Widget.Material3.Button.TextButton.Dialog.Icon 0x7f0f0368
int style Widget.Material3.Button.TextButton.Icon 0x7f0f0369
int style Widget.Material3.Button.TextButton.Snackbar 0x7f0f036a
int style Widget.Material3.Button.TonalButton 0x7f0f036b
int style Widget.Material3.Button.TonalButton.Icon 0x7f0f036c
int style Widget.Material3.Button.UnelevatedButton 0x7f0f036d
int style Widget.Material3.CardView.Elevated 0x7f0f036e
int style Widget.Material3.CardView.Filled 0x7f0f036f
int style Widget.Material3.CardView.Outlined 0x7f0f0370
int style Widget.Material3.CheckedTextView 0x7f0f0371
int style Widget.Material3.Chip.Assist 0x7f0f0372
int style Widget.Material3.Chip.Assist.Elevated 0x7f0f0373
int style Widget.Material3.Chip.Filter 0x7f0f0374
int style Widget.Material3.Chip.Filter.Elevated 0x7f0f0375
int style Widget.Material3.Chip.Input 0x7f0f0376
int style Widget.Material3.Chip.Input.Elevated 0x7f0f0377
int style Widget.Material3.Chip.Input.Icon 0x7f0f0378
int style Widget.Material3.Chip.Input.Icon.Elevated 0x7f0f0379
int style Widget.Material3.Chip.Suggestion 0x7f0f037a
int style Widget.Material3.Chip.Suggestion.Elevated 0x7f0f037b
int style Widget.Material3.ChipGroup 0x7f0f037c
int style Widget.Material3.CircularProgressIndicator 0x7f0f037d
int style Widget.Material3.CircularProgressIndicator.ExtraSmall 0x7f0f037e
int style Widget.Material3.CircularProgressIndicator.Legacy 0x7f0f037f
int style Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall 0x7f0f0380
int style Widget.Material3.CircularProgressIndicator.Legacy.Medium 0x7f0f0381
int style Widget.Material3.CircularProgressIndicator.Legacy.Small 0x7f0f0382
int style Widget.Material3.CircularProgressIndicator.Medium 0x7f0f0383
int style Widget.Material3.CircularProgressIndicator.Small 0x7f0f0384
int style Widget.Material3.CollapsingToolbar 0x7f0f0385
int style Widget.Material3.CollapsingToolbar.Large 0x7f0f0386
int style Widget.Material3.CollapsingToolbar.Medium 0x7f0f0387
int style Widget.Material3.CompoundButton.CheckBox 0x7f0f0388
int style Widget.Material3.CompoundButton.MaterialSwitch 0x7f0f0389
int style Widget.Material3.CompoundButton.RadioButton 0x7f0f038a
int style Widget.Material3.CompoundButton.Switch 0x7f0f038b
int style Widget.Material3.DrawerLayout 0x7f0f038c
int style Widget.Material3.ExtendedFloatingActionButton.Icon.Primary 0x7f0f038d
int style Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary 0x7f0f038e
int style Widget.Material3.ExtendedFloatingActionButton.Icon.Surface 0x7f0f038f
int style Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary 0x7f0f0390
int style Widget.Material3.ExtendedFloatingActionButton.Primary 0x7f0f0391
int style Widget.Material3.ExtendedFloatingActionButton.Secondary 0x7f0f0392
int style Widget.Material3.ExtendedFloatingActionButton.Surface 0x7f0f0393
int style Widget.Material3.ExtendedFloatingActionButton.Tertiary 0x7f0f0394
int style Widget.Material3.FloatingActionButton.Large.Primary 0x7f0f0395
int style Widget.Material3.FloatingActionButton.Large.Secondary 0x7f0f0396
int style Widget.Material3.FloatingActionButton.Large.Surface 0x7f0f0397
int style Widget.Material3.FloatingActionButton.Large.Tertiary 0x7f0f0398
int style Widget.Material3.FloatingActionButton.Primary 0x7f0f0399
int style Widget.Material3.FloatingActionButton.Secondary 0x7f0f039a
int style Widget.Material3.FloatingActionButton.Small.Primary 0x7f0f039b
int style Widget.Material3.FloatingActionButton.Small.Secondary 0x7f0f039c
int style Widget.Material3.FloatingActionButton.Small.Surface 0x7f0f039d
int style Widget.Material3.FloatingActionButton.Small.Tertiary 0x7f0f039e
int style Widget.Material3.FloatingActionButton.Surface 0x7f0f039f
int style Widget.Material3.FloatingActionButton.Tertiary 0x7f0f03a0
int style Widget.Material3.Light.ActionBar.Solid 0x7f0f03a1
int style Widget.Material3.LinearProgressIndicator 0x7f0f03a2
int style Widget.Material3.LinearProgressIndicator.Legacy 0x7f0f03a3
int style Widget.Material3.MaterialButtonToggleGroup 0x7f0f03a4
int style Widget.Material3.MaterialCalendar 0x7f0f03a5
int style Widget.Material3.MaterialCalendar.Day 0x7f0f03a6
int style Widget.Material3.MaterialCalendar.Day.Invalid 0x7f0f03a7
int style Widget.Material3.MaterialCalendar.Day.Selected 0x7f0f03a8
int style Widget.Material3.MaterialCalendar.Day.Today 0x7f0f03a9
int style Widget.Material3.MaterialCalendar.DayOfWeekLabel 0x7f0f03aa
int style Widget.Material3.MaterialCalendar.DayTextView 0x7f0f03ab
int style Widget.Material3.MaterialCalendar.Fullscreen 0x7f0f03ac
int style Widget.Material3.MaterialCalendar.HeaderCancelButton 0x7f0f03ad
int style Widget.Material3.MaterialCalendar.HeaderDivider 0x7f0f03ae
int style Widget.Material3.MaterialCalendar.HeaderLayout 0x7f0f03af
int style Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen 0x7f0f03b0
int style Widget.Material3.MaterialCalendar.HeaderSelection 0x7f0f03b1
int style Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen 0x7f0f03b2
int style Widget.Material3.MaterialCalendar.HeaderTitle 0x7f0f03b3
int style Widget.Material3.MaterialCalendar.HeaderToggleButton 0x7f0f03b4
int style Widget.Material3.MaterialCalendar.Item 0x7f0f03b5
int style Widget.Material3.MaterialCalendar.MonthNavigationButton 0x7f0f03b6
int style Widget.Material3.MaterialCalendar.MonthTextView 0x7f0f03b7
int style Widget.Material3.MaterialCalendar.Year 0x7f0f03b8
int style Widget.Material3.MaterialCalendar.Year.Selected 0x7f0f03b9
int style Widget.Material3.MaterialCalendar.Year.Today 0x7f0f03ba
int style Widget.Material3.MaterialCalendar.YearNavigationButton 0x7f0f03bb
int style Widget.Material3.MaterialDivider 0x7f0f03bc
int style Widget.Material3.MaterialDivider.Heavy 0x7f0f03bd
int style Widget.Material3.MaterialTimePicker 0x7f0f03be
int style Widget.Material3.MaterialTimePicker.Button 0x7f0f03bf
int style Widget.Material3.MaterialTimePicker.Clock 0x7f0f03c0
int style Widget.Material3.MaterialTimePicker.Display 0x7f0f03c1
int style Widget.Material3.MaterialTimePicker.Display.Divider 0x7f0f03c2
int style Widget.Material3.MaterialTimePicker.Display.HelperText 0x7f0f03c3
int style Widget.Material3.MaterialTimePicker.Display.TextInputEditText 0x7f0f03c4
int style Widget.Material3.MaterialTimePicker.Display.TextInputLayout 0x7f0f03c5
int style Widget.Material3.MaterialTimePicker.ImageButton 0x7f0f03c6
int style Widget.Material3.NavigationRailView 0x7f0f03c7
int style Widget.Material3.NavigationRailView.ActiveIndicator 0x7f0f03c8
int style Widget.Material3.NavigationRailView.Badge 0x7f0f03c9
int style Widget.Material3.NavigationView 0x7f0f03ca
int style Widget.Material3.PopupMenu 0x7f0f03cb
int style Widget.Material3.PopupMenu.ContextMenu 0x7f0f03cc
int style Widget.Material3.PopupMenu.ListPopupWindow 0x7f0f03cd
int style Widget.Material3.PopupMenu.Overflow 0x7f0f03ce
int style Widget.Material3.Search.ActionButton.Overflow 0x7f0f03cf
int style Widget.Material3.Search.Toolbar.Button.Navigation 0x7f0f03d0
int style Widget.Material3.SearchBar 0x7f0f03d1
int style Widget.Material3.SearchBar.Outlined 0x7f0f03d2
int style Widget.Material3.SearchView 0x7f0f03d3
int style Widget.Material3.SearchView.Prefix 0x7f0f03d4
int style Widget.Material3.SearchView.Toolbar 0x7f0f03d5
int style Widget.Material3.SideSheet 0x7f0f03d6
int style Widget.Material3.SideSheet.Detached 0x7f0f03d7
int style Widget.Material3.SideSheet.Modal 0x7f0f03d8
int style Widget.Material3.SideSheet.Modal.Detached 0x7f0f03d9
int style Widget.Material3.Slider 0x7f0f03da
int style Widget.Material3.Slider.Label 0x7f0f03db
int style Widget.Material3.Slider.Legacy 0x7f0f03dc
int style Widget.Material3.Slider.Legacy.Label 0x7f0f03dd
int style Widget.Material3.Snackbar 0x7f0f03de
int style Widget.Material3.Snackbar.FullWidth 0x7f0f03df
int style Widget.Material3.Snackbar.TextView 0x7f0f03e0
int style Widget.Material3.TabLayout 0x7f0f03e1
int style Widget.Material3.TabLayout.OnSurface 0x7f0f03e2
int style Widget.Material3.TabLayout.Secondary 0x7f0f03e3
int style Widget.Material3.TextInputEditText.FilledBox 0x7f0f03e4
int style Widget.Material3.TextInputEditText.FilledBox.Dense 0x7f0f03e5
int style Widget.Material3.TextInputEditText.OutlinedBox 0x7f0f03e6
int style Widget.Material3.TextInputEditText.OutlinedBox.Dense 0x7f0f03e7
int style Widget.Material3.TextInputLayout.FilledBox 0x7f0f03e8
int style Widget.Material3.TextInputLayout.FilledBox.Dense 0x7f0f03e9
int style Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu 0x7f0f03ea
int style Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu 0x7f0f03eb
int style Widget.Material3.TextInputLayout.OutlinedBox 0x7f0f03ec
int style Widget.Material3.TextInputLayout.OutlinedBox.Dense 0x7f0f03ed
int style Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu 0x7f0f03ee
int style Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu 0x7f0f03ef
int style Widget.Material3.Toolbar 0x7f0f03f0
int style Widget.Material3.Toolbar.OnSurface 0x7f0f03f1
int style Widget.Material3.Toolbar.Surface 0x7f0f03f2
int style Widget.Material3.Tooltip 0x7f0f03f3
int style Widget.MaterialComponents.ActionBar.Primary 0x7f0f03f4
int style Widget.MaterialComponents.ActionBar.PrimarySurface 0x7f0f03f5
int style Widget.MaterialComponents.ActionBar.Solid 0x7f0f03f6
int style Widget.MaterialComponents.ActionBar.Surface 0x7f0f03f7
int style Widget.MaterialComponents.ActionMode 0x7f0f03f8
int style Widget.MaterialComponents.AppBarLayout.Primary 0x7f0f03f9
int style Widget.MaterialComponents.AppBarLayout.PrimarySurface 0x7f0f03fa
int style Widget.MaterialComponents.AppBarLayout.Surface 0x7f0f03fb
int style Widget.MaterialComponents.AutoCompleteTextView.FilledBox 0x7f0f03fc
int style Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense 0x7f0f03fd
int style Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox 0x7f0f03fe
int style Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense 0x7f0f03ff
int style Widget.MaterialComponents.Badge 0x7f0f0400
int style Widget.MaterialComponents.BottomAppBar 0x7f0f0401
int style Widget.MaterialComponents.BottomAppBar.Colored 0x7f0f0402
int style Widget.MaterialComponents.BottomAppBar.PrimarySurface 0x7f0f0403
int style Widget.MaterialComponents.BottomNavigationView 0x7f0f0404
int style Widget.MaterialComponents.BottomNavigationView.Colored 0x7f0f0405
int style Widget.MaterialComponents.BottomNavigationView.PrimarySurface 0x7f0f0406
int style Widget.MaterialComponents.BottomSheet 0x7f0f0407
int style Widget.MaterialComponents.BottomSheet.Modal 0x7f0f0408
int style Widget.MaterialComponents.Button 0x7f0f0409
int style Widget.MaterialComponents.Button.Icon 0x7f0f040a
int style Widget.MaterialComponents.Button.OutlinedButton 0x7f0f040b
int style Widget.MaterialComponents.Button.OutlinedButton.Icon 0x7f0f040c
int style Widget.MaterialComponents.Button.TextButton 0x7f0f040d
int style Widget.MaterialComponents.Button.TextButton.Dialog 0x7f0f040e
int style Widget.MaterialComponents.Button.TextButton.Dialog.Flush 0x7f0f040f
int style Widget.MaterialComponents.Button.TextButton.Dialog.Icon 0x7f0f0410
int style Widget.MaterialComponents.Button.TextButton.Icon 0x7f0f0411
int style Widget.MaterialComponents.Button.TextButton.Snackbar 0x7f0f0412
int style Widget.MaterialComponents.Button.UnelevatedButton 0x7f0f0413
int style Widget.MaterialComponents.Button.UnelevatedButton.Icon 0x7f0f0414
int style Widget.MaterialComponents.CardView 0x7f0f0415
int style Widget.MaterialComponents.CheckedTextView 0x7f0f0416
int style Widget.MaterialComponents.Chip.Action 0x7f0f0417
int style Widget.MaterialComponents.Chip.Choice 0x7f0f0418
int style Widget.MaterialComponents.Chip.Entry 0x7f0f0419
int style Widget.MaterialComponents.Chip.Filter 0x7f0f041a
int style Widget.MaterialComponents.ChipGroup 0x7f0f041b
int style Widget.MaterialComponents.CircularProgressIndicator 0x7f0f041c
int style Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall 0x7f0f041d
int style Widget.MaterialComponents.CircularProgressIndicator.Medium 0x7f0f041e
int style Widget.MaterialComponents.CircularProgressIndicator.Small 0x7f0f041f
int style Widget.MaterialComponents.CollapsingToolbar 0x7f0f0420
int style Widget.MaterialComponents.CompoundButton.CheckBox 0x7f0f0421
int style Widget.MaterialComponents.CompoundButton.RadioButton 0x7f0f0422
int style Widget.MaterialComponents.CompoundButton.Switch 0x7f0f0423
int style Widget.MaterialComponents.ExtendedFloatingActionButton 0x7f0f0424
int style Widget.MaterialComponents.ExtendedFloatingActionButton.Icon 0x7f0f0425
int style Widget.MaterialComponents.FloatingActionButton 0x7f0f0426
int style Widget.MaterialComponents.Light.ActionBar.Solid 0x7f0f0427
int style Widget.MaterialComponents.LinearProgressIndicator 0x7f0f0428
int style Widget.MaterialComponents.MaterialButtonToggleGroup 0x7f0f0429
int style Widget.MaterialComponents.MaterialCalendar 0x7f0f042a
int style Widget.MaterialComponents.MaterialCalendar.Day 0x7f0f042b
int style Widget.MaterialComponents.MaterialCalendar.Day.Invalid 0x7f0f042c
int style Widget.MaterialComponents.MaterialCalendar.Day.Selected 0x7f0f042d
int style Widget.MaterialComponents.MaterialCalendar.Day.Today 0x7f0f042e
int style Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel 0x7f0f042f
int style Widget.MaterialComponents.MaterialCalendar.DayTextView 0x7f0f0430
int style Widget.MaterialComponents.MaterialCalendar.Fullscreen 0x7f0f0431
int style Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton 0x7f0f0432
int style Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton 0x7f0f0433
int style Widget.MaterialComponents.MaterialCalendar.HeaderDivider 0x7f0f0434
int style Widget.MaterialComponents.MaterialCalendar.HeaderLayout 0x7f0f0435
int style Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen 0x7f0f0436
int style Widget.MaterialComponents.MaterialCalendar.HeaderSelection 0x7f0f0437
int style Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen 0x7f0f0438
int style Widget.MaterialComponents.MaterialCalendar.HeaderTitle 0x7f0f0439
int style Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton 0x7f0f043a
int style Widget.MaterialComponents.MaterialCalendar.Item 0x7f0f043b
int style Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton 0x7f0f043c
int style Widget.MaterialComponents.MaterialCalendar.MonthTextView 0x7f0f043d
int style Widget.MaterialComponents.MaterialCalendar.Year 0x7f0f043e
int style Widget.MaterialComponents.MaterialCalendar.Year.Selected 0x7f0f043f
int style Widget.MaterialComponents.MaterialCalendar.Year.Today 0x7f0f0440
int style Widget.MaterialComponents.MaterialCalendar.YearNavigationButton 0x7f0f0441
int style Widget.MaterialComponents.MaterialDivider 0x7f0f0442
int style Widget.MaterialComponents.NavigationRailView 0x7f0f0443
int style Widget.MaterialComponents.NavigationRailView.Colored 0x7f0f0444
int style Widget.MaterialComponents.NavigationRailView.Colored.Compact 0x7f0f0445
int style Widget.MaterialComponents.NavigationRailView.Compact 0x7f0f0446
int style Widget.MaterialComponents.NavigationRailView.PrimarySurface 0x7f0f0447
int style Widget.MaterialComponents.NavigationView 0x7f0f0448
int style Widget.MaterialComponents.PopupMenu 0x7f0f0449
int style Widget.MaterialComponents.PopupMenu.ContextMenu 0x7f0f044a
int style Widget.MaterialComponents.PopupMenu.ListPopupWindow 0x7f0f044b
int style Widget.MaterialComponents.PopupMenu.Overflow 0x7f0f044c
int style Widget.MaterialComponents.ProgressIndicator 0x7f0f044d
int style Widget.MaterialComponents.ShapeableImageView 0x7f0f044e
int style Widget.MaterialComponents.Slider 0x7f0f044f
int style Widget.MaterialComponents.Snackbar 0x7f0f0450
int style Widget.MaterialComponents.Snackbar.FullWidth 0x7f0f0451
int style Widget.MaterialComponents.Snackbar.TextView 0x7f0f0452
int style Widget.MaterialComponents.TabLayout 0x7f0f0453
int style Widget.MaterialComponents.TabLayout.Colored 0x7f0f0454
int style Widget.MaterialComponents.TabLayout.PrimarySurface 0x7f0f0455
int style Widget.MaterialComponents.TextInputEditText.FilledBox 0x7f0f0456
int style Widget.MaterialComponents.TextInputEditText.FilledBox.Dense 0x7f0f0457
int style Widget.MaterialComponents.TextInputEditText.OutlinedBox 0x7f0f0458
int style Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense 0x7f0f0459
int style Widget.MaterialComponents.TextInputLayout.FilledBox 0x7f0f045a
int style Widget.MaterialComponents.TextInputLayout.FilledBox.Dense 0x7f0f045b
int style Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu 0x7f0f045c
int style Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu 0x7f0f045d
int style Widget.MaterialComponents.TextInputLayout.OutlinedBox 0x7f0f045e
int style Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense 0x7f0f045f
int style Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu 0x7f0f0460
int style Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu 0x7f0f0461
int style Widget.MaterialComponents.TextView 0x7f0f0462
int style Widget.MaterialComponents.TimePicker 0x7f0f0463
int style Widget.MaterialComponents.TimePicker.Button 0x7f0f0464
int style Widget.MaterialComponents.TimePicker.Clock 0x7f0f0465
int style Widget.MaterialComponents.TimePicker.Display 0x7f0f0466
int style Widget.MaterialComponents.TimePicker.Display.Divider 0x7f0f0467
int style Widget.MaterialComponents.TimePicker.Display.HelperText 0x7f0f0468
int style Widget.MaterialComponents.TimePicker.Display.TextInputEditText 0x7f0f0469
int style Widget.MaterialComponents.TimePicker.Display.TextInputLayout 0x7f0f046a
int style Widget.MaterialComponents.TimePicker.ImageButton 0x7f0f046b
int style Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance 0x7f0f046c
int style Widget.MaterialComponents.Toolbar 0x7f0f046d
int style Widget.MaterialComponents.Toolbar.Primary 0x7f0f046e
int style Widget.MaterialComponents.Toolbar.PrimarySurface 0x7f0f046f
int style Widget.MaterialComponents.Toolbar.Surface 0x7f0f0470
int style Widget.MaterialComponents.Tooltip 0x7f0f0471
int style Widget.Support.CoordinatorLayout 0x7f0f0472
int style collectionViewTheme 0x7f0f0473
int style scrollViewScrollBars 0x7f0f0474
int style scrollViewTheme 0x7f0f0475
int[] styleable ActionBar { 0x7f030049, 0x7f030050, 0x7f030051, 0x7f03013e, 0x7f03013f, 0x7f030140, 0x7f030141, 0x7f030142, 0x7f030143, 0x7f03016c, 0x7f030183, 0x7f030184, 0x7f0301a4, 0x7f03022c, 0x7f030234, 0x7f03023a, 0x7f03023b, 0x7f03023f, 0x7f030250, 0x7f030267, 0x7f0302e5, 0x7f030369, 0x7f0303a2, 0x7f0303aa, 0x7f0303ab, 0x7f030430, 0x7f030434, 0x7f0304bf, 0x7f0304cd }
int[] styleable ActionBarLayout { 0x00000000 }
int styleable ActionBarLayout_android_layout_gravity 0
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionMenuItemView { 0x00000000 }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView {  }
int[] styleable ActionMode { 0x7f030049, 0x7f030050, 0x7f0300e9, 0x7f03022c, 0x7f030434, 0x7f0304cd }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0301c1, 0x7f030257 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityFilter { 0x7f030028, 0x7f03002a }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityNavigator { 0x7f030002, 0x00000000, 0x7f030170, 0x7f030171, 0x7f03045d }
int styleable ActivityNavigator_action 0
int styleable ActivityNavigator_android_name 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable ActivityRule { 0x7f030034, 0x7f03045b }
int styleable ActivityRule_alwaysExpand 0
int styleable ActivityRule_tag 1
int[] styleable AlertDialog { 0x00000000, 0x7f030095, 0x7f030098, 0x7f0302da, 0x7f0302db, 0x7f030364, 0x7f0303ee, 0x7f0303f6 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int styleable AnimatedStateListDrawableCompat_android_constantSize 0
int styleable AnimatedStateListDrawableCompat_android_dither 1
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
int styleable AnimatedStateListDrawableCompat_android_visible 5
int[] styleable AnimatedStateListDrawableItem { 0x00000000, 0x00000000 }
int styleable AnimatedStateListDrawableItem_android_drawable 0
int styleable AnimatedStateListDrawableItem_android_id 1
int[] styleable AnimatedStateListDrawableTransition { 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_fromId 1
int styleable AnimatedStateListDrawableTransition_android_reversible 2
int styleable AnimatedStateListDrawableTransition_android_toId 3
int[] styleable AppBarLayout { 0x00000000, 0x00000000, 0x00000000, 0x7f0301a4, 0x7f0301c2, 0x7f0302cf, 0x7f0302d0, 0x7f0302d1, 0x7f030425 }
int[] styleable AppBarLayoutStates { 0x7f03041c, 0x7f03041d, 0x7f030421, 0x7f030422 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f0302cb, 0x7f0302cc, 0x7f0302cd }
int styleable AppBarLayout_Layout_layout_scrollEffect 0
int styleable AppBarLayout_Layout_layout_scrollFlags 1
int styleable AppBarLayout_Layout_layout_scrollInterpolator 2
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_keyboardNavigationCluster 1
int styleable AppBarLayout_android_touchscreenBlocksFocus 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollColor 6
int styleable AppBarLayout_liftOnScrollTargetViewId 7
int styleable AppBarLayout_statusBarForeground 8
int[] styleable AppCompatEmojiHelper {  }
int[] styleable AppCompatImageView { 0x00000000, 0x7f03040f, 0x7f0304bc, 0x7f0304bd }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x00000000, 0x7f0304b6, 0x7f0304b7, 0x7f0304b8 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int styleable AppCompatTextHelper_android_drawableBottom 0
int styleable AppCompatTextHelper_android_drawableEnd 1
int styleable AppCompatTextHelper_android_drawableLeft 2
int styleable AppCompatTextHelper_android_drawableRight 3
int styleable AppCompatTextHelper_android_drawableStart 4
int styleable AppCompatTextHelper_android_drawableTop 5
int styleable AppCompatTextHelper_android_textAppearance 6
int[] styleable AppCompatTextView { 0x00000000, 0x7f030043, 0x7f030044, 0x7f030045, 0x7f030046, 0x7f030047, 0x7f030190, 0x7f030191, 0x7f030192, 0x7f030193, 0x7f030195, 0x7f030196, 0x7f030197, 0x7f030198, 0x7f0301a8, 0x7f0301e5, 0x7f030209, 0x7f030213, 0x7f030283, 0x7f0302d3, 0x7f030461, 0x7f030498 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f03000f, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030023, 0x7f030029, 0x7f03002c, 0x7f03002d, 0x7f03002e, 0x7f03002f, 0x00000000, 0x00000000, 0x7f030042, 0x7f030079, 0x7f03008d, 0x7f03008e, 0x7f03008f, 0x7f030090, 0x7f030091, 0x7f030099, 0x7f03009a, 0x7f0300b4, 0x7f0300bf, 0x7f0300f7, 0x7f0300f8, 0x7f0300f9, 0x7f0300fb, 0x7f0300fc, 0x7f0300fd, 0x7f0300fe, 0x7f030117, 0x7f030119, 0x7f03012e, 0x7f03014d, 0x7f030180, 0x7f030181, 0x7f030182, 0x7f030186, 0x7f03018b, 0x7f03019e, 0x7f03019d, 0x7f0301a1, 0x7f0301a2, 0x7f0301a3, 0x7f03023a, 0x7f03024a, 0x7f0302d6, 0x7f0302d7, 0x7f0302d8, 0x7f0302d9, 0x7f0302dc, 0x7f0302dd, 0x7f0302de, 0x7f0302df, 0x7f0302e0, 0x7f0302e1, 0x7f0302e2, 0x7f0302e3, 0x7f0302e4, 0x7f030385, 0x7f030386, 0x7f030387, 0x7f0303a1, 0x7f0303a3, 0x7f0303b2, 0x7f0303b4, 0x7f0303b5, 0x7f0303b6, 0x7f0303d0, 0x7f0303d3, 0x7f0303d4, 0x7f0303d5, 0x7f030400, 0x7f030401, 0x7f03043c, 0x7f030478, 0x7f03047a, 0x7f03047b, 0x7f03047c, 0x7f03047e, 0x7f03047f, 0x7f030480, 0x7f030481, 0x7f03048c, 0x7f03048d, 0x7f0304d0, 0x7f0304d1, 0x7f0304d3, 0x7f0304d4, 0x7f0304fa, 0x7f030508, 0x7f030509, 0x7f03050a, 0x7f03050b, 0x7f03050c, 0x7f03050d, 0x7f03050e, 0x7f03050f, 0x7f030510, 0x7f030511 }
int styleable AppCompatTheme_actionBarDivider 0
int styleable AppCompatTheme_actionBarItemBackground 1
int styleable AppCompatTheme_actionBarPopupTheme 2
int styleable AppCompatTheme_actionBarSize 3
int styleable AppCompatTheme_actionBarSplitStyle 4
int styleable AppCompatTheme_actionBarStyle 5
int styleable AppCompatTheme_actionBarTabBarStyle 6
int styleable AppCompatTheme_actionBarTabStyle 7
int styleable AppCompatTheme_actionBarTabTextStyle 8
int styleable AppCompatTheme_actionBarTheme 9
int styleable AppCompatTheme_actionBarWidgetTheme 10
int styleable AppCompatTheme_actionButtonStyle 11
int styleable AppCompatTheme_actionDropDownStyle 12
int styleable AppCompatTheme_actionMenuTextAppearance 13
int styleable AppCompatTheme_actionMenuTextColor 14
int styleable AppCompatTheme_actionModeBackground 15
int styleable AppCompatTheme_actionModeCloseButtonStyle 16
int styleable AppCompatTheme_actionModeCloseContentDescription 17
int styleable AppCompatTheme_actionModeCloseDrawable 18
int styleable AppCompatTheme_actionModeCopyDrawable 19
int styleable AppCompatTheme_actionModeCutDrawable 20
int styleable AppCompatTheme_actionModeFindDrawable 21
int styleable AppCompatTheme_actionModePasteDrawable 22
int styleable AppCompatTheme_actionModePopupWindowStyle 23
int styleable AppCompatTheme_actionModeSelectAllDrawable 24
int styleable AppCompatTheme_actionModeShareDrawable 25
int styleable AppCompatTheme_actionModeSplitBackground 26
int styleable AppCompatTheme_actionModeStyle 27
int styleable AppCompatTheme_actionModeTheme 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_android_windowAnimationStyle 37
int styleable AppCompatTheme_android_windowIsFloating 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 67
int styleable AppCompatTheme_dropdownListPreferredItemHeight 66
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable Badge { 0x7f030040, 0x7f03004a, 0x7f030054, 0x7f030055, 0x7f030056, 0x7f030057, 0x7f030058, 0x7f03005a, 0x7f03005b, 0x7f03005c, 0x7f03005d, 0x7f03005e, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f03023c, 0x7f03023d, 0x7f030282, 0x7f03031e, 0x7f030322, 0x7f030370, 0x7f030372, 0x7f0304f8, 0x7f0304f9 }
int styleable Badge_autoAdjustToWithinGrandparentBounds 0
int styleable Badge_backgroundColor 1
int styleable Badge_badgeGravity 2
int styleable Badge_badgeHeight 3
int styleable Badge_badgeRadius 4
int styleable Badge_badgeShapeAppearance 5
int styleable Badge_badgeShapeAppearanceOverlay 6
int styleable Badge_badgeText 7
int styleable Badge_badgeTextAppearance 8
int styleable Badge_badgeTextColor 9
int styleable Badge_badgeVerticalPadding 10
int styleable Badge_badgeWidePadding 11
int styleable Badge_badgeWidth 12
int styleable Badge_badgeWithTextHeight 13
int styleable Badge_badgeWithTextRadius 14
int styleable Badge_badgeWithTextShapeAppearance 15
int styleable Badge_badgeWithTextShapeAppearanceOverlay 16
int styleable Badge_badgeWithTextWidth 17
int styleable Badge_horizontalOffset 18
int styleable Badge_horizontalOffsetWithText 19
int styleable Badge_largeFontVerticalOffsetAdjustment 20
int styleable Badge_maxCharacterCount 21
int styleable Badge_maxNumber 22
int styleable Badge_number 23
int styleable Badge_offsetAlignmentMode 24
int styleable Badge_verticalOffset 25
int styleable Badge_verticalOffsetWithText 26
int[] styleable BaseProgressIndicator { 0x00000000, 0x7f030231, 0x7f030251, 0x7f030256, 0x7f03032c, 0x7f0303e6, 0x7f0303e8, 0x7f0304dc, 0x7f0304df, 0x7f0304e6 }
int styleable BaseProgressIndicator_android_indeterminate 0
int styleable BaseProgressIndicator_hideAnimationBehavior 1
int styleable BaseProgressIndicator_indicatorColor 2
int styleable BaseProgressIndicator_indicatorTrackGapSize 3
int styleable BaseProgressIndicator_minHideDelay 4
int styleable BaseProgressIndicator_showAnimationBehavior 5
int styleable BaseProgressIndicator_showDelay 6
int styleable BaseProgressIndicator_trackColor 7
int styleable BaseProgressIndicator_trackCornerRadius 8
int styleable BaseProgressIndicator_trackThickness 9
int[] styleable BottomAppBar { 0x7f03002b, 0x7f030052, 0x7f0301a4, 0x7f0301d4, 0x7f0301d5, 0x7f0301d6, 0x7f0301d7, 0x7f0301d8, 0x7f0301d9, 0x7f0301da, 0x7f030235, 0x7f030327, 0x7f030368, 0x7f03037d, 0x7f03037f, 0x7f030380, 0x7f0303c0 }
int styleable BottomAppBar_addElevationShadow 0
int styleable BottomAppBar_backgroundTint 1
int styleable BottomAppBar_elevation 2
int styleable BottomAppBar_fabAlignmentMode 3
int styleable BottomAppBar_fabAlignmentModeEndMargin 4
int styleable BottomAppBar_fabAnchorMode 5
int styleable BottomAppBar_fabAnimationMode 6
int styleable BottomAppBar_fabCradleMargin 7
int styleable BottomAppBar_fabCradleRoundedCornerRadius 8
int styleable BottomAppBar_fabCradleVerticalOffset 9
int styleable BottomAppBar_hideOnScroll 10
int styleable BottomAppBar_menuAlignmentMode 11
int styleable BottomAppBar_navigationIconTint 12
int styleable BottomAppBar_paddingBottomSystemWindowInsets 13
int styleable BottomAppBar_paddingLeftSystemWindowInsets 14
int styleable BottomAppBar_paddingRightSystemWindowInsets 15
int styleable BottomAppBar_removeEmbeddedFabElevation 16
int[] styleable BottomNavigationView { 0x00000000, 0x7f030134, 0x7f030261, 0x7f0303d9, 0x7f0303e1 }
int styleable BottomNavigationView_android_minHeight 0
int styleable BottomNavigationView_compatShadowEnabled 1
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 2
int styleable BottomNavigationView_shapeAppearance 3
int styleable BottomNavigationView_shapeAppearanceOverlay 4
int[] styleable BottomSheetBehavior_Layout { 0x00000000, 0x00000000, 0x00000000, 0x7f030052, 0x7f03006b, 0x7f03006c, 0x7f03006d, 0x7f03006e, 0x7f03006f, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f030074, 0x7f03021a, 0x7f0302ea, 0x7f0302eb, 0x7f0302ec, 0x7f03037d, 0x7f03037f, 0x7f030380, 0x7f030384, 0x7f0303d9, 0x7f0303e1, 0x7f0303e5 }
int styleable BottomSheetBehavior_Layout_android_elevation 0
int styleable BottomSheetBehavior_Layout_android_maxHeight 1
int styleable BottomSheetBehavior_Layout_android_maxWidth 2
int styleable BottomSheetBehavior_Layout_backgroundTint 3
int styleable BottomSheetBehavior_Layout_behavior_draggable 4
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 5
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 6
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 7
int styleable BottomSheetBehavior_Layout_behavior_hideable 8
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 9
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 10
int styleable BottomSheetBehavior_Layout_behavior_significantVelocityThreshold 11
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 12
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 13
int styleable BottomSheetBehavior_Layout_marginLeftSystemWindowInsets 14
int styleable BottomSheetBehavior_Layout_marginRightSystemWindowInsets 15
int styleable BottomSheetBehavior_Layout_marginTopSystemWindowInsets 16
int styleable BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets 17
int styleable BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets 18
int styleable BottomSheetBehavior_Layout_paddingRightSystemWindowInsets 19
int styleable BottomSheetBehavior_Layout_paddingTopSystemWindowInsets 20
int styleable BottomSheetBehavior_Layout_shapeAppearance 21
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 22
int styleable BottomSheetBehavior_Layout_shouldRemoveExpandedCorners 23
int[] styleable ButtonBarLayout { 0x7f030030 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f0303b1, 0x7f0303e4 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x00000000, 0x00000000, 0x7f03009d, 0x7f03009e, 0x7f03009f, 0x7f0300a1, 0x7f0300a2, 0x7f0300a3, 0x7f030144, 0x7f030145, 0x7f030147, 0x7f030148, 0x7f03014a }
int styleable CardView_android_minHeight 0
int styleable CardView_android_minWidth 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Carousel { 0x7f0300a5, 0x7f0300a6, 0x7f0300a7, 0x7f0300a8, 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae }
int styleable Carousel_carousel_backwardTransition 0
int styleable Carousel_carousel_emptyViewsBehavior 1
int styleable Carousel_carousel_firstView 2
int styleable Carousel_carousel_forwardTransition 3
int styleable Carousel_carousel_infinite 4
int styleable Carousel_carousel_nextState 5
int styleable Carousel_carousel_previousState 6
int styleable Carousel_carousel_touchUpMode 7
int styleable Carousel_carousel_touchUp_dampeningFactor 8
int styleable Carousel_carousel_touchUp_velocityThreshold 9
int[] styleable CheckedTextView { 0x00000000, 0x7f0300b1, 0x7f0300b2, 0x7f0300b3 }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable Chip { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f0300b7, 0x7f0300b8, 0x7f0300bc, 0x7f0300bd, 0x7f0300c0, 0x7f0300c1, 0x7f0300c2, 0x7f0300c4, 0x7f0300c5, 0x7f0300c6, 0x7f0300c7, 0x7f0300c8, 0x7f0300c9, 0x7f0300ca, 0x7f0300cf, 0x7f0300d0, 0x7f0300d1, 0x7f0300d3, 0x7f0300e2, 0x7f0300e3, 0x7f0300e4, 0x7f0300e5, 0x7f0300e6, 0x7f0300e7, 0x7f0300e8, 0x7f0301b4, 0x7f030232, 0x7f030240, 0x7f030244, 0x7f0303c3, 0x7f0303d9, 0x7f0303e1, 0x7f0303eb, 0x7f03048e, 0x7f03049d }
int[] styleable ChipGroup { 0x7f0300b6, 0x7f0300cb, 0x7f0300cc, 0x7f0300cd, 0x7f0303d6, 0x7f0303f7, 0x7f0303f8 }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int styleable Chip_android_checkable 0
int styleable Chip_android_ellipsize 1
int styleable Chip_android_maxWidth 2
int styleable Chip_android_text 3
int styleable Chip_android_textAppearance 4
int styleable Chip_android_textColor 5
int styleable Chip_android_textSize 6
int styleable Chip_checkedIcon 7
int styleable Chip_checkedIconEnabled 8
int styleable Chip_checkedIconTint 9
int styleable Chip_checkedIconVisible 10
int styleable Chip_chipBackgroundColor 11
int styleable Chip_chipCornerRadius 12
int styleable Chip_chipEndPadding 13
int styleable Chip_chipIcon 14
int styleable Chip_chipIconEnabled 15
int styleable Chip_chipIconSize 16
int styleable Chip_chipIconTint 17
int styleable Chip_chipIconVisible 18
int styleable Chip_chipMinHeight 19
int styleable Chip_chipMinTouchTargetSize 20
int styleable Chip_chipStartPadding 21
int styleable Chip_chipStrokeColor 22
int styleable Chip_chipStrokeWidth 23
int styleable Chip_chipSurfaceColor 24
int styleable Chip_closeIcon 25
int styleable Chip_closeIconEnabled 26
int styleable Chip_closeIconEndPadding 27
int styleable Chip_closeIconSize 28
int styleable Chip_closeIconStartPadding 29
int styleable Chip_closeIconTint 30
int styleable Chip_closeIconVisible 31
int styleable Chip_ensureMinTouchTargetSize 32
int styleable Chip_hideMotionSpec 33
int styleable Chip_iconEndPadding 34
int styleable Chip_iconStartPadding 35
int styleable Chip_rippleColor 36
int styleable Chip_shapeAppearance 37
int styleable Chip_shapeAppearanceOverlay 38
int styleable Chip_showMotionSpec 39
int styleable Chip_textEndPadding 40
int styleable Chip_textStartPadding 41
int[] styleable CircularProgressIndicator { 0x7f030252, 0x7f030254, 0x7f030255 }
int styleable CircularProgressIndicator_indicatorDirectionCircular 0
int styleable CircularProgressIndicator_indicatorInset 1
int styleable CircularProgressIndicator_indicatorSize 2
int[] styleable ClockFaceView { 0x7f0300de, 0x7f0300e1 }
int styleable ClockFaceView_clockFaceBackgroundColor 0
int styleable ClockFaceView_clockNumberTextColor 1
int[] styleable ClockHandView { 0x7f0300df, 0x7f030309, 0x7f0303d7 }
int styleable ClockHandView_clockHandColor 0
int styleable ClockHandView_materialCircleRadius 1
int styleable ClockHandView_selectorSize 2
int[] styleable CollapsingToolbarLayout { 0x7f0300ed, 0x7f0300ee, 0x7f0300ef, 0x7f03014b, 0x7f0301c4, 0x7f0301c5, 0x7f0301c6, 0x7f0301c7, 0x7f0301c8, 0x7f0301c9, 0x7f0301ca, 0x7f0301cb, 0x7f0301d3, 0x7f030215, 0x7f030321, 0x7f0303ca, 0x7f0303cc, 0x7f030426, 0x7f0304bf, 0x7f0304c1, 0x7f0304c2, 0x7f0304c9, 0x7f0304cc, 0x7f0304cf }
int[] styleable CollapsingToolbarLayout_Layout { 0x7f03028d, 0x7f03028e }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_collapsedTitleTextColor 2
int styleable CollapsingToolbarLayout_contentScrim 3
int styleable CollapsingToolbarLayout_expandedTitleGravity 4
int styleable CollapsingToolbarLayout_expandedTitleMargin 5
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 6
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 7
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 8
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 9
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 10
int styleable CollapsingToolbarLayout_expandedTitleTextColor 11
int styleable CollapsingToolbarLayout_extraMultilineHeightEnabled 12
int styleable CollapsingToolbarLayout_forceApplySystemWindowInsetTop 13
int styleable CollapsingToolbarLayout_maxLines 14
int styleable CollapsingToolbarLayout_scrimAnimationDuration 15
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 16
int styleable CollapsingToolbarLayout_statusBarScrim 17
int styleable CollapsingToolbarLayout_title 18
int styleable CollapsingToolbarLayout_titleCollapseMode 19
int styleable CollapsingToolbarLayout_titleEnabled 20
int styleable CollapsingToolbarLayout_titlePositionInterpolator 21
int styleable CollapsingToolbarLayout_titleTextEllipsize 22
int styleable CollapsingToolbarLayout_toolbarId 23
int[] styleable ColorStateListItem { 0x7f030031, 0x00000000, 0x00000000, 0x00000000, 0x7f03027e }
int styleable ColorStateListItem_alpha 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_color 2
int styleable ColorStateListItem_android_lStar 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x00000000, 0x7f030092, 0x7f03009b, 0x7f03009c }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030035, 0x7f030036, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f0300b0, 0x7f030139, 0x7f03013a, 0x7f03018f, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030228, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c2, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c9, 0x7f0302ce, 0x7f03035e, 0x7f03035f, 0x7f03038d, 0x7f030394, 0x7f03039a, 0x7f0303ac, 0x7f0303ad, 0x7f0303ae, 0x7f0304e9, 0x7f0304eb, 0x7f0304ed, 0x7f0304ff }
int[] styleable ConstraintLayout_Layout { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f0300b0, 0x7f0300d6, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f030136, 0x7f030139, 0x7f03013a, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030228, 0x7f030287, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c2, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c9, 0x7f0302ca, 0x7f0302ce }
int styleable ConstraintLayout_Layout_android_elevation 0
int styleable ConstraintLayout_Layout_android_layout_height 1
int styleable ConstraintLayout_Layout_android_layout_margin 2
int styleable ConstraintLayout_Layout_android_layout_marginBottom 3
int styleable ConstraintLayout_Layout_android_layout_marginEnd 4
int styleable ConstraintLayout_Layout_android_layout_marginHorizontal 5
int styleable ConstraintLayout_Layout_android_layout_marginLeft 6
int styleable ConstraintLayout_Layout_android_layout_marginRight 7
int styleable ConstraintLayout_Layout_android_layout_marginStart 8
int styleable ConstraintLayout_Layout_android_layout_marginTop 9
int styleable ConstraintLayout_Layout_android_layout_marginVertical 10
int styleable ConstraintLayout_Layout_android_layout_width 11
int styleable ConstraintLayout_Layout_android_maxHeight 12
int styleable ConstraintLayout_Layout_android_maxWidth 13
int styleable ConstraintLayout_Layout_android_minHeight 14
int styleable ConstraintLayout_Layout_android_minWidth 15
int styleable ConstraintLayout_Layout_android_orientation 16
int styleable ConstraintLayout_Layout_android_padding 17
int styleable ConstraintLayout_Layout_android_paddingBottom 18
int styleable ConstraintLayout_Layout_android_paddingEnd 19
int styleable ConstraintLayout_Layout_android_paddingLeft 20
int styleable ConstraintLayout_Layout_android_paddingRight 21
int styleable ConstraintLayout_Layout_android_paddingStart 22
int styleable ConstraintLayout_Layout_android_paddingTop 23
int styleable ConstraintLayout_Layout_android_visibility 24
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 25
int styleable ConstraintLayout_Layout_barrierDirection 26
int styleable ConstraintLayout_Layout_barrierMargin 27
int styleable ConstraintLayout_Layout_chainUseRtl 28
int styleable ConstraintLayout_Layout_circularflow_angles 29
int styleable ConstraintLayout_Layout_circularflow_defaultAngle 30
int styleable ConstraintLayout_Layout_circularflow_defaultRadius 31
int styleable ConstraintLayout_Layout_circularflow_radiusInDP 32
int styleable ConstraintLayout_Layout_circularflow_viewCenter 33
int styleable ConstraintLayout_Layout_constraintSet 34
int styleable ConstraintLayout_Layout_constraint_referenced_ids 35
int styleable ConstraintLayout_Layout_constraint_referenced_tags 36
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 37
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 38
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 39
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 40
int styleable ConstraintLayout_Layout_flow_horizontalAlign 41
int styleable ConstraintLayout_Layout_flow_horizontalBias 42
int styleable ConstraintLayout_Layout_flow_horizontalGap 43
int styleable ConstraintLayout_Layout_flow_horizontalStyle 44
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 45
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 46
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 47
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 48
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 49
int styleable ConstraintLayout_Layout_flow_verticalAlign 50
int styleable ConstraintLayout_Layout_flow_verticalBias 51
int styleable ConstraintLayout_Layout_flow_verticalGap 52
int styleable ConstraintLayout_Layout_flow_verticalStyle 53
int styleable ConstraintLayout_Layout_flow_wrapMode 54
int styleable ConstraintLayout_Layout_guidelineUseRtl 55
int styleable ConstraintLayout_Layout_layoutDescription 56
int styleable ConstraintLayout_Layout_layout_constrainedHeight 57
int styleable ConstraintLayout_Layout_layout_constrainedWidth 58
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 59
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 60
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBottomOf 61
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toTopOf 62
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 63
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 64
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 65
int styleable ConstraintLayout_Layout_layout_constraintCircle 66
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 67
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 68
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 69
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 70
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 71
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 72
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 73
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 74
int styleable ConstraintLayout_Layout_layout_constraintHeight 75
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 76
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 77
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 78
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 79
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 80
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 81
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 82
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 83
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 84
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 85
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 86
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 87
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 88
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 89
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 90
int styleable ConstraintLayout_Layout_layout_constraintTag 91
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 92
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 93
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 94
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 95
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 96
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 97
int styleable ConstraintLayout_Layout_layout_constraintWidth 98
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 99
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 100
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 101
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 102
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 103
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 104
int styleable ConstraintLayout_Layout_layout_goneMarginBaseline 105
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 106
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 107
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 108
int styleable ConstraintLayout_Layout_layout_goneMarginRight 109
int styleable ConstraintLayout_Layout_layout_goneMarginStart 110
int styleable ConstraintLayout_Layout_layout_goneMarginTop 111
int styleable ConstraintLayout_Layout_layout_marginBaseline 112
int styleable ConstraintLayout_Layout_layout_optimizationLevel 113
int styleable ConstraintLayout_Layout_layout_wrapBehaviorInParent 114
int[] styleable ConstraintLayout_ReactiveGuide { 0x7f0303b7, 0x7f0303b8, 0x7f0303b9, 0x7f0303ba }
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_animateChange 0
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToAllConstraintSets 1
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToConstraintSet 2
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_valueId 3
int[] styleable ConstraintLayout_placeholder { 0x7f03013c, 0x7f030399 }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintOverride { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030035, 0x7f030036, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f0300b0, 0x7f030139, 0x7f03018f, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030228, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030295, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302ac, 0x7f0302b1, 0x7f0302b2, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c2, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c9, 0x7f0302ce, 0x7f03035e, 0x7f03035f, 0x7f030360, 0x7f03038d, 0x7f030394, 0x7f03039a, 0x7f0303ac, 0x7f0303ad, 0x7f0303ae, 0x7f0304e9, 0x7f0304eb, 0x7f0304ed, 0x7f0304ff }
int styleable ConstraintOverride_android_alpha 0
int styleable ConstraintOverride_android_elevation 1
int styleable ConstraintOverride_android_id 2
int styleable ConstraintOverride_android_layout_height 3
int styleable ConstraintOverride_android_layout_marginBottom 4
int styleable ConstraintOverride_android_layout_marginEnd 5
int styleable ConstraintOverride_android_layout_marginLeft 6
int styleable ConstraintOverride_android_layout_marginRight 7
int styleable ConstraintOverride_android_layout_marginStart 8
int styleable ConstraintOverride_android_layout_marginTop 9
int styleable ConstraintOverride_android_layout_width 10
int styleable ConstraintOverride_android_maxHeight 11
int styleable ConstraintOverride_android_maxWidth 12
int styleable ConstraintOverride_android_minHeight 13
int styleable ConstraintOverride_android_minWidth 14
int styleable ConstraintOverride_android_orientation 15
int styleable ConstraintOverride_android_rotation 16
int styleable ConstraintOverride_android_rotationX 17
int styleable ConstraintOverride_android_rotationY 18
int styleable ConstraintOverride_android_scaleX 19
int styleable ConstraintOverride_android_scaleY 20
int styleable ConstraintOverride_android_transformPivotX 21
int styleable ConstraintOverride_android_transformPivotY 22
int styleable ConstraintOverride_android_translationX 23
int styleable ConstraintOverride_android_translationY 24
int styleable ConstraintOverride_android_translationZ 25
int styleable ConstraintOverride_android_visibility 26
int styleable ConstraintOverride_animateCircleAngleTo 27
int styleable ConstraintOverride_animateRelativeTo 28
int styleable ConstraintOverride_barrierAllowsGoneWidgets 29
int styleable ConstraintOverride_barrierDirection 30
int styleable ConstraintOverride_barrierMargin 31
int styleable ConstraintOverride_chainUseRtl 32
int styleable ConstraintOverride_constraint_referenced_ids 33
int styleable ConstraintOverride_drawPath 34
int styleable ConstraintOverride_flow_firstHorizontalBias 35
int styleable ConstraintOverride_flow_firstHorizontalStyle 36
int styleable ConstraintOverride_flow_firstVerticalBias 37
int styleable ConstraintOverride_flow_firstVerticalStyle 38
int styleable ConstraintOverride_flow_horizontalAlign 39
int styleable ConstraintOverride_flow_horizontalBias 40
int styleable ConstraintOverride_flow_horizontalGap 41
int styleable ConstraintOverride_flow_horizontalStyle 42
int styleable ConstraintOverride_flow_lastHorizontalBias 43
int styleable ConstraintOverride_flow_lastHorizontalStyle 44
int styleable ConstraintOverride_flow_lastVerticalBias 45
int styleable ConstraintOverride_flow_lastVerticalStyle 46
int styleable ConstraintOverride_flow_maxElementsWrap 47
int styleable ConstraintOverride_flow_verticalAlign 48
int styleable ConstraintOverride_flow_verticalBias 49
int styleable ConstraintOverride_flow_verticalGap 50
int styleable ConstraintOverride_flow_verticalStyle 51
int styleable ConstraintOverride_flow_wrapMode 52
int styleable ConstraintOverride_guidelineUseRtl 53
int styleable ConstraintOverride_layout_constrainedHeight 54
int styleable ConstraintOverride_layout_constrainedWidth 55
int styleable ConstraintOverride_layout_constraintBaseline_creator 56
int styleable ConstraintOverride_layout_constraintBottom_creator 57
int styleable ConstraintOverride_layout_constraintCircleAngle 58
int styleable ConstraintOverride_layout_constraintCircleRadius 59
int styleable ConstraintOverride_layout_constraintDimensionRatio 60
int styleable ConstraintOverride_layout_constraintGuide_begin 61
int styleable ConstraintOverride_layout_constraintGuide_end 62
int styleable ConstraintOverride_layout_constraintGuide_percent 63
int styleable ConstraintOverride_layout_constraintHeight 64
int styleable ConstraintOverride_layout_constraintHeight_default 65
int styleable ConstraintOverride_layout_constraintHeight_max 66
int styleable ConstraintOverride_layout_constraintHeight_min 67
int styleable ConstraintOverride_layout_constraintHeight_percent 68
int styleable ConstraintOverride_layout_constraintHorizontal_bias 69
int styleable ConstraintOverride_layout_constraintHorizontal_chainStyle 70
int styleable ConstraintOverride_layout_constraintHorizontal_weight 71
int styleable ConstraintOverride_layout_constraintLeft_creator 72
int styleable ConstraintOverride_layout_constraintRight_creator 73
int styleable ConstraintOverride_layout_constraintTag 74
int styleable ConstraintOverride_layout_constraintTop_creator 75
int styleable ConstraintOverride_layout_constraintVertical_bias 76
int styleable ConstraintOverride_layout_constraintVertical_chainStyle 77
int styleable ConstraintOverride_layout_constraintVertical_weight 78
int styleable ConstraintOverride_layout_constraintWidth 79
int styleable ConstraintOverride_layout_constraintWidth_default 80
int styleable ConstraintOverride_layout_constraintWidth_max 81
int styleable ConstraintOverride_layout_constraintWidth_min 82
int styleable ConstraintOverride_layout_constraintWidth_percent 83
int styleable ConstraintOverride_layout_editor_absoluteX 84
int styleable ConstraintOverride_layout_editor_absoluteY 85
int styleable ConstraintOverride_layout_goneMarginBaseline 86
int styleable ConstraintOverride_layout_goneMarginBottom 87
int styleable ConstraintOverride_layout_goneMarginEnd 88
int styleable ConstraintOverride_layout_goneMarginLeft 89
int styleable ConstraintOverride_layout_goneMarginRight 90
int styleable ConstraintOverride_layout_goneMarginStart 91
int styleable ConstraintOverride_layout_goneMarginTop 92
int styleable ConstraintOverride_layout_marginBaseline 93
int styleable ConstraintOverride_layout_wrapBehaviorInParent 94
int styleable ConstraintOverride_motionProgress 95
int styleable ConstraintOverride_motionStagger 96
int styleable ConstraintOverride_motionTarget 97
int styleable ConstraintOverride_pathMotionArc 98
int styleable ConstraintOverride_pivotAnchor 99
int styleable ConstraintOverride_polarRelativeTo 100
int styleable ConstraintOverride_quantizeMotionInterpolator 101
int styleable ConstraintOverride_quantizeMotionPhase 102
int styleable ConstraintOverride_quantizeMotionSteps 103
int styleable ConstraintOverride_transformPivotTarget 104
int styleable ConstraintOverride_transitionEasing 105
int styleable ConstraintOverride_transitionPathRotate 106
int styleable ConstraintOverride_visibilityMode 107
int[] styleable ConstraintSet { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030035, 0x7f030036, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f0300b0, 0x7f030135, 0x7f030139, 0x7f03013a, 0x7f03017e, 0x7f03018f, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030203, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030228, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c2, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c9, 0x7f0302ce, 0x7f03035e, 0x7f03035f, 0x7f03038d, 0x7f030394, 0x7f03039a, 0x7f0303ae, 0x7f03041a, 0x7f0304eb, 0x7f0304ed }
int styleable ConstraintSet_android_alpha 0
int styleable ConstraintSet_android_elevation 1
int styleable ConstraintSet_android_id 2
int styleable ConstraintSet_android_layout_height 3
int styleable ConstraintSet_android_layout_marginBottom 4
int styleable ConstraintSet_android_layout_marginEnd 5
int styleable ConstraintSet_android_layout_marginLeft 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginStart 8
int styleable ConstraintSet_android_layout_marginTop 9
int styleable ConstraintSet_android_layout_width 10
int styleable ConstraintSet_android_maxHeight 11
int styleable ConstraintSet_android_maxWidth 12
int styleable ConstraintSet_android_minHeight 13
int styleable ConstraintSet_android_minWidth 14
int styleable ConstraintSet_android_orientation 15
int styleable ConstraintSet_android_pivotX 16
int styleable ConstraintSet_android_pivotY 17
int styleable ConstraintSet_android_rotation 18
int styleable ConstraintSet_android_rotationX 19
int styleable ConstraintSet_android_rotationY 20
int styleable ConstraintSet_android_scaleX 21
int styleable ConstraintSet_android_scaleY 22
int styleable ConstraintSet_android_transformPivotX 23
int styleable ConstraintSet_android_transformPivotY 24
int styleable ConstraintSet_android_translationX 25
int styleable ConstraintSet_android_translationY 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_visibility 28
int styleable ConstraintSet_animateCircleAngleTo 29
int styleable ConstraintSet_animateRelativeTo 30
int styleable ConstraintSet_barrierAllowsGoneWidgets 31
int styleable ConstraintSet_barrierDirection 32
int styleable ConstraintSet_barrierMargin 33
int styleable ConstraintSet_chainUseRtl 34
int styleable ConstraintSet_constraintRotate 35
int styleable ConstraintSet_constraint_referenced_ids 36
int styleable ConstraintSet_constraint_referenced_tags 37
int styleable ConstraintSet_deriveConstraintsFrom 38
int styleable ConstraintSet_drawPath 39
int styleable ConstraintSet_flow_firstHorizontalBias 40
int styleable ConstraintSet_flow_firstHorizontalStyle 41
int styleable ConstraintSet_flow_firstVerticalBias 42
int styleable ConstraintSet_flow_firstVerticalStyle 43
int styleable ConstraintSet_flow_horizontalAlign 44
int styleable ConstraintSet_flow_horizontalBias 45
int styleable ConstraintSet_flow_horizontalGap 46
int styleable ConstraintSet_flow_horizontalStyle 47
int styleable ConstraintSet_flow_lastHorizontalBias 48
int styleable ConstraintSet_flow_lastHorizontalStyle 49
int styleable ConstraintSet_flow_lastVerticalBias 50
int styleable ConstraintSet_flow_lastVerticalStyle 51
int styleable ConstraintSet_flow_maxElementsWrap 52
int styleable ConstraintSet_flow_verticalAlign 53
int styleable ConstraintSet_flow_verticalBias 54
int styleable ConstraintSet_flow_verticalGap 55
int styleable ConstraintSet_flow_verticalStyle 56
int styleable ConstraintSet_flow_wrapMode 57
int styleable ConstraintSet_guidelineUseRtl 58
int styleable ConstraintSet_layout_constrainedHeight 59
int styleable ConstraintSet_layout_constrainedWidth 60
int styleable ConstraintSet_layout_constraintBaseline_creator 61
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 62
int styleable ConstraintSet_layout_constraintBaseline_toBottomOf 63
int styleable ConstraintSet_layout_constraintBaseline_toTopOf 64
int styleable ConstraintSet_layout_constraintBottom_creator 65
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 66
int styleable ConstraintSet_layout_constraintBottom_toTopOf 67
int styleable ConstraintSet_layout_constraintCircle 68
int styleable ConstraintSet_layout_constraintCircleAngle 69
int styleable ConstraintSet_layout_constraintCircleRadius 70
int styleable ConstraintSet_layout_constraintDimensionRatio 71
int styleable ConstraintSet_layout_constraintEnd_toEndOf 72
int styleable ConstraintSet_layout_constraintEnd_toStartOf 73
int styleable ConstraintSet_layout_constraintGuide_begin 74
int styleable ConstraintSet_layout_constraintGuide_end 75
int styleable ConstraintSet_layout_constraintGuide_percent 76
int styleable ConstraintSet_layout_constraintHeight_default 77
int styleable ConstraintSet_layout_constraintHeight_max 78
int styleable ConstraintSet_layout_constraintHeight_min 79
int styleable ConstraintSet_layout_constraintHeight_percent 80
int styleable ConstraintSet_layout_constraintHorizontal_bias 81
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 82
int styleable ConstraintSet_layout_constraintHorizontal_weight 83
int styleable ConstraintSet_layout_constraintLeft_creator 84
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 85
int styleable ConstraintSet_layout_constraintLeft_toRightOf 86
int styleable ConstraintSet_layout_constraintRight_creator 87
int styleable ConstraintSet_layout_constraintRight_toLeftOf 88
int styleable ConstraintSet_layout_constraintRight_toRightOf 89
int styleable ConstraintSet_layout_constraintStart_toEndOf 90
int styleable ConstraintSet_layout_constraintStart_toStartOf 91
int styleable ConstraintSet_layout_constraintTag 92
int styleable ConstraintSet_layout_constraintTop_creator 93
int styleable ConstraintSet_layout_constraintTop_toBottomOf 94
int styleable ConstraintSet_layout_constraintTop_toTopOf 95
int styleable ConstraintSet_layout_constraintVertical_bias 96
int styleable ConstraintSet_layout_constraintVertical_chainStyle 97
int styleable ConstraintSet_layout_constraintVertical_weight 98
int styleable ConstraintSet_layout_constraintWidth_default 99
int styleable ConstraintSet_layout_constraintWidth_max 100
int styleable ConstraintSet_layout_constraintWidth_min 101
int styleable ConstraintSet_layout_constraintWidth_percent 102
int styleable ConstraintSet_layout_editor_absoluteX 103
int styleable ConstraintSet_layout_editor_absoluteY 104
int styleable ConstraintSet_layout_goneMarginBaseline 105
int styleable ConstraintSet_layout_goneMarginBottom 106
int styleable ConstraintSet_layout_goneMarginEnd 107
int styleable ConstraintSet_layout_goneMarginLeft 108
int styleable ConstraintSet_layout_goneMarginRight 109
int styleable ConstraintSet_layout_goneMarginStart 110
int styleable ConstraintSet_layout_goneMarginTop 111
int styleable ConstraintSet_layout_marginBaseline 112
int styleable ConstraintSet_layout_wrapBehaviorInParent 113
int styleable ConstraintSet_motionProgress 114
int styleable ConstraintSet_motionStagger 115
int styleable ConstraintSet_pathMotionArc 116
int styleable ConstraintSet_pivotAnchor 117
int styleable ConstraintSet_polarRelativeTo 118
int styleable ConstraintSet_quantizeMotionSteps 119
int styleable ConstraintSet_stateLabels 120
int styleable ConstraintSet_transitionEasing 121
int styleable ConstraintSet_transitionPathRotate 122
int styleable Constraint_android_alpha 0
int styleable Constraint_android_elevation 1
int styleable Constraint_android_id 2
int styleable Constraint_android_layout_height 3
int styleable Constraint_android_layout_marginBottom 4
int styleable Constraint_android_layout_marginEnd 5
int styleable Constraint_android_layout_marginLeft 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginStart 8
int styleable Constraint_android_layout_marginTop 9
int styleable Constraint_android_layout_width 10
int styleable Constraint_android_maxHeight 11
int styleable Constraint_android_maxWidth 12
int styleable Constraint_android_minHeight 13
int styleable Constraint_android_minWidth 14
int styleable Constraint_android_orientation 15
int styleable Constraint_android_rotation 16
int styleable Constraint_android_rotationX 17
int styleable Constraint_android_rotationY 18
int styleable Constraint_android_scaleX 19
int styleable Constraint_android_scaleY 20
int styleable Constraint_android_transformPivotX 21
int styleable Constraint_android_transformPivotY 22
int styleable Constraint_android_translationX 23
int styleable Constraint_android_translationY 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_visibility 26
int styleable Constraint_animateCircleAngleTo 27
int styleable Constraint_animateRelativeTo 28
int styleable Constraint_barrierAllowsGoneWidgets 29
int styleable Constraint_barrierDirection 30
int styleable Constraint_barrierMargin 31
int styleable Constraint_chainUseRtl 32
int styleable Constraint_constraint_referenced_ids 33
int styleable Constraint_constraint_referenced_tags 34
int styleable Constraint_drawPath 35
int styleable Constraint_flow_firstHorizontalBias 36
int styleable Constraint_flow_firstHorizontalStyle 37
int styleable Constraint_flow_firstVerticalBias 38
int styleable Constraint_flow_firstVerticalStyle 39
int styleable Constraint_flow_horizontalAlign 40
int styleable Constraint_flow_horizontalBias 41
int styleable Constraint_flow_horizontalGap 42
int styleable Constraint_flow_horizontalStyle 43
int styleable Constraint_flow_lastHorizontalBias 44
int styleable Constraint_flow_lastHorizontalStyle 45
int styleable Constraint_flow_lastVerticalBias 46
int styleable Constraint_flow_lastVerticalStyle 47
int styleable Constraint_flow_maxElementsWrap 48
int styleable Constraint_flow_verticalAlign 49
int styleable Constraint_flow_verticalBias 50
int styleable Constraint_flow_verticalGap 51
int styleable Constraint_flow_verticalStyle 52
int styleable Constraint_flow_wrapMode 53
int styleable Constraint_guidelineUseRtl 54
int styleable Constraint_layout_constrainedHeight 55
int styleable Constraint_layout_constrainedWidth 56
int styleable Constraint_layout_constraintBaseline_creator 57
int styleable Constraint_layout_constraintBaseline_toBaselineOf 58
int styleable Constraint_layout_constraintBaseline_toBottomOf 59
int styleable Constraint_layout_constraintBaseline_toTopOf 60
int styleable Constraint_layout_constraintBottom_creator 61
int styleable Constraint_layout_constraintBottom_toBottomOf 62
int styleable Constraint_layout_constraintBottom_toTopOf 63
int styleable Constraint_layout_constraintCircle 64
int styleable Constraint_layout_constraintCircleAngle 65
int styleable Constraint_layout_constraintCircleRadius 66
int styleable Constraint_layout_constraintDimensionRatio 67
int styleable Constraint_layout_constraintEnd_toEndOf 68
int styleable Constraint_layout_constraintEnd_toStartOf 69
int styleable Constraint_layout_constraintGuide_begin 70
int styleable Constraint_layout_constraintGuide_end 71
int styleable Constraint_layout_constraintGuide_percent 72
int styleable Constraint_layout_constraintHeight 73
int styleable Constraint_layout_constraintHeight_default 74
int styleable Constraint_layout_constraintHeight_max 75
int styleable Constraint_layout_constraintHeight_min 76
int styleable Constraint_layout_constraintHeight_percent 77
int styleable Constraint_layout_constraintHorizontal_bias 78
int styleable Constraint_layout_constraintHorizontal_chainStyle 79
int styleable Constraint_layout_constraintHorizontal_weight 80
int styleable Constraint_layout_constraintLeft_creator 81
int styleable Constraint_layout_constraintLeft_toLeftOf 82
int styleable Constraint_layout_constraintLeft_toRightOf 83
int styleable Constraint_layout_constraintRight_creator 84
int styleable Constraint_layout_constraintRight_toLeftOf 85
int styleable Constraint_layout_constraintRight_toRightOf 86
int styleable Constraint_layout_constraintStart_toEndOf 87
int styleable Constraint_layout_constraintStart_toStartOf 88
int styleable Constraint_layout_constraintTag 89
int styleable Constraint_layout_constraintTop_creator 90
int styleable Constraint_layout_constraintTop_toBottomOf 91
int styleable Constraint_layout_constraintTop_toTopOf 92
int styleable Constraint_layout_constraintVertical_bias 93
int styleable Constraint_layout_constraintVertical_chainStyle 94
int styleable Constraint_layout_constraintVertical_weight 95
int styleable Constraint_layout_constraintWidth 96
int styleable Constraint_layout_constraintWidth_default 97
int styleable Constraint_layout_constraintWidth_max 98
int styleable Constraint_layout_constraintWidth_min 99
int styleable Constraint_layout_constraintWidth_percent 100
int styleable Constraint_layout_editor_absoluteX 101
int styleable Constraint_layout_editor_absoluteY 102
int styleable Constraint_layout_goneMarginBaseline 103
int styleable Constraint_layout_goneMarginBottom 104
int styleable Constraint_layout_goneMarginEnd 105
int styleable Constraint_layout_goneMarginLeft 106
int styleable Constraint_layout_goneMarginRight 107
int styleable Constraint_layout_goneMarginStart 108
int styleable Constraint_layout_goneMarginTop 109
int styleable Constraint_layout_marginBaseline 110
int styleable Constraint_layout_wrapBehaviorInParent 111
int styleable Constraint_motionProgress 112
int styleable Constraint_motionStagger 113
int styleable Constraint_pathMotionArc 114
int styleable Constraint_pivotAnchor 115
int styleable Constraint_polarRelativeTo 116
int styleable Constraint_quantizeMotionInterpolator 117
int styleable Constraint_quantizeMotionPhase 118
int styleable Constraint_quantizeMotionSteps 119
int styleable Constraint_transformPivotTarget 120
int styleable Constraint_transitionEasing 121
int styleable Constraint_transitionPathRotate 122
int styleable Constraint_visibilityMode 123
int[] styleable CoordinatorLayout { 0x7f03027d, 0x7f030424 }
int[] styleable CoordinatorLayout_Layout { 0x00000000, 0x7f03028a, 0x7f03028b, 0x7f03028c, 0x7f0302bd, 0x7f0302c7, 0x7f0302c8 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CustomAttribute { 0x7f03003f, 0x7f030166, 0x7f030167, 0x7f030168, 0x7f030169, 0x7f03016a, 0x7f03016b, 0x7f03016d, 0x7f03016e, 0x7f03016f, 0x7f030329 }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customReference 8
int styleable CustomAttribute_customStringValue 9
int styleable CustomAttribute_methodName 10
int[] styleable DialogFragmentNavigator { 0x00000000 }
int styleable DialogFragmentNavigator_android_name 0
int[] styleable DrawerArrowToggle { 0x7f03003d, 0x7f03003e, 0x7f030065, 0x7f0300f6, 0x7f030194, 0x7f030219, 0x7f0303ff, 0x7f0304a3 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable DrawerLayout { 0x7f0301a4 }
int styleable DrawerLayout_elevation 0
int[] styleable ExtendedFloatingActionButton { 0x7f0300ec, 0x7f0301a4, 0x7f0301cc, 0x7f0301cd, 0x7f030232, 0x7f0303eb, 0x7f0303ef }
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x7f030069, 0x7f03006a }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int styleable ExtendedFloatingActionButton_collapsedSize 0
int styleable ExtendedFloatingActionButton_elevation 1
int styleable ExtendedFloatingActionButton_extendMotionSpec 2
int styleable ExtendedFloatingActionButton_extendStrategy 3
int styleable ExtendedFloatingActionButton_hideMotionSpec 4
int styleable ExtendedFloatingActionButton_showMotionSpec 5
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 6
int[] styleable FloatingActionButton { 0x00000000, 0x7f030052, 0x7f030053, 0x7f030078, 0x7f0301a4, 0x7f0301b4, 0x7f0301db, 0x7f0301dc, 0x7f030232, 0x7f03023e, 0x7f030320, 0x7f0303a8, 0x7f0303c3, 0x7f0303d9, 0x7f0303e1, 0x7f0303eb, 0x7f0304f5 }
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f030069 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FlowLayout { 0x7f030272, 0x7f0302d4 }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f03020a, 0x7f03020b, 0x7f03020c, 0x7f03020d, 0x7f03020e, 0x7f03020f, 0x7f030210, 0x7f030211 }
int[] styleable FontFamilyFont { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030208, 0x7f030212, 0x7f030213, 0x7f030214, 0x7f0304f2 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontStyle 1
int styleable FontFamilyFont_android_fontVariationSettings 2
int styleable FontFamilyFont_android_fontWeight 3
int styleable FontFamilyFont_android_ttcIndex 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFallbackQuery 2
int styleable FontFamily_fontProviderFetchStrategy 3
int styleable FontFamily_fontProviderFetchTimeout 4
int styleable FontFamily_fontProviderPackage 5
int styleable FontFamily_fontProviderQuery 6
int styleable FontFamily_fontProviderSystemFontFamily 7
int[] styleable ForegroundLinearLayout { 0x00000000, 0x00000000, 0x7f030217 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x00000000, 0x00000000, 0x00000000 }
int[] styleable FragmentContainerView { 0x00000000, 0x00000000 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable FragmentNavigator { 0x00000000 }
int styleable FragmentNavigator_android_name 0
int styleable Fragment_android_id 0
int styleable Fragment_android_name 1
int styleable Fragment_android_tag 2
int[] styleable GradientColor { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int[] styleable GradientColorItem { 0x00000000, 0x00000000 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int styleable GradientColor_android_centerColor 0
int styleable GradientColor_android_centerX 1
int styleable GradientColor_android_centerY 2
int styleable GradientColor_android_endColor 3
int styleable GradientColor_android_endX 4
int styleable GradientColor_android_endY 5
int styleable GradientColor_android_gradientRadius 6
int styleable GradientColor_android_startColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_tileMode 10
int styleable GradientColor_android_type 11
int[] styleable Grid { 0x7f03021e, 0x7f03021d, 0x7f03021f, 0x7f030220, 0x7f030222, 0x7f030221, 0x7f030223, 0x7f030224, 0x7f030225, 0x7f030226, 0x7f030227 }
int styleable Grid_grid_columnWeights 1
int styleable Grid_grid_columns 0
int styleable Grid_grid_horizontalGaps 2
int styleable Grid_grid_orientation 3
int styleable Grid_grid_rowWeights 5
int styleable Grid_grid_rows 4
int styleable Grid_grid_skips 6
int styleable Grid_grid_spans 7
int styleable Grid_grid_useRtl 8
int styleable Grid_grid_validateInputs 9
int styleable Grid_grid_verticalGaps 10
int[] styleable ImageFilterView { 0x7f030033, 0x7f030075, 0x7f03008c, 0x7f03014c, 0x7f030161, 0x7f03024b, 0x7f03024c, 0x7f03024d, 0x7f03024e, 0x7f03037b, 0x7f0303c5, 0x7f0303c6, 0x7f0303c8, 0x7f030501 }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_blendSrc 1
int styleable ImageFilterView_brightness 2
int styleable ImageFilterView_contrast 3
int styleable ImageFilterView_crossfade 4
int styleable ImageFilterView_imagePanX 5
int styleable ImageFilterView_imagePanY 6
int styleable ImageFilterView_imageRotate 7
int styleable ImageFilterView_imageZoom 8
int styleable ImageFilterView_overlay 9
int styleable ImageFilterView_round 10
int styleable ImageFilterView_roundPercent 11
int styleable ImageFilterView_saturation 12
int styleable ImageFilterView_warmth 13
int[] styleable Insets { 0x7f0302ea, 0x7f0302eb, 0x7f0302ec, 0x7f03037d, 0x7f03037f, 0x7f030380, 0x7f030382, 0x7f030384 }
int styleable Insets_marginLeftSystemWindowInsets 0
int styleable Insets_marginRightSystemWindowInsets 1
int styleable Insets_marginTopSystemWindowInsets 2
int styleable Insets_paddingBottomSystemWindowInsets 3
int styleable Insets_paddingLeftSystemWindowInsets 4
int styleable Insets_paddingRightSystemWindowInsets 5
int styleable Insets_paddingStartSystemWindowInsets 6
int styleable Insets_paddingTopSystemWindowInsets 7
int[] styleable ItemsViewRendererTheme { 0x7f0300f5 }
int styleable ItemsViewRendererTheme_collectionViewStyle 0
int[] styleable KeyAttribute { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030165, 0x7f030218, 0x7f03035e, 0x7f030360, 0x7f0304e9, 0x7f0304eb, 0x7f0304ed }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_elevation 1
int styleable KeyAttribute_android_rotation 2
int styleable KeyAttribute_android_rotationX 3
int styleable KeyAttribute_android_rotationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_transformPivotX 7
int styleable KeyAttribute_android_transformPivotY 8
int styleable KeyAttribute_android_translationX 9
int styleable KeyAttribute_android_translationY 10
int styleable KeyAttribute_android_translationZ 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transformPivotTarget 16
int styleable KeyAttribute_transitionEasing 17
int styleable KeyAttribute_transitionPathRotate 18
int[] styleable KeyCycle { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030165, 0x7f030218, 0x7f03035e, 0x7f030360, 0x7f0304eb, 0x7f0304ed, 0x7f030503, 0x7f030504, 0x7f030505, 0x7f030506, 0x7f030507 }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_elevation 1
int styleable KeyCycle_android_rotation 2
int styleable KeyCycle_android_rotationX 3
int styleable KeyCycle_android_rotationY 4
int styleable KeyCycle_android_scaleX 5
int styleable KeyCycle_android_scaleY 6
int styleable KeyCycle_android_translationX 7
int styleable KeyCycle_android_translationY 8
int styleable KeyCycle_android_translationZ 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_wavePhase 18
int styleable KeyCycle_waveShape 19
int styleable KeyCycle_waveVariesBy 20
int[] styleable KeyFrame {  }
int[] styleable KeyFramesAcceleration {  }
int[] styleable KeyFramesVelocity {  }
int[] styleable KeyPosition { 0x7f030165, 0x7f03018f, 0x7f030218, 0x7f03027b, 0x7f030360, 0x7f03038d, 0x7f03038f, 0x7f030390, 0x7f030391, 0x7f030392, 0x7f0303f9, 0x7f0304eb }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030165, 0x7f030218, 0x7f03035e, 0x7f030360, 0x7f0304eb, 0x7f0304ed, 0x7f030502, 0x7f030503, 0x7f030504, 0x7f030505, 0x7f030506 }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_elevation 1
int styleable KeyTimeCycle_android_rotation 2
int styleable KeyTimeCycle_android_rotationX 3
int styleable KeyTimeCycle_android_rotationY 4
int styleable KeyTimeCycle_android_scaleX 5
int styleable KeyTimeCycle_android_scaleY 6
int styleable KeyTimeCycle_android_translationX 7
int styleable KeyTimeCycle_android_translationY 8
int styleable KeyTimeCycle_android_translationZ 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_wavePhase 19
int styleable KeyTimeCycle_waveShape 20
int[] styleable KeyTrigger { 0x7f030218, 0x7f030360, 0x7f030361, 0x7f030362, 0x7f030373, 0x7f030375, 0x7f030376, 0x7f0304ef, 0x7f0304f0, 0x7f0304f1, 0x7f0304fc, 0x7f0304fd, 0x7f0304fe }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int styleable KeyTrigger_viewTransitionOnCross 10
int styleable KeyTrigger_viewTransitionOnNegativeCross 11
int styleable KeyTrigger_viewTransitionOnPositiveCross 12
int[] styleable Layout { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f0300b0, 0x7f030139, 0x7f03013a, 0x7f030228, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f030298, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f03029d, 0x7f03029e, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f0302b9, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1, 0x7f0302c2, 0x7f0302c3, 0x7f0302c4, 0x7f0302c5, 0x7f0302c6, 0x7f0302c9, 0x7f0302ce, 0x7f03031f, 0x7f030324, 0x7f03032b, 0x7f03032f }
int styleable Layout_android_layout_height 0
int styleable Layout_android_layout_marginBottom 1
int styleable Layout_android_layout_marginEnd 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginRight 4
int styleable Layout_android_layout_marginStart 5
int styleable Layout_android_layout_marginTop 6
int styleable Layout_android_layout_width 7
int styleable Layout_android_orientation 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_constraint_referenced_tags 14
int styleable Layout_guidelineUseRtl 15
int styleable Layout_layout_constrainedHeight 16
int styleable Layout_layout_constrainedWidth 17
int styleable Layout_layout_constraintBaseline_creator 18
int styleable Layout_layout_constraintBaseline_toBaselineOf 19
int styleable Layout_layout_constraintBaseline_toBottomOf 20
int styleable Layout_layout_constraintBaseline_toTopOf 21
int styleable Layout_layout_constraintBottom_creator 22
int styleable Layout_layout_constraintBottom_toBottomOf 23
int styleable Layout_layout_constraintBottom_toTopOf 24
int styleable Layout_layout_constraintCircle 25
int styleable Layout_layout_constraintCircleAngle 26
int styleable Layout_layout_constraintCircleRadius 27
int styleable Layout_layout_constraintDimensionRatio 28
int styleable Layout_layout_constraintEnd_toEndOf 29
int styleable Layout_layout_constraintEnd_toStartOf 30
int styleable Layout_layout_constraintGuide_begin 31
int styleable Layout_layout_constraintGuide_end 32
int styleable Layout_layout_constraintGuide_percent 33
int styleable Layout_layout_constraintHeight 34
int styleable Layout_layout_constraintHeight_default 35
int styleable Layout_layout_constraintHeight_max 36
int styleable Layout_layout_constraintHeight_min 37
int styleable Layout_layout_constraintHeight_percent 38
int styleable Layout_layout_constraintHorizontal_bias 39
int styleable Layout_layout_constraintHorizontal_chainStyle 40
int styleable Layout_layout_constraintHorizontal_weight 41
int styleable Layout_layout_constraintLeft_creator 42
int styleable Layout_layout_constraintLeft_toLeftOf 43
int styleable Layout_layout_constraintLeft_toRightOf 44
int styleable Layout_layout_constraintRight_creator 45
int styleable Layout_layout_constraintRight_toLeftOf 46
int styleable Layout_layout_constraintRight_toRightOf 47
int styleable Layout_layout_constraintStart_toEndOf 48
int styleable Layout_layout_constraintStart_toStartOf 49
int styleable Layout_layout_constraintTop_creator 50
int styleable Layout_layout_constraintTop_toBottomOf 51
int styleable Layout_layout_constraintTop_toTopOf 52
int styleable Layout_layout_constraintVertical_bias 53
int styleable Layout_layout_constraintVertical_chainStyle 54
int styleable Layout_layout_constraintVertical_weight 55
int styleable Layout_layout_constraintWidth 56
int styleable Layout_layout_constraintWidth_default 57
int styleable Layout_layout_constraintWidth_max 58
int styleable Layout_layout_constraintWidth_min 59
int styleable Layout_layout_constraintWidth_percent 60
int styleable Layout_layout_editor_absoluteX 61
int styleable Layout_layout_editor_absoluteY 62
int styleable Layout_layout_goneMarginBaseline 63
int styleable Layout_layout_goneMarginBottom 64
int styleable Layout_layout_goneMarginEnd 65
int styleable Layout_layout_goneMarginLeft 66
int styleable Layout_layout_goneMarginRight 67
int styleable Layout_layout_goneMarginStart 68
int styleable Layout_layout_goneMarginTop 69
int styleable Layout_layout_marginBaseline 70
int styleable Layout_layout_wrapBehaviorInParent 71
int styleable Layout_maxHeight 72
int styleable Layout_maxWidth 73
int styleable Layout_minHeight 74
int styleable Layout_minWidth 75
int[] styleable LinearLayoutCompat { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030184, 0x7f030189, 0x7f030325, 0x7f0303e9 }
int[] styleable LinearLayoutCompat_Layout { 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_height 1
int styleable LinearLayoutCompat_Layout_android_layout_weight 2
int styleable LinearLayoutCompat_Layout_android_layout_width 3
int styleable LinearLayoutCompat_android_baselineAligned 0
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
int styleable LinearLayoutCompat_android_gravity 2
int styleable LinearLayoutCompat_android_orientation 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearProgressIndicator { 0x7f03024f, 0x7f030253, 0x7f0304e5 }
int styleable LinearProgressIndicator_indeterminateAnimationType 0
int styleable LinearProgressIndicator_indicatorDirectionLinear 1
int styleable LinearProgressIndicator_trackStopIndicatorSize 2
int[] styleable ListPopupWindow { 0x00000000, 0x00000000 }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MaterialAlertDialog { 0x7f03004b, 0x7f03004c, 0x7f03004d, 0x7f03004e, 0x7f030052 }
int[] styleable MaterialAlertDialogTheme { 0x7f0302ed, 0x7f0302ee, 0x7f0302ef, 0x7f0302f0, 0x7f0302f1, 0x7f0302f2 }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogButtonSpacerVisibility 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 4
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 5
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int styleable MaterialAlertDialog_backgroundTint 4
int[] styleable MaterialAutoCompleteTextView { 0x00000000, 0x00000000, 0x7f03019c, 0x7f0303f2, 0x7f0303f5, 0x7f0303f3, 0x7f0303f4 }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int styleable MaterialAutoCompleteTextView_android_popupElevation 1
int styleable MaterialAutoCompleteTextView_dropDownBackgroundTint 2
int styleable MaterialAutoCompleteTextView_simpleItemLayout 3
int styleable MaterialAutoCompleteTextView_simpleItemSelectedColor 5
int styleable MaterialAutoCompleteTextView_simpleItemSelectedRippleColor 6
int styleable MaterialAutoCompleteTextView_simpleItems 4
int[] styleable MaterialButton { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030052, 0x7f030053, 0x7f030155, 0x7f0301a4, 0x7f03023f, 0x7f030241, 0x7f030242, 0x7f030243, 0x7f030245, 0x7f030246, 0x7f0303c3, 0x7f0303d9, 0x7f0303e1, 0x7f030428, 0x7f030429, 0x7f0304ce }
int[] styleable MaterialButtonToggleGroup { 0x00000000, 0x7f0300b5, 0x7f0303d6, 0x7f0303f8 }
int styleable MaterialButtonToggleGroup_android_enabled 0
int styleable MaterialButtonToggleGroup_checkedButton 1
int styleable MaterialButtonToggleGroup_selectionRequired 2
int styleable MaterialButtonToggleGroup_singleSelection 3
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_checkable 1
int styleable MaterialButton_android_insetBottom 2
int styleable MaterialButton_android_insetLeft 3
int styleable MaterialButton_android_insetRight 4
int styleable MaterialButton_android_insetTop 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int styleable MaterialButton_toggleCheckedStateOnClick 21
int[] styleable MaterialCalendar { 0x00000000, 0x7f030052, 0x7f030172, 0x7f030173, 0x7f030174, 0x7f030175, 0x7f03036e, 0x7f0303b3, 0x7f030512, 0x7f030513, 0x7f030514 }
int[] styleable MaterialCalendarItem { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f03025f, 0x7f03026b, 0x7f03026c, 0x7f030273, 0x7f030274, 0x7f030279 }
int styleable MaterialCalendarItem_android_insetBottom 0
int styleable MaterialCalendarItem_android_insetLeft 1
int styleable MaterialCalendarItem_android_insetRight 2
int styleable MaterialCalendarItem_android_insetTop 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_backgroundTint 1
int styleable MaterialCalendar_dayInvalidStyle 2
int styleable MaterialCalendar_daySelectedStyle 3
int styleable MaterialCalendar_dayStyle 4
int styleable MaterialCalendar_dayTodayStyle 5
int styleable MaterialCalendar_nestedScrollable 6
int styleable MaterialCalendar_rangeFillColor 7
int styleable MaterialCalendar_yearSelectedStyle 8
int styleable MaterialCalendar_yearStyle 9
int styleable MaterialCalendar_yearTodayStyle 10
int[] styleable MaterialCardView { 0x00000000, 0x7f0300a0, 0x7f0300b7, 0x7f0300b9, 0x7f0300ba, 0x7f0300bb, 0x7f0300bc, 0x7f0303c3, 0x7f0303d9, 0x7f0303e1, 0x7f03041e, 0x7f030428, 0x7f030429 }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconGravity 3
int styleable MaterialCardView_checkedIconMargin 4
int styleable MaterialCardView_checkedIconSize 5
int styleable MaterialCardView_checkedIconTint 6
int styleable MaterialCardView_rippleColor 7
int styleable MaterialCardView_shapeAppearance 8
int styleable MaterialCardView_shapeAppearanceOverlay 9
int styleable MaterialCardView_state_dragged 10
int styleable MaterialCardView_strokeColor 11
int styleable MaterialCardView_strokeWidth 12
int[] styleable MaterialCheckBox { 0x00000000, 0x7f030092, 0x7f030094, 0x7f030096, 0x7f030097, 0x7f03009b, 0x7f0300af, 0x7f0300be, 0x7f0301b6, 0x7f0301bd, 0x7f0304f6 }
int[] styleable MaterialCheckBoxStates { 0x7f03041f, 0x7f030420 }
int styleable MaterialCheckBoxStates_state_error 0
int styleable MaterialCheckBoxStates_state_indeterminate 1
int styleable MaterialCheckBox_android_button 0
int styleable MaterialCheckBox_buttonCompat 1
int styleable MaterialCheckBox_buttonIcon 2
int styleable MaterialCheckBox_buttonIconTint 3
int styleable MaterialCheckBox_buttonIconTintMode 4
int styleable MaterialCheckBox_buttonTint 5
int styleable MaterialCheckBox_centerIfNoTextEnabled 6
int styleable MaterialCheckBox_checkedState 7
int styleable MaterialCheckBox_errorAccessibilityLabel 8
int styleable MaterialCheckBox_errorShown 9
int styleable MaterialCheckBox_useMaterialThemeColors 10
int[] styleable MaterialDivider { 0x7f030185, 0x7f030187, 0x7f030188, 0x7f03018a, 0x7f030284 }
int styleable MaterialDivider_dividerColor 0
int styleable MaterialDivider_dividerInsetEnd 1
int styleable MaterialDivider_dividerInsetStart 2
int styleable MaterialDivider_dividerThickness 3
int styleable MaterialDivider_lastItemDecorated 4
int[] styleable MaterialRadioButton { 0x7f03009b, 0x7f0304f6 }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x7f0303d9, 0x7f0303e1 }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialSwitch { 0x7f0304a7, 0x7f0304a8, 0x7f0304a9, 0x7f0304aa, 0x7f0304e0, 0x7f0304e1, 0x7f0304e2 }
int styleable MaterialSwitch_thumbIcon 0
int styleable MaterialSwitch_thumbIconSize 1
int styleable MaterialSwitch_thumbIconTint 2
int styleable MaterialSwitch_thumbIconTintMode 3
int styleable MaterialSwitch_trackDecoration 4
int styleable MaterialSwitch_trackDecorationTint 5
int styleable MaterialSwitch_trackDecorationTintMode 6
int[] styleable MaterialTextAppearance { 0x00000000, 0x00000000, 0x7f0302d3 }
int styleable MaterialTextAppearance_android_letterSpacing 0
int styleable MaterialTextAppearance_android_lineHeight 1
int styleable MaterialTextAppearance_lineHeight 2
int[] styleable MaterialTextView { 0x00000000, 0x00000000, 0x7f0302d3 }
int styleable MaterialTextView_android_lineHeight 0
int styleable MaterialTextView_android_textAppearance 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MaterialTimePicker { 0x7f030052, 0x7f0300e0, 0x7f03027c }
int styleable MaterialTimePicker_backgroundTint 0
int styleable MaterialTimePicker_clockIcon 1
int styleable MaterialTimePicker_keyboardIcon 2
int[] styleable MaterialToolbar { 0x7f0302e6, 0x7f0302e8, 0x7f030368, 0x7f030431, 0x7f0304c0 }
int styleable MaterialToolbar_logoAdjustViewBounds 0
int styleable MaterialToolbar_logoScaleType 1
int styleable MaterialToolbar_navigationIconTint 2
int styleable MaterialToolbar_subtitleCentered 3
int styleable MaterialToolbar_titleCentered 4
int[] styleable MenuGroup { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int styleable MenuGroup_android_checkableBehavior 0
int styleable MenuGroup_android_enabled 1
int styleable MenuGroup_android_id 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_visible 5
int[] styleable MenuItem { 0x7f030010, 0x7f030024, 0x7f030026, 0x7f030032, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f03013d, 0x7f030245, 0x7f030246, 0x7f030371, 0x7f0303e7, 0x7f0304d6 }
int styleable MenuItem_actionLayout 0
int styleable MenuItem_actionProviderClass 1
int styleable MenuItem_actionViewClass 2
int styleable MenuItem_alphabeticModifiers 3
int styleable MenuItem_android_alphabeticShortcut 4
int styleable MenuItem_android_checkable 5
int styleable MenuItem_android_checked 6
int styleable MenuItem_android_enabled 7
int styleable MenuItem_android_icon 8
int styleable MenuItem_android_id 9
int styleable MenuItem_android_menuCategory 10
int styleable MenuItem_android_numericShortcut 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_android_orderInCategory 13
int styleable MenuItem_android_title 14
int styleable MenuItem_android_titleCondensed 15
int styleable MenuItem_android_visible 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f0303a7, 0x7f03042a }
int styleable MenuView_android_headerBackground 0
int styleable MenuView_android_horizontalDivider 1
int styleable MenuView_android_itemBackground 2
int styleable MenuView_android_itemIconDisabledAlpha 3
int styleable MenuView_android_itemTextAppearance 4
int styleable MenuView_android_verticalDivider 5
int styleable MenuView_android_windowAnimationStyle 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f030330, 0x7f030331, 0x7f030332, 0x7f030333, 0x7f030334, 0x7f030335 }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f030035, 0x7f030036, 0x7f03018f, 0x7f03035d, 0x7f03035f, 0x7f03038d, 0x7f0303ac, 0x7f0303ad, 0x7f0303ae, 0x7f0304eb }
int[] styleable MotionEffect { 0x7f030353, 0x7f030354, 0x7f030355, 0x7f030356, 0x7f030357, 0x7f030358, 0x7f030359, 0x7f03035a }
int styleable MotionEffect_motionEffect_alpha 0
int styleable MotionEffect_motionEffect_end 1
int styleable MotionEffect_motionEffect_move 2
int styleable MotionEffect_motionEffect_start 3
int styleable MotionEffect_motionEffect_strict 4
int styleable MotionEffect_motionEffect_translationX 5
int styleable MotionEffect_motionEffect_translationY 6
int styleable MotionEffect_motionEffect_viewTransition 7
int[] styleable MotionHelper { 0x7f030374, 0x7f030377 }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLabel { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030076, 0x7f030077, 0x7f0303c9, 0x7f030487, 0x7f030488, 0x7f030489, 0x7f03048a, 0x7f03048b, 0x7f030499, 0x7f03049a, 0x7f03049b, 0x7f03049c, 0x7f03049e, 0x7f03049f, 0x7f0304a0, 0x7f0304a1 }
int styleable MotionLabel_android_autoSizeTextType 0
int styleable MotionLabel_android_fontFamily 1
int styleable MotionLabel_android_gravity 2
int styleable MotionLabel_android_shadowRadius 3
int styleable MotionLabel_android_text 4
int styleable MotionLabel_android_textColor 5
int styleable MotionLabel_android_textSize 6
int styleable MotionLabel_android_textStyle 7
int styleable MotionLabel_android_typeface 8
int styleable MotionLabel_borderRound 9
int styleable MotionLabel_borderRoundPercent 10
int styleable MotionLabel_scaleFromTextSize 11
int styleable MotionLabel_textBackground 12
int styleable MotionLabel_textBackgroundPanX 13
int styleable MotionLabel_textBackgroundPanY 14
int styleable MotionLabel_textBackgroundRotate 15
int styleable MotionLabel_textBackgroundZoom 16
int styleable MotionLabel_textOutlineColor 17
int styleable MotionLabel_textOutlineThickness 18
int styleable MotionLabel_textPanX 19
int styleable MotionLabel_textPanY 20
int styleable MotionLabel_textureBlurFactor 21
int styleable MotionLabel_textureEffect 22
int styleable MotionLabel_textureHeight 23
int styleable MotionLabel_textureWidth 24
int[] styleable MotionLayout { 0x7f03003a, 0x7f030162, 0x7f030287, 0x7f030336, 0x7f03035e, 0x7f0303ec }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f030176, 0x7f030288 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f03045e, 0x7f03045f, 0x7f030460 }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int styleable Motion_animateCircleAngleTo 0
int styleable Motion_animateRelativeTo 1
int styleable Motion_drawPath 2
int styleable Motion_motionPathRotate 3
int styleable Motion_motionStagger 4
int styleable Motion_pathMotionArc 5
int styleable Motion_quantizeMotionInterpolator 6
int styleable Motion_quantizeMotionPhase 7
int styleable Motion_quantizeMotionSteps 8
int styleable Motion_transitionEasing 9
int[] styleable NavAction { 0x00000000, 0x7f03017f, 0x7f0301b5, 0x7f0301c0, 0x7f030285, 0x7f03039b, 0x7f03039c, 0x7f03039d, 0x7f03039e, 0x7f03039f, 0x7f0303c1 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x00000000, 0x00000000, 0x7f03003c, 0x7f03036f }
int styleable NavArgument_android_defaultValue 0
int styleable NavArgument_android_name 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x7f030002, 0x00000000, 0x7f03032a, 0x7f0304f4 }
int styleable NavDeepLink_action 0
int styleable NavDeepLink_android_autoVerify 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f030412 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f030365 }
int[] styleable NavHostFragment { 0x7f030178 }
int styleable NavHostFragment_defaultNavHost 0
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f03021c }
int styleable NavInclude_graph 0
int[] styleable NavigationBarActiveIndicator { 0x00000000, 0x00000000, 0x00000000, 0x7f0302e9, 0x7f0303d9 }
int styleable NavigationBarActiveIndicator_android_color 0
int styleable NavigationBarActiveIndicator_android_height 1
int styleable NavigationBarActiveIndicator_android_width 2
int styleable NavigationBarActiveIndicator_marginHorizontal 3
int styleable NavigationBarActiveIndicator_shapeAppearance 4
int[] styleable NavigationBarView { 0x7f030027, 0x7f030052, 0x7f0301a4, 0x7f03025d, 0x7f03025e, 0x7f030263, 0x7f030264, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f030276, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f030281, 0x7f030326 }
int styleable NavigationBarView_activeIndicatorLabelPadding 0
int styleable NavigationBarView_backgroundTint 1
int styleable NavigationBarView_elevation 2
int styleable NavigationBarView_itemActiveIndicatorStyle 3
int styleable NavigationBarView_itemBackground 4
int styleable NavigationBarView_itemIconSize 5
int styleable NavigationBarView_itemIconTint 6
int styleable NavigationBarView_itemPaddingBottom 7
int styleable NavigationBarView_itemPaddingTop 8
int styleable NavigationBarView_itemRippleColor 9
int styleable NavigationBarView_itemTextAppearanceActive 10
int styleable NavigationBarView_itemTextAppearanceActiveBoldEnabled 11
int styleable NavigationBarView_itemTextAppearanceInactive 12
int styleable NavigationBarView_itemTextColor 13
int styleable NavigationBarView_labelVisibilityMode 14
int styleable NavigationBarView_menu 15
int[] styleable NavigationRailView { 0x7f03022b, 0x7f030266, 0x7f030328, 0x7f03037d, 0x7f030382, 0x7f030384, 0x7f0303d9, 0x7f0303e1 }
int styleable NavigationRailView_headerLayout 0
int styleable NavigationRailView_itemMinHeight 1
int styleable NavigationRailView_menuGravity 2
int styleable NavigationRailView_paddingBottomSystemWindowInsets 3
int styleable NavigationRailView_paddingStartSystemWindowInsets 4
int styleable NavigationRailView_paddingTopSystemWindowInsets 5
int styleable NavigationRailView_shapeAppearance 6
int styleable NavigationRailView_shapeAppearanceOverlay 7
int[] styleable NavigationView { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f03007b, 0x7f030187, 0x7f030188, 0x7f03019a, 0x7f0301a4, 0x7f03022b, 0x7f03025e, 0x7f030260, 0x7f030262, 0x7f030263, 0x7f030264, 0x7f030265, 0x7f03026a, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030275, 0x7f030277, 0x7f030279, 0x7f03027a, 0x7f030326, 0x7f0303d9, 0x7f0303e1, 0x7f03042b, 0x7f03042c, 0x7f03042d, 0x7f03042e, 0x7f0304d7 }
int styleable NavigationView_android_background 0
int styleable NavigationView_android_fitsSystemWindows 1
int styleable NavigationView_android_layout_gravity 2
int styleable NavigationView_android_maxWidth 3
int styleable NavigationView_bottomInsetScrimEnabled 4
int styleable NavigationView_dividerInsetEnd 5
int styleable NavigationView_dividerInsetStart 6
int styleable NavigationView_drawerLayoutCornerSize 7
int styleable NavigationView_elevation 8
int styleable NavigationView_headerLayout 9
int styleable NavigationView_itemBackground 10
int styleable NavigationView_itemHorizontalPadding 11
int styleable NavigationView_itemIconPadding 12
int styleable NavigationView_itemIconSize 13
int styleable NavigationView_itemIconTint 14
int styleable NavigationView_itemMaxLines 15
int styleable NavigationView_itemRippleColor 16
int styleable NavigationView_itemShapeAppearance 17
int styleable NavigationView_itemShapeAppearanceOverlay 18
int styleable NavigationView_itemShapeFillColor 19
int styleable NavigationView_itemShapeInsetBottom 20
int styleable NavigationView_itemShapeInsetEnd 21
int styleable NavigationView_itemShapeInsetStart 22
int styleable NavigationView_itemShapeInsetTop 23
int styleable NavigationView_itemTextAppearance 24
int styleable NavigationView_itemTextAppearanceActiveBoldEnabled 25
int styleable NavigationView_itemTextColor 26
int styleable NavigationView_itemVerticalPadding 27
int styleable NavigationView_menu 28
int styleable NavigationView_shapeAppearance 29
int styleable NavigationView_shapeAppearanceOverlay 30
int styleable NavigationView_subheaderColor 31
int styleable NavigationView_subheaderInsetEnd 32
int styleable NavigationView_subheaderInsetStart 33
int styleable NavigationView_subheaderTextAppearance 34
int styleable NavigationView_topInsetScrimEnabled 35
int[] styleable Navigator { 0x00000000, 0x00000000, 0x7f0303c7 }
int styleable Navigator_android_id 0
int styleable Navigator_android_label 1
int styleable Navigator_route 2
int[] styleable OnClick { 0x7f0300dd, 0x7f03045c }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f030041, 0x7f03018c, 0x7f03018d, 0x7f03018e, 0x7f0302d2, 0x7f03031b, 0x7f030323, 0x7f030363, 0x7f03036c, 0x7f030379, 0x7f0303c4, 0x7f03040a, 0x7f03040b, 0x7f03040c, 0x7f03040d, 0x7f03040e, 0x7f0304d8, 0x7f0304d9, 0x7f0304da }
int styleable OnSwipe_autoCompleteMode 0
int styleable OnSwipe_dragDirection 1
int styleable OnSwipe_dragScale 2
int styleable OnSwipe_dragThreshold 3
int styleable OnSwipe_limitBoundsTo 4
int styleable OnSwipe_maxAcceleration 5
int styleable OnSwipe_maxVelocity 6
int styleable OnSwipe_moveWhenScrollAtTop 7
int styleable OnSwipe_nestedScrollFlags 8
int styleable OnSwipe_onTouchUp 9
int styleable OnSwipe_rotationCenterId 10
int styleable OnSwipe_springBoundary 11
int styleable OnSwipe_springDamping 12
int styleable OnSwipe_springMass 13
int styleable OnSwipe_springStiffness 14
int styleable OnSwipe_springStopThreshold 15
int styleable OnSwipe_touchAnchorId 16
int styleable OnSwipe_touchAnchorSide 17
int styleable OnSwipe_touchRegionId 18
int[] styleable PopupWindow { 0x00000000, 0x00000000, 0x7f03037a }
int[] styleable PopupWindowBackgroundState { 0x7f03041b }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int styleable PopupWindow_android_popupAnimationStyle 0
int styleable PopupWindow_android_popupBackground 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PropertySet { 0x00000000, 0x00000000, 0x7f0302b1, 0x7f03035e, 0x7f0304ff }
int styleable PropertySet_android_alpha 0
int styleable PropertySet_android_visibility 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RadialViewGroup { 0x7f030309 }
int styleable RadialViewGroup_materialCircleRadius 0
int[] styleable RangeSlider { 0x7f03032d, 0x7f0304f7 }
int styleable RangeSlider_minSeparation 0
int styleable RangeSlider_values 1
int[] styleable RecycleListView { 0x7f03037c, 0x7f030383 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x00000000, 0x00000000, 0x00000000, 0x7f0301dd, 0x7f0301de, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f030289, 0x7f0303c2, 0x7f0303fe, 0x7f030410 }
int styleable RecyclerView_android_clipToPadding 0
int styleable RecyclerView_android_descendantFocusability 1
int styleable RecyclerView_android_orientation 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x7f030258 }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollViewRendererTheme { 0x7f0303cd }
int styleable ScrollViewRendererTheme_scrollViewStyle 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f030070 }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchBar { 0x00000000, 0x00000000, 0x00000000, 0x7f030052, 0x7f030177, 0x7f03017a, 0x7f0301a4, 0x7f030216, 0x7f030233, 0x7f030368, 0x7f030428, 0x7f030429, 0x7f0304be }
int styleable SearchBar_android_hint 0
int styleable SearchBar_android_text 1
int styleable SearchBar_android_textAppearance 2
int styleable SearchBar_backgroundTint 3
int styleable SearchBar_defaultMarginsEnabled 4
int styleable SearchBar_defaultScrollFlagsEnabled 5
int styleable SearchBar_elevation 6
int styleable SearchBar_forceDefaultNavigationOnClickListener 7
int styleable SearchBar_hideNavigationIcon 8
int styleable SearchBar_navigationIconTint 9
int styleable SearchBar_strokeColor 10
int styleable SearchBar_strokeWidth 11
int styleable SearchBar_tintNavigationIcon 12
int[] styleable SearchView { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f0300e2, 0x7f030133, 0x7f030179, 0x7f03021b, 0x7f030247, 0x7f030286, 0x7f0303af, 0x7f0303b0, 0x7f0303ce, 0x7f0303cf, 0x7f03042f, 0x7f030438, 0x7f030500 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_imeOptions 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_maxWidth 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable ShapeAppearance { 0x7f030150, 0x7f030151, 0x7f030152, 0x7f030153, 0x7f030154, 0x7f030156, 0x7f030157, 0x7f030158, 0x7f030159, 0x7f03015a }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x7f030144, 0x7f030145, 0x7f030146, 0x7f030147, 0x7f030148, 0x7f030149, 0x7f03014a, 0x7f0303d9, 0x7f0303e1, 0x7f030428, 0x7f030429 }
int styleable ShapeableImageView_contentPadding 0
int styleable ShapeableImageView_contentPaddingBottom 1
int styleable ShapeableImageView_contentPaddingEnd 2
int styleable ShapeableImageView_contentPaddingLeft 3
int styleable ShapeableImageView_contentPaddingRight 4
int styleable ShapeableImageView_contentPaddingStart 5
int styleable ShapeableImageView_contentPaddingTop 6
int styleable ShapeableImageView_shapeAppearance 7
int styleable ShapeableImageView_shapeAppearanceOverlay 8
int styleable ShapeableImageView_strokeColor 9
int styleable ShapeableImageView_strokeWidth 10
int[] styleable SideSheetBehavior_Layout { 0x00000000, 0x00000000, 0x00000000, 0x7f030052, 0x7f03006b, 0x7f03014f, 0x7f0303d9, 0x7f0303e1 }
int styleable SideSheetBehavior_Layout_android_elevation 0
int styleable SideSheetBehavior_Layout_android_maxHeight 1
int styleable SideSheetBehavior_Layout_android_maxWidth 2
int styleable SideSheetBehavior_Layout_backgroundTint 3
int styleable SideSheetBehavior_Layout_behavior_draggable 4
int styleable SideSheetBehavior_Layout_coplanarSiblingViewId 5
int styleable SideSheetBehavior_Layout_shapeAppearance 6
int styleable SideSheetBehavior_Layout_shapeAppearanceOverlay 7
int[] styleable Slider { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030229, 0x7f03022a, 0x7f03027f, 0x7f030280, 0x7f03032e, 0x7f0304a4, 0x7f0304a5, 0x7f0304a6, 0x7f0304ab, 0x7f0304ac, 0x7f0304ad, 0x7f0304b1, 0x7f0304b2, 0x7f0304b3, 0x7f0304b4, 0x7f0304b5, 0x7f0304b9, 0x7f0304ba, 0x7f0304bb, 0x7f0304dc, 0x7f0304dd, 0x7f0304de, 0x7f0304e3, 0x7f0304e4, 0x7f0304e5 }
int styleable Slider_android_enabled 0
int styleable Slider_android_stepSize 1
int styleable Slider_android_value 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_minTouchTargetSize 9
int styleable Slider_thumbColor 10
int styleable Slider_thumbElevation 11
int styleable Slider_thumbHeight 12
int styleable Slider_thumbRadius 13
int styleable Slider_thumbStrokeColor 14
int styleable Slider_thumbStrokeWidth 15
int styleable Slider_thumbTrackGapSize 16
int styleable Slider_thumbWidth 17
int styleable Slider_tickColor 18
int styleable Slider_tickColorActive 19
int styleable Slider_tickColorInactive 20
int styleable Slider_tickRadiusActive 21
int styleable Slider_tickRadiusInactive 22
int styleable Slider_tickVisible 23
int styleable Slider_trackColor 24
int styleable Slider_trackColorActive 25
int styleable Slider_trackColorInactive 26
int styleable Slider_trackHeight 27
int styleable Slider_trackInsideCornerSize 28
int styleable Slider_trackStopIndicatorSize 29
int[] styleable Snackbar { 0x7f0303fb, 0x7f0303fc, 0x7f0303fd }
int[] styleable SnackbarLayout { 0x7f030025, 0x00000000, 0x7f030038, 0x7f03004f, 0x7f030052, 0x7f030053, 0x7f0301a4, 0x7f03031c, 0x7f0303d9, 0x7f0303e1 }
int styleable SnackbarLayout_actionTextColorAlpha 0
int styleable SnackbarLayout_android_maxWidth 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int styleable SnackbarLayout_shapeAppearance 8
int styleable SnackbarLayout_shapeAppearanceOverlay 9
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable Spinner { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f0303a2 }
int styleable Spinner_android_dropDownWidth 0
int styleable Spinner_android_entries 1
int styleable Spinner_android_popupBackground 2
int styleable Spinner_android_prompt 3
int styleable Spinner_popupTheme 4
int[] styleable SplitPairFilter { 0x7f0303a9, 0x7f0303d1, 0x7f0303d2 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f030037, 0x7f0300db, 0x7f0301e3, 0x7f0301e4, 0x7f030402, 0x7f030403, 0x7f030404, 0x7f030405, 0x7f030406, 0x7f030407, 0x7f030408, 0x7f03045b }
int styleable SplitPairRule_animationBackgroundColor 0
int styleable SplitPairRule_clearTop 1
int styleable SplitPairRule_finishPrimaryWithSecondary 2
int styleable SplitPairRule_finishSecondaryWithPrimary 3
int styleable SplitPairRule_splitLayoutDirection 4
int styleable SplitPairRule_splitMaxAspectRatioInLandscape 5
int styleable SplitPairRule_splitMaxAspectRatioInPortrait 6
int styleable SplitPairRule_splitMinHeightDp 7
int styleable SplitPairRule_splitMinSmallestWidthDp 8
int styleable SplitPairRule_splitMinWidthDp 9
int styleable SplitPairRule_splitRatio 10
int styleable SplitPairRule_tag 11
int[] styleable SplitPlaceholderRule { 0x7f030037, 0x7f0301e2, 0x7f030395, 0x7f030402, 0x7f030403, 0x7f030404, 0x7f030405, 0x7f030406, 0x7f030407, 0x7f030408, 0x7f030427, 0x7f03045b }
int styleable SplitPlaceholderRule_animationBackgroundColor 0
int styleable SplitPlaceholderRule_finishPrimaryWithPlaceholder 1
int styleable SplitPlaceholderRule_placeholderActivityName 2
int styleable SplitPlaceholderRule_splitLayoutDirection 3
int styleable SplitPlaceholderRule_splitMaxAspectRatioInLandscape 4
int styleable SplitPlaceholderRule_splitMaxAspectRatioInPortrait 5
int styleable SplitPlaceholderRule_splitMinHeightDp 6
int styleable SplitPlaceholderRule_splitMinSmallestWidthDp 7
int styleable SplitPlaceholderRule_splitMinWidthDp 8
int styleable SplitPlaceholderRule_splitRatio 9
int styleable SplitPlaceholderRule_stickyPlaceholder 10
int styleable SplitPlaceholderRule_tag 11
int[] styleable State { 0x00000000, 0x7f03013b }
int[] styleable StateListDrawable { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int[] styleable StateListDrawableItem { 0x00000000 }
int styleable StateListDrawableItem_android_drawable 0
int styleable StateListDrawable_android_constantSize 0
int styleable StateListDrawable_android_dither 1
int styleable StateListDrawable_android_enterFadeDuration 2
int styleable StateListDrawable_android_exitFadeDuration 3
int styleable StateListDrawable_android_variablePadding 4
int styleable StateListDrawable_android_visible 5
int[] styleable StateSet { 0x7f03017b }
int styleable StateSet_defaultState 0
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable SwipeRefreshLayout { 0x7f030439 }
int styleable SwipeRefreshLayout_swipeRefreshLayoutProgressSpinnerBackgroundColor 0
int[] styleable SwitchCompat { 0x00000000, 0x00000000, 0x00000000, 0x7f0303ed, 0x7f030409, 0x7f03043a, 0x7f03043b, 0x7f03043d, 0x7f0304ae, 0x7f0304af, 0x7f0304b0, 0x7f0304db, 0x7f0304e7, 0x7f0304e8 }
int styleable SwitchCompat_android_textOff 0
int styleable SwitchCompat_android_textOn 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x7f0304f6 }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable TabItem { 0x00000000, 0x00000000, 0x00000000 }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f03043e, 0x7f03043f, 0x7f030440, 0x7f030441, 0x7f030442, 0x7f030443, 0x7f030444, 0x7f030445, 0x7f030446, 0x7f030447, 0x7f030448, 0x7f030449, 0x7f03044a, 0x7f03044b, 0x7f03044c, 0x7f03044d, 0x7f03044e, 0x7f03044f, 0x7f030450, 0x7f030451, 0x7f030452, 0x7f030453, 0x7f030455, 0x7f030456, 0x7f030458, 0x7f030459, 0x7f03045a }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorAnimationMode 7
int styleable TabLayout_tabIndicatorColor 8
int styleable TabLayout_tabIndicatorFullWidth 9
int styleable TabLayout_tabIndicatorGravity 10
int styleable TabLayout_tabIndicatorHeight 11
int styleable TabLayout_tabInlineLabel 12
int styleable TabLayout_tabMaxWidth 13
int styleable TabLayout_tabMinWidth 14
int styleable TabLayout_tabMode 15
int styleable TabLayout_tabPadding 16
int styleable TabLayout_tabPaddingBottom 17
int styleable TabLayout_tabPaddingEnd 18
int styleable TabLayout_tabPaddingStart 19
int styleable TabLayout_tabPaddingTop 20
int styleable TabLayout_tabRippleColor 21
int styleable TabLayout_tabSelectedTextAppearance 22
int styleable TabLayout_tabSelectedTextColor 23
int styleable TabLayout_tabTextAppearance 24
int styleable TabLayout_tabTextColor 25
int styleable TabLayout_tabUnboundedRipple 26
int[] styleable TextAppearance { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030209, 0x7f030213, 0x7f030461, 0x7f030498 }
int styleable TextAppearance_android_fontFamily 0
int styleable TextAppearance_android_shadowColor 1
int styleable TextAppearance_android_shadowDx 2
int styleable TextAppearance_android_shadowDy 3
int styleable TextAppearance_android_shadowRadius 4
int styleable TextAppearance_android_textColor 5
int styleable TextAppearance_android_textColorHint 6
int styleable TextAppearance_android_textColorLink 7
int styleable TextAppearance_android_textFontWeight 8
int styleable TextAppearance_android_textSize 9
int styleable TextAppearance_android_textStyle 10
int styleable TextAppearance_android_typeface 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextEffects { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030076, 0x7f030077, 0x7f03048f, 0x7f030499, 0x7f03049a }
int styleable TextEffects_android_fontFamily 0
int styleable TextEffects_android_shadowColor 1
int styleable TextEffects_android_shadowDx 2
int styleable TextEffects_android_shadowDy 3
int styleable TextEffects_android_shadowRadius 4
int styleable TextEffects_android_text 5
int styleable TextEffects_android_textSize 6
int styleable TextEffects_android_textStyle 7
int styleable TextEffects_android_typeface 8
int styleable TextEffects_borderRound 9
int styleable TextEffects_borderRoundPercent 10
int styleable TextEffects_textFillColor 11
int styleable TextEffects_textOutlineColor 12
int styleable TextEffects_textOutlineThickness 13
int[] styleable TextInputEditText { 0x7f030493 }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030081, 0x7f030082, 0x7f030083, 0x7f030084, 0x7f030085, 0x7f030086, 0x7f030087, 0x7f030088, 0x7f030089, 0x7f03008a, 0x7f03008b, 0x7f03015b, 0x7f03015c, 0x7f03015d, 0x7f03015e, 0x7f03015f, 0x7f030160, 0x7f030163, 0x7f030164, 0x7f0301aa, 0x7f0301ab, 0x7f0301ac, 0x7f0301ad, 0x7f0301ae, 0x7f0301af, 0x7f0301b0, 0x7f0301b1, 0x7f0301b7, 0x7f0301b8, 0x7f0301b9, 0x7f0301ba, 0x7f0301bb, 0x7f0301bc, 0x7f0301be, 0x7f0301bf, 0x7f0301c3, 0x7f03022d, 0x7f03022e, 0x7f03022f, 0x7f030230, 0x7f030236, 0x7f030237, 0x7f030238, 0x7f030239, 0x7f030388, 0x7f030389, 0x7f03038a, 0x7f03038b, 0x7f03038c, 0x7f030396, 0x7f030397, 0x7f030398, 0x7f0303a4, 0x7f0303a5, 0x7f0303a6, 0x7f0303d9, 0x7f0303e1, 0x7f030413, 0x7f030414, 0x7f030415, 0x7f030416, 0x7f030417, 0x7f030418, 0x7f030419, 0x7f030435, 0x7f030436, 0x7f030437 }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_hint 1
int styleable TextInputLayout_android_maxEms 2
int styleable TextInputLayout_android_maxWidth 3
int styleable TextInputLayout_android_minEms 4
int styleable TextInputLayout_android_minWidth 5
int styleable TextInputLayout_android_textColorHint 6
int styleable TextInputLayout_boxBackgroundColor 7
int styleable TextInputLayout_boxBackgroundMode 8
int styleable TextInputLayout_boxCollapsedPaddingTop 9
int styleable TextInputLayout_boxCornerRadiusBottomEnd 10
int styleable TextInputLayout_boxCornerRadiusBottomStart 11
int styleable TextInputLayout_boxCornerRadiusTopEnd 12
int styleable TextInputLayout_boxCornerRadiusTopStart 13
int styleable TextInputLayout_boxStrokeColor 14
int styleable TextInputLayout_boxStrokeErrorColor 15
int styleable TextInputLayout_boxStrokeWidth 16
int styleable TextInputLayout_boxStrokeWidthFocused 17
int styleable TextInputLayout_counterEnabled 18
int styleable TextInputLayout_counterMaxLength 19
int styleable TextInputLayout_counterOverflowTextAppearance 20
int styleable TextInputLayout_counterOverflowTextColor 21
int styleable TextInputLayout_counterTextAppearance 22
int styleable TextInputLayout_counterTextColor 23
int styleable TextInputLayout_cursorColor 24
int styleable TextInputLayout_cursorErrorColor 25
int styleable TextInputLayout_endIconCheckable 26
int styleable TextInputLayout_endIconContentDescription 27
int styleable TextInputLayout_endIconDrawable 28
int styleable TextInputLayout_endIconMinSize 29
int styleable TextInputLayout_endIconMode 30
int styleable TextInputLayout_endIconScaleType 31
int styleable TextInputLayout_endIconTint 32
int styleable TextInputLayout_endIconTintMode 33
int styleable TextInputLayout_errorAccessibilityLiveRegion 34
int styleable TextInputLayout_errorContentDescription 35
int styleable TextInputLayout_errorEnabled 36
int styleable TextInputLayout_errorIconDrawable 37
int styleable TextInputLayout_errorIconTint 38
int styleable TextInputLayout_errorIconTintMode 39
int styleable TextInputLayout_errorTextAppearance 40
int styleable TextInputLayout_errorTextColor 41
int styleable TextInputLayout_expandedHintEnabled 42
int styleable TextInputLayout_helperText 43
int styleable TextInputLayout_helperTextEnabled 44
int styleable TextInputLayout_helperTextTextAppearance 45
int styleable TextInputLayout_helperTextTextColor 46
int styleable TextInputLayout_hintAnimationEnabled 47
int styleable TextInputLayout_hintEnabled 48
int styleable TextInputLayout_hintTextAppearance 49
int styleable TextInputLayout_hintTextColor 50
int styleable TextInputLayout_passwordToggleContentDescription 51
int styleable TextInputLayout_passwordToggleDrawable 52
int styleable TextInputLayout_passwordToggleEnabled 53
int styleable TextInputLayout_passwordToggleTint 54
int styleable TextInputLayout_passwordToggleTintMode 55
int styleable TextInputLayout_placeholderText 56
int styleable TextInputLayout_placeholderTextAppearance 57
int styleable TextInputLayout_placeholderTextColor 58
int styleable TextInputLayout_prefixText 59
int styleable TextInputLayout_prefixTextAppearance 60
int styleable TextInputLayout_prefixTextColor 61
int styleable TextInputLayout_shapeAppearance 62
int styleable TextInputLayout_shapeAppearanceOverlay 63
int styleable TextInputLayout_startIconCheckable 64
int styleable TextInputLayout_startIconContentDescription 65
int styleable TextInputLayout_startIconDrawable 66
int styleable TextInputLayout_startIconMinSize 67
int styleable TextInputLayout_startIconScaleType 68
int styleable TextInputLayout_startIconTint 69
int styleable TextInputLayout_startIconTintMode 70
int styleable TextInputLayout_suffixText 71
int styleable TextInputLayout_suffixTextAppearance 72
int styleable TextInputLayout_suffixTextColor 73
int[] styleable ThemeEnforcement { 0x00000000, 0x7f0301b2, 0x7f0301b3 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x00000000, 0x00000000, 0x7f030093, 0x7f0300ea, 0x7f0300eb, 0x7f03013e, 0x7f03013f, 0x7f030140, 0x7f030141, 0x7f030142, 0x7f030143, 0x7f0302e5, 0x7f0302e7, 0x7f03031d, 0x7f030326, 0x7f030366, 0x7f030367, 0x7f0303a2, 0x7f030430, 0x7f030432, 0x7f030433, 0x7f0304bf, 0x7f0304c3, 0x7f0304c4, 0x7f0304c5, 0x7f0304c8, 0x7f0304c6, 0x7f0304c7, 0x7f0304ca, 0x7f0304cb }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 26
int styleable Toolbar_titleMarginTop 27
int styleable Toolbar_titleMargins 25
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f030052, 0x7f0303ea }
int styleable Tooltip_android_layout_margin 0
int styleable Tooltip_android_minHeight 1
int styleable Tooltip_android_minWidth 2
int styleable Tooltip_android_padding 3
int styleable Tooltip_android_text 4
int styleable Tooltip_android_textAppearance 5
int styleable Tooltip_android_textColor 6
int styleable Tooltip_backgroundTint 7
int styleable Tooltip_showMarker 8
int[] styleable Transform { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x7f0304e9 }
int styleable Transform_android_elevation 0
int styleable Transform_android_rotation 1
int styleable Transform_android_rotationX 2
int styleable Transform_android_rotationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_transformPivotX 6
int styleable Transform_android_transformPivotY 7
int styleable Transform_android_translationX 8
int styleable Transform_android_translationY 9
int styleable Transform_android_translationZ 10
int styleable Transform_transformPivotTarget 11
int[] styleable Transition { 0x00000000, 0x7f030048, 0x7f030137, 0x7f030138, 0x7f03019f, 0x7f030288, 0x7f03035b, 0x7f03038d, 0x7f030411, 0x7f0304ea, 0x7f0304ec }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f03013b, 0x7f0303bc, 0x7f0303bd, 0x7f0303be, 0x7f0303bf }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x00000000, 0x00000000, 0x7f03037e, 0x7f030381, 0x7f0304a2 }
int[] styleable ViewBackgroundHelper { 0x00000000, 0x7f030052, 0x7f030053 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x00000000 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x00000000, 0x00000000, 0x00000000 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_inflatedId 1
int styleable ViewStubCompat_android_layout 2
int[] styleable ViewTransition { 0x00000000, 0x7f0300dc, 0x7f03019f, 0x7f030248, 0x7f030249, 0x7f03035b, 0x7f030360, 0x7f030378, 0x7f03038d, 0x7f0303d8, 0x7f030000, 0x7f030001, 0x7f0304ea, 0x7f0304f3, 0x7f0304fb }
int styleable ViewTransition_SharedValue 10
int styleable ViewTransition_SharedValueId 11
int styleable ViewTransition_android_id 0
int styleable ViewTransition_clearsTag 1
int styleable ViewTransition_duration 2
int styleable ViewTransition_ifTagNotSet 3
int styleable ViewTransition_ifTagSet 4
int styleable ViewTransition_motionInterpolator 5
int styleable ViewTransition_motionTarget 6
int styleable ViewTransition_onStateTransition 7
int styleable ViewTransition_pathMotionArc 8
int styleable ViewTransition_setsTag 9
int styleable ViewTransition_transitionDisable 12
int styleable ViewTransition_upDuration 13
int styleable ViewTransition_viewTransitionMode 14
int styleable View_android_focusable 0
int styleable View_android_theme 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable include { 0x7f030136 }
int styleable include_constraintSet 0
int xml image_share_filepaths 0x7f110000
int xml microsoft_maui_essentials_fileprovider_file_paths 0x7f110001
