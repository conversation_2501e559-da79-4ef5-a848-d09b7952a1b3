using HabitBuilder.Constants;
using HabitBuilder.Models;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.Services;

public class MockAuthenticationProvider : IAuthenticationProvider
{
    private readonly ILocalStorageService _localStorageService;
    private readonly IDataService _dataService;
    private readonly ILogger<MockAuthenticationProvider> _logger;
    private UserProfile? _currentUser;

    // Mock user database
    private readonly List<MockUser> _mockUsers = new()
    {
        new MockUser 
        { 
            Id = "mock-user-1", 
            Email = "<EMAIL>", 
            Password = "password123", 
            DisplayName = "Test User",
            SubscriptionTier = AppConstants.SubscriptionTiers.FreeAccount,
            IsEmailVerified = true
        },
        new MockUser 
        { 
            Id = "mock-user-2", 
            Email = "<EMAIL>", 
            Password = "premium123", 
            DisplayName = "Premium User",
            SubscriptionTier = AppConstants.SubscriptionTiers.PaidAccount,
            IsEmailVerified = true
        },
        new MockUser 
        { 
            Id = "mock-user-3", 
            Email = "<EMAIL>", 
            Password = "guest123", 
            DisplayName = "Guest User",
            SubscriptionTier = AppConstants.SubscriptionTiers.NoAccount,
            IsEmailVerified = false
        }
    };

    public MockAuthenticationProvider(
        ILocalStorageService localStorageService,
        IDataService dataService,
        ILogger<MockAuthenticationProvider> logger)
    {
        _localStorageService = localStorageService;
        _dataService = dataService;
        _logger = logger;
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        try
        {
            // Check if we have a cached user
            if (_currentUser != null)
                return true;

            // Try to get user from local storage
            var cachedUser = await _localStorageService.GetAsync<UserProfile>("current_user");
            if (cachedUser != null)
            {
                _currentUser = cachedUser;
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authentication status");
            return false;
        }
    }

    public async Task<AuthenticationResult> LoginAsync()
    {
        try
        {
            // For mock, we'll use the first test user (Free Account)
            var mockUser = _mockUsers.First(u => u.Email == "<EMAIL>");

            return await LoginWithMockUser(mockUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Mock login error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Mock login failed"
            };
        }
    }

    public async Task<AuthenticationResult> LoginWithEmailAsync(string email)
    {
        try
        {
            var mockUser = _mockUsers.FirstOrDefault(u => u.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
            if (mockUser == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "User not found"
                };
            }

            return await LoginWithMockUser(mockUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Mock login error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Mock login failed"
            };
        }
    }

    private async Task<AuthenticationResult> LoginWithMockUser(MockUser mockUser)
    {
        var userProfile = new UserProfile
        {
            Id = mockUser.Id,
            Email = mockUser.Email,
            DisplayName = mockUser.DisplayName,
            SubscriptionTier = mockUser.SubscriptionTier,
            CreatedAt = DateTime.UtcNow.AddDays(-30), // Simulate existing user
            LastLoginAt = DateTime.UtcNow,
            IsEmailVerified = mockUser.IsEmailVerified
        };

        // Save user profile to data service
        await _dataService.SaveUserProfileAsync(userProfile);

        // Cache user locally
        _currentUser = userProfile;
        await _localStorageService.SetAsync("current_user", _currentUser);

        _logger.LogInformation("Mock login successful for user: {Email}", mockUser.Email);

        return new AuthenticationResult
        {
            IsSuccess = true,
            User = _currentUser
        };
    }

    public async Task<AuthenticationResult> LoginWithBiometricsAsync()
    {
        try
        {
            // Simulate biometric authentication
            await Task.Delay(1000); // Simulate biometric scan time

            // Check if biometric is set up
            var biometricData = await _localStorageService.GetAsync<BiometricCredentials>("biometric_credentials");
            if (biometricData == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Biometric authentication is not set up"
                };
            }

            // Find the user
            var mockUser = _mockUsers.FirstOrDefault(u => u.Id == biometricData.UserId);
            if (mockUser == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "User not found"
                };
            }

            var userProfile = new UserProfile
            {
                Id = mockUser.Id,
                Email = mockUser.Email,
                DisplayName = mockUser.DisplayName,
                SubscriptionTier = mockUser.SubscriptionTier,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                LastLoginAt = DateTime.UtcNow,
                IsEmailVerified = mockUser.IsEmailVerified
            };

            _currentUser = userProfile;
            await _localStorageService.SetAsync("current_user", _currentUser);

            return new AuthenticationResult
            {
                IsSuccess = true,
                User = _currentUser
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Mock biometric login error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Biometric authentication failed"
            };
        }
    }

    public async Task<AuthenticationResult> SignUpAsync(string email, string password)
    {
        try
        {
            // Check if user already exists
            if (_mockUsers.Any(u => u.Email.Equals(email, StringComparison.OrdinalIgnoreCase)))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "User with this email already exists"
                };
            }

            // Create new mock user
            var newMockUser = new MockUser
            {
                Id = Guid.NewGuid().ToString(),
                Email = email,
                Password = password,
                DisplayName = email.Split('@')[0],
                SubscriptionTier = AppConstants.SubscriptionTiers.FreeAccount,
                IsEmailVerified = false // Simulate email verification needed
            };

            _mockUsers.Add(newMockUser);

            var userProfile = new UserProfile
            {
                Id = newMockUser.Id,
                Email = newMockUser.Email,
                DisplayName = newMockUser.DisplayName,
                SubscriptionTier = newMockUser.SubscriptionTier,
                CreatedAt = DateTime.UtcNow,
                LastLoginAt = DateTime.UtcNow,
                IsEmailVerified = newMockUser.IsEmailVerified
            };

            // Save user profile
            await _dataService.SaveUserProfileAsync(userProfile);

            _currentUser = userProfile;
            await _localStorageService.SetAsync("current_user", _currentUser);

            _logger.LogInformation("Mock signup successful for user: {Email}", email);

            return new AuthenticationResult
            {
                IsSuccess = true,
                User = _currentUser,
                RequiresEmailVerification = !newMockUser.IsEmailVerified
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Mock signup error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Failed to create account"
            };
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            // Clear local storage
            await _localStorageService.RemoveAsync("current_user");
            await _localStorageService.RemoveAsync("biometric_credentials");

            _currentUser = null;
            
            _logger.LogInformation("Mock logout successful");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Mock logout error");
        }
    }

    public async Task<UserProfile?> GetCurrentUserAsync()
    {
        if (_currentUser == null)
        {
            await IsAuthenticatedAsync();
        }
        return _currentUser;
    }

    public async Task<bool> VerifyEmailAsync(string token)
    {
        // Simulate email verification
        await Task.Delay(500);
        
        if (_currentUser != null)
        {
            _currentUser.IsEmailVerified = true;
            await _dataService.SaveUserProfileAsync(_currentUser);
            await _localStorageService.SetAsync("current_user", _currentUser);
            
            // Update mock user
            var mockUser = _mockUsers.FirstOrDefault(u => u.Id == _currentUser.Id);
            if (mockUser != null)
            {
                mockUser.IsEmailVerified = true;
            }
        }
        
        return true; // Always succeed for mock
    }

    public async Task<bool> SendPasswordResetAsync(string email)
    {
        // Simulate sending password reset email
        await Task.Delay(500);
        
        var userExists = _mockUsers.Any(u => u.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
        return userExists;
    }

    public async Task<bool> ResetPasswordAsync(string token, string newPassword)
    {
        // Simulate password reset
        await Task.Delay(500);
        return true; // Always succeed for mock
    }

    public async Task<bool> IsBiometricAvailableAsync()
    {
        // Simulate biometric availability check
        await Task.Delay(100);
        return true; // Always available for mock
    }

    public async Task<bool> EnableBiometricAsync()
    {
        try
        {
            if (_currentUser == null)
                return false;

            // Simulate biometric setup
            await Task.Delay(1000);

            var biometricCredentials = new BiometricCredentials
            {
                UserId = _currentUser.Id,
                EnabledAt = DateTime.UtcNow
            };

            await _localStorageService.SetAsync("biometric_credentials", biometricCredentials);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling mock biometric authentication");
            return false;
        }
    }

    public async Task DisableBiometricAsync()
    {
        await _localStorageService.RemoveAsync("biometric_credentials");
    }
}

public class MockUser
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string SubscriptionTier { get; set; } = AppConstants.Defaults.DefaultSubscriptionTier;
    public bool IsEmailVerified { get; set; } = false;
}
