using HabitBuilder.Models;

namespace HabitBuilder.Services;

public interface IAuthenticationService
{
    Task<bool> IsAuthenticatedAsync();
    Task<AuthenticationResult> LoginAsync();
    Task<AuthenticationResult> LoginWithBiometricsAsync();
    Task<AuthenticationResult> SignUpAsync(string email, string password);
    Task LogoutAsync();
    Task<UserProfile?> GetCurrentUserAsync();
    Task<bool> VerifyEmailAsync(string token);
    Task<bool> SendPasswordResetAsync(string email);
    Task<bool> ResetPasswordAsync(string token, string newPassword);
    Task<bool> IsBiometricAvailableAsync();
    Task<bool> EnableBiometricAsync();
    Task DisableBiometricAsync();
}

public class AuthenticationResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public UserProfile? User { get; set; }
    public bool RequiresEmailVerification { get; set; }
    public bool RequiresPasswordReset { get; set; }
}

public enum AuthenticationError
{
    None,
    InvalidCredentials,
    NetworkError,
    EmailNotVerified,
    AccountLocked,
    BiometricNotAvailable,
    BiometricNotEnrolled,
    BiometricAuthenticationFailed,
    UnknownError
}
