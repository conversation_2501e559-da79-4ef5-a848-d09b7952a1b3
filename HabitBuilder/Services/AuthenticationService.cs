using Microsoft.Identity.Client;
using HabitBuilder.Constants;
using HabitBuilder.Models;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly IPublicClientApplication _msalClient;
    private readonly ILocalStorageService _localStorageService;
    private readonly IDataService _dataService;
    private readonly ILogger<AuthenticationService> _logger;
    private UserProfile? _currentUser;

    public AuthenticationService(
        ILocalStorageService localStorageService,
        IDataService dataService,
        ILogger<AuthenticationService> logger)
    {
        _localStorageService = localStorageService;
        _dataService = dataService;
        _logger = logger;

        // Initialize MSAL
        _msalClient = PublicClientApplicationBuilder
            .Create(AppConstants.Auth.ClientId)
            .WithAuthority($"https://login.microsoftonline.com/{AppConstants.Auth.TenantId}")
            .WithRedirectUri(AppConstants.Auth.RedirectUri)
            .Build();
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        try
        {
            // Check if we have a cached user
            if (_currentUser != null)
                return true;

            // Try to get user from local storage
            var cachedUser = await _localStorageService.GetAsync<UserProfile>("current_user");
            if (cachedUser != null)
            {
                _currentUser = cachedUser;
                return true;
            }

            // Try silent authentication with MSAL
            var accounts = await _msalClient.GetAccountsAsync();
            if (accounts.Any())
            {
                try
                {
                    var result = await _msalClient.AcquireTokenSilent(AppConstants.Auth.Scopes, accounts.FirstOrDefault())
                        .ExecuteAsync();
                    
                    if (result != null)
                    {
                        await LoadUserFromAuthResult(result);
                        return _currentUser != null;
                    }
                }
                catch (MsalUiRequiredException)
                {
                    // Silent auth failed, user needs to sign in interactively
                    return false;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authentication status");
            return false;
        }
    }

    public async Task<AuthenticationResult> LoginAsync()
    {
        try
        {
            var result = await _msalClient.AcquireTokenInteractive(AppConstants.Auth.Scopes)
                .WithPrompt(Prompt.SelectAccount)
                .ExecuteAsync();

            if (result != null)
            {
                await LoadUserFromAuthResult(result);
                
                return new AuthenticationResult
                {
                    IsSuccess = true,
                    User = _currentUser
                };
            }

            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Authentication failed"
            };
        }
        catch (MsalException ex)
        {
            _logger.LogError(ex, "MSAL authentication error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Authentication error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "An unexpected error occurred"
            };
        }
    }

    public async Task<AuthenticationResult> LoginWithBiometricsAsync()
    {
        try
        {
            // Check if biometric is available
            if (!await IsBiometricAvailableAsync())
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Biometric authentication is not available"
                };
            }

            // Get stored biometric credentials
            var biometricData = await _localStorageService.GetAsync<BiometricCredentials>("biometric_credentials");
            if (biometricData == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Biometric authentication is not set up"
                };
            }

            // Perform biometric authentication
            var biometricResult = await PerformBiometricAuthenticationAsync();
            if (!biometricResult)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Biometric authentication failed"
                };
            }

            // Load user profile
            _currentUser = await _dataService.GetUserProfileAsync(biometricData.UserId);
            if (_currentUser != null)
            {
                await _localStorageService.SetAsync("current_user", _currentUser);
                return new AuthenticationResult
                {
                    IsSuccess = true,
                    User = _currentUser
                };
            }

            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "User profile not found"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Biometric authentication error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Biometric authentication failed"
            };
        }
    }

    public async Task<AuthenticationResult> SignUpAsync(string email, string password)
    {
        try
        {
            // For now, we'll use a simplified signup process
            // In a real implementation, this would integrate with Azure AD B2C or similar
            
            var userId = Guid.NewGuid().ToString();
            var userProfile = new UserProfile
            {
                Id = userId,
                Email = email,
                DisplayName = email.Split('@')[0],
                CreatedAt = DateTime.UtcNow,
                LastLoginAt = DateTime.UtcNow,
                IsEmailVerified = false // Would require email verification
            };

            // Save user profile
            var saveResult = await _dataService.SaveUserProfileAsync(userProfile);
            if (!saveResult)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Failed to create user account"
                };
            }

            _currentUser = userProfile;
            await _localStorageService.SetAsync("current_user", _currentUser);

            return new AuthenticationResult
            {
                IsSuccess = true,
                User = _currentUser,
                RequiresEmailVerification = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Signup error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Failed to create account"
            };
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            // Clear MSAL cache
            var accounts = await _msalClient.GetAccountsAsync();
            foreach (var account in accounts)
            {
                await _msalClient.RemoveAsync(account);
            }

            // Clear local storage
            await _localStorageService.RemoveAsync("current_user");
            await _localStorageService.RemoveAsync("biometric_credentials");

            _currentUser = null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Logout error");
        }
    }

    public async Task<UserProfile?> GetCurrentUserAsync()
    {
        if (_currentUser == null)
        {
            await IsAuthenticatedAsync();
        }
        return _currentUser;
    }

    public async Task<bool> VerifyEmailAsync(string token)
    {
        // Placeholder implementation
        // In a real app, this would verify the token with the backend
        await Task.Delay(100);
        return true;
    }

    public async Task<bool> SendPasswordResetAsync(string email)
    {
        // Placeholder implementation
        await Task.Delay(100);
        return true;
    }

    public async Task<bool> ResetPasswordAsync(string token, string newPassword)
    {
        // Placeholder implementation
        await Task.Delay(100);
        return true;
    }

    public async Task<bool> IsBiometricAvailableAsync()
    {
        try
        {
            // Platform-specific implementation would go here
            // For now, return true as a placeholder
            return await Task.FromResult(true);
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> EnableBiometricAsync()
    {
        try
        {
            if (_currentUser == null || !await IsBiometricAvailableAsync())
                return false;

            var biometricResult = await PerformBiometricAuthenticationAsync();
            if (!biometricResult)
                return false;

            var biometricCredentials = new BiometricCredentials
            {
                UserId = _currentUser.Id,
                EnabledAt = DateTime.UtcNow
            };

            await _localStorageService.SetAsync("biometric_credentials", biometricCredentials);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling biometric authentication");
            return false;
        }
    }

    public async Task DisableBiometricAsync()
    {
        await _localStorageService.RemoveAsync("biometric_credentials");
    }

    private async Task LoadUserFromAuthResult(Microsoft.Identity.Client.AuthenticationResult result)
    {
        try
        {
            var userId = result.Account.HomeAccountId.Identifier;
            var email = result.Account.Username;
            var displayName = result.ClaimsPrincipal?.FindFirst("name")?.Value ?? email.Split('@')[0];

            // Try to get existing user profile
            _currentUser = await _dataService.GetUserProfileAsync(userId);
            
            if (_currentUser == null)
            {
                // Create new user profile
                _currentUser = new UserProfile
                {
                    Id = userId,
                    Email = email,
                    DisplayName = displayName,
                    CreatedAt = DateTime.UtcNow,
                    IsEmailVerified = true
                };
                
                await _dataService.SaveUserProfileAsync(_currentUser);
            }
            else
            {
                // Update last login
                _currentUser.LastLoginAt = DateTime.UtcNow;
                await _dataService.SaveUserProfileAsync(_currentUser);
            }

            // Cache user locally
            await _localStorageService.SetAsync("current_user", _currentUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading user from auth result");
        }
    }

    private async Task<bool> PerformBiometricAuthenticationAsync()
    {
        // Platform-specific biometric authentication would go here
        // For now, return true as a placeholder
        return await Task.FromResult(true);
    }
}

public class BiometricCredentials
{
    public string UserId { get; set; } = string.Empty;
    public DateTime EnabledAt { get; set; }
}
