using HabitBuilder.Models;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly IAuthenticationProvider _authProvider;
    private readonly ILogger<AuthenticationService> _logger;

    public AuthenticationService(
        IAuthenticationProvider authProvider,
        ILogger<AuthenticationService> logger)
    {
        _authProvider = authProvider;
        _logger = logger;
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        try
        {
            return await _authProvider.IsAuthenticatedAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authentication status");
            return false;
        }
    }

    public async Task<AuthenticationResult> LoginAsync()
    {
        try
        {
            return await _authProvider.LoginAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Authentication error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "An unexpected error occurred"
            };
        }
    }

    public async Task<AuthenticationResult> LoginWithBiometricsAsync()
    {
        try
        {
            return await _authProvider.LoginWithBiometricsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Biometric authentication error");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Biometric authentication failed"
            };
        }
    }

    public async Task<AuthenticationResult> SignUpAsync(string email, string password)
    {
        return await _authProvider.SignUpAsync(email, password);
    }

    public async Task LogoutAsync()
    {
        await _authProvider.LogoutAsync();
    }

    public async Task<UserProfile?> GetCurrentUserAsync()
    {
        return await _authProvider.GetCurrentUserAsync();
    }

    public async Task<bool> VerifyEmailAsync(string token)
    {
        return await _authProvider.VerifyEmailAsync(token);
    }

    public async Task<bool> SendPasswordResetAsync(string email)
    {
        return await _authProvider.SendPasswordResetAsync(email);
    }

    public async Task<bool> ResetPasswordAsync(string token, string newPassword)
    {
        return await _authProvider.ResetPasswordAsync(token, newPassword);
    }

    public async Task<bool> IsBiometricAvailableAsync()
    {
        return await _authProvider.IsBiometricAvailableAsync();
    }

    public async Task<bool> EnableBiometricAsync()
    {
        return await _authProvider.EnableBiometricAsync();
    }

    public async Task DisableBiometricAsync()
    {
        await _authProvider.DisableBiometricAsync();
    }

    public async Task<AuthenticationResult> LoginWithEmailAsync(string email)
    {
        return await _authProvider.LoginWithEmailAsync(email);
    }
}
