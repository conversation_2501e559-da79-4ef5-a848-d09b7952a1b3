namespace HabitBuilder.Services;

public class AuthenticationConfiguration
{
    public AuthenticationProviderType ProviderType { get; set; } = AuthenticationProviderType.Mock;
    public bool UseMockForTesting { get; set; } = true;
    
    // Azure configuration
    public string AzureClientId { get; set; } = string.Empty;
    public string AzureTenantId { get; set; } = string.Empty;
    public string AzureRedirectUri { get; set; } = string.Empty;
    
    // Mock configuration
    public bool EnableMockBiometrics { get; set; } = true;
    public bool SimulateNetworkDelay { get; set; } = true;
    public int MockDelayMs { get; set; } = 500;
}

public static class AuthenticationConfigurationExtensions
{
    public static AuthenticationConfiguration GetAuthConfiguration()
    {
        // In a real app, this would come from appsettings.json or environment variables
        return new AuthenticationConfiguration
        {
#if DEBUG
            ProviderType = AuthenticationProviderType.Mock,
            UseMockForTesting = true,
#else
            ProviderType = AuthenticationProviderType.Azure,
            UseMockForTesting = false,
#endif
            EnableMockBiometrics = true,
            SimulateNetworkDelay = true,
            MockDelayMs = 500
        };
    }
}
