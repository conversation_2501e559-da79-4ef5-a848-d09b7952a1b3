using HabitBuilder.Models;

namespace HabitBuilder.Services;

public interface IDataService
{
    // User operations
    Task<UserProfile?> GetUserProfileAsync(string userId);
    Task<bool> SaveUserProfileAsync(UserProfile userProfile);
    Task<bool> DeleteUserProfileAsync(string userId);

    // Activity operations
    Task<List<ActivityViewModel>> GetActivitiesAsync(string userId, bool includeArchived = false);
    Task<ActivityViewModel?> GetActivityAsync(string userId, string activityId);
    Task<bool> SaveActivityAsync(string userId, ActivityViewModel activity);
    Task<bool> DeleteActivityAsync(string userId, string activityId);
    Task<bool> ArchiveActivityAsync(string userId, string activityId);
    Task<bool> UnarchiveActivityAsync(string userId, string activityId);

    // Activity completion operations
    Task<bool> CompleteActivityAsync(string userId, string activityId, DateTime? completionDate = null);
    Task<bool> UndoActivityCompletionAsync(string userId, string activityId, DateTime? completionDate = null);
    Task<List<ActivityCompletion>> GetActivityCompletionsAsync(string userId, string activityId, DateTime? startDate = null, DateTime? endDate = null);

    // Sync operations
    Task<bool> SyncDataAsync(string userId);
    Task<DateTime?> GetLastSyncTimeAsync(string userId);
    Task<bool> HasPendingChangesAsync(string userId);

    // Offline support
    Task<bool> SaveOfflineAsync<T>(string key, T data);
    Task<T?> GetOfflineAsync<T>(string key);
    Task<bool> DeleteOfflineAsync(string key);
    Task<bool> IsOnlineAsync();
}
