using Azure.Data.Tables;
using HabitBuilder.Constants;
using HabitBuilder.Models;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.Services;

public class AzureDataService : IDataService
{
    private readonly TableServiceClient _tableServiceClient;
    private readonly ILocalStorageService _localStorageService;
    private readonly ILogger<AzureDataService> _logger;
    private readonly TableClient _usersTable;
    private readonly TableClient _activitiesTable;
    private readonly TableClient _completionsTable;

    public AzureDataService(ILocalStorageService localStorageService, ILogger<AzureDataService> logger)
    {
        _localStorageService = localStorageService;
        _logger = logger;
        
        // Initialize Azure Table Storage
        _tableServiceClient = new TableServiceClient(AppConstants.Azure.TableStorageConnectionString);
        _usersTable = _tableServiceClient.GetTableClient(AppConstants.Azure.UsersTableName);
        _activitiesTable = _tableServiceClient.GetTableClient(AppConstants.Azure.ActivitiesTableName);
        _completionsTable = _tableServiceClient.GetTableClient(AppConstants.Azure.CompletionsTableName);
        
        // Ensure tables exist
        InitializeTablesAsync().ConfigureAwait(false);
    }

    private async Task InitializeTablesAsync()
    {
        try
        {
            await _usersTable.CreateIfNotExistsAsync();
            await _activitiesTable.CreateIfNotExistsAsync();
            await _completionsTable.CreateIfNotExistsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Azure tables");
        }
    }

    public async Task<UserProfile?> GetUserProfileAsync(string userId)
    {
        try
        {
            // Try to get from Azure first
            if (await IsOnlineAsync())
            {
                var response = await _usersTable.GetEntityAsync<User>("Users", userId);
                if (response.HasValue)
                {
                    var user = response.Value;
                    var profile = MapToUserProfile(user);
                    
                    // Cache locally
                    await _localStorageService.SetAsync($"{AppConstants.StorageKeys.UserProfile}_{userId}", profile);
                    return profile;
                }
            }
            
            // Fallback to local storage
            return await _localStorageService.GetAsync<UserProfile>($"{AppConstants.StorageKeys.UserProfile}_{userId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user profile for {UserId}", userId);
            
            // Fallback to local storage
            return await _localStorageService.GetAsync<UserProfile>($"{AppConstants.StorageKeys.UserProfile}_{userId}");
        }
    }

    public async Task<bool> SaveUserProfileAsync(UserProfile userProfile)
    {
        try
        {
            // Save locally first
            await _localStorageService.SetAsync($"{AppConstants.StorageKeys.UserProfile}_{userProfile.Id}", userProfile);
            
            // Try to sync to Azure if online
            if (await IsOnlineAsync())
            {
                var user = MapToUser(userProfile);
                await _usersTable.UpsertEntityAsync(user);
                return true;
            }
            
            // Mark as pending sync
            await MarkPendingSync(userProfile.Id, "user_profile", userProfile);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save user profile for {UserId}", userProfile.Id);
            return false;
        }
    }

    public async Task<bool> DeleteUserProfileAsync(string userId)
    {
        try
        {
            // Delete locally
            await _localStorageService.RemoveAsync($"{AppConstants.StorageKeys.UserProfile}_{userId}");
            
            // Try to delete from Azure if online
            if (await IsOnlineAsync())
            {
                await _usersTable.DeleteEntityAsync("Users", userId);
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete user profile for {UserId}", userId);
            return false;
        }
    }

    public async Task<List<ActivityViewModel>> GetActivitiesAsync(string userId, bool includeArchived = false)
    {
        try
        {
            var activities = new List<ActivityViewModel>();
            
            // Try to get from Azure first
            if (await IsOnlineAsync())
            {
                var query = _activitiesTable.QueryAsync<Activity>(a => a.PartitionKey == userId);
                await foreach (var activity in query)
                {
                    if (!includeArchived && activity.IsArchived)
                        continue;
                        
                    var viewModel = await MapToActivityViewModel(userId, activity);
                    activities.Add(viewModel);
                }
                
                // Cache locally
                await _localStorageService.SetAsync($"{AppConstants.StorageKeys.Activities}_{userId}", activities);
            }
            else
            {
                // Fallback to local storage
                var cachedActivities = await _localStorageService.GetAsync<List<ActivityViewModel>>($"{AppConstants.StorageKeys.Activities}_{userId}");
                if (cachedActivities != null)
                {
                    activities = includeArchived ? cachedActivities : cachedActivities.Where(a => !a.IsArchived).ToList();
                }
            }
            
            return activities.OrderBy(a => a.SortOrder).ThenBy(a => a.CreatedAt).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get activities for {UserId}", userId);
            return new List<ActivityViewModel>();
        }
    }

    private async Task<ActivityViewModel> MapToActivityViewModel(string userId, Activity activity)
    {
        var viewModel = new ActivityViewModel
        {
            Id = activity.RowKey,
            Name = activity.Name,
            Description = activity.Description,
            Frequency = activity.Frequency,
            TargetCount = activity.TargetCount,
            CreatedAt = activity.CreatedAt,
            ArchivedAt = activity.ArchivedAt,
            IsArchived = activity.IsArchived,
            StartDate = activity.StartDate,
            EndDate = activity.EndDate,
            Color = activity.Color,
            SortOrder = activity.SortOrder
        };

        // Load completions for this activity
        await LoadActivityCompletions(userId, viewModel);
        
        return viewModel;
    }

    private async Task LoadActivityCompletions(string userId, ActivityViewModel activity)
    {
        try
        {
            var now = DateTime.UtcNow;
            var weekStart = now.AddDays(-(int)now.DayOfWeek);
            var fortnightStart = weekStart.AddDays(-7);
            
            var completions = await GetActivityCompletionsAsync(userId, activity.Id, fortnightStart, now);
            
            // Initialize completion arrays
            activity.WeeklyCompletions = InitializeDayCompletions(weekStart, 7);
            activity.FortnightlyCompletions = InitializeDayCompletions(fortnightStart, 14);
            
            // Fill in actual completions
            foreach (var completion in completions)
            {
                var date = completion.CompletedAt.Date;
                
                // Update weekly completions
                var weeklyDay = activity.WeeklyCompletions.FirstOrDefault(d => d.Date.Date == date);
                if (weeklyDay != null)
                {
                    weeklyDay.Count += completion.Count;
                    activity.CurrentWeekCompletions += completion.Count;
                }
                
                // Update fortnightly completions
                var fortnightlyDay = activity.FortnightlyCompletions.FirstOrDefault(d => d.Date.Date == date);
                if (fortnightlyDay != null)
                {
                    fortnightlyDay.Count += completion.Count;
                    activity.CurrentFortnightCompletions += completion.Count;
                }
            }
            
            // Set last completed date
            activity.LastCompletedAt = completions.OrderByDescending(c => c.CompletedAt).FirstOrDefault()?.CompletedAt;
            
            // Check if can complete today (not already at target for today)
            var todayCompletion = activity.WeeklyCompletions.FirstOrDefault(d => d.IsToday);
            activity.CanCompleteToday = todayCompletion?.Count < activity.TargetCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load completions for activity {ActivityId}", activity.Id);
        }
    }

    private List<DayCompletion> InitializeDayCompletions(DateTime startDate, int days)
    {
        var completions = new List<DayCompletion>();
        for (int i = 0; i < days; i++)
        {
            completions.Add(new DayCompletion
            {
                Date = startDate.AddDays(i),
                Count = 0
            });
        }
        return completions;
    }

    // Additional helper methods would continue here...
    
    private UserProfile MapToUserProfile(User user)
    {
        var notificationDaysDisabled = new List<int>();
        if (!string.IsNullOrEmpty(user.NotificationDaysDisabled))
        {
            try
            {
                notificationDaysDisabled = System.Text.Json.JsonSerializer.Deserialize<List<int>>(user.NotificationDaysDisabled) ?? new List<int>();
            }
            catch { }
        }

        return new UserProfile
        {
            Id = user.RowKey,
            Email = user.Email,
            DisplayName = user.DisplayName,
            SubscriptionTier = user.SubscriptionTier,
            ResolveLevel = user.ResolveLevel,
            Language = user.Language,
            CreatedAt = user.CreatedAt,
            LastLoginAt = user.LastLoginAt,
            IsEmailVerified = user.IsEmailVerified,
            Settings = new UserSettings
            {
                NotificationsEnabled = user.NotificationsEnabled,
                NotificationHour = user.NotificationHour,
                NotificationMinute = user.NotificationMinute,
                NotificationDaysDisabled = notificationDaysDisabled,
                VacationStartDate = user.VacationStartDate,
                VacationEndDate = user.VacationEndDate,
                IsDarkMode = user.IsDarkMode
            }
        };
    }

    private User MapToUser(UserProfile profile)
    {
        var notificationDaysJson = System.Text.Json.JsonSerializer.Serialize(profile.Settings.NotificationDaysDisabled);
        
        return new User
        {
            RowKey = profile.Id,
            Email = profile.Email,
            DisplayName = profile.DisplayName,
            SubscriptionTier = profile.SubscriptionTier,
            ResolveLevel = profile.ResolveLevel,
            Language = profile.Language,
            CreatedAt = profile.CreatedAt,
            LastLoginAt = profile.LastLoginAt,
            IsEmailVerified = profile.IsEmailVerified,
            NotificationsEnabled = profile.Settings.NotificationsEnabled,
            NotificationHour = profile.Settings.NotificationHour,
            NotificationMinute = profile.Settings.NotificationMinute,
            NotificationDaysDisabled = notificationDaysJson,
            VacationStartDate = profile.Settings.VacationStartDate,
            VacationEndDate = profile.Settings.VacationEndDate,
            IsDarkMode = profile.Settings.IsDarkMode
        };
    }

    // Placeholder implementations for remaining interface methods
    public Task<ActivityViewModel?> GetActivityAsync(string userId, string activityId) => throw new NotImplementedException();
    public Task<bool> SaveActivityAsync(string userId, ActivityViewModel activity) => throw new NotImplementedException();
    public Task<bool> DeleteActivityAsync(string userId, string activityId) => throw new NotImplementedException();
    public Task<bool> ArchiveActivityAsync(string userId, string activityId) => throw new NotImplementedException();
    public Task<bool> UnarchiveActivityAsync(string userId, string activityId) => throw new NotImplementedException();
    public Task<bool> CompleteActivityAsync(string userId, string activityId, DateTime? completionDate = null) => throw new NotImplementedException();
    public Task<bool> UndoActivityCompletionAsync(string userId, string activityId, DateTime? completionDate = null) => throw new NotImplementedException();
    public Task<List<ActivityCompletion>> GetActivityCompletionsAsync(string userId, string activityId, DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<bool> SyncDataAsync(string userId) => throw new NotImplementedException();
    public Task<DateTime?> GetLastSyncTimeAsync(string userId) => throw new NotImplementedException();
    public Task<bool> HasPendingChangesAsync(string userId) => throw new NotImplementedException();
    public Task<bool> SaveOfflineAsync<T>(string key, T data) => throw new NotImplementedException();
    public Task<T?> GetOfflineAsync<T>(string key) => throw new NotImplementedException();
    public Task<bool> DeleteOfflineAsync(string key) => throw new NotImplementedException();
    public Task<bool> IsOnlineAsync() => Task.FromResult(true); // Simplified for now
    
    private Task MarkPendingSync<T>(string userId, string type, T data) => Task.CompletedTask; // Simplified for now
}
