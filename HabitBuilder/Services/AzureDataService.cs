using HabitBuilder.Models;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.Services;

public class AzureDataService : IDataService
{
    private readonly ILocalStorageService _localStorageService;
    private readonly ILogger<AzureDataService> _logger;

    public AzureDataService(ILocalStorageService localStorageService, ILogger<AzureDataService> logger)
    {
        _localStorageService = localStorageService;
        _logger = logger;
    }

    public async Task<UserProfile?> GetUserProfileAsync(string userId)
    {
        try
        {
            return await _localStorageService.GetAsync<UserProfile>($"user_profile_{userId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user profile for {UserId}", userId);
            return null;
        }
    }

    public async Task<bool> SaveUserProfileAsync(UserProfile userProfile)
    {
        try
        {
            await _localStorageService.SetAsync($"user_profile_{userProfile.Id}", userProfile);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save user profile for {UserId}", userProfile.Id);
            return false;
        }
    }

    public async Task<bool> DeleteUserProfileAsync(string userId)
    {
        try
        {
            await _localStorageService.RemoveAsync($"user_profile_{userId}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete user profile for {UserId}", userId);
            return false;
        }
    }

    public async Task<List<ActivityViewModel>> GetActivitiesAsync(string userId, bool includeArchived = false)
    {
        try
        {
            var activities = await _localStorageService.GetAsync<List<ActivityViewModel>>($"activities_{userId}") ?? new List<ActivityViewModel>();
            return includeArchived ? activities : activities.Where(a => !a.IsArchived).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get activities for {UserId}", userId);
            return new List<ActivityViewModel>();
        }
    }

    public async Task<ActivityViewModel?> GetActivityAsync(string userId, string activityId)
    {
        try
        {
            var activities = await GetActivitiesAsync(userId, true);
            return activities.FirstOrDefault(a => a.Id == activityId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get activity {ActivityId} for {UserId}", activityId, userId);
            return null;
        }
    }

    public async Task<bool> SaveActivityAsync(string userId, ActivityViewModel activity)
    {
        try
        {
            var activities = await GetActivitiesAsync(userId, true);
            var existingIndex = activities.FindIndex(a => a.Id == activity.Id);

            if (existingIndex >= 0)
            {
                activities[existingIndex] = activity;
            }
            else
            {
                activities.Add(activity);
            }

            await _localStorageService.SetAsync($"activities_{userId}", activities);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save activity {ActivityId} for {UserId}", activity.Id, userId);
            return false;
        }
    }

    public async Task<bool> DeleteActivityAsync(string userId, string activityId)
    {
        try
        {
            var activities = await GetActivitiesAsync(userId, true);
            activities.RemoveAll(a => a.Id == activityId);
            await _localStorageService.SetAsync($"activities_{userId}", activities);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete activity {ActivityId} for {UserId}", activityId, userId);
            return false;
        }
    }

    public async Task<bool> ArchiveActivityAsync(string userId, string activityId)
    {
        try
        {
            var activity = await GetActivityAsync(userId, activityId);
            if (activity != null)
            {
                activity.IsArchived = true;
                activity.ArchivedAt = DateTime.UtcNow;
                return await SaveActivityAsync(userId, activity);
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to archive activity {ActivityId} for {UserId}", activityId, userId);
            return false;
        }
    }

    public async Task<bool> UnarchiveActivityAsync(string userId, string activityId)
    {
        try
        {
            var activity = await GetActivityAsync(userId, activityId);
            if (activity != null)
            {
                activity.IsArchived = false;
                activity.ArchivedAt = null;
                return await SaveActivityAsync(userId, activity);
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unarchive activity {ActivityId} for {UserId}", activityId, userId);
            return false;
        }
    }

    public async Task<bool> CompleteActivityAsync(string userId, string activityId, DateTime? completionDate = null)
    {
        try
        {
            var completions = await GetActivityCompletionsAsync(userId, activityId);
            var date = completionDate?.Date ?? DateTime.Today;

            var completion = new ActivityCompletion
            {
                RowKey = Guid.NewGuid().ToString(),
                ActivityId = activityId,
                CompletedAt = date,
                Count = 1
            };

            completions.Add(completion);
            await _localStorageService.SetAsync($"completions_{userId}_{activityId}", completions);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete activity {ActivityId} for {UserId}", activityId, userId);
            return false;
        }
    }

    public async Task<bool> UndoActivityCompletionAsync(string userId, string activityId, DateTime? completionDate = null)
    {
        try
        {
            var completions = await GetActivityCompletionsAsync(userId, activityId);
            var date = completionDate?.Date ?? DateTime.Today;

            var completion = completions.LastOrDefault(c => c.CompletedAt.Date == date);
            if (completion != null)
            {
                completions.Remove(completion);
                await _localStorageService.SetAsync($"completions_{userId}_{activityId}", completions);
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to undo activity completion {ActivityId} for {UserId}", activityId, userId);
            return false;
        }
    }

    public async Task<List<ActivityCompletion>> GetActivityCompletionsAsync(string userId, string activityId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var completions = await _localStorageService.GetAsync<List<ActivityCompletion>>($"completions_{userId}_{activityId}") ?? new List<ActivityCompletion>();

            if (startDate.HasValue || endDate.HasValue)
            {
                completions = completions.Where(c =>
                    (!startDate.HasValue || c.CompletedAt >= startDate.Value) &&
                    (!endDate.HasValue || c.CompletedAt <= endDate.Value)
                ).ToList();
            }

            return completions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get completions for activity {ActivityId} for {UserId}", activityId, userId);
            return new List<ActivityCompletion>();
        }
    }

    public async Task<bool> SyncDataAsync(string userId)
    {
        // Placeholder for sync implementation
        await Task.Delay(100);
        return true;
    }

    public async Task<DateTime?> GetLastSyncTimeAsync(string userId)
    {
        return await _localStorageService.GetAsync<DateTime?>($"last_sync_{userId}");
    }

    public async Task<bool> HasPendingChangesAsync(string userId)
    {
        // Placeholder implementation
        return false;
    }

    public async Task<bool> SaveOfflineAsync<T>(string key, T data)
    {
        await _localStorageService.SetAsync(key, data);
        return true;
    }

    public async Task<T?> GetOfflineAsync<T>(string key)
    {
        return await _localStorageService.GetAsync<T>(key);
    }

    public async Task<bool> DeleteOfflineAsync(string key)
    {
        await _localStorageService.RemoveAsync(key);
        return true;
    }

    public async Task<bool> IsOnlineAsync()
    {
        // Simplified implementation - in real app would check network connectivity
        return await Task.FromResult(true);
    }
}
