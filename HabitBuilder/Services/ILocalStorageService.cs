namespace HabitBuilder.Services;

public interface ILocalStorageService
{
    Task<T?> GetAsync<T>(string key);
    Task SetAsync<T>(string key, T value);
    Task RemoveAsync(string key);
    Task ClearAsync();
    Task<bool> ContainsKeyAsync(string key);
    Task<List<string>> GetAllKeysAsync();
}

public class LocalStorageService : ILocalStorageService
{
    private readonly string _dataDirectory;

    public LocalStorageService()
    {
        _dataDirectory = Path.Combine(FileSystem.AppDataDirectory, "HabitBuilder");
        Directory.CreateDirectory(_dataDirectory);
    }

    public async Task<T?> GetAsync<T>(string key)
    {
        try
        {
            var filePath = GetFilePath(key);
            if (!File.Exists(filePath))
                return default;

            var json = await File.ReadAllTextAsync(filePath);
            if (string.IsNullOrEmpty(json))
                return default;

            return System.Text.Json.JsonSerializer.Deserialize<T>(json);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error reading from local storage: {ex.Message}");
            return default;
        }
    }

    public async Task SetAsync<T>(string key, T value)
    {
        try
        {
            var filePath = GetFilePath(key);
            var json = System.Text.Json.JsonSerializer.Serialize(value);
            await File.WriteAllTextAsync(filePath, json);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error writing to local storage: {ex.Message}");
            throw;
        }
    }

    public async Task RemoveAsync(string key)
    {
        try
        {
            var filePath = GetFilePath(key);
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error removing from local storage: {ex.Message}");
        }
        
        await Task.CompletedTask;
    }

    public async Task ClearAsync()
    {
        try
        {
            if (Directory.Exists(_dataDirectory))
            {
                Directory.Delete(_dataDirectory, true);
                Directory.CreateDirectory(_dataDirectory);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error clearing local storage: {ex.Message}");
        }
        
        await Task.CompletedTask;
    }

    public async Task<bool> ContainsKeyAsync(string key)
    {
        var filePath = GetFilePath(key);
        return File.Exists(filePath);
    }

    public async Task<List<string>> GetAllKeysAsync()
    {
        try
        {
            if (!Directory.Exists(_dataDirectory))
                return new List<string>();

            var files = Directory.GetFiles(_dataDirectory, "*.json");
            return files.Select(f => Path.GetFileNameWithoutExtension(f)).ToList();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error getting all keys: {ex.Message}");
            return new List<string>();
        }
    }

    private string GetFilePath(string key)
    {
        var safeKey = string.Join("_", key.Split(Path.GetInvalidFileNameChars()));
        return Path.Combine(_dataDirectory, $"{safeKey}.json");
    }
}
