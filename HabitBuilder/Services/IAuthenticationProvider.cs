using HabitBuilder.Models;

namespace HabitBuilder.Services;

public interface IAuthenticationProvider
{
    Task<AuthenticationResult> LoginAsync();
    Task<AuthenticationResult> LoginWithBiometricsAsync();
    Task<AuthenticationResult> SignUpAsync(string email, string password);
    Task LogoutAsync();
    Task<UserProfile?> GetCurrentUserAsync();
    Task<bool> IsAuthenticatedAsync();
    Task<bool> VerifyEmailAsync(string token);
    Task<bool> SendPasswordResetAsync(string email);
    Task<bool> ResetPasswordAsync(string token, string newPassword);
    Task<bool> IsBiometricAvailableAsync();
    Task<bool> EnableBiometricAsync();
    Task DisableBiometricAsync();

    // Mock-specific method for testing
    Task<AuthenticationResult> LoginWithEmailAsync(string email);
}

public enum AuthenticationProviderType
{
    Mock,
    Azure,
    Firebase,
    Custom
}
