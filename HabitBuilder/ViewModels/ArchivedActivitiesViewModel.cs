using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using HabitBuilder.Models;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.ViewModels;

public partial class ArchivedActivitiesViewModel : ObservableObject
{
    private readonly IDataService _dataService;
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<ArchivedActivitiesViewModel> _logger;

    [ObservableProperty]
    private List<ActivityViewModel> archivedActivities = new();

    [ObservableProperty]
    private bool isLoading = false;

    public ArchivedActivitiesViewModel(IDataService dataService, IAuthenticationService authenticationService, ILogger<ArchivedActivitiesViewModel> logger)
    {
        _dataService = dataService;
        _authenticationService = authenticationService;
        _logger = logger;
    }

    [RelayCommand]
    private async Task LoadArchivedActivitiesAsync()
    {
        try
        {
            IsLoading = true;
            var user = await _authenticationService.GetCurrentUserAsync();
            if (user != null)
            {
                var allActivities = await _dataService.GetActivitiesAsync(user.Id, includeArchived: true);
                ArchivedActivities = allActivities.Where(a => a.IsArchived).ToList();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading archived activities");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task UnarchiveActivityAsync(ActivityViewModel activity)
    {
        try
        {
            var user = await _authenticationService.GetCurrentUserAsync();
            if (user != null)
            {
                await _dataService.UnarchiveActivityAsync(user.Id, activity.Id);
                await LoadArchivedActivitiesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unarchiving activity");
        }
    }
}
