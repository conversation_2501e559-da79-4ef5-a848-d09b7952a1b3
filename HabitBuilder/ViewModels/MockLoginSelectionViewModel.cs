using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.ViewModels;

public partial class MockLoginSelectionViewModel : ObservableObject
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<MockLoginSelectionViewModel> _logger;

    [ObservableProperty]
    private string customEmail = string.Empty;

    [ObservableProperty]
    private string customPassword = string.Empty;

    [ObservableProperty]
    private bool isLoading = false;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    public MockLoginSelectionViewModel(
        IAuthenticationService authenticationService,
        ILogger<MockLoginSelectionViewModel> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    [RelayCommand]
    private async Task LoginAsTestUserAsync(string email)
    {
        if (IsLoading) return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // Use the specific email to login as that test user
            var result = await _authenticationService.LoginWithEmailAsync(email);

            if (result.IsSuccess)
            {
                // For demo purposes, let's skip onboarding for existing test users
                // and go directly to the main page
                await Shell.Current.GoToAsync("//main");
            }
            else
            {
                ErrorMessage = result.ErrorMessage ?? "Login failed";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during test user login");
            ErrorMessage = "An unexpected error occurred";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task LoginWithCustomCredentialsAsync()
    {
        if (IsLoading) return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(CustomEmail) || string.IsNullOrWhiteSpace(CustomPassword))
            {
                ErrorMessage = "Please enter both email and password";
                return;
            }

            // For custom credentials, we'll use the signup method to create a new user
            var result = await _authenticationService.SignUpAsync(CustomEmail, CustomPassword);
            
            if (result.IsSuccess)
            {
                // Navigate to onboarding for new users
                await Shell.Current.GoToAsync("//onboarding");
            }
            else
            {
                ErrorMessage = result.ErrorMessage ?? "Login failed";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during custom credentials login");
            ErrorMessage = "An unexpected error occurred";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task NavigateToRegularLoginAsync()
    {
        await Shell.Current.GoToAsync("login");
    }

    partial void OnCustomEmailChanged(string value)
    {
        ErrorMessage = string.Empty;
    }

    partial void OnCustomPasswordChanged(string value)
    {
        ErrorMessage = string.Empty;
    }
}
