using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using HabitBuilder.Constants;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.ViewModels;

public partial class OnboardingViewModel : ObservableObject
{
    private readonly IAuthenticationService _authenticationService;
    private readonly IDataService _dataService;
    private readonly ILocalStorageService _localStorageService;
    private readonly ILogger<OnboardingViewModel> _logger;

    [ObservableProperty]
    private bool showLanguageSelection = true;

    [ObservableProperty]
    private bool showResolveLevelSelection = false;

    [ObservableProperty]
    private List<string> languages = new() { "English", "Spanish", "French", "German", "Italian" };

    [ObservableProperty]
    private string selectedLanguage = AppConstants.Defaults.DefaultLanguage;

    public OnboardingViewModel(
        IAuthenticationService authenticationService,
        IDataService dataService,
        ILocalStorageService localStorageService,
        ILogger<OnboardingViewModel> logger)
    {
        _authenticationService = authenticationService;
        _dataService = dataService;
        _localStorageService = localStorageService;
        _logger = logger;
    }

    [RelayCommand]
    private async Task ContinueFromLanguageAsync()
    {
        try
        {
            // Save selected language
            await _localStorageService.SetAsync(AppConstants.StorageKeys.SelectedLanguage, SelectedLanguage);
            
            // Move to resolve level selection
            ShowLanguageSelection = false;
            ShowResolveLevelSelection = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving language selection");
        }
    }

    [RelayCommand]
    private async Task SelectResolveLevelAsync(string resolveLevel)
    {
        try
        {
            // Save resolve level
            await _localStorageService.SetAsync(AppConstants.StorageKeys.ResolveLevel, resolveLevel);
            
            // Update user profile if authenticated
            var user = await _authenticationService.GetCurrentUserAsync();
            if (user != null)
            {
                user.Language = SelectedLanguage;
                user.ResolveLevel = resolveLevel;
                await _dataService.SaveUserProfileAsync(user);
            }
            
            // Mark onboarding as complete
            await _localStorageService.SetAsync(AppConstants.StorageKeys.IsFirstLaunch, false);
            
            // Navigate to main page
            await Shell.Current.GoToAsync("//main");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing onboarding");
            await Shell.Current.DisplayAlert("Error", "Failed to complete setup. Please try again.", "OK");
        }
    }
}
