using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using HabitBuilder.Models;
using HabitBuilder.Constants;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.ViewModels;

public partial class ActivityHistoryViewModel : ObservableObject, IQueryAttributable
{
    private readonly IDataService _dataService;
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<ActivityHistoryViewModel> _logger;

    [ObservableProperty]
    private string activityId = string.Empty;

    [ObservableProperty]
    private string activityName = string.Empty;

    [ObservableProperty]
    private string activityDescription = string.Empty;

    [ObservableProperty]
    private string activityColor = "#512BD4";

    [ObservableProperty]
    private string frequencyText = string.Empty;

    [ObservableProperty]
    private List<DayCompletion> currentPeriodCompletions = new();

    [ObservableProperty]
    private int gridSpan = 7;

    [ObservableProperty]
    private string progressText = string.Empty;

    [ObservableProperty]
    private List<WeeklyScore> weeklyHistory = new();

    [ObservableProperty]
    private bool showUpgradePrompt = false;

    [ObservableProperty]
    private bool isLoading = false;

    public ActivityHistoryViewModel(
        IDataService dataService,
        IAuthenticationService authenticationService,
        ILogger<ActivityHistoryViewModel> logger)
    {
        _dataService = dataService;
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public void ApplyQueryAttributes(IDictionary<string, object> query)
    {
        if (query.TryGetValue("activityId", out var activityIdObj))
        {
            ActivityId = activityIdObj.ToString() ?? string.Empty;
            LoadActivityHistoryAsync().ConfigureAwait(false);
        }
    }

    [RelayCommand]
    private async Task LoadActivityHistoryAsync()
    {
        if (string.IsNullOrEmpty(ActivityId)) return;

        try
        {
            IsLoading = true;
            
            var user = await _authenticationService.GetCurrentUserAsync();
            if (user == null) return;

            var activity = await _dataService.GetActivityAsync(user.Id, ActivityId);
            if (activity == null) return;

            // Set activity info
            ActivityName = activity.Name;
            ActivityDescription = activity.Description;
            ActivityColor = activity.Color;
            FrequencyText = $"{activity.TargetCount} times per {activity.Frequency.ToLower()}";

            // Set current period completions
            if (activity.Frequency == AppConstants.ActivityFrequencies.Fortnightly)
            {
                CurrentPeriodCompletions = activity.FortnightlyCompletions;
                GridSpan = 7; // 2 rows of 7 days
            }
            else
            {
                CurrentPeriodCompletions = activity.WeeklyCompletions;
                GridSpan = 7; // 1 row of 7 days
            }

            // Set progress text
            var completed = CurrentPeriodCompletions.Sum(c => c.Count);
            var target = activity.TargetCount;
            var percentage = target > 0 ? (double)completed / target * 100 : 0;
            ProgressText = $"{completed}/{target} completed ({percentage:F0}%)";

            // Load weekly history
            await LoadWeeklyHistoryAsync(user.Id, activity);

            // Check if should show upgrade prompt
            ShowUpgradePrompt = user.SubscriptionTier != AppConstants.SubscriptionTiers.PaidAccount && 
                               WeeklyHistory.Count >= AppConstants.Scoring.WeeklyHistoryLimit;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading activity history");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task UpgradeAsync()
    {
        await Shell.Current.DisplayAlert("Upgrade", "Upgrade functionality coming soon!", "OK");
    }

    private async Task LoadWeeklyHistoryAsync(string userId, ActivityViewModel activity)
    {
        try
        {
            var now = DateTime.UtcNow;
            var weeksToLoad = 12; // Load up to 12 weeks of history
            
            var history = new List<WeeklyScore>();
            
            for (int i = 0; i < weeksToLoad; i++)
            {
                var weekStart = now.AddDays(-(int)now.DayOfWeek - (i * 7));
                var weekEnd = weekStart.AddDays(6);
                
                // Don't include future weeks
                if (weekStart > now) continue;
                
                var completions = await _dataService.GetActivityCompletionsAsync(
                    userId, 
                    activity.Id, 
                    weekStart, 
                    weekEnd);
                
                var completedCount = completions.Sum(c => c.Count);
                var score = CalculateWeeklyScore(completedCount, activity.TargetCount, activity.Frequency);
                
                history.Add(new WeeklyScore
                {
                    WeekStartDate = weekStart,
                    WeekEndDate = weekEnd,
                    CompletedCount = completedCount,
                    TargetCount = activity.TargetCount,
                    Score = score
                });
            }
            
            WeeklyHistory = history.OrderByDescending(h => h.WeekStartDate).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading weekly history");
        }
    }

    private double CalculateWeeklyScore(int completed, int target, string frequency)
    {
        if (target == 0) return 0;
        
        var percentage = Math.Min((double)completed / target, 1.0);
        var baseScore = percentage * 100;
        
        // Apply frequency multiplier if needed
        if (frequency == AppConstants.ActivityFrequencies.Fortnightly)
        {
            // For fortnightly activities, calculate weekly equivalent
            baseScore = Math.Min(baseScore * 2, 100);
        }
        
        return baseScore;
    }
}
