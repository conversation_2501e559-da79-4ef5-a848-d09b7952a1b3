using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using HabitBuilder.Models;
using HabitBuilder.Constants;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;

namespace HabitBuilder.ViewModels;

public partial class AddActivityViewModel : ObservableObject
{
    private readonly IDataService _dataService;
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<AddActivityViewModel> _logger;

    [ObservableProperty]
    private string activityName = string.Empty;

    [ObservableProperty]
    private string description = string.Empty;

    [ObservableProperty]
    private List<string> frequencyOptions = new() { "Weekly", "Fortnightly" };

    [ObservableProperty]
    private string selectedFrequency = "Weekly";

    [ObservableProperty]
    private int targetCount = 1;

    [ObservableProperty]
    private ObservableCollection<ColorOption> colorOptions;

    [ObservableProperty]
    private ColorOption? selectedColorOption;

    [ObservableProperty]
    private bool isLimitedPeriod = false;

    [ObservableProperty]
    private DateTime startDate = DateTime.Today;

    [ObservableProperty]
    private DateTime endDate = DateTime.Today.AddDays(30);

    [ObservableProperty]
    private bool isPaidUser = false;

    [ObservableProperty]
    private bool isLoading = false;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    public AddActivityViewModel(
        IDataService dataService,
        IAuthenticationService authenticationService,
        ILogger<AddActivityViewModel> logger)
    {
        _dataService = dataService;
        _authenticationService = authenticationService;
        _logger = logger;

        // Initialize color options
        var colors = ColorOption.GetDefaultColors();
        colorOptions = new ObservableCollection<ColorOption>(colors);

        // Set default selected color (Purple)
        selectedColorOption = colorOptions.FirstOrDefault();
        if (selectedColorOption != null)
        {
            selectedColorOption.IsSelected = true;
        }

        CheckUserTier();
    }

    [RelayCommand]
    private void SelectColor(ColorOption colorOption)
    {
        // Deselect all colors
        foreach (var option in ColorOptions)
        {
            option.IsSelected = false;
        }

        // Select the chosen color
        colorOption.IsSelected = true;
        SelectedColorOption = colorOption;

        // Trigger property change notifications for UI updates
        OnPropertyChanged(nameof(ColorOptions));
    }

    [RelayCommand]
    private async Task SaveActivityAsync()
    {
        if (IsLoading) return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // Validation
            if (string.IsNullOrWhiteSpace(ActivityName))
            {
                ErrorMessage = "Activity name is required";
                return;
            }

            var user = await _authenticationService.GetCurrentUserAsync();
            if (user == null)
            {
                ErrorMessage = "User not found";
                return;
            }

            // Check activity limits based on subscription tier
            var existingActivities = await _dataService.GetActivitiesAsync(user.Id);
            var maxActivities = GetMaxActivitiesForTier(user.SubscriptionTier);
            
            if (existingActivities.Count >= maxActivities)
            {
                ErrorMessage = $"You can only have {maxActivities} activities with your current plan";
                return;
            }

            // Create new activity
            var activity = new ActivityViewModel
            {
                Id = Guid.NewGuid().ToString(),
                Name = ActivityName.Trim(),
                Description = Description.Trim(),
                Frequency = SelectedFrequency,
                TargetCount = TargetCount,
                Color = SelectedColorOption?.HexValue ?? AppConstants.UI.PrimaryColor,
                CreatedAt = DateTime.UtcNow,
                StartDate = IsLimitedPeriod ? StartDate : null,
                EndDate = IsLimitedPeriod ? EndDate : null,
                SortOrder = existingActivities.Count
            };

            // Initialize completion tracking
            InitializeActivityCompletions(activity);

            // Save activity
            var success = await _dataService.SaveActivityAsync(user.Id, activity);
            if (success)
            {
                await Shell.Current.GoToAsync("..");
            }
            else
            {
                ErrorMessage = "Failed to save activity";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving activity");
            ErrorMessage = "An unexpected error occurred";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task CancelAsync()
    {
        await Shell.Current.GoToAsync("..");
    }

    private async void CheckUserTier()
    {
        try
        {
            var user = await _authenticationService.GetCurrentUserAsync();
            IsPaidUser = user?.SubscriptionTier == AppConstants.SubscriptionTiers.PaidAccount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user tier");
        }
    }

    private int GetMaxActivitiesForTier(string subscriptionTier)
    {
        return subscriptionTier switch
        {
            AppConstants.SubscriptionTiers.NoAccount => AppConstants.ActivityLimits.NoAccountMaxActivities,
            AppConstants.SubscriptionTiers.FreeAccount => AppConstants.ActivityLimits.FreeAccountMaxActivities,
            AppConstants.SubscriptionTiers.PaidAccount => AppConstants.ActivityLimits.PaidAccountMaxActivities,
            _ => AppConstants.ActivityLimits.NoAccountMaxActivities
        };
    }

    private void InitializeActivityCompletions(ActivityViewModel activity)
    {
        var now = DateTime.UtcNow;
        var weekStart = now.AddDays(-(int)now.DayOfWeek);
        var fortnightStart = weekStart.AddDays(-7);

        // Initialize weekly completions
        activity.WeeklyCompletions = new List<DayCompletion>();
        for (int i = 0; i < 7; i++)
        {
            activity.WeeklyCompletions.Add(new DayCompletion
            {
                Date = weekStart.AddDays(i),
                Count = 0
            });
        }

        // Initialize fortnightly completions
        activity.FortnightlyCompletions = new List<DayCompletion>();
        for (int i = 0; i < 14; i++)
        {
            activity.FortnightlyCompletions.Add(new DayCompletion
            {
                Date = fortnightStart.AddDays(i),
                Count = 0
            });
        }

        activity.CurrentWeekCompletions = 0;
        activity.CurrentFortnightCompletions = 0;
        activity.CurrentScore = 0;
        activity.CanCompleteToday = true;
    }

    partial void OnActivityNameChanged(string value)
    {
        ErrorMessage = string.Empty;
    }

    partial void OnSelectedFrequencyChanged(string value)
    {
        // Update target count display text
        OnPropertyChanged(nameof(TargetCount));
    }
}
