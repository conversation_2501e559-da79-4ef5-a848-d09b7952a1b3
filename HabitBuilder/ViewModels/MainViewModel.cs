using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using HabitBuilder.Models;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.ViewModels;

public partial class MainViewModel : ObservableObject
{
    private readonly IDataService _dataService;
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<MainViewModel> _logger;

    [ObservableProperty]
    private List<ActivityViewModel> activities = new();

    [ObservableProperty]
    private double totalScore = 0;

    [ObservableProperty]
    private bool isLoading = false;

    public MainViewModel(IDataService dataService, IAuthenticationService authenticationService, ILogger<MainViewModel> logger)
    {
        _dataService = dataService;
        _authenticationService = authenticationService;
        _logger = logger;
    }

    [RelayCommand]
    private async Task LoadActivitiesAsync()
    {
        try
        {
            IsLoading = true;
            var user = await _authenticationService.GetCurrentUserAsync();
            if (user != null)
            {
                Activities = await _dataService.GetActivitiesAsync(user.Id);
                CalculateTotalScore();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading activities");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task AddActivityAsync()
    {
        // Navigate to add activity page
        await Shell.Current.GoToAsync("addactivity");
    }

    [RelayCommand]
    private async Task CompleteActivityAsync(ActivityViewModel activity)
    {
        try
        {
            var user = await _authenticationService.GetCurrentUserAsync();
            if (user != null)
            {
                await _dataService.CompleteActivityAsync(user.Id, activity.Id);
                await LoadActivitiesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing activity");
        }
    }

    [RelayCommand]
    private async Task UndoActivityAsync(ActivityViewModel activity)
    {
        try
        {
            var user = await _authenticationService.GetCurrentUserAsync();
            if (user != null)
            {
                await _dataService.UndoActivityCompletionAsync(user.Id, activity.Id);
                await LoadActivitiesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error undoing activity");
        }
    }

    [RelayCommand]
    private async Task ShowActivityHistoryAsync(ActivityViewModel activity)
    {
        // Navigate to activity history page
        await Shell.Current.GoToAsync($"activityhistory?activityId={activity.Id}");
    }

    private void CalculateTotalScore()
    {
        if (Activities?.Any() == true)
        {
            var totalPossible = Activities.Count * 100.0;
            var totalActual = Activities.Sum(a => a.CurrentScore);
            TotalScore = totalPossible > 0 ? (totalActual / totalPossible) * 100 : 0;
        }
        else
        {
            TotalScore = 0;
        }
    }
}
