using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using HabitBuilder.Models;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.ViewModels;

public partial class MainViewModel : ObservableObject
{
    private readonly IDataService _dataService;
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<MainViewModel> _logger;

    [ObservableProperty]
    private List<ActivityViewModel> activities = new();

    [ObservableProperty]
    private double totalScore = 0;

    [ObservableProperty]
    private bool isLoading = false;

    public MainViewModel(IDataService dataService, IAuthenticationService authenticationService, ILogger<MainViewModel> logger)
    {
        _dataService = dataService;
        _authenticationService = authenticationService;
        _logger = logger;
    }

    [RelayCommand]
    private async Task LoadActivitiesAsync()
    {
        try
        {
            IsLoading = true;
            var user = await _authenticationService.GetCurrentUserAsync();
            if (user != null)
            {
                Activities = await _dataService.GetActivitiesAsync(user.Id);
                CalculateTotalScore();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading activities");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task AddActivityAsync()
    {
        // Placeholder for add activity functionality
        await Task.CompletedTask;
    }

    private void CalculateTotalScore()
    {
        // Placeholder for score calculation
        TotalScore = Activities.Sum(a => a.CurrentScore);
    }
}
