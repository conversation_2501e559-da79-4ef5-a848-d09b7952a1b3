using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.ViewModels;

public partial class LoginViewModel : ObservableObject
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<LoginViewModel> _logger;

    [ObservableProperty]
    private string email = string.Empty;

    [ObservableProperty]
    private string password = string.Empty;

    [ObservableProperty]
    private bool isLoading = false;

    [ObservableProperty]
    private bool isBiometricAvailable = false;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private bool showSkipModal = false;

    public LoginViewModel(IAuthenticationService authenticationService, ILogger<LoginViewModel> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
        
        CheckBiometricAvailability();
    }

    [RelayCommand]
    private async Task LoginAsync()
    {
        if (IsLoading) return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(Password))
            {
                ErrorMessage = "Please enter both email and password";
                return;
            }

            var result = await _authenticationService.LoginAsync();
            
            if (result.IsSuccess)
            {
                // Navigate to main page
                await Shell.Current.GoToAsync("//main");
            }
            else
            {
                ErrorMessage = result.ErrorMessage ?? "Login failed";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Login error");
            ErrorMessage = "An unexpected error occurred";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task LoginWithBiometricsAsync()
    {
        if (IsLoading || !IsBiometricAvailable) return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            var result = await _authenticationService.LoginWithBiometricsAsync();
            
            if (result.IsSuccess)
            {
                // Navigate to main page
                await Shell.Current.GoToAsync("//main");
            }
            else
            {
                ErrorMessage = result.ErrorMessage ?? "Biometric login failed";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Biometric login error");
            ErrorMessage = "Biometric authentication failed";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task NavigateToSignupAsync()
    {
        await Shell.Current.GoToAsync("signup");
    }

    [RelayCommand]
    private void ShowSkipModal()
    {
        ShowSkipModal = true;
    }

    [RelayCommand]
    private void HideSkipModal()
    {
        ShowSkipModal = false;
    }

    [RelayCommand]
    private async Task SkipLoginAsync()
    {
        try
        {
            ShowSkipModal = false;
            
            // Set user as no-account tier and navigate to main page
            // This would typically create a temporary/guest user profile
            await Shell.Current.GoToAsync("//main");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Skip login error");
            ErrorMessage = "Failed to continue without login";
        }
    }

    private async void CheckBiometricAvailability()
    {
        try
        {
            IsBiometricAvailable = await _authenticationService.IsBiometricAvailableAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking biometric availability");
            IsBiometricAvailable = false;
        }
    }

    partial void OnEmailChanged(string value)
    {
        ErrorMessage = string.Empty;
    }

    partial void OnPasswordChanged(string value)
    {
        ErrorMessage = string.Empty;
    }
}
