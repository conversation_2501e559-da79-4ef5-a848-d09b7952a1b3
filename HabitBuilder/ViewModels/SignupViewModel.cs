using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.ViewModels;

public partial class SignupViewModel : ObservableObject
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<SignupViewModel> _logger;

    [ObservableProperty]
    private string email = string.Empty;

    [ObservableProperty]
    private string password = string.Empty;

    [ObservableProperty]
    private bool isLoading = false;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    public SignupViewModel(IAuthenticationService authenticationService, ILogger<SignupViewModel> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    [RelayCommand]
    private async Task SignupAsync()
    {
        if (IsLoading) return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(Password))
            {
                ErrorMessage = "Please enter both email and password";
                return;
            }

            var result = await _authenticationService.SignUpAsync(Email, Password);
            
            if (result.IsSuccess)
            {
                // Navigate to main page or show email verification message
                await Shell.Current.GoToAsync("//main");
            }
            else
            {
                ErrorMessage = result.ErrorMessage ?? "Signup failed";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Signup error");
            ErrorMessage = "An unexpected error occurred";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task NavigateToLoginAsync()
    {
        await Shell.Current.GoToAsync("login");
    }
}
