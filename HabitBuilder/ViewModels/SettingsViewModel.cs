using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HabitBuilder.Services;
using Microsoft.Extensions.Logging;

namespace HabitBuilder.ViewModels;

public partial class SettingsViewModel : ObservableObject
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<SettingsViewModel> _logger;

    [ObservableProperty]
    private bool isDarkMode = false;

    [ObservableProperty]
    private bool notificationsEnabled = true;

    public SettingsViewModel(IAuthenticationService authenticationService, ILogger<SettingsViewModel> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    [RelayCommand]
    private async Task LogoutAsync()
    {
        try
        {
            await _authenticationService.LogoutAsync();
            await Shell.Current.GoToAsync("//login");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
        }
    }
}
