<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="HabitBuilder.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:HabitBuilder"
    xmlns:views="clr-namespace:HabitBuilder.Views"
    Title="HabitBuilder">

    <!-- Authentication pages (no tabs) -->
    <ShellContent
        Title="Login"
        ContentTemplate="{DataTemplate views:LoginPage}"
        Route="login" />

    <!-- Main app with bottom tabs -->
    <TabBar Route="main">
        <ShellContent
            Title="Activities"
            Icon="home.png"
            ContentTemplate="{DataTemplate local:MainPage}"
            Route="activities" />

        <!-- Center + button -->
        <ShellContent
            Title=""
            Icon="add.png"
            ContentTemplate="{DataTemplate local:MainPage}"
            Route="add" />

        <ShellContent
            Title="Archived"
            Icon="archive.png"
            ContentTemplate="{DataTemplate views:ArchivedActivitiesPage}"
            Route="archived" />

        <ShellContent
            Title="Settings"
            Icon="settings.png"
            ContentTemplate="{DataTemplate views:SettingsPage}"
            Route="settings" />
    </TabBar>

</Shell>
