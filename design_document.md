*The best time to plant a tree was 20 years ago. The second best time is now*

The idea is for an app that lets users track activities that users would like to perform on a weekly basis as part of building better habits. Instead sticking to a strict schedule the app allows the user to  set x number of repetitions a week and give them a color coded score on how many times they have completed the task in the given week. 
There is a cumulative score of all tasks, and a per activity score as well. 
The app should encourage users who have been unable to keep up with their resolutions to stick to them

A cross platform MAUI app that targets Android, iOS, Windows, and MacCatalyst.
Backend for the app is Azure Table and Azure functions and Microsoft Entra External ID for oAuth
App has the following pages
1. Login
2. Signup
3. Main Page
4. Settings
5. Archived activities
Navigation - bottom nav bar for all pages that require user to be authenticated. 

**Subscription Tiers**
1. NoAccount - no sync between devices, limited to 2 activities, notifications are limited to once per day at a hard coded time. Frequency of activities are limited to once per day. 
2. Free Account - sync between devices, set when reminder notifications are sent every day.
   Gets a moral booster notification 2 days before the week is about to end. Limited to 4 weeks of look history. 
3. Paid Account - 10 active activities and unlimited archived activities. Widgets, disable notifications for holidays, weekend etc. Activity cadence can go from 1 per fortnight to 14 per week. Limited period activities - activities that have a start date and and end date and are then auto archived. 

**Login** 
A background image that has hues of purple and the pastel colored circles colored red, yellow and green. 
A centered email and password field and a login button and a signup link at the bottom. 
User's should have an option to skip signup as well but this will limit some features. 
When they hit skip a modal is shown outlining the benefits of signing up. The modal should have an option to continue without login or go back to login screen. 
Also provide an option to use facial recognition for login. 
Should have necessary validations and keyboard customizations that a login form should have.

**Signup**
Contains two fields:
1. Email address 
2. Password
3. Signup button - clicking which sends user an email with a verification and magic link. 
Should have necessary validations and keyboard customizations that a login form should have.
App uses deeplinking to verify the link.

**Main Page**
Onboarding - the first time a user signs up show them a language selector. Select English by default. 
Then show a modal that allows them to set their resolve level. There are 3 levels. 
1. Easygoing - this is the default level where the scoring is more forgiving. Verbiage should encourage most users to stay at this level. 
2. Resolved - a slightly stricter scoring algorithm. 
3. Clockwork - no cheat days, for the most ambitious go-getter. This is a joke level and the user won't be able to select it. 
The main screen has two parts the top 20% of the screen shows a progress fill bar that uses the cumulative score as value. 
The second half of the screen is a scroll list of the different activities. 
Each row contains the text of the activity and a x/y text next to it. 
Below the text the row is broken down into 7 cells for weekly and 14 cells for fortnightly tasks. 
Every day that a particular activity is completed will color the cell green. A number overlay for should show up if an activity is completed more than once per day. 
Swiping to the left on a row marks an activity as done. Swiping to the right will undo that if mark as done was performed in the last minute.  Long press  on the row should give the user option to archive or delete an activity. 
Short tap should show the week over week history of the activity in a modal. 

The Center of the nav bar on the Main page has a + icon that lets the user add another activity if their tier quota permits it. 
The app should discourage the users from adding more than 5 activities. This should only happen when the current activity count is a multiple of 5 and caps at 20.

On other pages this center icon should take the user back to the main page. 

**Archived Activities** 
Show all archived activities each in its own row. Button on the right side to unarchive an activity. Show a dialog to confirm this action. 

**Settings** 
Has the following options. 
1. Toggle between light and dark mode. 
2. Toggle once or twice a day notifications - tier permitting, and set times for those notifications. 
3. Silence notifications on particular days. User should be given 7 boxes representing 7 days of the week and should be able to click them to toggle notifications on certain days. 
4. Vacation mode: Notification - present the user with a calendar to select a start date and an end date. No notification in this period. 
5. Turn off all notifications. 
6. Change resolve level 
7. Delete account -permadeath. 
